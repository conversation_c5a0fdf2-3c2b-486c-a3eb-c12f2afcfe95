# There are a number of modifiers that are allowed to be used in some of the
# fields.  See the udev man page for a full description of them.
#
# See the udev.rules.examples file for more examples of how to create rules
#


# Note you must use the RUN option if trying to use actual device in script

KERNEL=="fd[0-9]*",      SYMLINK=="floppy/%n",  RUN+="/etc/udev/scripts/floppy.sh"
KERNEL=="lp[0-9]*",      SYMLINK=="printers/%n", OWNER="root", GROUP="lp", MODE="0666"

#Storage Devices
SUBSYSTEM=="block", ENV{ID_CDROM}=="1", ENV{GENERATED}!="1", IMPORT{program}="cdrom_id $devnode"
ENV{ID_FS_USAGE}=="filesystem", RUN+="/etc/udev/scripts/scsi.sh", OPTIONS="last_rule"
SUBSYSTEM=="block", <PERSON><PERSON><PERSON>{ID_CDROM}=="1", RUN+="/etc/udev/scripts/scsi.sh", OPTIONS="last_rule"
