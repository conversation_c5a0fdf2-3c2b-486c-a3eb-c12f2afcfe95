.ig
www.tmac - macro package for adding HTML elements to roff documents.

------------------------------------------------------------------------
    Legalese
------------------------------------------------------------------------

This file is part of groff, the GNU roff type-setting system.

Copyright (C) 2001-2020 Free Software Foundation, Inc.
written by <PERSON> <<EMAIL>>, with additions by
<PERSON> <<EMAIL>> and
<PERSON><PERSON> <<EMAIL>>.

groff is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License as published by the Free
Software Foundation, either version 3 of the License, or (at your
option) any later version.

groff is distributed in the hope that it will be useful, but WITHOUT ANY
WARRANTY; without even the implied warranty of MERCHANTABILITY or
FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
for more details.

You should have received a copy of the GNU General Public License along
with this program.  If not, see <http://www.gnu.org/licenses/>.


------------------------------------------------------------------------
    Description
------------------------------------------------------------------------

A simple set of macros to provide HTML (or XHTML) documents with basic
www functionality.  It should work with any macro set.  In the
following, HTML always denotes XHTML also.
..
.
.
.\" --------------------------------------------------------------------
.\" Setup
.\" --------------------------------------------------------------------
.
.do if d www:lenstr .nx
.
.do nr *groff_www_tmac_in_C \n[.cp]
.cp 0
.
.mso devtag.tmac
.
.
.\" Taken from tmac/an-ext.tmac
.\" Map mono-width fonts to standard fonts for groff's TTY device.
.if n \{\
.  do ftr CR R
.  do ftr CW R
.  do ftr CI I
.  do ftr CB B
.\}
.
.ie r xhtml \
.  ds www-> />\"
.el \
.  ds www-> >\"
.
.nr www-html 0
.if '\*[.T]'html' \
.  nr www-html 1
.
.\" set up www-image-template
.
.if !d www-image-template \
.  ds www-image-template
.
.if r ps4html \{\
.  \" remove the title command when we are generating images for HTML
.  \" (stops a title accidentally appearing inside an image)
.  di www-notitle
.  tl ''''
.  di
.  rm tl
.  de tl
.  .
.\}
.
.
.\" --------------------------------------------------------------------
.\" Test for '.substring'; result in register 'www.substring_ok'.
.\" The automated break points in .URL addresses are only added if
.\" this register is non-zero.
.\"
.nr www:substring_ok 0
.de www:@test_substring
.  if !d substring \
.    return
.  ds \\$0:s abcdefg\"
.  substring \\$0:s 1 1
.  if !'\\*[\\$0:s]'b' \{\
.    rm \\$0:s
.    return
.  \}
.  ds \\$0:s abcdefg\"
.  substring \\$0:s 0 0
.  if !'\\*[\\$0:s]'a' \{\
.    rm \\$0:s
.    return
.  \}
.  ds \\$0:s abcdefg\"
.  substring \\$0:s 1 -1
.  if !'\\*[\\$0:s]'bcdefg' \{\
.    rm \\$0:s
.    return
.  \}
.  nr www:substring_ok 1
.  rm \\$0:s
..
.www:@test_substring
.rm www:@test_substring
.
.
.\" --------------------------------------------------------------------
.\" Local Macros
.\" --------------------------------------------------------------------
.
.\" --------------------------------------------------------------------
.\" www:paraspace
.\"
.\" Space before paragraph. Use \n[PD] if it exists.
.\"
.nr www:pd 0.5v
.
.de www:paraspace
.  ie r PD \
.    sp \\n[PD]u
.  el \
.    sp \\n[www:pd]u
..
.
.\" --------------------------------------------------------------------
.\" www:error (<test>...)
.\"
.\" Print error message.
.\"
.de www-error
.  tm \\n[.F]:\\n[.c]: macro error: \\$*
..
.
.als www:error www-error
.
.\" --------------------------------------------------------------------
.\" www:fatal (<test>...)
.\"
.\" Print fatal error message and abort.
.\"
.de www-fatal
.  ab \\n[.F]:\\n[.c]: fatal macro error: \\$*
..
.
.als www:fatal www-fatal
.
.\" --------------------------------------------------------------------
.\" www:lenstr (<register_name> <string_name>)
.\"
.\" Store length of string named <string_name> into register named
.\" <register_name>.
.\"
.de www:lenstr
.  if !(\\n[.$] == 2) \
.    www:fatal .\\$0 expects 2 arguments, got \\n[.$]
.  length \\$0:n x\\*[\\$2]
.  nr \\$1 (\\n[\\$0:n] - 1)
.  rr \\$0:n
..
.
.\" --------------------------------------------------------------------
.\" www:splitstr (<name>)
.\"
.\" Add a space character between any two adjacent characters in string
.\" <name> and restore result into the string variable <name>; space
.\" characters are first replaced by the word 'space'.
.\"
.de www:splitstr
.  if !(\\n[.$] == 1) \
.    www:error .\\$0 expects 1 argument, got \\n[.$]
.  if '\\*[\\$1]'' \
.    return
.  ds \\$0:r "\\*[\\$1]\"
.  ds \\$0:s
.  while 1 \{\
.    ds \\$0:c "\\*[\\$0:r]\"
.    substring \\$0:c 0 0
.    ie '\\*[\\$0:c]' ' \
.      as \\$0:s " space\"
.    el \
.      as \\$0:s " \\*[\\$0:c]\"
.    www:lenstr \\$0:n \\$0:r
.    if (\\n[\\$0:n] <= 1) \
.      break
.    substring \\$0:r 1 -1
.  \}
.  if !'\\*[\\$0:s]'' \
.    substring \\$0:s 1 -1 
.  ds \\$1 \\*[\\$0:s]\"
.  rm \\$0:c
.  rr \\$0:n
.  rm \\$0:r
.  rm \\$0:s
..
.
.\" --------------------------------------------------------------------
.\" www:url_breaks (<string_name>)
.\"
.\" Add '\:' (possible break point) within URL strings after '/'.
.\"
.\" Smart about multiple '/', existing '\:', and space characters;
.\" does not set a break point if less than 5 characters would go to
.\" the next line.
.\"
.de www:url_breaks
.  if !(\\n[.$] == 1) \
.    www:error .\\$0 expects 1 argument, got \\n[.$]
.  if !\n[www:substring_ok] \
.    return
.  ds \\$0:s "\\*[\\$1]\"
.  www:splitstr \\$0:s
.  www:url_breaks_split \\$0:s \\*[\\$0:s]
.  ds \\$1 "\\*[\\$0:s]\"
.  rm \\$0:s
..
.
.\" --------------------------------------------------------------------
.\" www:url_breaks_split (<result> <char> [<char>...])
.\"
.\" Add '\:' within URL strings, but arguments are a split string.
.\"
.\" Arguments: >=2: <result> <char> [<char>...]
.\"
.de www:url_breaks_split
.  nr \\$0:min 5        \" minimal number of characters for next line
.  if (\\n[.$] < 2) \
.    www:error .\\$0 expects at least 2 arguments, got \\n[.$]
.  ds \\$0:res \\$1\"
.  shift
.  ds \\$0:s
.  nr \\$0:done 0
.  while !\\n[\\$0:done] \{\
.    if (\\n[.$] <= 0) \
.      break
.    if '\\$1'space' \{\
.      as \\$0:s " \"
.      shift
.      continue
.    \}
.    if (\\n[.$] < \\n[\\$0:min]) \{\
.      as \\$0:s "\\$1\"
.      shift
.      continue
.    \}
.    if !'\\$1'/' \{\
.      as \\$0:s "\\$1\"
.      shift
.      continue
.    \}
.    \" we are at a '/' character
.    while '\\$1'/' \{\
.      as \\$0:s /\"
.      if (\\n[.$] == 0) \{\
.        nr \\$0:done 1
.        break
.      \}
.      shift
.    \}
.    if \\n[\\$0:done] \
.      break
.    if (\\n[.$] < \\n[\\$0:min]) \
.      continue
.    if '\\$1'\:' \
.      shift
.    as \\$0:s \:\"
.  \}
.  ds \\*[\\$0:res] \\*[\\$0:s]\"
.  rm \\$0:res
.  rm \\$0:s
..
.
.
.\" --------------------------------------------------------------------
.\" User Interface
.\" --------------------------------------------------------------------
.
.\" --------------------------------------------------------------------
.\" HTML
.\"
.\" The main auxiliary macro for the HTML interface.
.\"
.de HTML
.  if \\n[www-html] \{\
.    \" was implemented via .nop \&\X^html:\\$*^ but
.    \" is now implemented using HTML-NS to utilize code factoring.
.    \"
.    \" the '\&' makes the vertical mode leave, so to say
.    \"
.    nop \&\c
.    HTML-NS \\$*
.    nop \&
.  \}
..
.
.\"
.\" An auxiliary macro for HTML (without following space).
.\"
.de HTML-NS
.  nop \X^html:\\$*^\c
..
.
.\"
.\" Emit an HTML tag after shutting down a (possibly open) paragraph.
.\"
.de HTML</p>
.  ie \\n[www-html] \{\
.    \" the '\&' makes the vertical mode leave, so to say
.    nop \&\X^html</p>:\\$*^
.  \}
.  el \
.    www:paraspace
..
.
.\"
.\" Emit an HTML tag.  If text has been written in the paragraph
.\"                    then do not shut the paragraph down.
.\"                    If text was not written, remove the empty
.\"                    paragraph tag and emit the desired HTML tag.
.\"
.de HTML<?p>
.  if \\n[www-html] \
.    nop \&\X^html<?p>:\\$*^
..
.
.\"
.\" Emit a MATH tag.  If text has been written in the paragraph
.\"                   then do not shut the paragraph down.
.\"                   If text was not written, remove the empty
.\"                   paragraph tag and emit the desired math tag.
.\"
.de MATH<?p>
.  if \\n[www-html] \
.    nop \&\X^math<?p>:\\$*^
..
.
.\" --------------------------------------------------------------------
.\" HX n
.\"
.\"   Automatic heading level cut off.
.\"
.\"   N is the depth limit of automatically linked headings.  So a depth
.\"   of 2 would cause grohtml to generate a list of links for '.NH 1'
.\"   and '.NH 2' but not for '.NH 3'.
.\"
.de HX
.  if \\n[www-html] \
.    nop \X^index:\\$*^
..
.
.\" --------------------------------------------------------------------
.\" BCL foreground background active not-visited visited
.\"
.de BCL
.  HTML <body "text=""\\$1""" \
              "bgcolor=""\\$2""" \
              "link=""\\$3""" \
              "alink=""\\$4""" \
              "vlink=""\\$5"">"
..
.
.\" --------------------------------------------------------------------
.\" BGIMG imagefile
.\"
.de BGIMG
.  HTML <body "background=""\\$1"">"
..
.
.\" www:url_check_tag str
.\"    If url named by \*[str] is internal (starts with #), 
.\"    redefine \*[str] to be the value of the string TAG_url.
.
.de www:url_check_tag
.  ds \\$0:tmp \\*[\\$1]\"
.  substring \\$0:tmp 0 0
.  if '\\*[\\$0:tmp]'#' \{\
.    ds \\$0:tmp \\*[\\$1]\"
.    substring \\$0:tmp 1
.    ds \\$0:tmp TAG_\\*[\\$0:tmp]\"
.
.    ie d \\*[\\$0:tmp] \
.      ds \\$1 \\*[\\*[\\$0:tmp]]\"
.    el \
.      ds \\$1 see below\"
.  \} 
.  rm \\$0:tmp
..
.
.\" --------------------------------------------------------------------
.\" URL url [description] [after]
.\"
.\"   If description is absent then the url becomes the anchor text.
.\"
.de URL
.  if !'\\$1'' \{\
.    ds \\$0:adr \\$1\"
.    www:url_check_tag \\$0:adr
.    if !\\n[www-html] \
.      www:url_breaks \\$0:adr
.  \}
.  ie \\n[www-html] \{\
.    ie '\\$3'' \
.      ds \\$0:after \&\"
.    el \
.      ds \\$0:after \&\\$3\"
.
.    ie '\\$2'' \
.       HTML-NS <a "href=""\\$1"">\\*[\\$0:adr]</a>"
.    el \
.       HTML-NS <a "href=""\\$1"">\\$2</a>"
.
.    nop \\*[\\$0:after]
.    rm \\$0:after
.  \}
.  el \{\
.    if !r ps4html \
.      ad l
.
.    ie '\\$2'' \{\
.      ie '\\$1'' \{\
.        if !'\\$3'' \
.          nop \\$3
.      \}
.      el \{\
.        ie \\n[.color] \
.          nop \%\\*[www:open]\m[\\*[www:color]]\f[\\*[www:fontstyle]]\\*[\\$0:adr]\f[]\m[]\\*[www:close]\\$3
.        el \
.          nop \%\\*[www:open]\f[\\*[www:fontstyle]]\\*[\\$0:adr]\f[]\\*[www:close]\\$3
.      \}
.    \}
.    el \{\
.      ie '\\$1'' \{\
.        ie \\n[.color] \
.          nop \m[\\*[www:color]]\\$2\m[]\\$3
.        el \
.          nop \f[\\*[www:fontstyle]]\\$2\f[]\\$3
.      \}
.      el \{\
.        ie \\n[.color] \{\
.          nop \m[\\*[www:color]]\\$2\m[]
.          nop \%\\*[www:open]\f[\\*[www:fontstyle]]\\*[\\$0:adr]\f[]\\*[www:close]\\$3
.        \}
.        el \{\
.          nop \f[\\*[www:fontstyle]]\\$2\f[]
.          nop \%\\*[www:open]\f[\\*[www:fontstyle]]\\*[\\$0:adr]\f[]\\*[www:close]\\$3
.        \}
.      \}
.    \}
.
.    if !r ps4html \
.      ad
.  \}
.  rm \\$0:adr
..
.
.\" --------------------------------------------------------------------
.\" FTP url description [after]
.\"
.\"   Same as URL.
.\"
.als FTP URL
.
.\" --------------------------------------------------------------------
.\" MTO address description [after]
.\"
.\"   ADDRESS is the email address (without the 'mailto:' prefix).
.\"
.\"   DESCRIPTION is the optional name.  If an empty argument is given,
.\"   ADDRESS is used instead.
.\"
.\"   AFTER is optional stuff printed immediately after ADDRESS
.\"   (resp. DESCRIPTION).
.\"
.\"     Example:
.\"
.\"       Foobar has been written by
.\"       .MTO <EMAIL> "Fredrick Bloggs" .
.\"
.de MTO
.  ie \\n[www-html] \{\
.    ie '\\$2'' \
.      URL mailto:\\$1 \\$1 "\\$3"
.    el \
.      URL mailto:\\$1 "\\$2" "\\$3"
.  \}
.  el \{\
.    ie '\\$2'' \{\
.      ie '\\$1'' \{\
.        ie !'\\$3'' \
.          nop \\$3
.      \}
.      el \{\
.        ie \\n[.color] \
.          nop \%\m[\\*[www:color]]\f[\\*[www:fontstyle]]\\$1\f[]\m[]\\$3
.        el \
.          nop \%\f[\\*[www:fontstyle]]\\$1\f[]\\$3
.      \}
.    \}
.    el \{\
.      ie '\\$1'' \{\
.        ie \\n[.color] \
.          nop \m[\\*[www:color]]\\$2\m[]\\$3
.        el \
.          nop \f[\\*[www:fontstyle]]\\$2\f[]\\$3
.      \}
.      el \{\
.        ie \\n[.color] \{\
.          nop \m[\\*[www:color]]\\$2\m[]
.          nop \%\\*[www:open]\f[\\*[www:fontstyle]]\\$1\f[]\\*[www:close]\\$3
.        \}
.        el \{\
.          nop \f[\\*[www:fontstyle]]\\$2\f[]
.          nop \%\\*[www:open]\f[\\*[www:fontstyle]]\\$1\f[]\\*[www:close]\\$3
.        \}
.      \}
.    \}
.  \}
..
.
.\" --------------------------------------------------------------------
.\" TAG name [text]
.\"
.\"   Generate an HTML name NAME.
.\"   Define string TAG_NAME to TEXT, if present, otherwise to \n[PN].
.\"   Register PN is the current page number.
.\"
.de TAG
.  HTML <a "name=""\\$1""></a>"
.  ie '\\$2'' \
.    if !rPN \
.      nr PN \\n%
.    ds TAG_\\$1 \\n[PN]\"
.  el \
.    ds TAG_\\$1 \\$2\"
..
.
.\" --------------------------------------------------------------------
.\" IMG [-R|-L|-C] filename [width] [height]
.\"
.\"   Include an image of any type (only works for -Thtml).
.\"
.\"   Alignment is centered by default (-C).
.\"   Default value for WIDTH is 1i.
.\"   If HEIGHT is not given, WIDTH is used as the height.
.\"
.de IMG
.  ie \\n[www-html] \{\
.    ie '\\$2'-R' \
.      DEVTAG .right-image
.    el \{\
.      ie '\\$2'-L' \
.        DEVTAG .left-image
.      el \
.        DEVTAG .centered-image
.    \}
.    nr www-width 100
.    if !'\\$3'' \
.      nr www-width \\$3
.    nr www-height \\n[www-width]
.    if !'\\$4'' \
.      nr www-height \\$4
.    HTML <img "src=""\\$1""" \
               "alt=""Image \\$1""" \
               "width=""\\n[www-width]""" \
               "height=""\\n[www-height]""\\*[www->]"
.  \}
.  el \
.    nop \\*[www:open]\f[\\*[www:fontstyle]]\\$1\f[]\\*[www:close]
..
.
.\" --------------------------------------------------------------------
.\" PIMG  [-R|-L|-C] filename [width] [height]
.\"
.\"   Include a PNG image.  It works for -Tps and -Thtml.
.\"   The default value for WIDTH and HEIGHT is zero; the default
.\"   alignment is centering (-C).
.\"
.\" Note: This macro can only be used with the '-U' option of groff,
.\"       activating unsafe mode, if not used with -Thtml; the PNG image
.\"       is then converted to the EPS format using netpbm utilities.
.\"
.de PIMG
.  ds www-pic-align -C\"
.  ie '\\$1'-R' \{\
.    ds www-pic-align -R\"
.    shift
.  \}
.  el \{\
.    if '\\$1'-L' \{\
.      ds www-pic-align -L\"
.      shift
.    \}
.  \}
.  if '\\$1'-C' \
.    shift
.
.  ie \\n[www-html] \{\
.    ds www-htmlalign align="center"\"
.    if '\\*[www-pic-align]'-R' \
.      ds www-htmlalign align="right"\"
.    if '\\*[www-pic-align]'-L' \
.      ds www-htmlalign align="left"\"
.
.    nr www-width 0
.    nr www-height 0
.    if !'\\$2'' \
.      nr www-width (\\$3 * 100 / 240)
.    if !'\\$3'' \
.      nr www-height (\\$4 * 100 / 240)
.    ie (\\n[www-width] == 0) \{\
.      ie (\\n[www-height] == 0) \
.        HTML</p> <p \\*[www-htmlalign]><img "src=""\\$1""" \
                                             "alt=""Image \\$1""\\*[www->]</p>"
.      el \
.        HTML</p> <p \\*[www-htmlalign]><img "src=""\\$1""" \
                                             "alt=""Image \\$1""" \
                                             "height=""\\n[www-height]""\\*[www->]</p>"
.    \}
.    el \{\
.      ie (\\n[www-height] == 0) \
.        HTML</p> <p \\*[www-htmlalign]><img "src=""\\$1""" \
                                             "alt=""Image \\$1""" \
                                             "width=""\\n[www-width]""\\*[www->]</p>"
.      el \
.        HTML</p> <p \\*[www-htmlalign]><img "src=""\\$1""" \
                                             "alt=""Image \\$1""" \
                                             "width=""\\n[www-width]""" \
                                             "height=""\\n[www-height]""\\*[www->]</p>"
.    \}
.  \}
.  el \{\
.    if !r ps4html \{\
.      www-make-unique-name
.      sy pngtopnm \\$1 \
          | pnmcrop -white \
          | pnmtops -quiet -nosetpage -noturn \
          > \\*[www-unique-name].eps
.      shift
.      PSPIC \\*[www-pic-align] \\*[www-unique-name].eps \\$*
.    \}
.  \}
..
.
.\" --------------------------------------------------------------------
.\" auxiliary definitions for MPIMG
.\"
.nr www-left-ll-trap 0
.nr www-left-po-trap 0
.nr www-right-ll-trap 0
.
.de www-finish-left-po
.  po -(\\n[www-left-indent]u + \\n[www-image-gap]u)
.  wh \\n[www-left-po-trap]u
.  nr www-left-indent 0
..
.
.\" called when the -R picture is finished
.de www-finish-right-ll
.  ll +(\\n[www-right-indent]u + \\n[www-image-gap]u)
.
.  \" now check whether we need to inline www-finish-left-ll
.  if (\\n[www-left-ll-trap]u > 0) \
.    if ((\\n[www-right-ll-trap]u + 1v) >= \\n[www-left-ll-trap]u) \{\
.      mk www-left-po-trap
.      nr www-left-po-trap +1v
.      wh \\n[www-left-po-trap]u www-finish-left-po
.      ll +\\n[www-left-indent]u
.      wh \\n[www-left-ll-trap]u
.      nr www-left-ll-trap 0
.    \}
.
.  \" and check whether we need to inline www-finish-left-po
.  if (\\n[www-left-po-trap]u > 0) \
.    if ((\\n[www-right-ll-trap]u + 1v) >= \\n[www-left-po-trap]u) \{\
.      po -\\n[www-left-indent]u
.      wh \\n[www-left-po-trap]u
.      nr www-left-indent 0
.    \}
.
.  wh \\n[www-right-ll-trap]u
.  nr www-right-ll-trap 0
..
.
.de www-finish-left-ll
.  if (\\n[www-right-ll-trap] > 0) \
.    if ((\\n[www-left-ll-trap] + 1v) >= \\n[www-right-ll-trap]) \{\
.      ll +\\n[www-right-indent]u
.      nr www-right-ll-trap 0
.    \}
.
.  mk www-left-po-trap
.  nr www-left-po-trap +1v
.  wh \\n[www-left-po-trap]u www-finish-left-po
.  ll +(\\n[www-left-indent]u + \\n[www-image-gap]u)
.  wh \\n[www-left-ll-trap]u
.  nr www-left-ll-trap 0
..
.
.\" www-handle-percent arg N1 N2 S1
.\"     arg - input string (number or number%)
.\"     output parameters:
.\"         N1 - name of number register 1=absolute 0=percentage
.\"         N2 - number register name for absolute value
.\"         S1 - string register name for percentage value
.\"
.de www-handle-percent
.  ds www-percent \\$1\"
.  substring www-percent -1 -1
.
.  ie '\\*[www-percent]'%' \{\
.    ds www-abs \\$1\"
.    substring www-abs 0 -2
.    nr \\$2 0
.    nr \\$3 \\*[www-abs]
.    ds \\$4 \\$1\"
.  \}
.  el \{\
.    nr \\$2 1
.    nr \\$3 \\$1
.    ds \\$4 none\"
.  \}
..
.
.\" --------------------------------------------------------------------
.\" MPIMG [-R|-L] [-G gap] filename [width [height]]
.\"
.\"   Include a PNG image and wrap text around it.  It works for
.\"   -Tps and -Thtml.  The default value for WIDTH is 1i; default value
.\"   for HEIGHT is WIDTH; the default alignment is left (-L).
.\"   -G is used to insert a gap between the text and the image.
.\"   The height and width can also be given as a percentage.
.\"   The PostScript device converts the percentage width into an
.\"   absolute value by using \\n[.l], and the height by using \\n[.p].
.\"
.\"
.\" Note: This macro can only be used with the '-U' option of groff,
.\"       activating unsafe mode, if not used with -Thtml; the PNG image
.\"       is then converted to the EPS format using netpbm utilities.
.\"
.nr www-htmlimage-gap 0
.
.de MPIMG
.  nr www-image-just 1
.  nr www-image-gap 0
.  while (\\n[.$] > 0) \{\
.    if '-L'\\$1' \{\
.      nr www-image-just 1
.      shift
.      continue
.    \}
.    if '-R'\\$1' \{\
.      nr www-image-just 0
.      shift
.      continue
.    \}
.    if '-G'\\$1' \{\
.      nr www-image-gap \\$2
.      nr www-htmlimage-gap (\\$2 * 100 / 240)
.      shift 2
.      continue
.    \}
.    break
.  \}
.
.  nr www-width 1i
.  nr www-height 1i
.  ds www-size-specs width="\\n[www-width]" height="\\n[www-height]"\"
.  ie !'\\$2'' \{\
.    nr www-is-absolute 0
.    nr www-absolute 0
.    ds www-percentage none\"
.    www-handle-percent \\$2 www-is-absolute www-absolute www-percentage
.    ie !\\n[www-is-absolute] \{\
.      \" percentage of linelength requested
.      nr www-width (\\n[www-absolute] * \\n[.l] / 100)
.      if \\n[www-html] \
.        nr www-width (\\n[www-width] * 100 / 240)
.      ds www-size-specs width="\\*[www-percentage]"\"
.    \}
.    el \{\
.      nr www-width \\n[www-absolute]
.      if \\n[www-html] \
.        nr www-width (\\n[www-width] * 100 / 240)
.      ds www-size-specs width="\\n[www-width]"\"
.    \}
.
.    nr www-height \\n[www-width]
.    ie !'\\$3'' \{\
.      nr www-is-absolute 0
.      nr www-absolute 0
.      ds www-percentage none\"
.      www-handle-percent \\$3 www-is-absolute www-absolute www-percentage
.      ie !\\n[www-is-absolute] \{\
.        \" percentage of pagelength requested
.        nr www-height (\\n[www-absolute] * \\n[.p] / 100)
.        if \\n[www-html] \
.           nr www-height (\\n[www-height] * 100 / 240)
.        ds www-size-specs "\\*[www-size-specs] height="\\*[www-percentage]"\"
.      \}
.      el \{\
.        nr www-height \\n[www-absolute]
.        if \\n[www-html] \
.           nr www-height (\\n[www-height] * 100 / 240)
.        ds www-size-specs "\\*[www-size-specs] height="\\*[www-height]"\"
.      \}
.    \}
.  \}
.  el \{\
.    \" height not specified; use width value
.    ie !\\n[www-is-absolute] \{\
.       \" percentage value
.       ds www-size-specs "\\*[www-size-specs] height="\\*[www-percentage]"\"
.       nr www-height \\n[www-width]
.    \}
.    el \{\
.       ds www-size-specs "\\*[www-size-specs] height="\\*[www-width]"\"
.       nr www-height \\n[www-width]
.    \}
.  \}
.
.  ie \\n[www-html] \{\
.    ie !\\n[www-image-just] \
.      HTML <img "src=""\\$1""" \
                 "alt=""Image \\$1""" \
                 "hspace=""\\n[www-htmlimage-gap]""" \
                 "align=""right""" \
                 "\\*[www-size-specs]\\*[www->]"
.    el \
.      HTML <img "src=""\\$1""" \
                 "alt=""Image \\$1""" \
                 "hspace=""\\n[www-htmlimage-gap]""" \
                 "align=""left""" \
                 "\\*[www-size-specs]\\*[www->]"
.  \}
.  el \{\
.    tm www-width is \\n[www-width]
.    tm www-height is \\n[www-height]
.    if !r ps4html \{\
.      www-make-unique-name
.      sy pngtopnm \\$1 \
          | pnmcrop -white \
          | pnmtops -quiet -nosetpage -noturn \
          > \\*[www-unique-name].eps
.      ie !\\n[www-image-just] \{\
.        \" we must now disable a possible left image trap
.        sp -1
.        if (\\n[www-left-ll-trap] > 0) \
.          wh \\n[www-left-ll-trap]u
.        if (\\n[www-left-po-trap] > 0) \
.          wh \\n[www-left-po-trap]u
.        PSPIC -R \\*[www-unique-name].eps \\n[www-width]u \\n[www-height]u
.        sp -\\n[ps-desht]u
.
.        nr www-right-indent \\n[ps-deswid]u
.        \" we want to have some space between text and image,
.        \" so the line length must be shorter
.        ll -(\\n[www-right-indent]u + \\n[www-image-gap]u)
.        mk www-right-ll-trap
.        nr www-right-ll-trap +(\\n[ps-desht]u - 1v)
.        wh \\n[www-right-ll-trap]u www-finish-right-ll
.
.        \" now restore possible left trap
.        if (\\n[www-left-ll-trap] > 0) \
.          wh \\n[www-left-ll-trap]u www-finish-left-ll
.        if (\\n[www-left-po-trap] > 0) \
.          wh \\n[www-left-po-trap]u
.      \}
.      el \{\
.        \" we must now disable a possible right image trap
.        if (\\n[www-right-ll-trap] > 0) \
.          wh \\n[www-right-ll-trap]u
.        PSPIC -L \\*[www-unique-name].eps \\n[www-width]u \\n[www-height]u
.        sp -\\n[ps-desht]u
.
.        nr www-left-indent \\n[ps-deswid]u
.        \" increase offset by gap
.        po +(\\n[www-left-indent]u + \\n[www-image-gap]u)
.        \" decrease line length by gap
.        ll -(\\n[www-left-indent]u + \\n[www-image-gap]u)
.        mk www-left-ll-trap
.        nr www-left-ll-trap +(\\n[ps-desht]u - 1v)
.        wh \\n[www-left-ll-trap]u www-finish-left-ll
.
.        \" now restore possible right trap
.        if (\\n[www-right-ll-trap] > 0) \
.          wh \\n[www-right-ll-trap]u www-finish-right-ll
.      \}
.    \}
.  \}
..
.
.\" --------------------------------------------------------------------
.\" HnS n
.\"
.\"   Begin heading.  Heading level is N.
.\"
.\" HnE
.\"
.\"   End heading.
.\"
.\" If your heading contains URL, FTP, MTO macros you might wish to
.\" disable automatic links to headings.  This can be done via '-P-l'
.\" from the command line or by using a cakk to '.HX 0'.
.\"
.nr www-heading-no -1
.
.de HnS
.  ie '\\$1'' \
.    nr www-heading-no 1
.  el \
.    nr www-heading-no \\$1
.  DEVTAG-NH \\n[www-heading-no]
..
.
.de HnE
.  if (\\n[www-heading-no] == -1) \
.    www-error HnE found without a corresponding HnS
.  DEVTAG-EO-H
..
.
.\" --------------------------------------------------------------------
.\" LK
.\"
.\"   Emit the automatically collected links derived from
.\"   section/numbered headings at this position.
.\"
.de LK
.  DEVTAG .links
..
.
.\" --------------------------------------------------------------------
.\" HR
.\"
.\"   Produce a horizontal line.
.\"
.de HR
.  HTML</p> <hr\\*[www->]
..
.
.\" --------------------------------------------------------------------
.\" NHR
.\"
.\"  Suppress the generation of the top and bottom rules which grohtml
.\"  emits by default.
.\"
.de NHR
.  DEVTAG .no-auto-rule
..
.
.\"
.\" www-end-nowhere - end of input trap called to finish diversion.
.\"
.de www-end-nowhere
.  if !\\n[www-html] \
.    di
.  DEVTAG-EO-TL
..
.
.\" --------------------------------------------------------------------
.\" HTL
.\"
.\"   Generate an HTML title only.  This differs from the -ms .TL macro
.\"   which generates both an HTML title and an H1 heading.
.\"
.\"   This is useful when an author wishes to use an HTML title as
.\"   search engine fodder but a graphic title in the document.
.\"
.\"   The macro terminates when a space or break is seen (.sp, .br).
.\"
.de HTL
.  DEVTAG .html-tl
.  if !\\n[www-html] \
.    di www-nowhere
.  it 2 www-end-nowhere
..
.
.\" --------------------------------------------------------------------
.\" auxiliary definitions for lists
.\"
.ds www-ul-level1 \[bu]\ \ \"
.ds www-ul-level2 \[sq]\ \ \"
.ds www-ul-level3 \[ci]\ \ \"
.nr www-ul-level 0
.
.ds www-ol-level1 decimal\"
.ds www-ol-level2 lower-alpha\"
.ds www-ol-level3 lower-roman\"
.ds www-ol-tmp 00\ \ \"
.nr www-ol-ctr1 0 1
.nr www-ol-ctr2 0 1
.nr www-ol-ctr3 0 1
.af www-ol-ctr2 a
.af www-ol-ctr3 i
.nr www-ol-level 0
.
.nr www-dl-level 0
.nr www-dl-shift 5n
.
.\"
.\" allow nested lists
.\"
.nr www-depth 0
.nr www-li-indent \n[.i]
.ds www-level0 nop\"
.ds www-level1
.ds www-level2
.ds www-level3
.ds www-level4
.ds www-level5
.ds www-level6
.ds www-level7
.ds www-level8
.ds www-level9
.
.\" which macro to use for LI
.de www-push-li
.  nr www-li-indent \\n[.i]
.  nr www-depth +1
.  ds www-level\\n[www-depth] \\$1\"
.  ds www-ltag\\n[www-depth]
.  als LI \\$1
..
.
.de www-pop-li
.  nr www-depth -1
.  als LI \\*[www-level\\n[www-depth]]
..
.
.\" www-emit-ltag - shut down a previous open list tag
.\"                 before issuing a new tag \\$1.
.\"                 It then records tag \\$1 is open.
.
.de www-emit-ltag
.  if !'\\*[www-ltag\\n[www-depth]]'' \
.    HTML-NS </\\*[www-ltag\\n[www-depth]]>
.  if !'\\$1'' \
.    HTML-NS <\\$1>
.  ds www-ltag\\n[www-depth] \\$1\"
..
.
.\"
.\" Auxiliary macro for ULS.
.\"
.de www-push-ul-level
.  nr www-ul-level +1
.  if (\\n[www-ul-level] > 3) \
.    www-error ULS: too many levels of indentation (\\n[www-ul-level])
..
.\"
.\" Auxiliary macro for ULE.
.\"
.de www-pop-ul-level
.  if !\\n[www-ul-level] \
.    www-error ULE: trying to terminate a list which does not exist
.  nr www-ul-level -1
..
.
.\"
.\" Auxiliary macro for OLS.
.\"
.de www-push-ol-level
.  nr www-ol-level +1
.  if (\\n[www-ol-level] > 3) \
.    www-error OLS: too many levels of indentation (\\n[www-ol-level])
..
.
.\"
.\" Auxiliary macro for OLE.
.\"
.de www-pop-ol-level
.  if !\\n[www-ol-level] \
.    www-error OLE: trying to terminate a list which does not exist
.  nr www-ol-level -1
..
.
.\" --------------------------------------------------------------------
.\" ULS
.\"
.\"   Start an unordered list.
.\"
.de ULS
.  www-push-li www-li-ul
.  www-push-ul-level
.  ie \\n[www-html] \{\
.    www-emit-ltag
.    HTML</p> <ul>
.  \}
.  el \
.    nr www-li-indent +\w'\\*[www-ul-level\\n[www-ul-level]]'u
..
.
.\" --------------------------------------------------------------------
.\" ULE
.\"
.\"   End an unordered list.
.\"
.de ULE
.  ie \\n[www-html] \{\
.    www-emit-ltag
.    HTML</p> </ul>
.  \}
.  el \{\
.    nr www-li-indent -\w'\\*[www-ul-level\\n[www-ul-level]]'u
.    in \\n[www-li-indent]u
.  \}
.  www-pop-ul-level
.  www-pop-li
..
.
.\" --------------------------------------------------------------------
.\" OLS
.\"
.\"   Start an ordered list.
.\"
.de OLS
.  www-push-li www-li-ol
.  www-push-ol-level
.  ie \\n[www-html] \{\
.    www-emit-ltag
.    HTML</p> <ol "style=""list-style-type:" \
                           "\\*[www-ol-level\\n[www-ol-level]]"">"
.  \}
.  el \
.    nr www-li-indent +\w'\\*[www-ol-tmp]'u
..
.
.\" --------------------------------------------------------------------
.\" OLE
.\"
.\"   End an ordered list.
.\"
.de OLE
.  ie \\n[www-html] \{\
.    www-emit-ltag
.    HTML </ol>
.  \}
.  el \{\
.    nr www-li-indent -\w'\\*[www-ol-tmp]'u
.    in \\n[www-li-indent]u
.    nr www-ol-ctr\\n[www-ol-level] 0 1
.  \}
.  www-pop-ol-level
.  www-pop-li
..
.
.\" --------------------------------------------------------------------
.\" DLS
.\"
.\"   Start a definition list.
.\"
.de DLS
.  www-push-li www-li-dl
.  nr www-dl-level +1
.  ie \\n[www-html] \{\
.    www-emit-ltag
.    HTML</p> <dl>
.  \}
.  el \{\
.    nr www-li-indent +\\n[www-dl-shift]u
.    in \\n[www-li-indent]u
.  \}
..
.
.\" --------------------------------------------------------------------
.\" DLE
.\"
.\"   End a definition list.
.\"
.de DLE
.  ie \\n[www-html] \{\
.    www-emit-ltag
.    HTML </dl>
.  \}
.  el \{\
.    nr www-li-indent -\\n[www-dl-shift]u
.    in \\n[www-li-indent]u
.  \}
.  nr www-dl-level -1
.  www-pop-li
..
.
.\" --------------------------------------------------------------------
.\" LI
.\"
.\"   Insert a list item.
.\"
.
.\" ********
.\" www-li-ul - bulleted list item
.\"
.de www-li-ul
.  ie \\n[www-html] \
.    www-emit-ltag li
.  el \{\
.    www:paraspace
.    if rPORPHANS \
.      ne \\n[PORPHANS]v
.    in \\n[www-li-indent]u
.    ti -\w'\\*[www-ul-level\\n[www-ul-level]]'u
.    nop \\*[www-ul-level\\n[www-ul-level]]\c
.  \}
..
.
.\" ********
.\" www-li-ol - numbered list item
.\"
.de www-li-ol
.  ie \\n[www-html] \
.    www-emit-ltag li
.  el \{\
.    www:paraspace
.    if rPORPHANS \
.      ne \\n[PORPHANS]v
.    in \\n[www-li-indent]u
.    ti -\w'\\n+[www-ol-ctr\\n[www-ol-level]]\ \ 'u
.    nop \\n[www-ol-ctr\\n[www-ol-level]]\ \ \c
.  \}
..
.
.\" ********
.\" www-li-dl - definition list item
.\"
.de www-li-dl
.  ie \\n[www-html] \{\
.    HTML <dt>\\$1</dt>
.    www-emit-ltag dd
.  \}
.  el \{\
.    www:paraspace
.    if rPORPHANS \
.      ne \\n[PORPHANS]v
.    in \\n[www-li-indent]u
.    ti -\\n[www-dl-shift]u
.    nop \&\\$1
.    br
.  \}
..
.
.\" --------------------------------------------------------------------
.\" DC l text [color]
.\"
.\"   L is the letter to be dropped and enlarged.
.\"
.\"   TEXT is the following text whose height the first letter should
.\"   not exceed.
.\"
.\"   COLOR is the optional color of the dropped letter (default black).
.\"
.de DC
.  ds www-dropcolor black\"
.  if !'\\$3'' \
.    ds www-dropcolor \\$3\"
.  ie '\*[.T]'html' \{\
.    www-make-unique-name
.    nr www-drop-width (100u * \\n[.v]u * 3u / \\n[.l]u)
.    MPIMG -L \\*[www-unique-name].png \\n[www-drop-width]%
.  \}
.  el \{\
.    ie r ps4html \{\
.      www-make-unique-name
.      \" To avoid interferences with another DC macro call which is
.      \" located very near to the current one, we draw the glyph on a
.      \" separate page.  Otherwise it could theoretically happen that
.      \" the dropped capital glyphs overlap.
.      bp
.      ev www-DC
.      vs 320p
.      nop \O[5i\\*[www-unique-name].png]\O[1]
.      nop \m[\\*[www-dropcolor]]\s[160]\O[3]\\$1\O[4]
.      nop \O[2]\O[0]
.      br
.      ev
.      bp
.    \}
.    el \{\
.      ie n \
.        nop \\$1\c
.      el \{\
.        nr dummy \w'\\$1'u
.        nr dcht ((\\n[.v] + \\n[rst]) * \\n[.ps] / \\n[rst])
.        char \[dcap] \m[\\*[www-dropcolor]]\s'\\n[dcht]u'\\$1
.        nop \v'\\n[.v]u'\\[dcap]\v'-\\n[.v]u'\c
'        ti \w'\\[dcap]'u
.      \}
.    \}
.  \}
.  nop \\$2
..
.
.\"
.\" supplementary macros used by other macro sets
.\"
.\" here are some tags specially for -Tps or -Thtml when invoked by
.\" pre-html to generate PNG images from postscript.
.
.\" --------------------------------------------------------------------
.\" HTML-DO-IMAGE - tell troff to issue an image marker which can be
.\"                 read back by pre-html
.\"
.de HTML-DO-IMAGE
.  if r ps4html \
.    nop \O[5\\$2\\$1.png]\O[1]\O[3]
.  if \\n[www-html] \
.    nop \O[5\\$2\\$1.png]\O[0]\O[3]
..
.
.\" --------------------------------------------------------------------
.\" HTML-IMAGE-END - terminate an image for HTML
.\"
.de HTML-IMAGE-END
.  if r ps4html \
.    nop \O[4]\O[2]\O[0]
.  if \\n[www-html] \
.    nop \O[4]\O[2]\O[1]
..
.
.nr www-png-no 0
.
.\" --------------------------------------------------------------------
.\" www-make-unique-name - generate another unique name in string
.\"                        'www-unique-name'
.\"
.de www-make-unique-name
.  nr www-png-no +1
.  ds www-unique-name \\*[www-image-template]\\n[www-png-no]\"
..
.
.\" --------------------------------------------------------------------
.\" HTML-IMAGE and friends tell grohtml that this region of text needs
.\"            to be rendered as an image.
.\"
.de HTML-IMAGE
.  \" generates a centered image
.  www-make-unique-name
.  HTML-DO-IMAGE \\*[www-unique-name] c
..
.
.de HTML-IMAGE-RIGHT
.  www-make-unique-name
.  HTML-DO-IMAGE \\*[www-unique-name] r
..
.
.de HTML-IMAGE-LEFT
.  www-make-unique-name
.  HTML-DO-IMAGE \\*[www-unique-name] l
..
.
.de HTML-IMAGE-INLINE
.  www-make-unique-name
.  HTML-DO-IMAGE \\*[www-unique-name] i
..
.
.\"  EQN-HTML-IMAGE and friends check to see whether the equation is
.\"                 not in an image, in which case it allows HTML
.\"                 (mathml) to be generated (if -Txhtml was specified).
.
.de EQN-HTML-IMAGE
.  \" generates a centered image
.  www-make-unique-name
.  EQN-HTML-DO-IMAGE \\*[www-unique-name] c
..
.
.de EQN-HTML-IMAGE-RIGHT
.  www-make-unique-name
.  EQN-HTML-DO-IMAGE \\*[www-unique-name] r
..
.
.de EQN-HTML-IMAGE-LEFT
.  www-make-unique-name
.  EQN-HTML-DO-IMAGE \\*[www-unique-name] l
..
.
.de EQN-HTML-IMAGE-INLINE
.  www-make-unique-name
.  EQN-HTML-DO-IMAGE \\*[www-unique-name] i
..
.
.\" --------------------------------------------------------------------
.\" EQN-HTML-DO-IMAGE - tell troff to issue an image marker which can be
.\"                     read back by pre-html
.\"
.de EQN-HTML-DO-IMAGE
.  ie r xhtml \{\
.    if !(\\n[.O] == 0) \{\
.      if r ps4html \
.        nop \O[5\\$2\\$1.png]\O[1]\O[3]
.      if \\n[www-html] \
.        nop \O[5\\$2\\$1.png]\O[0]\O[3]
.    \}
.  \}
.  el \
.    HTML-DO-IMAGE \\$*
..
.
.\" --------------------------------------------------------------------
.\" EQN-HTML-IMAGE-END - terminate an image for HTML
.\"
.de EQN-HTML-IMAGE-END
.  ie r xhtml \{\
.    if !(\\n[.O] == 0) \{\
.      if r ps4html \
.        nop \O[4]\O[2]\O[0]
.      if \\n[www-html] \
.        nop \O[4]\O[2]\O[1]
.    \}
.  \}
.  el \
.    HTML-IMAGE-END
..
.
.\" --------------------------------------------------------------------
.\" Setup around HTML-IMAGE and friends
.\"
.\" now set up TS, TE, EQ, EN default macros
.\"
.\" we must not use '.als': the definition of .TE in s.tmac, for
.\" example, calls .HTML-IMAGE-END, which would refer to itself due to
.\" the alias, causing an endless loop
.\"
.if !d TS \{\
.  de TS
.    HTML-IMAGE \\$@
.    if \\n[www-html] \{\
.      nr www-TS-ll \\n[.l]
.      ll 1000n
.    \}
.  .
.\}
.if !d TE \{\
.  de TE
.    if \\n[www-html] \
.      ll \\n[www-TS-ll]u
.    HTML-IMAGE-END \\$@
.  .
.\}
.if !d EQ \{\
.  de EQ
.    EQN-HTML-IMAGE \\$@
.    if \\n[www-html] \{\
.      nr www-EQ-ll \\n[.l]
.      ll 1000n
.    \}
.  .
.\}
.if !d EN \{\
.  de EN
.    if \\n[www-html] \
.      ll \\n[www-EQ-ll]u
.    EQN-HTML-IMAGE-END \\$@
.  .
.\}
.
.\" --------------------------------------------------------------------
.\" JOBNAME
.\"
.\"   Generate multiple output files containing the HTML.
.\"   A file is split whenever a .SH or .NH 1 is encountered.
.\"   The argument to JOBNAME is the file stem for future output files.
.\"
.de JOBNAME
.  DEVTAG .job-name \\$1
..
.
.\" --------------------------------------------------------------------
.\" HEAD
.\"
.\"   Add information to the <head> </head> section of the HTML
.\"   document
.\"
.de HEAD
.  DEVTAG .head "\\$*"
..
.
.\" --------------------------------------------------------------------
.\" start of some code
.\"
.de CDS
.  ft CR
.  nf
..
.
.\" --------------------------------------------------------------------
.\" end of some code
.\"
.de CDE
.  fi
.  ft P
..
.
.ds www-nav-colour #eeeeee\"
.nr www-nav-width-left 30
.nr www-nav-width-right 70
.
.\" --------------------------------------------------------------------
.\" LNS - left navigation start
.\"
.de LNS
.  HTML</p> <table><tr><td "valign=""top""" \
                           "width=""\\n[www-nav-width-left]%""" \
                           "bgcolor=""\\*[www-nav-colour]"">"
.  LK
.  HTML</p> </td><td "valign=""top""" \
                     "width=""\\n[www-nav-width-right]%"">"
.  nr SH-open 1
..
.
.\" --------------------------------------------------------------------
.\" LNE - left navigation end
.\"
.de LNE
.  HTML</p> </td></tr></table>
.  HR
..
.
.nr SH-open 0
.nr needs-begin 0
.
.\"
.\" some auxiliary macros for left navigation lists
.\"
.de www-SH
.  if (0\\$1 == 0) \{\
.    if (\\n[SH-open] == 1) \
.      LNE
.    nr needs-begin 1
.    @SH-old
.  \}
..
.
.de www-NH
.  if (0\\$1 <= 1) \{\
.    if (\\n[SH-open] == 1) \
.      LNE
.    nr needs-begin 1
.    @NH-old
.  \}
..
.
.de www-LP
.  @LP-old
.  if (\\n[needs-begin] == 1) \{\
.    HR
.    LNS
.  \}
.  nr needs-begin 0
..
.
.\" --------------------------------------------------------------------
.\" ALN [colour] [left width percentage]
.\"
.\"   Turn on automatic left navigation.  This macro should only be
.\"   called once (normally at the start of the document) as it
.\"   indicates that all top-level section headings form a navigation
.\"   list on the left of the main text.
.\"
.de ALN
.  if '\*[.T]'html' \{\
.    if !'\\$1'' \
.      ds www-nav-colour \\$1\"
.    if (0\\$2 > 0) \{\
.      nr www-nav-width-left \\$2
.      nr www-nav-width-right (100 - \\$2)
.    \}
.    rn @SH @SH-old
.    rn www-SH @SH
.    rn @NH @NH-old
.    rn www-NH @NH
.    rn @LP @LP-old
.    rn www-LP @LP
.  \}
..
.
.\" --------------------------------------------------------------------
.\" LINKSTYLE color [fontstyle [openglyph closeglyph]]
.\"
.\"   Initialize www.tmac so that when this macro set is used with
.\"   non-HTML devices the urls are rendered the user defined
.\"   attributes.  For example:
.\"
.\"   LINKSTYLE blue CR < >
.\"
.de LINKSTYLE
.  if (\\n[.$] < 1) \
.    www:error .\\$0 expects at least 1 argument, got \\n[.$]
.  ds www:color \\$1\"
.  shift
.  if (\\n[.$] < 1) \
.    return
.  ds www:fontstyle \\$1\"
.  shift
.  if (\\n[.$] < 1) \
.    www:error .\\$0 expects both open and close glyphs to be specified
.  ds www:open \\$1\"
.  ds www:close \\$2\"
..
.
.\" MATHML - enable eqn mathml output to pass through to the device
.\"          driver
.
.de MATHML
.  if (\\n[.O] == 0) \
.    MATH<?p> \\$*
..
.
.\" --------------------------------------------------------------------
.\" final setup
.\" --------------------------------------------------------------------
.
.LINKSTYLE blue CR \[la] \[ra]
.
.if \n[www-html] \{\
.  nh
.  nr HY 0
.\}
.
.if r ps4html .nop \O[0]
.cp \n[*groff_www_tmac_in_C]
.do rr *groff_www_tmac_in_C
.
.\" now set
.
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set filetype=groff textwidth=72:
