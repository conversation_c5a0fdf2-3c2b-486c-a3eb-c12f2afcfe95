#!/bin/sh

if [ -z "$2" ]; then
	theme=hicolor
else
	theme=$2
fi
reslist="256 128 64 48 32 24 22 16"
workdir=/build/packages/$1/lib64/icons/$theme
pixdir=/build/packages/$1/lib64/pixmaps
iconlist=`find $workdir/scalable -type f -name \*.svg`

for source in $iconlist; do
	name=`basename $source .svg`
	type=`dirname $source`
	type=`basename $type`
	for res in $reslist; do
		mkdir -p $workdir/${res}x${res}/$type
		rsvg-convert -w $res -h $res -f png -o $workdir/${res}x${res}/$type/$name.png $source
		if [ "$res" == "32" ]; then
			mkdir -p $pixdir
			magick $workdir/${res}x${res}/$type/$name.png $pixdir/${name}_32x32.xpm
		fi
		if [ "$res" == "16" ]; then
			mkdir -p $pixdir
			magick $workdir/${res}x${res}/$type/$name.png $pixdir/${name}_16x16.xpm
		fi
	done
done
