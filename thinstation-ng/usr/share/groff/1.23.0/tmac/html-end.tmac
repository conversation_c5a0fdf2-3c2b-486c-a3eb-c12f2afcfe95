.\" -*- nroff -*-
.\"
.\" html-end.tmac
.\"
.do nr *groff_html-end_tmac_C \n[.cp]
.cp 0
.
.\" turn off all headers and footers for ms, me, and mm macro sets
.if d EF .EF ''''
.if d EH .EH ''''
.if d OF .OF ''''
.if d OH .OH ''''
.if d ef .ef ''''
.if d of .of ''''
.if d oh .oh ''''
.if d eh .eh ''''
.tl ''''
.
.\" tell grohtml some default parameter values
.pl 99999i
.po 0
.ll \n[.l]u
.ta \n[.tabs]
.
.cp \n[*groff_html-end_tmac_C]
.
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set filetype=groff textwidth=72:
