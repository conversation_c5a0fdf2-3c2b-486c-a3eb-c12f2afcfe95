
##
# --- HD Update Options
#
# HDUPDATE_ENABLED=Y        Enable Update
# HDUPDATE_WS_VERSION       Version of files on workstation (set in thinstation.buildtime only)
# HDUPDATE_SERVER_VERSION   Version of files on server (set in both buildtime and network)
#                           This should be set in buildtime to match the WS_VERSION to prevent
#                           unintended upgrades
#
# HDUPDATE_SERVER           server name
# HDUPDATE_SERVER_TYPE      WGET, SCP or TFTP
# HDUPDATE_PATH             Path on server to files
# HDUPDATE_TEMP             Location to temp files, make sure this is an area which is big enough to
#                           cope with the size of the files
#                           Can be one of     HD     will do an auto dection place of device
#                                             RAM    will place in /tmp
# HDUPDATE_FORCE            Force install even if not needed
# HDUPDATE_FILES            List of files to upgrade
# HDUPDATE_DELETE           List of files to delete (no sanity checks, use with caution)

#HDUPDATE_ENABLED=Y
#HDUPDATE_WS_VERSION=1
#HDUPDATE_SERVER_VERSION=1
#HDUPDATE_SERVER=roadrunner
#HDUPDATE_SERVER_TYPE=tftp
#HDUPDATE_USER=donald
#HDUPDATE_PATH=/ts/update
#HDUPDATE_TEMP=HD
#HDUPDATE_FORCE=N

# Set the list of files to download
#HDUPDATE_FILES="vmlinuz hires lowres syslinux.cfg thin.txt firefox.pkg flash.pkg"
#HDUPDATE_FILES="hires lowres"
#HDUPDATE_FILES="lowres"
#HDUPDATE_FILES="hires"
#HDUPDATE_FILES="syslinux.cfg thin.txt"

