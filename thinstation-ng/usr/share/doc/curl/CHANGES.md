<!--
Copyright (C) <PERSON>, <<EMAIL>>, et al.

SPDX-License-Identifier: curl
-->

In a release tarball, check the RELEASES-NOTES file for what was done in the
most recent release. In a git check-out, that file mentions changes that have
been done since the previous release.

See the online [changelog](https://curl.se/changes.html) for the edited and
human readable version of what has changed in different curl releases.
