#!/bin/bash
# Description: Thinstation Utility to clean chroot for git push.
# URL: http://www.thinstation.org
# Maintainer: <PERSON> (don cupp jr at ya hoo dot com)

create_wind()
{
	pkg=$1
	printf "\n\tCreating fast clean cache for $pkg"
	echo "#`md5sum .dna`" > .wind
        update -c -d $pkg >> .wind
        . .wind
	errata=true
}

create_unwind()
{
	pkg=$1
	printf "\n\tCreating fast install cache for $pkg"
        echo "#`md5sum .dna`" > .unwind
        update -d $pkg |
        sed -e 's/^mkdir \-/2:/g' -e 's/^ln \-/4:/g' -e 's/^touch /5:/g' -e 's/^ln \//3:/g' |
        sort |
        uniq |
        sed -e 's/^2:/mkdir \-/g' -e 's/^3:/ln \//g' -e 's/^4:/ln \-/g' -e 's/^5:/touch /g' >> .unwind
	errata=true
}

list_folders()
{
	find -L . -mindepth 1 -maxdepth 1 -name \* -type d -printf '%f\n'
}

save_archive()
{
	port_archive=$1
	mv $port_archive tempfile
	pkgmk -c
	find -L . -type f -name \*.pkg.tar.\* -delete
	mv tempfile $port_archive
}

stage1()
{
	echo "Cleaning ThinStation Build Environment"
	cd $TSWRKNG
	./build --removeall
	cp conf/default/* .
	cd /

	# Cleanup theme derivatives in the utils folder
#	for theme in `ls $TSWRKNG/utils/tools/splash |grep -v bin`; do
#		for res in `ls $TSWRKNG/utils/tools/splash/$theme |grep -E -v '1024x768|scalable'`; do
#			rm -rf $TSWRKNG/utils/tools/splash/$theme/$res
#		done
#	done

	# Cleanup typical leave behinds from build
	for crumb in \
		/build \
		$TSWRKNG/tmp-tree \
		$TSWRKNG/{thinstation.conf.sample,background-1024x768.jpg}; do
		if [ -e $crumb ]; then
			rm -rf $crumb
		fi
	done

	for working in $TSWRKNG $XTRWRKNG; do
		if [ $working = $TSWRKNG ]; then
			export cache=/ts
		else
			export cache=$working
		fi

		# Remove any empty folders
		find -L $working -type d -empty -delete

		export working
		export -f create_wind
		export -f create_unwind
		export -f wind_package
	        procs=`nproc`
	        let procs-=1

	        ls --color=never $working/packages |xargs -P $procs -I {} bash -c 'wind_package "$@"' _ {}

	done
	printf "\rPackages are %-40s\n" "Clean"
	echo -e "Cleaning tools"
	update -c -t
#	echo "Checking Ports Directories for Downloaded Files"
#	for distro in /ts $XTRWRKNG; do
#		cd $distro/ports
#		for port_type in `list_folders |grep -v relegated`; do
#			cd $port_type
#			export distro
#			export port_type
#			export super
#			export -f save_archive
#			export -f clean_port

#			list_folders |xargs -n 1 -P $procs -I {} bash -c 'clean_port "$@"' _ {}

#			cd ..
#		done
#	done
	touch /cleanstage2
	sync
	if [ -z "$clean" ]; then
		printf "\rPlease Type \"exit\" to Finish the Cleaning Process\t\t\t"
	fi
}

clean_port()
{
	port=$1
	cd $port
        printf "\rCleaning %-59s" "$port"
        . Pkgfile
        whole_version=$version-$release
        port_archive=`find -L . -maxdepth 1 -type f -name ${port}#$whole_version.pkg.tar.\*`
        if [ -n "$port_archive" ]; then
        	case $port_type in
                	proprietary|binary-core|binary-opt|nocache)
                        	if $super; then
                                	pkgmk -c
                                else
                                        save_archive $port_archive
                                fi
                                echo $port_archive |cut -d "." -f2- > .gitignore
                        ;;
                        *) save_archive $port_archive;;
                esac
         else
         	printf "\rCould not find pkg file for %-40s\n" "$port"
         	pkgmk -c
         fi
         rm -rf work *.partial
         cd ..
}

wind_package()
{
	pkg=$1
	errata=false
        if [ -e $working/packages/$pkg/.dna ]; then
        	cd $working/packages/$pkg
                printf "\rCleaning package %-40s" "$pkg"
                if [ -e .wind ]; then
                	if cat .wind |grep -e "#" |cut -d "#" -f2 |md5sum -c --quiet; then
                        	. .wind
                        else
                               	create_wind $pkg
                        fi
                else
                      	create_wind $pkg
                fi
                if [ -e .unwind ]; then
                	if cat .unwind |grep -e "#" |cut -d "#" -f2 |md5sum -c --quiet; then
                        	echo "Already made" >> /dev/null
                        else
                             	create_unwind $pkg
                        fi
                else
                      	create_unwind $pkg
                fi
	fi
        if $errata; then
        	printf "\n"
	fi
}

stage2()
{
	for mount in dev/pts dev proc sys mnt tmp; do
		while mountpoint $mount > /dev/null 2>&1; do
			umount $mount
		done
	done
	for trash in BUILD.* autodl usr lib64 run www share home man bin boot dev etc lib mnt opt proc root sbin sys tmp var label ts/components srv media afs; do
		if [ -e $trash ] || [ -h $trash ]; then
			rm -rf $ROOT/$trash
		fi
	done
	rm cleanstage2
}

if [ "$1" == "-s" ]; then
	super=true
else
	super=false
fi

ROOT=$PWD
if [ -e $ROOT/cleanstage2 ]; then
	stage2
else
	stage1
fi
