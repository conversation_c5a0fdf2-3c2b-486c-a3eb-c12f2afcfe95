#!/bin/sh

. `dirname $0`/common

if ! pkg_initialized $PACKAGE; then
        pkg_set_init_flag $PACKAGE

        # Requirement for cups printing to /dev/usb/lp0 and /dev/lp0 (filedevices)
        echo "FileDevice yes" >> /etc/cups/cups-files.conf

        # restart after configure FileDevice
        systemctl restart cups

        # Iterate through configured CUPS printers
        let x=0
        CUPS_PRINTER_X_NAME=$CUPS_PRINTER_0_NAME

        while [ -n "$CUPS_PRINTER_X_NAME" ] ; do

          CUPS_PRINTER_X_DEVICEURI=`eval echo '$CUPS_PRINTER_'$x'_DEVICEURI'`
          CUPS_PRINTER_X_DRIVER=`eval echo '$CUPS_PRINTER_'$x'_DRIVER'`
          CUPS_PRINTER_X_DEFAULT=`eval echo '$CUPS_PRINTER_'$x'_DEFAULT'`

          # Configure the printer using lpadmin
          lpadmin -p "$CUPS_PRINTER_X_NAME" -v "$CUPS_PRINTER_X_DEVICEURI" -m "$CUPS_PRINTER_X_DRIVER" -E

          # Set as default if specified
          if is_enabled $CUPS_PRINTER_X_DEFAULT; then
                lpadmin -d "$CUPS_PRINTER_X_NAME"
          fi

          # Handle additional printer options
          let y=0
          CUPS_PRINTER_X_OPTION_Y=`eval echo '$CUPS_PRINTER_'$x'_OPTION_'$y`

          while [ -n "$CUPS_PRINTER_X_OPTION_Y" ] ; do
            lpadmin -p "$CUPS_PRINTER_X_NAME" -o "$CUPS_PRINTER_X_OPTION_Y"

            let y=y+1
            CUPS_PRINTER_X_OPTION_Y=`eval echo '$CUPS_PRINTER_'$x'_OPTION_'$y`
          done

          let x=x+1
          CUPS_PRINTER_X_NAME=`eval echo '\$CUPS_PRINTER_'$x'_NAME'`
        done
fi
