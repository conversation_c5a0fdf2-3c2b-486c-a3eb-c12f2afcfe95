#!/bin/sh

export PACKAGE=alsa-plugins
export PORTS="alsa-plugins-a52 alsa-plugins-arcamav alsa-plugins-avtp alsa-plugins-jack alsa-plugins-lavrate alsa-plugins-maemo alsa-plugins-oss alsa-plugins-pulseaudio alsa-plugins-samplerate alsa-plugins-speex alsa-plugins-upmix alsa-plugins-usbstream alsa-plugins-vdownmix"
export DROP_FILES="speaker-test alsa aconnect alsaloop alsaucm amidi aplay aplaymidi arecord arecordmidi aseqdump aseqnet iecset alsaconf aserver alsamixer"
export DROP_DIRS="lib/sounds lib/alsa/speaker-test lib/udev"
repackage -e

returnval=$?

exit $returnval
