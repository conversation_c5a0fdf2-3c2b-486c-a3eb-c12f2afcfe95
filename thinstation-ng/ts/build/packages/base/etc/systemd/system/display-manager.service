[Unit]
Description=ThinStation Display Manager
Requires=systemd-udev-settle.service
After=tsinit.target
After=systemd-user-sessions.service session-setup.service fastboot.service
After=rc-local.service
After=rc-local.service plymouth-start.service systemd-user-sessions.service livesys-late.service systemd-hostnamed.service
Before=plymouth-quit.service
OnFailure=plymouth-quit.service


[Service]
# the VT is cleared by TTYVTDisallocate
ExecStart=-/sbin/agetty -c --skip-login --noclear --nohints --noissue --nohostname -a tsuser tty1 115200 linux
Type=idle
Restart=always
RestartSec=0
UtmpIdentifier=tty1
TTYPath=/dev/tty1
TTYReset=yes
TTYVHangup=yes
#TTYVTDisallocate=yes
KillMode=process
IgnoreSIGPIPE=no
SendSIGHUP=yes

# Unset locale for the console getty since the console has problems
# displaying some internationalized messages.
#Environment=LANG= LANGUAGE= LC_CTYPE= LC_NUMERIC= LC_TIME= LC_COLLATE= LC_MONETARY= LC_MESSAGES= LC_PAPER= LC_NAME= LC_ADDRESS= LC_TELEPHONE= LC_MEASUREMENT= LC_IDENTIFICATION=

[Install]
DefaultInstance=tty1
