<?xml version="1.0"?> <!--*-nxml-*-->
<!DOCTYPE busconfig PUBLIC "-//freedesktop//DTD D-BUS Bus Configuration 1.0//EN"
        "https://www.freedesktop.org/standards/dbus/1.0/busconfig.dtd">

<!--
  SPDX-License-Identifier: LGPL-2.1-or-later

  This file is part of systemd.

  systemd is free software; you can redistribute it and/or modify it
  under the terms of the GNU Lesser General Public License as published by
  the Free Software Foundation; either version 2.1 of the License, or
  (at your option) any later version.
-->

<busconfig>

        <policy user="root">
                <allow own="org.freedesktop.login1"/>
                <allow send_destination="org.freedesktop.login1"/>
                <allow receive_sender="org.freedesktop.login1"/>
        </policy>

        <policy context="default">
                <deny send_destination="org.freedesktop.login1"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.DBus.Introspectable"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.DBus.Peer"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.DBus.Properties"
                       send_member="Get"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.DBus.Properties"
                       send_member="GetAll"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="GetSession"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="GetSessionByPID"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="GetUser"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="GetUserByPID"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="GetSeat"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="ListSessions"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="ListSessionsEx"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="ListUsers"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="ListSeats"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="ListInhibitors"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="Inhibit"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="SetUserLinger"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="ActivateSession"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="ActivateSessionOnSeat"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="LockSession"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="UnlockSession"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="LockSessions"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="UnlockSessions"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="KillSession"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="KillUser"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="TerminateSession"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="TerminateUser"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="TerminateSeat"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="PowerOff"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="PowerOffWithFlags"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="Reboot"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="RebootWithFlags"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="Halt"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="HaltWithFlags"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="Suspend"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="SuspendWithFlags"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="Hibernate"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="HibernateWithFlags"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="HybridSleep"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="HybridSleepWithFlags"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="SuspendThenHibernate"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="SuspendThenHibernateWithFlags"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="Sleep"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="CanPowerOff"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="CanReboot"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="CanHalt"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="CanSuspend"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="CanHibernate"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="CanHybridSleep"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="CanSuspendThenHibernate"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="CanSleep"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="ScheduleShutdown"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="CancelScheduledShutdown"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="CanRebootParameter"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="SetRebootParameter"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="CanRebootToFirmwareSetup"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="SetRebootToFirmwareSetup"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="CanRebootToBootLoaderMenu"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="SetRebootToBootLoaderMenu"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="CanRebootToBootLoaderEntry"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="SetRebootToBootLoaderEntry"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="SetWallMessage"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="AttachDevice"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="FlushDevices"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Manager"
                       send_member="ReleaseSession"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Seat"
                       send_member="Terminate"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Seat"
                       send_member="ActivateSession"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Seat"
                       send_member="SwitchTo"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Seat"
                       send_member="SwitchToPrevious"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Seat"
                       send_member="SwitchToNext"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Session"
                       send_member="Terminate"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Session"
                       send_member="Activate"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Session"
                       send_member="Lock"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Session"
                       send_member="Unlock"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Session"
                       send_member="SetIdleHint"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Session"
                       send_member="SetLockedHint"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Session"
                       send_member="Kill"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Session"
                       send_member="TakeControl"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Session"
                       send_member="ReleaseControl"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Session"
                       send_member="SetType"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Session"
                       send_member="SetClass"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Session"
                       send_member="TakeDevice"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Session"
                       send_member="ReleaseDevice"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Session"
                       send_member="PauseDeviceComplete"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Session"
                       send_member="SetBrightness"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Session"
                       send_member="SetDisplay"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.Session"
                       send_member="SetTTY"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.User"
                       send_member="Terminate"/>

                <allow send_destination="org.freedesktop.login1"
                       send_interface="org.freedesktop.login1.User"
                       send_member="Kill"/>

                <allow receive_sender="org.freedesktop.login1"/>
        </policy>

</busconfig>
