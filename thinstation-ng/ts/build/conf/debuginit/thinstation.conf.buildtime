DEBUGPAUSE=true
AUDIO_LEVEL=90
MIC_LEVEL=0
#X_DPI=100
#USE_XRANDR=TRUE
#XRANDR_OPTIONS="-s 1680x1050"
#MEMORY_CONSTRAINED="TRUE"
SESSION_0_TYPE=sh
#SESSION_1_TYPE=vmview
#ICEWM_THEME=blueCrux
#SESSION_0_AUTOSTART=on
#SESSION_1_AUTOSTART=on
#SAMBA_SECURITY=user
#SAMBA_WORKGROUP=WORKGROUP
NO_SESSION="sleep 1000;poweroff"
#SESSION_1_FIREFOX_HOMEPAGE=http://www.doncuppjr.net/projects
#SESSION_1_CHROME_HOMEPAGE=http://www.doncuppjr.net/projects
#GTK_THEME=Adwaita
#XFWM4_THEME=Default
#ICONS_COMPOSITE_THEME_ORDER="gnome Tango Neu"
#ICONS_CUT_SIZES="256x256 scalable"
#SERVER=**************
#MENU_SHOWRES=off
#MENU_SHOWAPPS=off
#MENU_SHOWABOUT=off
#MENU_SHOWLOGOUT=off
#HIDE_CONTROLS=on
#SCREEN_BLANK_TIME=0
#SCREEN_STANDBY_TIME=0
#SCREEN_SUSPEND_TIME=0
#SCREEN_OFF_TIME=0
#SCREEN_HORIZSYNC="30-70 | *"
#DONT_VT_SWITCH_STATE=TRUE
#DONT_ZAP_STATE=TRUE
#ALWAYS_ENTER_SERVER=TRUE
#FASTBOOT_URL=http://*************
NET_USE=BOTH
NET_USE_DHCP=on
NET_HOSTNAME=ts-*
NET_FILE_ENABLED=Off
NET_TELNETD_ENABLED=ON
#DEBUGPAUSE=TRUE
#EGALAX_DEVICE=/dev/ttyS1
#EGALAX_DEVICE=usbauto
#EGALAX_PARAMFILE=/boot/eeti.param
#RECONNECT_PROMPT=AUTO
TIME_ZONE=America/Los_Angeles
NET_TIME_SERVER=us.pool.ntp.org
NET_REMOTE_ACCESS_FROM="0.0.0.0"
#X_DRIVER_OPTION1="HWcursor False"
#X_DRIVER_OPTION2="PanelSize 1024x600"
#WIRELESS_ESSID="Example"
#WIRELESS_MODE="managed"
#WIRELESS_WPAKEY="3x@mpl39@559hr@53"
#WIRELESS_DRIVER="wext"
#TFTP_BLOCKSIZE=8192
TFTP_BLOCKSIZE=1024
BIND_MOUNT0="home:/root"
BIND_MOUNT1="boot:/boot"
BIND_MOUNT2="tsdev:/thinstation"
USB_STORAGE_SYNC=on
