# This file is part of systemd.

# You really want to adjust this to your local distribution. If you use this
# unmodified you are not building systems safely and securely.

auth      sufficient pam_unix.so
-auth     sufficient pam_systemd_home.so
auth      required   pam_deny.so

account   required   pam_nologin.so
-account  sufficient pam_systemd_home.so
account   sufficient pam_unix.so
account   required   pam_permit.so

-password sufficient pam_systemd_home.so
password  sufficient pam_unix.so sha512 shadow try_first_pass
password  required   pam_deny.so

-session  optional   pam_keyinit.so revoke
-session  optional   pam_loginuid.so
-session  optional   pam_systemd_home.so
-session  optional   pam_systemd.so
session   required   pam_unix.so
