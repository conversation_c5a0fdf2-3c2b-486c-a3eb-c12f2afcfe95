XFWM4_RED=0
XFWM4_BLUE=0
XFWM4_GREEN=0
AUDIO_LEVEL=90
MIC_LEVEL=0
#X_DPI=100
USE_XRANDR=TRUE
#XRANDR_OPTIONS="-s 1680x1050"
#MEMORY_CONSTRAINED="TRUE"
ICONMODE=MANUAL
MAXIMIZE_DEFAULT=window
SESSION_0_TYPE=xfwm4
SESSION_0_AUTOSTART=on
SESSION_1_TYPE=installer
SESSION_1_TITLE="Install to HD"
SESSION_1_ICON=on
SESSION_1_AUTOSTART=on
SESSION_2_TYPE=Terminal
SESSION_2_TITLE=Terminal
SESSION_2_ICON=on
SESSION_2_AUTOSTART=off
GTK_THEME=Adwaita
XFWM4_THEME=Default
XTERM_CMD="xfce4-terminal"
#INSTALLER_WEB_ADDRESS="https://sourceforge.net/projects/thinstation/files/DevStation-Source"
INSTALLER_WEB_ADDRESS="https://www.thinstation.net"
INSTALLER_ARCHIVE_NAME="thindev-default-$TS_VERSION.tar.xz"
INSTALLER_DEV=on
INSTALLER_REBOOT_MESSAGE_BOX="Please remove the CD and click <b>Next</b> to reboot."
DESKTOP_ICON_SIZE=48
DESKTOP_FONT_SIZE=8
DESKTOP_SHOWTRASH=false
DEKSTOP_SHOWHOME=false
DESKTOP_SHOWREMOVEABLE=false
DESKTOP_SHOWFILESYSTEM=false
# PANEL_AUTOHIDE=false
PANEL_PAGER=false
PANEL_REV_CLOCKSYSTRAY=true
PANEL_USER=false
XFWM4_START_POSITION="BOTTOM"
XFWM4_START_TITLE="Start"
XFWM4_START_ICON="installer"
#ICONS_COMPOSITE_THEME_ORDER="gnome Tango Neu"
#ICONS_CUT_SIZES="256x256 scalable"
ICONS_THEME=gnome
#MENU_SHOWRES=off
#MENU_SHOWAPPS=off
#MENU_SHOWABOUT=off
#MENU_SHOWLOGOUT=off
#HIDE_CONTROLS=on
#SCREEN_BLANK_TIME=0
#SCREEN_STANDBY_TIME=0
#SCREEN_SUSPEND_TIME=0
#SCREEN_OFF_TIME=0
#SCREEN_HORIZSYNC="30-70 | *"
#DONT_VT_SWITCH_STATE=TRUE
#DONT_ZAP_STATE=TRUE
#ALWAYS_ENTER_SERVER=TRUE
#FASTBOOT_URL=http://*************
NET_USE=BOTH
NET_USE_DHCP=on
NET_HOSTNAME=ts-*
NET_FILE_ENABLED=Off
NET_TELNETD_ENABLED=ON
#DEBUGPAUSE=TRUE
#EGALAX_DEVICE=/dev/ttyS1
#EGALAX_DEVICE=usbauto
#EGALAX_PARAMFILE=/boot/eeti.param
RECONNECT_PROMPT=OFF
NO_SESSION=exit
TIME_ZONE=America/Los_Angeles
NET_TIME_SERVER=us.pool.ntp.org
NET_REMOTE_ACCESS_FROM="0.0.0.0"
#X_DRIVER_OPTION1="HWcursor False"
#X_DRIVER_OPTION2="PanelSize 1024x600"
#WIRELESS_ESSID="Example"
#WIRELESS_MODE="managed"
#WIRELESS_WPAKEY="3x@mpl39@559hr@53"
#WIRELESS_DRIVER="wext"
#TFTP_BLOCKSIZE=1024
TFTP_BLOCKSIZE=8940
#MOUNT_0="LABEL=THINSTATION     /boot           auto    x-mount.mkdir,defaults  0       0"
MOUNT_1="LABEL=ThinStation     /boot           auto    x-mount.mkdir,defaults  0       0"
#BIND_MOUNT0="home:/tmp-home"
#BIND_MOUNT1="boot:/tmp-boot"
#BIND_MOUNT2="tsdev:/thinstation"
USB_STORAGE_SYNC=on
WAIT_FOR_LINK=true
