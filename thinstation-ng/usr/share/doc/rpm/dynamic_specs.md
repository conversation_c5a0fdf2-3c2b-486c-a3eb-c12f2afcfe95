---
layout: default
title: rpm.org - Package Build Process
---
# Dynamic Spec Generation

Since rpm 4.19 RPM supports parsing dynamically generated specs. This
allows the build scripts (**%build** or **%install**) to create parts
of the spec file. The parts are read in and parsed after **%install**
and before **%check**. Because of this they obviously can't contain
the build scripts but are intended to create sub packages based on the
build results.

The files need to be placed in the **%{specpartsdir}** (also available
as **$RPM_SPECPARTS_DIR** in the build scripts) and have a
**.specpart** postfix. The directory is created by **%setup**.
Scripts must not create it themselves but must
either fail if it is not present or switch to an alternative that does
not require the feature. They should give an error message that
dynamic spec generation is not supported on the given RPM version when
failing.

The **.specparts** files are read in alphabetical order. If build
scripts rely on a specific order they should use a common prefix and
have postfixes take care of the ordering.

Generally the specparts should be generated by separate scripts and not
directly from the build scripts themselves. This can be done for
testing but one needs to be careful that the spec syntax is not
already parsed when the spec file is read. Avoid Spec directives or
sections starting right at the beginning of the line as they will be
interpreted right away.

[Example](https://github.com/rpm-software-management/rpm/blob/master/tests/data/SPECS/dynamic.spec)
from our tests set.

As dynamic spec parts are generate during build they cannot include
directives that are needed for or influence building. This includes
all build scripts, sources and patches, Build dependencies, tags
regarding the build environment (**ExcludeArch**, **ExclusiveArch**,
**ExcludeOS**, **ExclusiveOS**), **BuildArch** except for declaring
sub packages **noarch** and **BuildSystem**. These will create an
error if encountered in a dynamically generated spec part.

While declaring macros used in the build scripts are not an error they
won't have an influence on the build for obvious reasons.
