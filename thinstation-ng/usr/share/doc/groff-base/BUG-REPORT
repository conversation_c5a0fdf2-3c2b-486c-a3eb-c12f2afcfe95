    Copyright (C) 1999-2020, 2022 Free Software Foundation, Inc.

    Copying and distribution of this file, with or without
    modification, are permitted in any medium without royalty provided
    the copyright notice and this notice are preserved.

			   groff Bug Report

[Please read the 'PROBLEMS' file before submitting a bug report.

Please fill in all fields, even if you think they are not relevant.

Please delete the text in square brackets before submitting it.

Please report distinguishable problems separately.

Place this completed form in a new bug report at
<http://savannah.gnu.org/bugs/?group=groff>.  Click on the "Bugs" link,
then select "Submit new" from the menu.]

+verbatim+
GROFF VERSION:
[Put the output of the failing command when run with the '--version'
option here.  For example, "groff --version" or "indxbib --version".]

PLATFORM:
[Put the output of the "uname -a" command here.  On non-POSIX systems,
some alternative should be available; on Windows systems, use "ver".]

CONFIGURATION REPORT:
[If you compiled groff yourself, include the portion of the 'configure'
script's output between rows of dashes.  If you didn't compile groff
yourself, supply the packaging information from your distributor: in
DEB- and RPM-based GNU/Linux distributions, gather it with the commands
"dpkg -s groff" or "rpm -qi groff", respectively.]

INPUT FILES:
[Include all the files necessary to reproduce the problem that are not
part of the standard groff distribution.  This includes device and font
description files and any macro files your document uses that groff does
not supply.  Attach them to the bug report.

It's easier for us if you can provide an example that doesn't depend on
any macro package, but obviously if you're reporting a problem with a
macro package that won't be possible.  Further, a short example is more
convenient than a long one, but don't worry if you can't find a short
example.  A claim like "any file that X" is not helpful: always include
a concrete example.]

COMMAND LINE:
[The command line that we should run in order to observe the bug.  For
example, "groff -Tps bug.tr".]

DESCRIPTION OF INCORRECT BEHAVIOUR:
[What goes wrong when that command line is run?  For example, "groff
gets a segmentation fault.", or "The output looks bad because the bar
over the x is too long and is too far over to the left."  If you get an
error message, include it here without modification: don't edit it to
make it more readable.]

SUGGESTED FIX [optional]:
[If you can suggest a fix for the problem, you might include a unified
diff here.  But don't delay submitting a bug report in the hope of
finding a fix.  A guess about a bug's cause is not usually helpful.]
-verbatim-

##### Editor settings
Local Variables:
fill-column: 72
mode: text
End:
vim: set filetype= textwidth=72:
