[Unit]
Description=Configure Chrome Settings
DefaultDependencies=no
Conflicts=shutdown.target
After=profile-setup.service net.profile-setup.service pkg.service tsinit.target
Before=session-setup.service
ConditionPathIsReadWrite=/etc

[Service]
Type=oneshot
RemainAfterExit=yes
EnvironmentFile=/etc/thinstation.env
ExecStart=/etc/init.d/chrome-init
SyslogIdentifier=thinstation

[Install]
WantedBy=tsinit.target
