<?xml version="1.0" encoding="UTF-8"?> <!--*-nxml-*-->
<!DOCTYPE policyconfig PUBLIC "-//freedesktop//DTD PolicyKit Policy Configuration 1.0//EN"
        "https://www.freedesktop.org/standards/PolicyKit/1/policyconfig.dtd">

<!-- SPDX-License-Identifier: LGPL-2.1-or-later -->

<policyconfig>

        <vendor>The systemd Project</vendor>
        <vendor_url>https://systemd.io</vendor_url>

        <action id="org.freedesktop.portable1.inspect-images">
                <description gettext-domain="systemd">Inspect a portable service image</description>
                <message gettext-domain="systemd">Authentication is required to inspect a portable service image.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
        </action>

        <action id="org.freedesktop.portable1.attach-images">
                <description gettext-domain="systemd">Attach or detach a portable service image</description>
                <message gettext-domain="systemd">Authentication is required to attach or detach a portable service image.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.imply">org.freedesktop.systemd1.reload-daemon</annotate>
        </action>

        <action id="org.freedesktop.portable1.manage-images">
                <description gettext-domain="systemd">Delete or modify portable service image</description>
                <message gettext-domain="systemd">Authentication is required to delete or modify a portable service image.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
        </action>

</policyconfig>
