.\" tty-char.tmac
.\"
.\" This file defines standard troff characters and some groff
.\" characters for use with -Tascii, -Tlatin1, -Tutf8, and -Tcp1047.
.\"
.\" These definitions are chosen so that, as far as possible, they:
.\" - work with all of -Tascii, -Tlatin1, -Tutf8, and -Tcp1047.
.\" - work on devices that display only the last overstruck character
.\"   as well as on devices that support overstriking
.\" - help understanding the character's meaning, only aiming to imitate
.\"   a particular graphical shape when that doesn't hinder
.\"   understanding
.\"
.\" Note that the optical appearance of the definitions contained in
.\" this file is inferior compared to those of the replacement
.\" characters defined in the file tty.tmac.
.\"
.do nr *groff_tty-char_tmac_C \n[.cp]
.cp 0
.
.de tty-char
.	if !c\\$1 .char \\$1 "\\$2
..
.
.ie c\[a-] \
.	ds tty-rn \[a-]
.el \
.	ds tty-rn \v'-1m'_\v'+1m'
.tty-char \[tm] tm
.tty-char \[rn] \*[tty-rn]
.tty-char \[ua] \z|^
.tty-char \[da] \z|v
.tty-char \[sc] <section>
.tty-char \[ct] \z/c
.tty-char \[dg] <*>
.tty-char \[dd] <**>
.tty-char \[ib] <subset\~or\~equal>
.tty-char \[ip] <superset\~or\~equal>
.tty-char \[sb] <proper\~subset>
.tty-char \[sp] <proper\~superset>
.tty-char \[nb] <not\~subset>
.tty-char \[nc] <not\~superset>
.tty-char \[if] <infinity>
.tty-char \[pt] <proportional\~to>
.tty-char \[es] {}
.tty-char \[ca] <intersection>
.tty-char \[cu] <union>
.tty-char \[de] <degree>
.tty-char \[di] /
.tty-char \[tdi] /
.tty-char \[no] ~
.tty-char \[tno] ~
.tty-char \[gr] <nabla>
.tty-char \[is] <integral>
.tty-char \[integral] <integral>
.tty-char \[sum] <sum>
.tty-char \[product] <product>
.tty-char \[coproduct] <coproduct>
.tty-char \[mo] <element\~of>
.tty-char \[pd] <del>
.tty-char \[sr] <sqrt>
.tty-char \[sqrt] <sqrt>
.tty-char \[*C] <Xi>
.tty-char \[*D] <Delta>
.tty-char \[*F] <Phi>
.tty-char \[*G] <Gamma>
.tty-char \[*H] <Theta>
.tty-char \[*L] <Lambda>
.tty-char \[*P] <Pi>
.tty-char \[*Q] <Psi>
.tty-char \[*S] <Sigma>
.tty-char \[*W] <Omega>
.tty-char \[*b] <beta>
.tty-char \[*a] <alpha>
.tty-char \[*c] <xi>
.tty-char \[*d] <delta>
.tty-char \[*e] <epsilon>
.tty-char \[+e] <epsilon>
.tty-char \[*f] <phi>
.tty-char \[+f] <phi>
.tty-char \[*g] <gamma>
.tty-char \[*h] <theta>
.tty-char \[+h] <theta>
.tty-char \[*i] <iota>
.tty-char \[*k] <kappa>
.tty-char \[*l] <lambda>
.tty-char \[*m] <mu>
.tty-char \[*n] <nu>
.tty-char \[*p] <pi>
.tty-char \[+p] <pi>
.tty-char \[*q] <psi>
.tty-char \[*r] <rho>
.tty-char \[*s] <sigma>
.tty-char \[*t] <tau>
.tty-char \[*u] <upsilon>
.tty-char \[*w] <omega>
.tty-char \[*x] <chi>
.tty-char \[*y] <eta>
.tty-char \[*z] <zeta>
.tty-char \[ts] <sigma>
.tty-char \[ss] ss
.tty-char \[c*] \zO\[mu]
.tty-char \[c+] \zO+
.tty-char \[AN] ^
.tty-char \[OR] v
.tty-char \[uA] \z=^
.tty-char \[dA] \z=v
.if c\[md] .tty-char \[pc] \[md]
.if c\[pc] .tty-char \[md] \[pc]
.ie c\[pc] .tty-char \[a.] \[pc]
.el .tty-char \[a.] .
.tty-char \[Im] <Im>
.tty-char \[Re] <Re>
.tty-char \[/L] \z/L
.tty-char \[/l] \z/l
.tty-char \[%0] <permille>
.tty-char \[ao] o
.tty-char \[a"] """"
.tty-char \[ab] \z'`
.tty-char \[ah] v
.tty-char \[ho] \[ac]
.tty-char \[/_] <angle>
.tty-char \[=~] =~
.tty-char \[|=] -~
.tty-char \[Ah] <Aleph>
.tty-char \[CR] <cr>
.tty-char \[fa] <for\~all>
.tty-char \[nm] <not\~element\~of>
.tty-char \[pp] <perpendicular>
.tty-char \[st] <such\~that>
.tty-char \[te] <there\~exists>
.if c\[md] .tty-char \[tf] .\[md].
.tty-char \[tf] <therefore>
.if c\[md] .tty-char \[3d] .\[md].
.tty-char \[3d] <therefore>
.tty-char \[wp] p
.tty-char \[~~] ~~
.tty-char \[Fn] \z,f
.tty-char \[Bq] ,,
.tty-char \[lz] <>
.tty-char \[lf] |_
.tty-char \[rf] _|
.tty-char \[lc] |~
.tty-char \[rc] ~|
.tty-char \[lb] `-
.tty-char \[rb] -'
.tty-char \[lk] {
.tty-char \[rk] }
.tty-char \[lt] ,-
.tty-char \[rt] -.
.tty-char \[CL] C
.tty-char \[SP] S
.tty-char \[HE] H
.tty-char \[DI] D
.\" Latin characters
.tty-char \[r!] !
.tty-char \[Po] \z-L
.tty-char \[Cs] \zox
.tty-char \[Ye] \z=Y
.tty-char \[bb] |
.tty-char \[ad] """"
.tty-char \[Of] \z_a
.tty-char \[Fo] <<
.tty-char \[a-] \*[tty-rn]
.tty-char \[S2] ^2
.tty-char \[S3] ^3
.tty-char \[ps] <paragraph>
.tty-char \[md] .
.tty-char \[pc] .
.tty-char \[ac] ,
.tty-char \[S1] ^1
.tty-char \[Om] \z_o
.tty-char \[Fc] >>
.tty-char \[r?] ?
.tty-char \[`A] \z`A
.tty-char \['A] \z'A
.tty-char \[^A] \z^A
.tty-char \[~A] \z~A
.tty-char \[:A] \z"A
.tty-char \[oA] \zoA
.tty-char \[,C] \z,C
.tty-char "\[S ,]" \z,S
.tty-char \[`E] \z`E
.tty-char \['E] \z'E
.tty-char \[^E] \z^E
.tty-char \[:E] \z"E
.tty-char \[`I] \z`I
.tty-char \['I] \z'I
.tty-char \[^I] \z^I
.tty-char \[:I] \z"I
.tty-char \[-D] Dh
.tty-char \[~N] \z~N
.tty-char \[`O] \z`O
.tty-char \['O] \z'O
.tty-char \[^O] \z^O
.tty-char \[~O] \z~O
.tty-char \[:O] \z"O
.tty-char \[/O] \z/O
.tty-char \[`U] \z`U
.tty-char \['U] \z'U
.tty-char \[^U] \z^U
.tty-char \[:U] \z"U
.tty-char \['Y] \z'Y
.tty-char \[TP] Th
.tty-char \[`a] \z`a
.tty-char \['a] \z'a
.tty-char \[^a] \z^a
.tty-char \[~a] \z~a
.tty-char \[:a] \z"a
.tty-char \[oa] \zoa
.tty-char \[,c] \z,c
.tty-char "\[s ,]" \z,s
.tty-char \[`e] \z`e
.tty-char \['e] \z'e
.tty-char \[^e] \z^e
.tty-char \[:e] \z"e
.tty-char \[`i] \z`i
.tty-char \['i] \z'i
.tty-char \[^i] \z^i
.tty-char \[:i] \z"i
.tty-char \[Sd] dh
.tty-char \[~n] \z~n
.tty-char \[`o] \z`o
.tty-char \['o] \z'o
.tty-char \[^o] \z^o
.tty-char \[~o] \z~o
.tty-char \[:o] \z"o
.tty-char \[/o] \z/o
.tty-char \[`u] \z`u
.tty-char \['u] \z'u
.tty-char \[^u] \z^u
.tty-char \[:u] \z"u
.tty-char \['y] \z'y
.tty-char \[Tp] th
.tty-char \[:y] \z"y
.\" for Turkish
.tty-char "\[G ab]" G
.tty-char "\[g ab]" g
.tty-char "\[I .]" I
.\"tty-char \[:y] \ij
.tty-char \[arrowvertex] |
.tty-char \[mc] <micro>
.
.cp \n[*groff_tty-char_tmac_C]
.do rr *groff_tty-char_tmac_C
.
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set filetype=groff textwidth=72:
