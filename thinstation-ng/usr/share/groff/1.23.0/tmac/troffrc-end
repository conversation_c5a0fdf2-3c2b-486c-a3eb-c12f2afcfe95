.\" final startup file for GNU troff
.\"
.\" This file is parsed after all -m file arguments have been read.
.\"
.\" Use .do for any groff extensions so that this file works with -C.
.
.do if '\*[.T]'html' \
.	do mso html-end.tmac
.
.\" Load www if we are running the PostScript driver for HTML images.
.do if r ps4html \
.	do mso www.tmac
.
.\" For all other devices, make these macros no-ops.
.do if !d HTML-IMAGE-INLINE      .do ds HTML-IMAGE-INLINE \" empty
.do if !d HTML-IMAGE             .do ds HTML-IMAGE \" empty
.do if !d HTML-IMAGE-RIGHT       .do ds HTML-IMAGE-RIGHT \" empty
.do if !d HTML-IMAGE-LEFT        .do ds HTML-IMAGE-LEFT \" empty
.do if !d HTML-IMAGE-END         .do ds HTML-IMAGE-END \" empty
.do if !d DEVTAG                 .do ds DEVTAG \" empty
.do if !d HTML-DO-IMAGE          .do ds HTML-DO-IMAGE \" empty
.do if !d EQN-HTML-IMAGE-END     .do ds EQN-HTML-IMAGE-END \" empty
.do if !d EQN-HTML-IMAGE         .do ds EQN-HTML-IMAGE \" empty
.do if !d EQN-HTML-IMAGE-RIGHT   .do ds EQN-HTML-IMAGE-RIGHT \" empty
.do if !d EQN-HTML-IMAGE-LEFT    .do ds EQN-HTML-IMAGE-LEFT \" empty
.do if !d EQN-HTML-IMAGE-INLINE  .do ds EQN-HTML-IMAGE-INLINE \" empty
.do if !d EQN-HTML-DO-IMAGE      .do ds EQN-HTML-DO-IMAGE \" empty
.do if !d EQN-HTML-IMAGE-END     .do ds EQN-HTML-IMAGE-END \" empty
.
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set filetype=groff textwidth=72:
