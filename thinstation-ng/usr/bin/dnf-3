#! /usr/bin/python3 -sP
# The dnf executable script.
#
# Copyright (C) 2012-2016 Red Hat, Inc.
#
# This copyrighted material is made available to anyone wishing to use,
# modify, copy, or redistribute it subject to the terms and conditions of
# the GNU General Public License v.2, or (at your option) any later version.
# This program is distributed in the hope that it will be useful, but WITHOUT
# ANY WARRANTY expressed or implied, including the implied warranties of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General
# Public License for more details.  You should have received a copy of the
# GNU General Public License along with this program; if not, write to the
# Free Software Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA
# 02110-1301, USA.  Any Red Hat trademarks that are incorporated in the
# source code or documentation are not subject to the GNU General Public
# License and may only be used or replicated with the express permission of
# Red Hat, Inc.
#

from __future__ import unicode_literals
import sys


def suppress_keyboard_interrupt_message():
    """Prevent unsightly KeyboardInterrupt tracebacks.

    Nothing will be printed to the terminal after an uncaught
    :class:`exceptions.KeyboardInterrupt`.

    """
    old_excepthook = sys.excepthook

    def new_hook(type, value, traceback):
        if type != KeyboardInterrupt:
            old_excepthook(type, value, traceback)
        else:
            pass

    sys.excepthook = new_hook


# do this ASAP to prevent tracebacks after ^C during imports
suppress_keyboard_interrupt_message()

if __name__ != "__main__":
    sys.stderr.write('The executable DNF module must not be imported.')
    sys.exit(1)

here = sys.path[0]
if here == '/usr/bin':
    # we never import Python modules from /usr/bin
    # removing this lowers the risk of accidental imports of weird files
    del sys.path[0]

from dnf.cli import main
main.user_main(sys.argv[1:], exit_code=True)
