.\" Italian localization for groff
.\"
.\" Copyright (C) 2021-2022 Free Software Foundation, Inc.
.\"   Written by <PERSON> (edmond.orign<PERSON>@wanadoo.fr)
.\"
.\" This file is part of groff.
.\"
.\" groff is free software; you can redistribute it and/or modify it
.\" under the terms of the GNU General Public License as published by
.\" the Free Software Foundation, either version 3 of the License, or
.\" (at your option) any later version.
.\"
.\" groff is distributed in the hope that it will be useful, but WITHOUT
.\" ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
.\" or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public
.\" License for more details.
.\"
.\" You should have received a copy of the GNU General Public License
.\" along with this program.  If not, see
.\" <http://www.gnu.org/licenses/>.
.\"
.\" Please send comments/corrections to edmond.orign<PERSON>@wanadoo.fr.
.
.do nr *groff_it_tmac_C \n[.cp]
.cp 0
.
.
.\" If changing from an existing locale, we need to preserve the state
.\" of the "suppress hyphenation before a page location trap" bit.
.nr locale*use-trap-hyphenation-mode 0
.if d locale \
.  if \n[.hy]=\n[\*[locale]*hyphenation-mode-trap] \
.    nr locale*use-trap-hyphenation-mode 1
.
.
.ds locale italian\"
.
.
.\" Predefined text translations
.
.ds \*[locale]-abstract RIASSUNTO\"
.ds \*[locale]-app ALLEGATO\"
.ds \*[locale]-appendix_string Appendice\"
.ds \*[locale]-april Aprile\"
.ds \*[locale]-attribute_string da\"
.ds \*[locale]-august Agosto\"
.ds \*[locale]-chapter_string Capitolo\"
.ds \*[locale]-december Dicembre\"
.ds \*[locale]-draft_string Brutta Copia\"
.ds \*[locale]-endnote_string ANNOTAZIONI\"
.ds \*[locale]-february Febbraio\"
.ds \*[locale]-finis_string FINE\"
.ds \*[locale]-friday Venerd\[i `]\"
.ds \*[locale]-january Gennaio\"
.ds \*[locale]-july Luglio\"
.ds \*[locale]-june Giugno\"
.ds \*[locale]-le ELENCO DEI EQUAZIONI\"
.ds \*[locale]-letapp LETTO E APPROVATO\"
.ds \*[locale]-letat ALLA CORTESE ATTENZIONE DI:\"
.ds \*[locale]-letcn CONFIDENZIALE\"
.ds \*[locale]-letdate Data\"
.ds \*[locale]-letfc Accolga, signore, l'espressione dei miei sentimenti pi\[u `] distinti.\"
.ds \*[locale]-letns!0 Copia ad\"
.ds \*[locale]-letns!1 Esemplare (con destinatario) a\"
.ds \*[locale]-letns!10 Esemplare (con destinatarie) a\"
.ds \*[locale]-letns!11 Esemplare (sin destinatarie) a\"
.ds \*[locale]-letns!12 Riassunto ad\"
.ds \*[locale]-letns!13 Promemoria completa ad\"
.ds \*[locale]-letns!14 Cc:\"
.ds \*[locale]-letns!2 Esemplare (sin destinatario) a\"
.ds \*[locale]-letns!3 Destinatario\"
.ds \*[locale]-letns!4 Destinatarie\"
.ds \*[locale]-letns!5 Allegato\"
.ds \*[locale]-letns!6 Allegati\"
.ds \*[locale]-letns!7 In plico a parte\"
.ds \*[locale]-letns!8 Lettere ad\"
.ds \*[locale]-letns!9 Promemoria ad\"
.ds \*[locale]-letns!copy Copia \" (a space is needed)\"
.ds \*[locale]-letns!to  ad\"
.ds \*[locale]-letrn In relazione a:\"
.ds \*[locale]-letsa A chiunque riguardate:\"
.ds \*[locale]-letsj Soggetto:\"
.ds \*[locale]-lf ELENCO DELLE FIGURE\"
.ds \*[locale]-licon SOMMARIO\"
.ds \*[locale]-liec Equatio\"
.ds \*[locale]-liex Documento\"
.ds \*[locale]-lifg Figura\"
.ds \*[locale]-litb Tabella\"
.ds \*[locale]-lt ELENCO DEI TABELLE\"
.ds \*[locale]-lx ELENCO DEI DOCUMENTI\"
.ds \*[locale]-man-section1 Manuale dei comandi generali\"
.ds \*[locale]-man-section2 Manuale delle chiamate di sistema\"
.ds \*[locale]-man-section3 Manuale delle funzioni di libreria\"
.ds \*[locale]-man-section4 Manuale delle interfacce del kernel\"
.ds \*[locale]-man-section5 Manuale dei formati di file\"
.ds \*[locale]-man-section6 Manuale dei giochi\"
.ds \*[locale]-man-section7 Manuale di informazioni varie\"
.ds \*[locale]-man-section8 Manuale del gestore di sistema\"
.ds \*[locale]-man-section9 Manuale dello sviluppatore del kernel\"
.ds \*[locale]-march Marzo\"
.ds \*[locale]-may Maggio\"
.ds \*[locale]-monday Luned\[i `]\"
.ds \*[locale]-november Novembre\"
.ds \*[locale]-october Ottobre\"
.ds \*[locale]-paper A4\"
.ds \*[locale]-qrf Cf. capitulo \\*[Qrfh], pagina \\*[Qrfp].\"
.ds \*[locale]-references Bibliografia\"
.ds \*[locale]-revision_string Revisione\"
.ds \*[locale]-rp BIBLIOGRAFIA\"
.ds \*[locale]-saturday Sabato\"
.ds \*[locale]-september Settembre\"
.ds \*[locale]-sunday Domenica\"
.ds \*[locale]-thursday Gioved\[i `]\"
.ds \*[locale]-toc Indice\"
.ds \*[locale]-toc_header_string Indice\"
.ds \*[locale]-tuesday Marted\[i `]\"
.ds \*[locale]-wednesday Mercoled\[i `]\"
.
.
.\" Activate the translations
.
.mso trans.tmac
.
.
.\" ms package
.if r GS \{\
.	\" update the date
.	ds DY \n[dy] \*[MO] \n[year]
.	\" set hyphenation flags
.	nr HY 2
.\}
.
.
.\" mm package
.if d PH \{\
.	\" update the date with the new strings
.	ds cov*new-date \\n[dy] \\*[MO\\n[mo]] \\n[year]
.
.	\" ISODATE and DT update
.	de ISODATE
.		nr cov*mm \\n[mo]
.		nr cov*dd \\n[dy]
.		af cov*mm 01
.		af cov*dd 01
.		ie '0'\\$1' \
.			ds cov*new-date \\n[dy] \\*[MO\\n[mo]] \\n[year]
.		el \
.			ds cov*new-date \\n[year]-\\n[cov*mm]-\\n[cov*dd]
.	.
.
.	als DT cov*new-date
.\}
.
.
.ss 12 0
.
.\" Set up hyphenation.
.
.\" Italian hyphenation (\lefthyphenmin=2, \righthyphenmin=2)
.nr \*[locale]*hyphenation-mode-base 1
.nr \*[locale]*hyphenation-mode-trap 2
.
.ie \n[locale*use-trap-hyphenation-mode] \
.  hy \n[\*[locale]*hyphenation-mode-trap]
.el \
.  hy \n[\*[locale]*hyphenation-mode-base]
.
.rr locale*use-trap-hyphenation-mode
.
.hla it
.hpf hyphen.it
.
.
.\" man package
.if d an \
.	an*reset-hyphenation-mode
.
.
.\" me package
.if d @R \{\
.	ds _td_format \En(dy \E*(mo \En(y4
.	ld
.\}
.
.
.cp \n[*groff_it_tmac_C]
.do rr *groff_it_tmac_C
.
.\" Local Variables:
.\" mode: nroff
.\" coding: latin-1
.\" fill-column: 72
.\" End:
.\" vim: set fileencoding=iso-8859-1 filetype=groff textwidth=72:
