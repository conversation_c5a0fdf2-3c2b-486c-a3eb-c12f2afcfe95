LICENSE:
        Licensed under the Apache License, Version 2.0 (the "License");
        you may not use this file except in compliance with the License.
        You may obtain a copy of the License at

                http://www.apache.org/licenses/LICENSE-2.0

        Unless required by applicable law or agreed to in writing, software
        distributed under the License is distributed on an "AS IS" BASIS,
        WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
        See the License for the specific language governing permissions and
        limitations under the License.

COPYRIGHT: (ordered alphabetically)
        Copyright (C) 2016-2023 Red Hat, Inc.
        Copyright (C) 2023 <PERSON> <<EMAIL>>
        Copyright (C) 2023 <PERSON> <<EMAIL>>

AUTHORS: (ordered alphabetically)
        <PERSON> <<EMAIL>>
        <PERSON><PERSON> <<EMAIL>>
        <PERSON>-<PERSON> <<EMAIL>>
        <PERSON> <<EMAIL>>
        <PERSON> <<EMAIL>>
        <PERSON><PERSON> <<EMAIL>>
        <PERSON> <david.r<PERSON><PERSON>@gmail.com>
        <PERSON><PERSON><PERSON> <<EMAIL>>
        <PERSON><PERSON><PERSON><PERSON> <frant<PERSON><EMAIL>>
        <PERSON> <georg<PERSON><PERSON>@gmx.net>
        <PERSON> Osvaldo Barrera <<EMAIL>>
        <PERSON> Alzén <<EMAIL>>
        Jake Dane
        Lily Danzig <<EMAIL>>
        Khem Raj <<EMAIL>>
        Laurent Bigonville <<EMAIL>>
        Luca Boccassi <<EMAIL>>
        Marc-Antoine Perennou <<EMAIL>>
        Marcus Sundberg <<EMAIL>>
        Mark Esler <<EMAIL>>
        Michal Schmidt <<EMAIL>>
        Mike Gilbert <<EMAIL>>
        msizanoen1 <<EMAIL>>
        Stefan Agner <<EMAIL>>
        Thomas Mühlbacher <<EMAIL>>
        Tim Gates <<EMAIL>>
        Tom Gundersen <<EMAIL>>
        Yanko Kaneti <<EMAIL>>
        Zbigniew Jędrzejewski-Szmek <<EMAIL>>
