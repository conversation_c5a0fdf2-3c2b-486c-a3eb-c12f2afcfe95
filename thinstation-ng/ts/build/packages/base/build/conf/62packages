
##
# --- PKG Options
#
# PKG_PACKAGES       Choice of packages to download for PKG
#                    You can also use PKG_PACKAGES1-8 for additional package selections
#                    This is useful for using multiple network group files
# PKG_PREFIX         Download PKGs from a subdir of /tftpboot or PKG Path if PKG_PATH
#                    is set in thinstation.conf
# PKG_PATH           Path to PKG files if not using tftpboot
#                    Floppy: /mnt/floppy
#                    CD-ROM: /mnt/cdrom
#                    HD:     /mnt/disc/hdX/part1   (first disc, first partition)
#                    NFS:    /mnt/nfs
#                    Samba:  /mnt/smb
# MOD_PACKAGES       Choice of modules to download for MPKG
#                    You can also use MOD_PACKAGES1-8 for additional package selections
#                    This is useful for using multiple network group files
# MOD_PREFIX         Downloads and insmod's a module from a subdir of /tftpboot
#                    if MOD_PREFIX is set in thinstation.conf

#PKG_PACKAGES="blackbox rxvt"
#PKG_PREFIX=pkg
#PKG_PATH=/mnt/cdrom
#MOD_PACKAGES="usb-hid usb-storage"
#MOD_PREFIX=modules

