#!/bin/bash

package=$1
pdir=/ts/build/packages/$package

mkdir -p $pdir/build
touch $pdir/dependencies
touch $pdir/.dna

cat << EOF > $pdir/build/install
#!/bin/sh

export PACKAGE="$package"
export PORTS="$package"
export DROP_FILES=""
export DROP_DIRS=""
export CREATE_EMPTY_DIRS=""
repackage -e

returnval=\$?

exit \$returnval
EOF
chmod +x $pdir/build/install

cat << EOF > $pdir/build/remove
#!/bin/sh

export PACKAGE=$package
repackage -c

EOF
chmod +x $pdir/build/remove

