##
# --- Networking Options (NetworkManager)
### PROTOCOL INDEPENDENT   
#
## Maximum transmission unit (for either IPv4 or 6)
# NET_MTU=1480          
#
#
### IPV4
#
## IPv4 interface mode: auto->DHCP, 
##                      manual->static IP
##                      disabled: interface disabled
# NET_IPV4_MODE=auto|manual|disabled
#
## static IP address to assign (/<CIDR prefix length>)
## (address & gateway must be provided if "manual" is set)
# NET_IPV4_ADDRESS=************/12
# NET_IPV4_GATEWAY=**********
#
## Additional static IP addresses
# NET_IPV4_ADDRESS2=
# NET_IPV4_GATEWAY2=
#
## DNS server(s) to use 
# NET_IPV4_DNS1=***********
# NET_IPV4_DNS2=
#   or
# NET_IPV4_DNS="***********;***********"
#
## DNS search suffix  
# NET_IPV4_DNS_SEARCH=somedomain.org
#
## Routes
# NET_IPV4_ROUTE1_IP=***********/24
# NET_IPV4_ROUTE1_GW=************
# NET_IPV4_ROUTE1_METRIC=
# NET_IPV4_ROUTE2_IP=....
#
#
### IPV6
#
## IPv6 interface mode: auto->stateless autoconfiguration, 
##                      manual->static IP
##                      dhcp->stateful DHCP configuration
##                      disabled: interface disabled
##
## NET_IPV6_MODE=auto|manual|dhcp|disabled
#NET_IPV6_MODE=manual
## static IP address to assign (/<CIDR prefix length>)
## (address & gateway must be provided if "manual" is set)
#NET_IPV6_ADDRESS=2001:470:8:677::92a3/64
#NET_IPV6_GATEWAY=2001:470:8:677::2
#NET_IPV6_DNS1=2001:470:20::2
#NET_IPV6_DNS2=
#NET_IPV6_DNS_SEARCH=
##

# NM System Connection Config - IPv4
#NET_IPV4_MODE=manual
#NET_IPV4_ADDRESS=************/12
#NET_IPV4_GATEWAY=**********
#NET_IPV4_DNS1=***********
#NET_IPV4_DNS2=
#NET_IPV4_DNS_SEARCH=google.com

# NM System Connection Config - IPv6
#NET_IPV6_MODE=manual
#NET_IPV6_ADDRESS=2001:470:8:677::92a3/64
#NET_IPV6_GATEWAY=2001:470:8:677::2
#NET_IPV6_DNS1=2001:470:20::2
#NET_IPV6_DNS2=
#NET_IPV6_DNS_SEARCH=

# NM System Connection Config - Protocol-independent 
#NET_MTU=1480
