set default="0"
set timeout=10

if [ -f /boot/grub2/theme/theme.txt ]; then
	set gfxmode=3840x2160,2880x1800,2560x1440,1920x1200,1920x1080,1680x1050,1600x900,1440x900,1400x1050,1366x768,1280x1024,1280x960,1280x800,1280x768,1280x720,1152x864,1024x768,1024x600,auto
	if [ -f /boot/grub2/fonts/unicode.pf2 ]; then
		loadfont /boot/grub2/fonts/unicode.pf2
	fi
	for font in /boot/grub2/theme/*.pf2; do
		loadfont $font
	done
	insmod jpeg
	set theme=/boot/grub2/theme/theme.txt
	set icondir=/boot/grub2/theme/icons
	export icondir
	export theme
	terminal_output gfxterm
fi

# Try to read the machine-id file
set machine_id_file="/boot/machine-id"
if [ -f $machine_id_file ]; then
        source $machine_id_file
else
        # Fallback to a default machine-id
        set machine_id="10000000000000000000000000000001"
fi

menuentry 'DevStation' --class thinstation --class gnu-linux --class gnu --class os --unrestricted {
	set enable_progress_indicator=1
	linux /boot/vmlinuz selinux=0 fips=1 console=tty1 splash vt.global_cursor_default=0 loglevel=3 udev.log_priority=3 systemd.show_status=false rd.systemd.show_status=false boot_device=$root machine_id=$machine_id quiet
	initrd /boot/initrd
}
menuentry 'Oops' --class recovery --class gnu-linux --class gnu --class os --unrestricted {
	set enable_progress_indicator=1
	linux /boot/vmlinuz-backup selinux=0 fips=1 console=tty1 splash vt.global_cursor_default=0 loglevel=3 udev.log_priority=3 systemd.show_status=false rd.systemd.show_status=false boot_device=$root machine_id=$machine_id quiet
	initrd /boot/initrd-backup
}
