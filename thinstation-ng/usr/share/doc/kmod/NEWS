kmod 33
=======

- Improvements

	- Allow to handle compressed modules even without decompression
	  libraries linked in. Previously we would detect if the kernel
	  supported the decompression algorithm and pass the module directly
	  through finit_module(). However it wouldn't consider the file if
	  the respective decompression library was compiled out. Now it's
	  possible to completely disable all libraries and still have module
	  load working with libkmod.

	  Tools that inspect module content themselves like modinfo and depmod
	  won't work if the decompression library is not enabled.

	- Add weak dependencies - these are similar to pre softdep, but they
	  don't cause the dependency to be loaded by libkmod: when a module has
	  a weak dependency, it is expected that module may or may not be used,
	  with decision happening in runtime by the kernel. It's purpose is to
	  be informational for other tools like ones used to create initramfs,
	  so the module is made available before switch_root(), but doesn't
	  imply it to be loaded when not needed.

	- Improve compatibility with non-gnu libc like musl and uClibc. Now it's
	  possible to build and use libkmod and tools without any additional compat
	  patches.

	- Move manpages from xsltproc to scdoc, which is now needed during build.

	- Improve documentation in manpages, fixing typos, rewording sentences,
	  detailing how configuration files are handled with precedence order
	  and making all the manpages more consistent on how to reference options,
	  environment variables, configuration, authors, etc.

	- Speed up zstd decompression, particularly when not using glibc.

	- Stop parsing .alias files from modprobe.d directories. Configuration files
	  were always documented as needing the .conf extension. For compatibility
	  reason with module-init-tools, kmod also parsed .alias files. However that
	  was also done in module-init-tools for compatibility reasons and not
	  documented anywhere. From inspection on what distros are using, none use
	  .alias files in practice, so stop parsing those files and follow what's
	  documented.

	- Adopt SPDX for license and cleanup comments on individual files.

	- Since kmod 29 there's a github mirror for the repository. Now it's
	  also used for issues and improvement tracking. With that, the old
	  TODO file has been removed and distros/users are encouraged to file
	  issues in github.

- Bug fixes

	- Move kmod.pc to the right dir, ${datadir}/pkgconfig, as it's related
	  to kmod, not libkmod.

	- Fix error handling while loading a file and mmap fails.

	- Fix error handling while handling errors from the decompression
	  libraries.

	- Add missing documentation for KMOD_INDEX_MODULES_BUILTIN that was
	  added in v27 breaking the ABI. A wide search has found one external
	  tool using it, which hasn't been updated in the past 12 years. It
	  was deemed safe to simply update the documentation to include the
	  missing enum.

	- Move kmod_module_new_from_name_lookup() to the correct symbol
	  version. It was added by mistake to @LIBKMOD_5 when v30 got released.
	  No external user of this API was found, so it was considered safe
	  to just move it.

- Others

	- Overwrite symlinks when installing tools.

	- General cleanup of how (de)compression libraries are integrated.

	- Add CI infrastructure to automatically test in several distros
	  before applying commit series. Currently the latest versions of
	  Alpine, Archlinux, Fedora and Ubuntu are covered. More distros are
	  easy to add as they are all containerized.

kmod 32
=======

- Improvements

	- Use any hash algo known by kernel/openssl instead of keep needing
	  to update the mapping

	- Teach kmod to load modprobe.d/depmod.d configuration from ${prefix}/lib
	  and allow it to be overriden during build with --with-distconfdir=DIR

	- Make kernel modules directory configurable. This allows distro to
	  make kmod use only files from /usr regardless of having a compat
	  symlink in place.

	- Install kmod.pc containing the features selected at build time.

	- Install all tools and symlinks by default. Previously kmod relied on
	  distro packaging to set up the symlinks in place like modprobe,
	  depmod, lsmod, etc. Now those symlinks are created by kmod itself
	  and they are always placed in $bindir.

- Bug Fixes

	- Fix warnings due to -Walloc-size

- Others

	- Drop python bindings. Those were not update in ages and not compatible
	  with latest python releases.

	- Cleanup test infra, dropping what was not used anymore

	- Drop experimental tools `kmod insert` / `kmod remove`. Building those
	  was protected by a configure option never set by distros. They also
	  didn't gain enough traction to replace the older interfaces via
	  modprobe/insmod/rmmod.

kmod 31
=======

- Improvements

	- Allow passing a path to modprobe so the module is loaded from
	  anywhere from the filesystem, but still handling the module
	  dependencies recorded in the indexes. This is mostly intended for kernel
	  developers to speedup testing their kernel modules without having to load the
	  dependencies manually or override the module in /usr/lib/modules/.
	  Now it's possible to do:

		# modprobe ./drivers/gpu/drm/i915/i915.ko

	  As long as the dependencies didn't change, this should do the right thing

	- Use in-kernel decompression if available. This will check the runtime support
	  in the kernel for decompressing modules and use it through finit_module().
	  Previously kmod would fallback to the older init_module() when using
	  compressed modules since there wasn't a way to instruct the kernel to
	  uncompress it on load or check if the kernel supported it or not.
	  This requires a recent kernel (>= 6.4) to have that support and
	  in-kernel decompression properly working in the kernel.

	- Make modprobe fallback to syslog when stderr is not available, as was
	  documented in the man page, but not implemented

	- Better explaing `modprobe -r` and how it differentiates from rmmod

	- depmod learned a `-o <dir>` option to allow using a separate output
	  directory. With this, it's possible to split the output files from
	  the ones used as input from the kernel build system

	- Add compat with glibc >= 2.32.9000 that dropped __xstat

	- Improve testsuite to stop skipping tests when sysconfdir is something
	  other than /etc

	- Build system improvements and updates

	- Change a few return codes from -ENOENT to -ENODATA to avoid confusing output
	  in depmod when the module itself lacks a particular ELF section due to e.g.
	  CONFIG_MODVERSIONS=n in the kernel.


- Bug Fixes

	- Fix testsuite using uninitialized memory when testing module removal
	  with --wait

	- Fix testsuite not correctly overriding the stat syscall on 32-bit
	  platforms. For most architectures this was harmless, but for MIPS it
	  was causing some tests to fail.

	- Fix handling unknown signature algorithm

	- Fix linking with a static liblzma, libzstd or zlib

	- Fix memory leak when removing module holders

	- Fix out-of-bounds access when using very long paths as argument to rmmod

	- Fix warnings reported by UBSan

kmod 30
=======

- Improvements
	- Stop adding duplicate information on modules.builtin.alias.bin, just use
	  the modules.builtin.bin index

	- Speedup depmod, particularly under qemu with emulated arch, by
	  avoiding a lot of open/read/close of modules.alias.bin. On an
	  emulated ARM rootfs, depmod with only 2 modules was taking ~32s
	  vs ~0.07s now.

	- Add kmod_module_new_from_name_lookup() which allows doing a lookup by
	  module name, without considering the aliases. Other than that search
	  order is similar to kmod_module_new_from_lookup().

	- modinfo learned the --modname option to explicitely show information
	  about the module, even if there is an alias with the same name. This
	  allows showing information about e.g. kernel/lib/crc32.ko, even if
	  kernel also exports a crc32 alias in modules.alias:

		alias crc32 crc32_pclmul
		alias crc32 crc32_generic

	  Same behavior will be used to other modules and to aliases provided
	  by user/distro.

	- depmod.conf learned a new "excludedir" directive so distro/user can
	  configure more directories to be excluded from its search, besides
	  the hardcoded values "build" and "source".

	- Better group modprobe options on help output under "Management, Query and General".

	- modprobe learned a --wait <MSEC> option to be used together with -r
	  when removing a module. This allows modprobe to keep trying the
	  removal if it fails because the module is still in use. An exponential backoff
	  time is used for further retries.

	  The wait behavior provided by the kernel when not passing O_NONBLOCK
	  to delete_module() was removed in v3.13 due to not be used and the
	  consequences of having to support it in the kernel. However there may
	  be some users, particularly on testsuites for individual susbsystems, that
	  would want that. So provide a userspace implementation inside modprobe for
	  such users. "rmmod" doesn't have a --wait as it remains a bare minimal over
	  the API provided by the kernel. In future the --wait behavior can be added
	  to libkmod for testsuites not exec'ing modprobe for module removal.

	- kmod_module_remove_module() learned a new flag to silence output when
	  caller wants to handle them - this is particularly important for the
	  --wait flag to modprobe, as it's not desired to keep seeing error messages
	  while waiting for the module to be unused.

	- Add SM3 hash algo support to modinfo output, as already available in the kernel.

- Bug Fixes
	- Fix modinfo output when showing information for a .ko module when running
	  on a kernel that has that module as builtin.

	- Fix kmod_module_new_from_lookup() returning > 0 rather than 0
	  when it matches an alias.

	- Fix modinfo segfault when module doesn't exist.

	- Add missing function in the html documentation: kmod_get_dirname().

	- Fix modprobe incorrectly handling number of arguments when prepending values from
	  MODPROBE_OPTIONS environment variable.

	- Fix modprobe -r --remove-dependencies and since "dependencies" was a
	  misnomer, add the preferred argument option: "--remove-holders". This
	  is the same name used by the kernel. It allows users to also remove
	  other modules holding the one that is being removed.

	- Fix off-by-one in max module name length in depmod.

- Infra/internal
	- Start some changes in the out-of-tree test modules in kmod so they are useful
	  for being really inserted in the kernel rather than relying on kmod's mock
	  interface. This helps manual testing and may be used to exercise to test
	  changes in the kernel.

kmod 29
=======

- Improvements
	- Add support to use /usr/local as a place for configuration files. This makes it easier
	  to install locally without overriding distro files.

- Bug fixes
	- Fix `modinfo -F` when module is builtin: when we asked by a specific field from modinfo,
	  it was not working correctly if the module was builtin

	- Documentation fixes on precedence order of /etc and /run: the correct order is
	  /etc/modprobe.d, /run/modprobe.d, /lib/modprobe.d

	- Fix the priority order that we use for searching configuration files. The
	  correct one is /etc, /run, /usr/local/lib, /lib, for both modprobe.d
	  and depmo.d

	- Fix kernel command line parsing when there are quotes present. Grub
	  mangles the command line and changes it from 'module.option="val with
	  spaces"' to '"module.option=val with spaces"'. Although this is weird
	  behavior and grub could have been fixed, the kernel understands it
	  correctly for builtin modules. So change libkmod to also parse it
	  correctly. This also brings another hidden behavior from the kernel:
	  newline in the kernel command line is also allowed and can be used to
	  separate options.

	- Fix a memory leak, overflow and double free on error path

	- Fix documentation for return value from kmod_module_get_info(): we
	  return the number of entries we added to the list

	- Fix output of modules.builtin.alias.bin index: we were writing an empty file due to
	  the misuse of kmod_module_get_info()

- Infra/internal
	- Retire integration with semaphoreci

	- Declare the github mirror also as an official upstream source: now besides accepting
	  patches via mailing list, PRs on github are also acceptable

	- Misc improvements to testsuite, so we can use it reliably regardless
	  of the configuration used: now tests will skip if we don't have the
	  build dependencies)

kmod 28
=======

- Improvements
	- Add Zstandard to the supported compression formats using libzstd
	  (pass --with-zstd to configure)

- Bug fixes
	- Ignore ill-formed kernel command line, e.g. with "ivrs_acpihid[00:14.5]=AMD0020:0"
	  option in it
	- Fix some memory leaks
	- Fix 0-length builtin.alias.bin: it needs at least the index header

kmod 27
=======

- Improvements
	- Link to libcrypto rather than requiring openssl

	- Print a better error message when kernel doesn't support module unload

	- Use PKCS#7 instead of CMS for parsing module signature to be
	  compatible with LibreSSL and OpenSSL < 1.1.0

	- Teach modinfo to parse modules.builtin.modinfo. When using Linux kernel
	  >= v5.2-rc1 it's possible to get module information from this new file. Now
	  modinfo is able to show it instead of an error message that the module is
	  built-in:

	  Before:
	  $ modinfo ext4
	  modinfo: ERROR: Module ext4 not found.

	  After:
	  $ modinfo ext4
	  name:           ext4
	  filename:       (builtin)
	  softdep:        pre: crc32c
	  license:        GPL
	  description:    Fourth Extended Filesystem
	  author:         Remy Card, Stephen Tweedie, Andrew Morton, Andreas Dilger, Theodore Ts'o and others
	  alias:          fs-ext4
	  alias:          ext3
	  alias:          fs-ext3
	  alias:          ext2
	  alias:          fs-ext2

- Bug fixes
	- Do not link python bindings with libpython to be compatible with
	  python3.8

	- Fix module removal with `modprobe -r` when a dependency is built-in.
	  Now it properly ignores them and proceed with removal of other
	  dependencies

	- Fix propagation of return code from install/remove commands to the
	  the probe function. The return values of kmod_module_probe_insert_module()
	  have very specific meanings, do not confuse the caller by return codes
	  from system()

	- Fix softdep config parsing leading to buffer overflow

kmod 26
=======

- Improvements
	- Add more error-checking in library functions and remove warnings on newer
	  toolchains

	- Depmod now handles parallel invoctions better by protecting the temporary
	  files being used

	- Improvements to testsuite and added tests to check the our behavior
	  regardless of the features enabled in the kernel, or libraries we link to

	- Teach the --show-exports option to modprobe. This works similarly to
	  --show-modversions, but it reports the exported symbols from that module.
	  Under the hood this reads the .symtab and .strtab section rather than
	  __versions so it shows useful data even if kernel is configured without
	  modversions (CONFIG_MODVERSIONS)

	- Teach pkcs7 parsing to modinfo by using openssl. This allows modinfo to
	  correctly parse the signature appended to a module by the kernel build
	  system when configured with CONFIG_MODULE_SIG_ALL, or when externally
	  signed by the distro. Traditionally modules were signed and a struct
	  was appended together with the signature to the end of the module.
	  This has changed on the kernel for pkcs#7 and now the structure isn't
	  filled out with useful information.  So we have to parse the signature
	  block in order to return useful data to the user.

	  If kmod is linked with openssl we parse the signature and return the
	  fields as we do for other signatures. An example of the relevant part
	  on the output of modinfo is below:

	  Before:
		  sig_id:         PKCS#7
		  signer:
		  sig_key:
		  sig_hashalgo:   md4
	  After:
		  sig_id:         PKCS#7
		  signer:         Fedora kernel signing key
		  sig_key:        51:C4:0C:6D:7E:A5:6C:D8:8F:B4:3A:DF:91:78:4F:18:BC:D5:E4:C5
		  sig_hashalgo:   sha256

	  If kmod is not linked to openssl we just start printing "unknonwn" in the
	  sig_hashalgo field rather than the bogus value.


kmod 25
=======

- Improvements
	- Add module signature to modinfo output

	- Add support for external directories in depmod: now there's a new
	  "external" keyword parsed by depmod when calculating the dependencies.
	  It allows to add modules to other directories which are not relative
	  to where the modules are commonly installed.  This results in
	  modules.dep and friends now understanding absolute paths rather than
	  relative paths only. For more information see depmod.d(1).

	- Add support for CONFIG_MODULE_REL_CRCS

	- Add missing documentation references in man pages

	- Handle the case in which module has a .TOC symbol already while
	  calculating dependencies

	- Improve testsuite and allow to use mkosi to run testsuite in different
	  distros

kmod 24
=======

- Improvements:
	- Add more information on dependency loop

	- Sanitize use of strcpy and allow to grow from small strings on stack
	  (common case) to bigger strings on heap when needed

- Bug fixes
	- Fix wrong dependency loops being reported by depmod

	- Fix crashes when reporting dependency loops

	- Fix parsing kernel command line containing quotes

	- Fix leaks on error paths

kmod 23
=======

- Improvements:
	- Don't add comment to modules.devname if it would otherwise be empty
	  to play nice with tools detecting empty files

	- Allow building with BSD sed, that doesn't have -E flag

	- Ignore .TOC. symbols in depmod parsing as it's for PPC64 the
	  equivalent of _GLOBAL_OFFSET_TABLE_

	- Teach modinfo about PKCS#7 module signatures: it doesn't add any
	  other info besides telling the user the module is signed since
	  kernel doesn't add other info on the module section

- Bug fixes

	- Fix -s and -p compat options to insmod triggering force flag

	- Fix long lines from /proc/modules not being handled correctly by
	  kmod_module_new_from_loaded() and kmod_module_get_size() and several
	  other library functions that use them

	- Fix crash on modinfo while checking for available signature of
	  unknown type

	- Fix documentation generation with gtk-doc

kmod 22
=======

- Tools:
	- Change defaul log level for tools to WARNING rather than ERROR and update
	  some log levels for current messages

	- depmod doesn't fallback to uname if a bad version is passed in the command
	  line anymore. We just exit with an error.

	- insmod was taught the -f flag, just like in modprobe. It was previously
	  silently ignoring it.

- libkmod
	- New kmod_get_dirname() API to get the module directory set in the
	  context

- Bug fixes:
	- Fix return code in error path of kmod_module_insert_module(). We were
	  previously returning ENOSYS rather than ENOENT.

kmod 21
=======

- New features:
	- kmod tool started to learn the "insert" and "remove" commands that
	  are the simplified versions of the older modprobe tool.  These
	  commands are still work in progress so they are hidden behind a
	  --enable-experimental flag during build.  It should not be enabled
	  unless you know what you're doing.
	- kmod tool now prints the relevant configuration options it was built
	  with when the "--version" argument is passed. This helps to mitigate
	  problems for example when the user is trying to load a compressed
	  module but kmod was built without support for the compression method.

- Improvements to testsuite:
	- Cache built modules so it is easier to run "make check" on build
	  servers by distro maintainers. If kmod is configured with
	  --disable-test-modules the modules from cache will be used by
	  "make check". No changes to the tests are needed and all of them
	  can run fine.

kmod 20
=======
- Bug fixes:
	- Handle bogus values from ELF, making sure they don't overflow while
	  parsing the file
	- Fix leak in depmod when -b flag is passed multiple times
	- Multiple minor fixes from static analysis by coverity and
	  clang-analyze
	- Fix race between loading modules and checking if it's loaded in the
	  kernel

- New features:
	- There's a change in behavior regarding builtin modules: we now only
	  consider as builtin those that are present in modules.builtin index.
	  Previously we were also checking the presence of
	  /sys/module/<module-name>, but this is racy and only modules that
	  contain parameters are the ones creating a directory in sysfs.

	  Now some commands will start to fail, e.g. "modprobe vt". Since vt
	  can't be compiled as a module it's not present in modules.builtin
	  index. Previously we would report at as builtin, but now we fail
	  because we couldn't find the module.

- Improvements:
	- Integration of gcov into the build. Currently libkmod is at ~70%
	  covered and tools at ~50% by tests in the testsuite. Utility
	  functions and structures in shared have more than 90% of coverage.
	- Upload build to coverity

- Improvements to testsuite:
	- Fix parsing return codes of init_module() calls
	- Add tests for utility functions in shared/
	- Add tests for kmod_module_remove_module()
	- Add playground, in which our own modules are compiled
	- Port all tests to use modules from module-playground instead of
	  copying prebuilt modules to the repository
	- Properly handle binaries that exit with no output
	- Besides comparing the output of commands, allow to copy to
	  stdout/stderr

kmod 19
=======

- Bug fixes:
	- Fix missing CLOEXEC in library
	- Fix error message while opening kmod's index

- New features:
	- Add kmod(8) man page
	- Allow to build with libc's without be32toh()
	- Move code around separating common code and data structures into a
	  shared directory. This allows to share more code between library and
	  tools, making the binary size of tools smaller.
	- Clarify tools vs library licenses
	- static-nodes: when writing in tmpfiles format, indicate that
	  creation of static nodes should only happen at boot. This is used and
	  required by systemd-217+.

- Improvements to testsuite:
	- Add tests for newly created shared/ code
	- Improve how tests are declared so there's less boilerplate code for
	  each test.

kmod 18
=======

- Bug fixes:
	- Fix leaks in error paths
	- Fix use-after-free in hash implementation causing a wrong index to be
	  generated by depmod with out-of-tree modules

- New features:
	- Calling depmod with modules creating a dependency loop will now make
	  depmod return an error and not update the indexes. This is to protect
	  the current index not being overridden by another index that may cause
	  a boot failure, depending on the buggy module. It's a necessary
	  change in behavior regarding previous kmod releases and
	  module-init-tools. The error message was also improved to output
	  the modules that caused the dependency cycle.

- Improvements to testsuite:
	- Fix and improve expected-fail test
	- Add tests for hashmap implementation

kmod 17
=======

- Bug fixes:
	- Fix matching a "." in kernel cmdline, making garbage in the command
	  line be parsed as kmod options
	- Fix man pages to clarify we don't fallback to parsing modules.dep
	  but instead we depend on modules.dep.bin (generated by depmod) to
	  be present
	- Fix ELF parsing on 32 bit systems assigning the wrong class.
	- Fix partial matches of search directives in depmod. Previously having
	  a line in depmod.conf such as "search foo foobar built-in" would cause
	  unpretictable results because foo is a partial match of foobar as well.
	- Fix unaligned access in modinfo when getting the signature from a
	  module
	- Make sure softdeps are treated as optional dependencies

- New features:
	- Accept special files given to "-C" switch in modprobe. This way it's
	  possible to skip system configuration with "modprobe -C /dev/null"
	- Do not require xsltproc on released tarballs
	- Don't use Werror anymore
	- Add experimental python bindings, merged from python-kmod repository
	  (https://github.com/agrover/python-kmod)
	- Parse softdeps exported by the kernel as
	  /lib/modules/`uname -r`/modules.softdep

- Improvements to testsuite:
	- Check the list of loaded modules after a test

kmod 16
=======

- Bug fixes:
	- Fix usage of readdir_r()
	- Add some missing checks for memory allocation errors

- New features:
	- Remove option from libkmod to allow waiting on module removal if
	  the module is being used. It's dangerous since it can block the
	  caller indefinitely.
	- Improve compatibility with musl libc
	- Add fallback implementation for compilers without _Static_assert(),
	  e.g. gcc < 4.6
	- Minor optimizations to the hash table
	- Make depmod warn if a module has incorrect devname specification
	- Use cleanup attribute

kmod 15
=======

- Bug fixes:
	- kmod static-nodes doesn't fail if modules.devname isn't available
	- Fix getting boolean parameter from kernel cmdline in case the value
	  is omitted
	- Fix some mkdir_p() corner cases (used in testsuite and static-nodes)

- New features:
	- kmod static-nodes creates parent directories if given a -o option
	- kmod binary statically links to libkmod - if distro is only interested
	  in the kmod tool (for example in an initrd) it can refrain from
	  installing the library
	- Add shell completion for kmod tool

kmod 14
=======

- Bug fixes:
	- Fix some format strings
	- Protect against NULL being passed around to index
	- Avoid calling syscall() with -1 when finit_module() is not available,
	  since this doesn't always work
	- Fix not being able to remove alias due to checking the module's
	  refcount
	- Minor fixes and refactors

- New features:
	- Improve libkmod documentation, particularly on how flags are dealt
	  with.
	- Remove ability to build a static libkmod
	- Add static-nodes command to kmod that parses modules.devname
	  generating output in useful formats

kmod 13
=======

- Bug fixes:
	- Add the long option --symbol-prefix option to depmod (it was absent)
	  and fix its behavior
	- Don't abort if there's a bogus line in configuration file like "alias
	  psmouse off". Some distros are carrying this since the days of
	  modutils

- New features:
	- Add support for finit_module(2). If the module is load straight from
	  the disk and without compression we use finit_module() syscall when
	  available, falling back to init_module() otherwise
	- kmod_module_get_info() also returns the signature if the module is
	  signed and modinfo uses it
	- Use secure_getenv if available
	- rmmod understands builtin modules, just like modprobe does
	- Improve compatibility with musl-libc
	- Test cases exit with success when receiving a signal if they are
	  xfail tests

kmod 12
=======

- Bug fixes:
	- Fix removing vermagic from module when told to force load a module
	- Fix removing __versions section when told to force load a module: we
	  need to mangle the section header, not the section.
	- modinfo no longer fails while loading a module from file when path
	  contains ".ko" substring

kmod 11
=======

- Improvements to testsuite:
	- Fix testsuite defining symbols twice on 32 bit systems
	- Allow to check generated files against correct ones

- New features:
	- libkmod now keeps a file opened after the first call to
	  kmod_module_get_{info,versions,symbols,dependency_symbols}. This
	  reduces significantly the amount of time depmod tool takes to
	  execute. Particularly if compressed modules are used.
	- Remove --with-rootprefix from build system. It was not a great idea
	  after all and should not be use since it causes more harm then
	  benefits.
	- Hide --wait option on rmmod. This feature is being targeted for
	  removal from kernel. rmmod still accepts this option, but it's hidden
	  now: man page and usage() say nothing about it and if it's used,
	  user will get a 10s sleep. This way we can check and help if anyone
	  is using this feature.
	- Refactor message logging on all tools, giving proper prefix, routing
	  everything to syslog when asked for, etc.

- Bug fixes:
	- Fix parsing of modules.order when using compressed modules
	- Usage messages go to stdout instead of stderr
	- Fix memory leak in hash implementation

kmod 10
=======

- New features:
	- Read coresize from /sys if supported

	- Add flag to kmod_module_probe_insert() to apply blacklisting during
	  probe only if mod is an alias. Now modprobe uses this flag by default.
	  This is needed to fix a change in behavior regarding module-init-tools
	  and ultimately makes us loading a blacklisted module.

- Better formatting in man pages

- Add option to disable building man pages at build time

- Fixes in the testsuite and refactoring of LDPRELOAD'ed libraries

- Re-licensing testsuite as LGPL

kmod 9
======

- Improvements to the testsuite:
	- Check for correct handling of softdep loops
	- Check for correct handling of install command loops

- Bug fixes:
	- Fix build with compilers that don't support --gc-sections
	- Handle errors when dealing with gzipped modules
	- depmod now handles errors while writing indices, so it doesn't end up
	  with a corrupted index without telling the user

kmod 8
======

- No new features, small bug fixes only.
	- Fix a bug in "modprobe -c" output: be compatible with
	  module-init-tools

	- Give a useful error message when init_module fails due to bad
	  parameter or unknown symbols

	- Fix doc generation

kmod 7
======

- Re-order dirs for configuration files to match the change in systemd and
  udev: now the priority is:
	1. /etc/modprobe.d
	2. /run/modprobe.d
	3. /lib/modprobe.d

- Fix setting CFLAGS/LDFLAGS in build system. This prevented us from not
  allowing the user to set his preferences.

- Bug fixes:
	- Return same error codes of module-init-tools when removing modules
	  with modprobe
	- Fix builtin output in "--show-depends" when target kernel is not the
	  same of the running kernel
	- 'modprobe -r' always look at all command line arguments
	- Fix '-q' usage in modprobe

kmod 6
======

- New API in libkmod:
	- kmod_module_apply_filter(): a generic function to apply filters in a
	  list of modules. This deprecates the use of
	  kmod_module_get_filtered_blacklist()

- More tests in testsuite

- Add compatibility with uClibc again

- Lookup modules.builtin.bin to decide if a module is built in kernel

- Downgrade some log messages so we don't annoy people with useless messages

- Bug fixes:
	- Flag --ignore-loaded was not being properly handled
	- Infinite loop with softdeps
	- Infinite loop with dumb user configuration with install commands
	- Fix leak in index when there's a partial match

- Move repository and tarballs to kernel.org

kmod 5
======

- Break libkmod's API to insert a module like modprobe does. It now accepts
  extra an extra argument to print its action and acceptable flags were
  sanitized.

- Share more code between modprobe and libkmod: using the new version of
  kmod_module_probe_insert_module() it's possible to share a great amount of
  code between modprobe and libkmod

- modprobe no longer works with paths: it only accepts module names and/or
  aliases now.

- testsuite was added to repository, allowing automated tests to be run and
  easing the way bugs are reproduced.

- modprobe: when dumping configuration ('-c' option) separate config
  and indexes by adding a commented line between them.

- Fix bugs wrt normalizing aliases and module names

- Fix bug wrt inserting an alias that resolves to multiple modules: we should
  not stop on the first error, but rather continue to try loading other
  modules.

- Fix unaligned memory access in hash function, causing depmod to output wrong
  information in ARMv5

- Fix man page build and install: now they are only installed if tools are
  enabled

kmod 4
======

- New APIs in libkmod to:
	- Get configuration lists: blacklists, install commands, remove
	  commands, aliases, options and softdeps
	- Dump indexes

- Several bugs fixed in libkmod, modprobe, depmod and modinfo

- API documentation: if configure with run with --enable-gtk-doc, the API doc
  will be generated by make. Gtk-doc is required for that.

- Man pages are built, which replace man pages from module-init-tools

- 'include' and 'config' options in *.conf files were deprecated

- configure is not run by autogen.sh. Instead, a common set of options is
  printed. If you are hacking on kmod, consider using bootstrap-configure
  script.

- 'modprobe -c' works as expected now. As opposed to module-init-tools, it
  dumps the parsed configuration, not only the file contents.

kmod 3
======

- New APIs in libkmod to:
	- Get symbols from module, parsing the ELF section
	- Get dependency symbols
	- Check if resources are still valid or if libkmod must be reloaded
	- Insert module like modprobe, checking (soft-)dependencies, commands,
	  blacklist. It can run commands by itself and to call a callback
	  function.

- Support to load modules compressed with xz

- Tools are now bundled together in a single tool called kmod. It can be
  called using symlinks with the same names as tools from module-init-tools.
  E.g: /usr/bin/lsmod -> /usr/bin/kmod. With this we are aiming to complete a
  1:1 replacement of module-init-tools.

- The only missing tool, depmod, was added to kmod together with the necessary
  APIs in libkmod.

- If a program using libkmod runs for a long time, as for example udev, it must
  check if it doesn't have to re-load libkmod. A new helper function was added
  in libkmod to check if context is still valid and udev is already using it.

- An 'unaligned access' bug was fixed. So those architecture that does not
  handle unaligned access can use kmod, too.

kmod 2
======

Some bugs fixed: the worst of them was with an infinite loop when an alias
matched more than one module.

- New APIs in libkmod to:
	- Get soft dependencies
	- Get info from module files parsing ELF
	- Get modversions from files parsing ELF

- Support to load gzipped kernel modules: kmod can be compiled with support to
  gzipped modules by giving the --enable-zlib flag

- Support to forcefully load modules, both vermagic and modversion

- Support to force and nowait removal flags

- Configuration files are parsed in the same order as modprobe: files are
  sorted alphabetically (independently of their dir) and files with the same
  name obey a precedence order

- New tool: kmod-modinfo

- kmod-modprobe gained several features to be a 1:1 replacement for modprobe.
  The only missing things are the options '--showconfig' and '-t / -l'. These
  last ones have been deprecated long ago and they will be removed from
  modprobe. A lot of effort has been put on kmod-modprobe to ensure it
  maintains compabitility with modprobe.

- <EMAIL> became the official mailing list for kmod

kmod 1
======

First version of kmod and its library, libkmod.

In the libkmod it's currently possible to:
	- List modules currently loaded
	- Get information about loaded modules such as initstate, refcount,
	  holders, sections, address and size
	- Lookup modules by alias, module name or path
	- Insert modules: options from configuration and extra options can be
	  passed, but flags are not implemented, yet
	- Remove modules
	- Filter list of modules using blacklist
	- For each module, get the its list of options and install/remove
	  commands
	- Indexes can be loaded on startup to speedup lookups later

Tools provided with the same set of options as in module-init-tools:
	- kmod-lsmod
	- kmod-insmod
	- kmod-rmmod
	- kmod-modprobe, with some functionality still missing (use of softdep,
	  dump configuration, show modversions)
