#!/bin/bash

home=`pwd`
set -x
port=$1
version=`prt-get printf %v --filter=$port`
release=`prt-get printf %r --filter=$port`
path=`prt-get path $port`
if [ -e $path/${port}#${version}-${release}.pkg.tar.gz ]; then
	sarchive=$path/${port}#${version}-${release}.pkg.tar.gz
	darchive=$path/`basename $sarchive .gz`.xz
elif [ -e $path/${port}#${version}-${release}.pkg.tar.xz ]; then
	sarchive=$path/${port}#${version}-${release}.pkg.tar.xz
	darchive=$sarchive
fi

if [ -z $sarchive ]; then
	echo "couldn't find an archive"
	prt-get update -fr $port
	echo $port >> /tryagain
	exit 1
fi
if [ -z $path ]; then
	echo "you failed $port"
	exit 1
fi

if [ -e /tmp/shrink ]; then
	while mountpoint /tmp/shrink; do
		umount /tmp/shrink
	done
	rmdir /tmp/shrink
fi

mkdir -p /tmp/shrink
mount -t tmpfs -o size=1G tmpfs /tmp/shrink
tar -xf $sarchive -C /tmp/shrink 2>/dev/null || exit 1
oldsize=`du -sb $sarchive |cut -f1`
cd /tmp/shrink
tar -c * |xz -9 --lzma2=dict=max > $darchive.new
newsize=`du -sb $darchive.new |cut -f1`
if [ "$oldsize" -gt "$newsize" ]; then
	cp $darchive.new $darchive
fi
rm $darchive.new

cd $home
umount /tmp/shrink
rmdir /tmp/shrink
