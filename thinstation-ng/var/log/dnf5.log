2025-08-08T00:10:42+0000 [14309] INFO --- DNF5 launched with arguments: "dnf install --setopt=keepcache=1 -y --installroot=/home/<USER>/test/thinstation-ng --use-host-config --releasever=42 basesystem dnf dnf-plugins-core filesystem kernel-core kernel-modules kernel-modules-core kernel-modules-extra grub2-common grub2-efi-x64 grub2-efi-x64-modules grub2-pc-modules grub2-tools grub2-tools-efi grub2-tools-minimal shim-x64 alsa-sof-firmware amd-gpu-firmware amd-ucode-firmware atheros-firmware brcmfmac-firmware cirrus-audio-firmware intel-audio-firmware intel-gpu-firmware intel-vsc-firmware iwlegacy-firmware iwlwifi-dvm-firmware iwlwifi-mvm-firmware libertas-firmware linux-firmware linux-firmware-whence microcode_ctl mt7xxx-firmware nvidia-gpu-firmware nxpwireless-firmware realtek-firmware tiwilink-firmware wireless-regdb" ---
2025-08-08T00:10:42+0000 [14309] DEBUG Loading plugin file="/usr/lib64/dnf5/plugins/builddep_cmd_plugin.so"
2025-08-08T00:10:42+0000 [14309] INFO Loaded dnf5 plugin "builddep" ("/usr/lib64/dnf5/plugins/builddep_cmd_plugin.so") version "1.0.0"
2025-08-08T00:10:42+0000 [14309] DEBUG Loading plugin file="/usr/lib64/dnf5/plugins/changelog_cmd_plugin.so"
2025-08-08T00:10:42+0000 [14309] INFO Loaded dnf5 plugin "changelog" ("/usr/lib64/dnf5/plugins/changelog_cmd_plugin.so") version "1.0.0"
2025-08-08T00:10:42+0000 [14309] DEBUG Loading plugin file="/usr/lib64/dnf5/plugins/config-manager_cmd_plugin.so"
2025-08-08T00:10:42+0000 [14309] INFO Loaded dnf5 plugin "config-manager" ("/usr/lib64/dnf5/plugins/config-manager_cmd_plugin.so") version "0.1.0"
2025-08-08T00:10:42+0000 [14309] DEBUG Loading plugin file="/usr/lib64/dnf5/plugins/copr_cmd_plugin.so"
2025-08-08T00:10:42+0000 [14309] INFO Loaded dnf5 plugin "copr" ("/usr/lib64/dnf5/plugins/copr_cmd_plugin.so") version "0.1.0"
2025-08-08T00:10:42+0000 [14309] DEBUG Loading plugin file="/usr/lib64/dnf5/plugins/needs_restarting_cmd_plugin.so"
2025-08-08T00:10:42+0000 [14309] INFO Loaded dnf5 plugin "needs_restarting" ("/usr/lib64/dnf5/plugins/needs_restarting_cmd_plugin.so") version "1.0.0"
2025-08-08T00:10:42+0000 [14309] DEBUG Loading plugin file="/usr/lib64/dnf5/plugins/repoclosure_cmd_plugin.so"
2025-08-08T00:10:42+0000 [14309] INFO Loaded dnf5 plugin "repoclosure" ("/usr/lib64/dnf5/plugins/repoclosure_cmd_plugin.so") version "1.0.0"
2025-08-08T00:10:42+0000 [14309] DEBUG Loading plugin file="/usr/lib64/dnf5/plugins/reposync_cmd_plugin.so"
2025-08-08T00:10:42+0000 [14309] INFO Loaded dnf5 plugin "reposync" ("/usr/lib64/dnf5/plugins/reposync_cmd_plugin.so") version "1.0.0"
2025-08-08T00:10:43+0000 [14309] DEBUG Creating repo "fedora-cisco-openh264" from config file "/etc/yum.repos.d/fedora-cisco-openh264.repo" section "fedora-cisco-openh264"
2025-08-08T00:10:43+0000 [14309] DEBUG Creating repo "fedora-cisco-openh264-debuginfo" from config file "/etc/yum.repos.d/fedora-cisco-openh264.repo" section "fedora-cisco-openh264-debuginfo"
2025-08-08T00:10:43+0000 [14309] DEBUG Creating repo "fedora-cisco-openh264-source" from config file "/etc/yum.repos.d/fedora-cisco-openh264.repo" section "fedora-cisco-openh264-source"
2025-08-08T00:10:43+0000 [14309] DEBUG Creating repo "updates-testing" from config file "/etc/yum.repos.d/fedora-updates-testing.repo" section "updates-testing"
2025-08-08T00:10:43+0000 [14309] DEBUG Creating repo "updates-testing-debuginfo" from config file "/etc/yum.repos.d/fedora-updates-testing.repo" section "updates-testing-debuginfo"
2025-08-08T00:10:43+0000 [14309] DEBUG Creating repo "updates-testing-source" from config file "/etc/yum.repos.d/fedora-updates-testing.repo" section "updates-testing-source"
2025-08-08T00:10:43+0000 [14309] DEBUG Creating repo "updates" from config file "/etc/yum.repos.d/fedora-updates.repo" section "updates"
2025-08-08T00:10:43+0000 [14309] DEBUG Creating repo "updates-debuginfo" from config file "/etc/yum.repos.d/fedora-updates.repo" section "updates-debuginfo"
2025-08-08T00:10:43+0000 [14309] DEBUG Creating repo "updates-source" from config file "/etc/yum.repos.d/fedora-updates.repo" section "updates-source"
2025-08-08T00:10:43+0000 [14309] DEBUG Creating repo "fedora" from config file "/etc/yum.repos.d/fedora.repo" section "fedora"
2025-08-08T00:10:43+0000 [14309] DEBUG Creating repo "fedora-debuginfo" from config file "/etc/yum.repos.d/fedora.repo" section "fedora-debuginfo"
2025-08-08T00:10:43+0000 [14309] DEBUG Creating repo "fedora-source" from config file "/etc/yum.repos.d/fedora.repo" section "fedora-source"
2025-08-08T00:10:43+0000 [14309] DEBUG Loading system repo rpmdb from root "/"
2025-08-08T00:10:43+0000 [14309] INFO [librepo] Librepo version: 1.19.0 with CURL_GLOBAL_ACK_EINTR support (libcurl/8.9.1 OpenSSL/3.2.4 zlib/1.3.1.zlib-ng brotli/1.1.0 libidn2/2.3.8 libpsl/0.21.5 libssh/0.10.6/openssl/zlib nghttp2/1.62.1 OpenLDAP/2.6.9)
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] Current date: 2025-08-08T00:10:43+0000
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Using dir: /home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-cisco-openh264-98ac828bd7db7eab
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Using own SIGINT handler
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Preparing internal mirrorlist
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing internal mirrorlist
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing mirrors reported via LRI_MIRRORS
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Downloading/Locating yum repo
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_use_local: Locating repo..
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_use_local_load_base: open(/home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-cisco-openh264-98ac828bd7db7eab/repodata/repomd.xml): No such file or directory
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Restoring an old SIGINT handler
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Using dir: /home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-c27b7abdec3274a0
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Using own SIGINT handler
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Preparing internal mirrorlist
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing internal mirrorlist
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing mirrors reported via LRI_MIRRORS
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Downloading/Locating yum repo
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_use_local: Locating repo..
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_use_local_load_base: open(/home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-c27b7abdec3274a0/repodata/repomd.xml): No such file or directory
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Restoring an old SIGINT handler
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Using dir: /home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Using own SIGINT handler
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Preparing internal mirrorlist
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing internal mirrorlist
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing mirrors reported via LRI_MIRRORS
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Downloading/Locating yum repo
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_use_local: Locating repo..
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_use_local_load_base: open(/home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4/repodata/repomd.xml): No such file or directory
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Restoring an old SIGINT handler
2025-08-08T00:10:43+0000 [14309] DEBUG Downloading metadata for repo "fedora-cisco-openh264"
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Using dir: /home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-cisco-openh264-98ac828bd7db7eab/tmpdir.mQTC7j
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Using own SIGINT handler
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Preparing internal mirrorlist
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_download: Target: https://mirrors.fedoraproject.org/metalink?repo=fedora-cisco-openh264-42&arch=aarch64 (-)
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] Selecting mirror for: https://mirrors.fedoraproject.org/metalink?repo=fedora-cisco-openh264-42&arch=aarch64
2025-08-08T00:10:43+0000 [14309] INFO [librepo] Downloading: https://mirrors.fedoraproject.org/metalink?repo=fedora-cisco-openh264-42&arch=aarch64
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_download: Downloading started
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] Transfer finished: https://mirrors.fedoraproject.org/metalink?repo=fedora-cisco-openh264-42&arch=aarch64 (Effective url: https://mirrors.fedoraproject.org/metalink?repo=fedora-cisco-openh264-42&arch=aarch64)
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Parsing metalink.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Mirrors from metalink:
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://codecs.fedoraproject.org/openh264/42/aarch64/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Metalink parsed
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing internal mirrorlist
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing mirrors reported via LRI_MIRRORS
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Downloading/Locating yum repo
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_download_remote: Downloading/Copying repo..
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_download_repomd: Downloading repomd.xml via mirrorlist
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_get_best_checksum: Expected checksum for repomd.xml: (sha512) 220e6921312486d40ed9c75b6ffb41644cf7984e5a85763f9f5074c4da5b11174238f69ed092c5d406e81bd90747183b40d37f1e18f6416a9ac757a348ab4d8f
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_download: Target: repodata/repomd.xml (-)
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Preparing internal mirror list for handle id: 0xaaaae2675740
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://codecs.fedoraproject.org/openh264/42/aarch64/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] Selecting mirror for: repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] INFO [librepo] Downloading: https://codecs.fedoraproject.org/openh264/42/aarch64/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_download: Downloading started
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] Transfer finished: repodata/repomd.xml (Effective url: https://codecs.fedoraproject.org/openh264/42/aarch64/repodata/repomd.xml)
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] check_finished_transfer_checksum: Checksum (sha512) 220e6921312486d40ed9c75b6ffb41644cf7984e5a85763f9f5074c4da5b11174238f69ed092c5d406e81bd90747183b40d37f1e18f6416a9ac757a348ab4d8f is OK
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_download_remote: Parsing repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] 1742403213: Repomd revision: lr_yum_download_remote
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_download: Target: repodata/0432e82f46dcdf0d6da2898de160539fda423fe57eb1b90b97e1f55b82bdd07a-primary.xml.zst (-)
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Preparing internal mirror list for handle id: 0xaaaae2675740
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://codecs.fedoraproject.org/openh264/42/aarch64/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] Selecting mirror for: repodata/0432e82f46dcdf0d6da2898de160539fda423fe57eb1b90b97e1f55b82bdd07a-primary.xml.zst
2025-08-08T00:10:43+0000 [14309] INFO [librepo] Downloading: https://codecs.fedoraproject.org/openh264/42/aarch64/repodata/0432e82f46dcdf0d6da2898de160539fda423fe57eb1b90b97e1f55b82bdd07a-primary.xml.zst
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_download: Downloading started
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] Transfer finished: repodata/0432e82f46dcdf0d6da2898de160539fda423fe57eb1b90b97e1f55b82bdd07a-primary.xml.zst (Effective url: https://codecs.fedoraproject.org/openh264/42/aarch64/repodata/0432e82f46dcdf0d6da2898de160539fda423fe57eb1b90b97e1f55b82bdd07a-primary.xml.zst)
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] check_finished_transfer_checksum: Checksum (sha256) 0432e82f46dcdf0d6da2898de160539fda423fe57eb1b90b97e1f55b82bdd07a is OK
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Restoring an old SIGINT handler
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Using dir: /home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-cisco-openh264-98ac828bd7db7eab
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Using own SIGINT handler
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Preparing internal mirrorlist
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Local metalink.xml found at /home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-cisco-openh264-98ac828bd7db7eab/metalink.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Parsing metalink.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Mirrors from metalink:
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://codecs.fedoraproject.org/openh264/42/aarch64/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Metalink parsed
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing internal mirrorlist
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing mirrors reported via LRI_MIRRORS
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Downloading/Locating yum repo
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_use_local: Locating repo..
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_use_local_load_base: Found local metalink: /home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-cisco-openh264-98ac828bd7db7eab/metalink.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_use_local_load_base: Parsing repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_use_local_load_base: Repomd revision: 1742403213
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_use_local: Repository was successfully located
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_check_checksum_of_md_record: Checking checksum of /home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-cisco-openh264-98ac828bd7db7eab/repodata/0432e82f46dcdf0d6da2898de160539fda423fe57eb1b90b97e1f55b82bdd07a-primary.xml.zst (expected: 0432e82f46dcdf0d6da2898de160539fda423fe57eb1b90b97e1f55b82bdd07a [sha256])
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_checksum_fd_compare: Using mtime cached in xattr: [user.librepo.checksum.mtime] 1754611843398146148
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_checksum_fd_compare: Using checksum cached in xattr: [user.librepo.checksum.sha256] 0432e82f46dcdf0d6da2898de160539fda423fe57eb1b90b97e1f55b82bdd07a
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_check_checksum_of_md_record: Checksum check - Passed
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Restoring an old SIGINT handler
2025-08-08T00:10:43+0000 [14309] DEBUG Downloading metadata for repo "fedora"
2025-08-08T00:10:43+0000 [14309] TRACE countme: event triggered for repo "fedora": bucket 1
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Using dir: /home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-c27b7abdec3274a0/tmpdir.Pw2Cd7
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Using own SIGINT handler
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Preparing internal mirrorlist
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_download: Target: https://mirrors.fedoraproject.org/metalink?repo=fedora-42&arch=aarch64 (-)
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] Selecting mirror for: https://mirrors.fedoraproject.org/metalink?repo=fedora-42&arch=aarch64
2025-08-08T00:10:43+0000 [14309] INFO [librepo] Downloading: https://mirrors.fedoraproject.org/metalink?repo=fedora-42&arch=aarch64&countme=1
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_download: Downloading started
2025-08-08T00:10:43+0000 [14309] TRACE Cache file "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-cisco-openh264-98ac828bd7db7eab/solv/fedora-cisco-openh264.solv" not found
2025-08-08T00:10:43+0000 [14309] DEBUG Loading repomd and primary for repo "fedora-cisco-openh264"
2025-08-08T00:10:43+0000 [14309] TRACE Writing primary cache for repo "fedora-cisco-openh264" to "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-cisco-openh264-98ac828bd7db7eab/solv/fedora-cisco-openh264.solv.kDwBEE" (checksum: 0x9dae47b9c58011388e9f2f03272c45f647ceb7ec419f59c70ff6da833ef20cea)
2025-08-08T00:10:43+0000 [14309] DEBUG No updateinfo metadata available for repo "fedora-cisco-openh264"
2025-08-08T00:10:43+0000 [14309] DEBUG No group metadata available for repo "fedora-cisco-openh264"
2025-08-08T00:10:43+0000 [14309] DEBUG No modules metadata available for repo fedora-cisco-openh264
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] Transfer finished: https://mirrors.fedoraproject.org/metalink?repo=fedora-42&arch=aarch64 (Effective url: https://mirrors.fedoraproject.org/metalink?repo=fedora-42&arch=aarch64&countme=1)
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Parsing metalink.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Mirrors from metalink:
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://download-ib01.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://download-ib01.fedoraproject.org/fedora-enchilada/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://download-ib01.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirrors.rc.rit.edu/fedora/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://mirrors.rc.rit.edu/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://na.edge.kernel.org/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirror.math.princeton.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://mirror.math.princeton.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirror.fcix.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://mirror.fcix.net/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://mirror.fcix.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://mirror.umd.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://mirror.umd.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirror.umd.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://mirror.servaxnet.com/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirror.servaxnet.com/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirror.web-ster.com/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://mirror.web-ster.com/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://mirror.web-ster.com/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirror.lstn.net/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://mirror.lstn.net/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://mirror.lstn.net/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://mirror.us.mirhosting.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirror.us.mirhosting.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://mirror.us.mirhosting.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirror.facebook.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://mirror.facebook.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://mirror.facebook.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://mirror.cs.princeton.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirror.cs.princeton.edu/pub/mirrors/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://download-cc-rdu01.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://download-cc-rdu01.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://download-cc-rdu01.fedoraproject.org/fedora-enchilada/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://mirror.chpc.utah.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirror.chpc.utah.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://mirror.chpc.utah.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://fedora.mirror.constant.com/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://fedora.mirror.constant.com/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirrors.oit.uci.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://mirrors.mit.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://mirrors.mit.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirrors.mit.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirror.cs.siue.edu/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://mirror.cs.siue.edu/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://146.163.150.1/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://pubmirror1.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://pubmirror1.math.uh.edu/fedora-enchilada/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://pubmirror1.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://pubmirror3.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://pubmirror3.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://pubmirror3.math.uh.edu/fedora-enchilada/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://packages.oit.ncsu.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://packages.oit.ncsu.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://pubmirror2.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://pubmirror2.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://pubmirror2.math.uh.edu/fedora-enchilada/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://mirrors.lug.mtu.edu/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirrors.lug.mtu.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://mirrors.lug.mtu.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://mirrors.xmission.com/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://mirrors.xmission.com/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirrors.xmission.com/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirror.uoregon.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://ftp.usf.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://sjc.mirror.rackspace.com/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://sjc.mirror.rackspace.com/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://sjc.mirror.rackspace.com/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://dl.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://dl.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirror.xenyth.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://mirror.xenyth.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://mirror.xenyth.net/fedora-linux/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://muug.ca/mirror/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://muug.ca/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://muug.ca/mirror/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://mirror.cpsc.ucalgary.ca/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://mirror.cpsc.ucalgary.ca/mirror/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirror.cpsc.ucalgary.ca/mirror/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://mirror.csclub.uwaterloo.ca/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirror.csclub.uwaterloo.ca/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://mirror.csclub.uwaterloo.ca/fedora-enchilada/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://fedora.mirror.iweb.com/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://fedora.mirror.iweb.com/fedora-enchilada/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   http://mirror.dst.ca/fedora-linux/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   rsync://mirror.dst.ca/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo]   https://mirror.dst.ca/fedora-linux/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Metalink parsed
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing internal mirrorlist
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing mirrors reported via LRI_MIRRORS
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_handle_perform: Downloading/Locating yum repo
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_download_remote: Downloading/Copying repo..
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_download_repomd: Downloading repomd.xml via mirrorlist
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_get_best_checksum: Expected checksum for repomd.xml: (sha512) a18d69ba8f40455b82a5c3b6759498ddd815a1169b514aa7987c37501b731d60b18cdb823bd3e574441c7b15314bfa94c3348ac5fb4dc4298b3017df04621e47
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_download: Target: repodata/repomd.xml (-)
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Preparing internal mirror list for handle id: 0xaaaae27ad2c0
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://download-ib01.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://download-ib01.fedoraproject.org/fedora-enchilada/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://download-ib01.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirrors.rc.rit.edu/fedora/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirrors.rc.rit.edu/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://na.edge.kernel.org/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.math.princeton.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.math.princeton.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.fcix.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.fcix.net/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.fcix.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.umd.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.umd.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.umd.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.servaxnet.com/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.servaxnet.com/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.web-ster.com/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.web-ster.com/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.web-ster.com/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.lstn.net/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.lstn.net/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.lstn.net/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.us.mirhosting.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.us.mirhosting.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.us.mirhosting.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.facebook.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.facebook.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.facebook.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.cs.princeton.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.cs.princeton.edu/pub/mirrors/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://download-cc-rdu01.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://download-cc-rdu01.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://download-cc-rdu01.fedoraproject.org/fedora-enchilada/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.chpc.utah.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.chpc.utah.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.chpc.utah.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://fedora.mirror.constant.com/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://fedora.mirror.constant.com/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirrors.oit.uci.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirrors.mit.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirrors.mit.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirrors.mit.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.cs.siue.edu/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.cs.siue.edu/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://146.163.150.1/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://pubmirror1.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://pubmirror1.math.uh.edu/fedora-enchilada/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://pubmirror1.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://pubmirror3.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://pubmirror3.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://pubmirror3.math.uh.edu/fedora-enchilada/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://packages.oit.ncsu.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://packages.oit.ncsu.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://pubmirror2.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://pubmirror2.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://pubmirror2.math.uh.edu/fedora-enchilada/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirrors.lug.mtu.edu/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirrors.lug.mtu.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirrors.lug.mtu.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirrors.xmission.com/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirrors.xmission.com/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirrors.xmission.com/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.uoregon.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://ftp.usf.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://sjc.mirror.rackspace.com/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://sjc.mirror.rackspace.com/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://sjc.mirror.rackspace.com/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://dl.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://dl.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.xenyth.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.xenyth.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.xenyth.net/fedora-linux/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://muug.ca/mirror/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://muug.ca/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://muug.ca/mirror/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.cpsc.ucalgary.ca/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.cpsc.ucalgary.ca/mirror/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.cpsc.ucalgary.ca/mirror/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.csclub.uwaterloo.ca/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.csclub.uwaterloo.ca/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.csclub.uwaterloo.ca/fedora-enchilada/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://fedora.mirror.iweb.com/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://fedora.mirror.iweb.com/fedora-enchilada/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.dst.ca/fedora-linux/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.dst.ca/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.dst.ca/fedora-linux/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] Selecting mirror for: repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] INFO [librepo] Downloading: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_download: Downloading started
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] Transfer finished: repodata/repomd.xml (Effective url: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml)
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] check_finished_transfer_checksum: Checksum (sha512) a18d69ba8f40455b82a5c3b6759498ddd815a1169b514aa7987c37501b731d60b18cdb823bd3e574441c7b15314bfa94c3348ac5fb4dc4298b3017df04621e47 is OK
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_yum_download_remote: Parsing repomd.xml
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] 1744196818: Repomd revision: lr_yum_download_remote
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] Found primary_zck so using instead of primary
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_download: Target: repodata/4192d5ed7e2b32030b54fb9bab150353d27d6164d887a3fd83d5007a2efd24ca-primary.xml.zck (-)
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Preparing internal mirror list for handle id: 0xaaaae27ad2c0
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://download-ib01.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://download-ib01.fedoraproject.org/fedora-enchilada/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://download-ib01.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirrors.rc.rit.edu/fedora/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirrors.rc.rit.edu/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://na.edge.kernel.org/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.math.princeton.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.math.princeton.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.fcix.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.fcix.net/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.fcix.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.umd.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.umd.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.umd.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.servaxnet.com/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.servaxnet.com/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.web-ster.com/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.web-ster.com/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.web-ster.com/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.lstn.net/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.lstn.net/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.lstn.net/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.us.mirhosting.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.us.mirhosting.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.us.mirhosting.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.facebook.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.facebook.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.facebook.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.cs.princeton.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.cs.princeton.edu/pub/mirrors/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://download-cc-rdu01.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://download-cc-rdu01.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://download-cc-rdu01.fedoraproject.org/fedora-enchilada/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.chpc.utah.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.chpc.utah.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.chpc.utah.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://fedora.mirror.constant.com/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://fedora.mirror.constant.com/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirrors.oit.uci.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirrors.mit.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirrors.mit.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirrors.mit.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.cs.siue.edu/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.cs.siue.edu/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://146.163.150.1/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://pubmirror1.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://pubmirror1.math.uh.edu/fedora-enchilada/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://pubmirror1.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://pubmirror3.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://pubmirror3.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://pubmirror3.math.uh.edu/fedora-enchilada/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://packages.oit.ncsu.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://packages.oit.ncsu.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://pubmirror2.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://pubmirror2.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://pubmirror2.math.uh.edu/fedora-enchilada/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirrors.lug.mtu.edu/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirrors.lug.mtu.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirrors.lug.mtu.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirrors.xmission.com/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirrors.xmission.com/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirrors.xmission.com/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.uoregon.edu/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://ftp.usf.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://sjc.mirror.rackspace.com/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://sjc.mirror.rackspace.com/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://sjc.mirror.rackspace.com/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://dl.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://dl.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.xenyth.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.xenyth.net/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.xenyth.net/fedora-linux/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://muug.ca/mirror/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://muug.ca/fedora/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://muug.ca/mirror/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.cpsc.ucalgary.ca/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.cpsc.ucalgary.ca/mirror/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.cpsc.ucalgary.ca/mirror/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.csclub.uwaterloo.ca/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.csclub.uwaterloo.ca/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.csclub.uwaterloo.ca/fedora-enchilada/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://fedora.mirror.iweb.com/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://fedora.mirror.iweb.com/fedora-enchilada/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.dst.ca/fedora-linux/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.dst.ca/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.dst.ca/fedora-linux/fedora/linux/releases/42/Everything/aarch64/os/
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_download: Target: repodata/be41ebace03b39f50d1973b8c7d44bd695b8e3ce0f9121624f06fa1da4a4f6fb-comps-Everything.aarch64.xml.zst (-)
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] Selecting mirror for: repodata/4192d5ed7e2b32030b54fb9bab150353d27d6164d887a3fd83d5007a2efd24ca-primary.xml.zck
2025-08-08T00:10:43+0000 [14309] INFO [librepo] Downloading: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/4192d5ed7e2b32030b54fb9bab150353d27d6164d887a3fd83d5007a2efd24ca-primary.xml.zck
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] check_zck: Unable to read zchunk header: repodata/4192d5ed7e2b32030b54fb9bab150353d27d6164d887a3fd83d5007a2efd24ca-primary.xml.zck
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] find_local_zck_header: Cache directory: /home/<USER>/test/thinstation-ng/var/cache/libdnf5

2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] Selecting mirror for: repodata/be41ebace03b39f50d1973b8c7d44bd695b8e3ce0f9121624f06fa1da4a4f6fb-comps-Everything.aarch64.xml.zst
2025-08-08T00:10:43+0000 [14309] INFO [librepo] Downloading: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/be41ebace03b39f50d1973b8c7d44bd695b8e3ce0f9121624f06fa1da4a4f6fb-comps-Everything.aarch64.xml.zst
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_download: Downloading started
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] Transfer finished: repodata/4192d5ed7e2b32030b54fb9bab150353d27d6164d887a3fd83d5007a2efd24ca-primary.xml.zck (Effective url: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/4192d5ed7e2b32030b54fb9bab150353d27d6164d887a3fd83d5007a2efd24ca-primary.xml.zck)
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] Selecting mirror for: repodata/4192d5ed7e2b32030b54fb9bab150353d27d6164d887a3fd83d5007a2efd24ca-primary.xml.zck
2025-08-08T00:10:43+0000 [14309] INFO [librepo] Downloading: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/4192d5ed7e2b32030b54fb9bab150353d27d6164d887a3fd83d5007a2efd24ca-primary.xml.zck
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] Checking checksum: 4: 0f41a285d983dc7926f5bd82415e60fb873fd5cf7d9a8c9558ff0447fad788fe
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] lr_zck_init_read: Found matching header in repodata/4192d5ed7e2b32030b54fb9bab150353d27d6164d887a3fd83d5007a2efd24ca-primary.xml.zck
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] check_zck: Checking zchunk data checksum: repodata/4192d5ed7e2b32030b54fb9bab150353d27d6164d887a3fd83d5007a2efd24ca-primary.xml.zck
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] check_zck: Downloading rest of zchunk body: repodata/4192d5ed7e2b32030b54fb9bab150353d27d6164d887a3fd83d5007a2efd24ca-primary.xml.zck
2025-08-08T00:10:43+0000 [14309] DEBUG [librepo] find_local_zck_chunks: Cache directory: /home/<USER>/test/thinstation-ng/var/cache/libdnf5

2025-08-08T00:10:44+0000 [14309] DEBUG [librepo] prep_zck_body: Chunks that still need to be downloaded: 28284
2025-08-08T00:10:44+0000 [14309] DEBUG [librepo] Transfer finished: repodata/be41ebace03b39f50d1973b8c7d44bd695b8e3ce0f9121624f06fa1da4a4f6fb-comps-Everything.aarch64.xml.zst (Effective url: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/be41ebace03b39f50d1973b8c7d44bd695b8e3ce0f9121624f06fa1da4a4f6fb-comps-Everything.aarch64.xml.zst)
2025-08-08T00:10:44+0000 [14309] DEBUG [librepo] check_finished_transfer_checksum: Checksum (sha256) be41ebace03b39f50d1973b8c7d44bd695b8e3ce0f9121624f06fa1da4a4f6fb is OK
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] Transfer finished: repodata/4192d5ed7e2b32030b54fb9bab150353d27d6164d887a3fd83d5007a2efd24ca-primary.xml.zck (Effective url: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/4192d5ed7e2b32030b54fb9bab150353d27d6164d887a3fd83d5007a2efd24ca-primary.xml.zck)
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] Checking checksum: 4: 0f41a285d983dc7926f5bd82415e60fb873fd5cf7d9a8c9558ff0447fad788fe
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_zck_init_read: Found matching header in repodata/4192d5ed7e2b32030b54fb9bab150353d27d6164d887a3fd83d5007a2efd24ca-primary.xml.zck
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_perform: Restoring an old SIGINT handler
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_perform: Using dir: /home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-c27b7abdec3274a0
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_perform: Using own SIGINT handler
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Preparing internal mirrorlist
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Local metalink.xml found at /home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-c27b7abdec3274a0/metalink.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Parsing metalink.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Mirrors from metalink:
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://download-ib01.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://download-ib01.fedoraproject.org/fedora-enchilada/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://download-ib01.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirrors.rc.rit.edu/fedora/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirrors.rc.rit.edu/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://na.edge.kernel.org/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.math.princeton.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.math.princeton.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.fcix.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.fcix.net/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.fcix.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.umd.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.umd.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.umd.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.servaxnet.com/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.servaxnet.com/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.web-ster.com/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.web-ster.com/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.web-ster.com/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.lstn.net/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.lstn.net/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.lstn.net/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.us.mirhosting.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.us.mirhosting.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.us.mirhosting.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.facebook.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.facebook.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.facebook.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.cs.princeton.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.cs.princeton.edu/pub/mirrors/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://download-cc-rdu01.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://download-cc-rdu01.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://download-cc-rdu01.fedoraproject.org/fedora-enchilada/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.chpc.utah.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.chpc.utah.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.chpc.utah.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://fedora.mirror.constant.com/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://fedora.mirror.constant.com/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirrors.oit.uci.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirrors.mit.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirrors.mit.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirrors.mit.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.cs.siue.edu/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.cs.siue.edu/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://146.163.150.1/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://pubmirror1.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://pubmirror1.math.uh.edu/fedora-enchilada/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://pubmirror1.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://pubmirror3.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://pubmirror3.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://pubmirror3.math.uh.edu/fedora-enchilada/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://packages.oit.ncsu.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://packages.oit.ncsu.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://pubmirror2.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://pubmirror2.math.uh.edu/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://pubmirror2.math.uh.edu/fedora-enchilada/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirrors.lug.mtu.edu/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirrors.lug.mtu.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirrors.lug.mtu.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirrors.xmission.com/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirrors.xmission.com/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirrors.xmission.com/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.uoregon.edu/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://ftp.usf.edu/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://sjc.mirror.rackspace.com/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://sjc.mirror.rackspace.com/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://sjc.mirror.rackspace.com/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://dl.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://dl.fedoraproject.org/pub/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.xenyth.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.xenyth.net/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.xenyth.net/fedora-linux/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://muug.ca/mirror/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://muug.ca/fedora/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://muug.ca/mirror/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.cpsc.ucalgary.ca/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.cpsc.ucalgary.ca/mirror/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.cpsc.ucalgary.ca/mirror/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.csclub.uwaterloo.ca/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.csclub.uwaterloo.ca/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.csclub.uwaterloo.ca/fedora-enchilada/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://fedora.mirror.iweb.com/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://fedora.mirror.iweb.com/fedora-enchilada/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.dst.ca/fedora-linux/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.dst.ca/fedora-buffet/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.dst.ca/fedora-linux/fedora/linux/releases/42/Everything/aarch64/os/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Metalink parsed
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing internal mirrorlist
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing mirrors reported via LRI_MIRRORS
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_perform: Downloading/Locating yum repo
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_yum_use_local: Locating repo..
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_yum_use_local_load_base: Found local metalink: /home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-c27b7abdec3274a0/metalink.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_yum_use_local_load_base: Parsing repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_yum_use_local_load_base: Repomd revision: 1744196818
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] Found primary_zck so using instead of primary
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_yum_use_local: Repository was successfully located
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_yum_check_checksum_of_md_record: Checking checksum of /home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-c27b7abdec3274a0/repodata/4192d5ed7e2b32030b54fb9bab150353d27d6164d887a3fd83d5007a2efd24ca-primary.xml.zck (expected: 0f41a285d983dc7926f5bd82415e60fb873fd5cf7d9a8c9558ff0447fad788fe [sha256])
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_yum_check_checksum_of_md_record: Checksum check - Passed
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_yum_check_checksum_of_md_record: Checking checksum of /home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-c27b7abdec3274a0/repodata/be41ebace03b39f50d1973b8c7d44bd695b8e3ce0f9121624f06fa1da4a4f6fb-comps-Everything.aarch64.xml.zst (expected: be41ebace03b39f50d1973b8c7d44bd695b8e3ce0f9121624f06fa1da4a4f6fb [sha256])
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_checksum_fd_compare: Using mtime cached in xattr: [user.librepo.checksum.mtime] 1754611844342153656
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_checksum_fd_compare: Using checksum cached in xattr: [user.librepo.checksum.sha256] be41ebace03b39f50d1973b8c7d44bd695b8e3ce0f9121624f06fa1da4a4f6fb
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_yum_check_checksum_of_md_record: Checksum check - Passed
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_perform: Restoring an old SIGINT handler
2025-08-08T00:10:45+0000 [14309] DEBUG Downloading metadata for repo "updates"
2025-08-08T00:10:45+0000 [14309] TRACE countme: no event for repo "updates": budget to spend: 2
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_perform: Using dir: /home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4/tmpdir.fFALv8
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_perform: Using own SIGINT handler
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Preparing internal mirrorlist
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_download: Target: https://mirrors.fedoraproject.org/metalink?repo=updates-released-f42&arch=aarch64 (-)
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] Selecting mirror for: https://mirrors.fedoraproject.org/metalink?repo=updates-released-f42&arch=aarch64
2025-08-08T00:10:45+0000 [14309] INFO [librepo] Downloading: https://mirrors.fedoraproject.org/metalink?repo=updates-released-f42&arch=aarch64
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_download: Downloading started
2025-08-08T00:10:45+0000 [14309] TRACE Cache file "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-c27b7abdec3274a0/solv/fedora.solv" not found
2025-08-08T00:10:45+0000 [14309] DEBUG Loading repomd and primary for repo "fedora"
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] Transfer finished: https://mirrors.fedoraproject.org/metalink?repo=updates-released-f42&arch=aarch64 (Effective url: https://mirrors.fedoraproject.org/metalink?repo=updates-released-f42&arch=aarch64)
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Parsing metalink.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Mirrors from metalink:
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.us.mirhosting.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.us.mirhosting.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.us.mirhosting.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.umd.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.umd.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.umd.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.web-ster.com/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.web-ster.com/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.web-ster.com/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://fedora.mirror.constant.com/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://fedora.mirror.constant.com/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.cs.princeton.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.cs.princeton.edu/pub/mirrors/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.cs.siue.edu/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.cs.siue.edu/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://146.163.150.1/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://pubmirror1.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://pubmirror1.math.uh.edu/fedora-enchilada/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://pubmirror1.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirrors.rc.rit.edu/fedora/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirrors.rc.rit.edu/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.uoregon.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://pubmirror2.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://pubmirror2.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://pubmirror2.math.uh.edu/fedora-enchilada/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.servaxnet.com/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.servaxnet.com/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.fcix.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.fcix.net/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.fcix.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.facebook.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.facebook.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.facebook.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.math.princeton.edu/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.math.princeton.edu/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://na.edge.kernel.org/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirrors.xmission.com/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirrors.xmission.com/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirrors.xmission.com/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.chpc.utah.edu/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.chpc.utah.edu/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.chpc.utah.edu/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://download-ib01.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://download-ib01.fedoraproject.org/fedora-enchilada/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://download-ib01.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://pubmirror3.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://pubmirror3.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://pubmirror3.math.uh.edu/fedora-enchilada/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://download-cc-rdu01.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://download-cc-rdu01.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://download-cc-rdu01.fedoraproject.org/fedora-enchilada/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://ftp.usf.edu/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://packages.oit.ncsu.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://packages.oit.ncsu.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirrors.mit.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirrors.mit.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirrors.mit.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://sjc.mirror.rackspace.com/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://sjc.mirror.rackspace.com/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://sjc.mirror.rackspace.com/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://dl.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://dl.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://muug.ca/mirror/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://muug.ca/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://muug.ca/mirror/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.dst.ca/fedora-linux/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.dst.ca/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.dst.ca/fedora-linux/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.cpsc.ucalgary.ca/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.cpsc.ucalgary.ca/mirror/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.cpsc.ucalgary.ca/mirror/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.xenyth.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.xenyth.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.xenyth.net/fedora-linux/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   https://mirror.csclub.uwaterloo.ca/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://mirror.csclub.uwaterloo.ca/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://mirror.csclub.uwaterloo.ca/fedora-enchilada/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   http://fedora.mirror.iweb.com/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo]   rsync://fedora.mirror.iweb.com/fedora-enchilada/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Metalink parsed
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing internal mirrorlist
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing mirrors reported via LRI_MIRRORS
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_handle_perform: Downloading/Locating yum repo
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_yum_download_remote: Downloading/Copying repo..
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_yum_download_repomd: Downloading repomd.xml via mirrorlist
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_get_best_checksum: Expected checksum for repomd.xml: (sha512) 5b937499095dd334908e3792e39d8b58f30ce1f49d72a96bd53189f01d0fb6f3d6648cb1c6738bb468fc9ede0027c8fd94ecd6e9a8471c2f0851b5be58e55616
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_get_best_checksum: Expected alternate checksum for repomd.xml: (sha512) f781cecdc297d87418bbdc8e58cbdf7ec5b6080d500aedcf520b94ca185c6102cdcc3c65be05f928b60c0b5024925a05d7388dd0263b54100c7e4bd32cf285c5
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_get_best_checksum: Expected alternate checksum for repomd.xml: (sha512) f67d605cf11abe4528d3e15759d526c91d8e313cd38e6e986c88b474a614e4bcf67a35ac7fdca954c48e1a48cb69fa847653992dfa1d015a9c2fce8e80ea880f
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_get_best_checksum: Expected alternate checksum for repomd.xml: (sha512) 9d9dab349f9438dd99cf1c93121adff8596461e7cdf9cfb7b16d335bb269911d63e6e7522ab7b605c797665e4ed7f4b166c13b05bae7222efa0e2a9810c21829
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_download: Target: repodata/repomd.xml (-)
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Preparing internal mirror list for handle id: 0xaaaae278cb50
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.us.mirhosting.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.us.mirhosting.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.us.mirhosting.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.umd.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.umd.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.umd.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.web-ster.com/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.web-ster.com/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.web-ster.com/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://fedora.mirror.constant.com/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://fedora.mirror.constant.com/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.cs.princeton.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.cs.princeton.edu/pub/mirrors/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.cs.siue.edu/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.cs.siue.edu/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://146.163.150.1/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://pubmirror1.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://pubmirror1.math.uh.edu/fedora-enchilada/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://pubmirror1.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirrors.rc.rit.edu/fedora/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirrors.rc.rit.edu/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.uoregon.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://pubmirror2.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://pubmirror2.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://pubmirror2.math.uh.edu/fedora-enchilada/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.servaxnet.com/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.servaxnet.com/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.fcix.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.fcix.net/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.fcix.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.facebook.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.facebook.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.facebook.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.math.princeton.edu/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.math.princeton.edu/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://na.edge.kernel.org/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirrors.xmission.com/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirrors.xmission.com/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirrors.xmission.com/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.chpc.utah.edu/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.chpc.utah.edu/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.chpc.utah.edu/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://download-ib01.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://download-ib01.fedoraproject.org/fedora-enchilada/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://download-ib01.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://pubmirror3.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://pubmirror3.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://pubmirror3.math.uh.edu/fedora-enchilada/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://download-cc-rdu01.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://download-cc-rdu01.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://download-cc-rdu01.fedoraproject.org/fedora-enchilada/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://ftp.usf.edu/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://packages.oit.ncsu.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://packages.oit.ncsu.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirrors.mit.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirrors.mit.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirrors.mit.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://sjc.mirror.rackspace.com/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://sjc.mirror.rackspace.com/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://sjc.mirror.rackspace.com/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://dl.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://dl.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://muug.ca/mirror/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://muug.ca/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://muug.ca/mirror/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.dst.ca/fedora-linux/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.dst.ca/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.dst.ca/fedora-linux/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.cpsc.ucalgary.ca/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.cpsc.ucalgary.ca/mirror/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.cpsc.ucalgary.ca/mirror/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.xenyth.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.xenyth.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.xenyth.net/fedora-linux/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.csclub.uwaterloo.ca/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.csclub.uwaterloo.ca/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.csclub.uwaterloo.ca/fedora-enchilada/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://fedora.mirror.iweb.com/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://fedora.mirror.iweb.com/fedora-enchilada/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] Selecting mirror for: repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] INFO [librepo] Downloading: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_download: Downloading started
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] Transfer finished: repodata/repomd.xml (Effective url: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml)
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] check_finished_transfer_checksum: Checksum (sha512) 5b937499095dd334908e3792e39d8b58f30ce1f49d72a96bd53189f01d0fb6f3d6648cb1c6738bb468fc9ede0027c8fd94ecd6e9a8471c2f0851b5be58e55616 is OK
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_yum_download_remote: Parsing repomd.xml
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] 1754526111: Repomd revision: lr_yum_download_remote
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] Found primary_zck so using instead of primary
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] Found updateinfo_zck so using instead of updateinfo
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_download: Target: repodata/9b80e66e5ce9dd76d120c460163d224111fb1b785c40e4486dbc5b0af8249192-primary.xml.zck (-)
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Preparing internal mirror list for handle id: 0xaaaae278cb50
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.us.mirhosting.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.us.mirhosting.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.us.mirhosting.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.umd.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.umd.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.umd.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.web-ster.com/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.web-ster.com/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.web-ster.com/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://fedora.mirror.constant.com/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://fedora.mirror.constant.com/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.cs.princeton.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.cs.princeton.edu/pub/mirrors/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.cs.siue.edu/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.cs.siue.edu/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://146.163.150.1/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://pubmirror1.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://pubmirror1.math.uh.edu/fedora-enchilada/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://pubmirror1.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirrors.rc.rit.edu/fedora/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirrors.rc.rit.edu/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.uoregon.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://pubmirror2.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://pubmirror2.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://pubmirror2.math.uh.edu/fedora-enchilada/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.servaxnet.com/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.servaxnet.com/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.fcix.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.fcix.net/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.fcix.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.facebook.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.facebook.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.facebook.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.math.princeton.edu/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.math.princeton.edu/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://na.edge.kernel.org/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirrors.xmission.com/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirrors.xmission.com/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirrors.xmission.com/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.chpc.utah.edu/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.chpc.utah.edu/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.chpc.utah.edu/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://download-ib01.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://download-ib01.fedoraproject.org/fedora-enchilada/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://download-ib01.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://pubmirror3.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://pubmirror3.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://pubmirror3.math.uh.edu/fedora-enchilada/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://download-cc-rdu01.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://download-cc-rdu01.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://download-cc-rdu01.fedoraproject.org/fedora-enchilada/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://ftp.usf.edu/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://packages.oit.ncsu.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://packages.oit.ncsu.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirrors.mit.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirrors.mit.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirrors.mit.edu/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://sjc.mirror.rackspace.com/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://sjc.mirror.rackspace.com/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://sjc.mirror.rackspace.com/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://dl.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://dl.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://muug.ca/mirror/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://muug.ca/fedora/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://muug.ca/mirror/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.dst.ca/fedora-linux/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.dst.ca/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.dst.ca/fedora-linux/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.cpsc.ucalgary.ca/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.cpsc.ucalgary.ca/mirror/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.cpsc.ucalgary.ca/mirror/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.xenyth.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.xenyth.net/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.xenyth.net/fedora-linux/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: https://mirror.csclub.uwaterloo.ca/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://mirror.csclub.uwaterloo.ca/fedora/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://mirror.csclub.uwaterloo.ca/fedora-enchilada/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: http://fedora.mirror.iweb.com/linux/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_prepare_lrmirrors: Mirror: rsync://fedora.mirror.iweb.com/fedora-enchilada/updates/42/Everything/aarch64/
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_download: Target: repodata/8d67b44dd98f9cbdd5861bd10043e5b0853be29501d276c3c737f5b9b61f546a-comps-Everything.aarch64.xml.zst (-)
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_download: Target: repodata/07e31b86d7da7f17c451a20836e0468d350d3da665ea6bac854fa7ac1135ef8e-updateinfo.xml.zck (-)
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] Selecting mirror for: repodata/9b80e66e5ce9dd76d120c460163d224111fb1b785c40e4486dbc5b0af8249192-primary.xml.zck
2025-08-08T00:10:45+0000 [14309] INFO [librepo] Downloading: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/updates/42/Everything/aarch64/repodata/9b80e66e5ce9dd76d120c460163d224111fb1b785c40e4486dbc5b0af8249192-primary.xml.zck
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] check_zck: Unable to read zchunk header: repodata/9b80e66e5ce9dd76d120c460163d224111fb1b785c40e4486dbc5b0af8249192-primary.xml.zck
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] find_local_zck_header: Cache directory: /home/<USER>/test/thinstation-ng/var/cache/libdnf5

2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] Selecting mirror for: repodata/8d67b44dd98f9cbdd5861bd10043e5b0853be29501d276c3c737f5b9b61f546a-comps-Everything.aarch64.xml.zst
2025-08-08T00:10:45+0000 [14309] INFO [librepo] Downloading: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/updates/42/Everything/aarch64/repodata/8d67b44dd98f9cbdd5861bd10043e5b0853be29501d276c3c737f5b9b61f546a-comps-Everything.aarch64.xml.zst
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] Selecting mirror for: repodata/07e31b86d7da7f17c451a20836e0468d350d3da665ea6bac854fa7ac1135ef8e-updateinfo.xml.zck
2025-08-08T00:10:45+0000 [14309] INFO [librepo] Downloading: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/updates/42/Everything/aarch64/repodata/07e31b86d7da7f17c451a20836e0468d350d3da665ea6bac854fa7ac1135ef8e-updateinfo.xml.zck
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] check_zck: Unable to read zchunk header: repodata/07e31b86d7da7f17c451a20836e0468d350d3da665ea6bac854fa7ac1135ef8e-updateinfo.xml.zck
2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] find_local_zck_header: Cache directory: /home/<USER>/test/thinstation-ng/var/cache/libdnf5

2025-08-08T00:10:45+0000 [14309] DEBUG [librepo] lr_download: Downloading started
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] Transfer finished: repodata/07e31b86d7da7f17c451a20836e0468d350d3da665ea6bac854fa7ac1135ef8e-updateinfo.xml.zck (Effective url: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/updates/42/Everything/aarch64/repodata/07e31b86d7da7f17c451a20836e0468d350d3da665ea6bac854fa7ac1135ef8e-updateinfo.xml.zck)
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] Selecting mirror for: repodata/07e31b86d7da7f17c451a20836e0468d350d3da665ea6bac854fa7ac1135ef8e-updateinfo.xml.zck
2025-08-08T00:10:46+0000 [14309] INFO [librepo] Downloading: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/updates/42/Everything/aarch64/repodata/07e31b86d7da7f17c451a20836e0468d350d3da665ea6bac854fa7ac1135ef8e-updateinfo.xml.zck
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] Checking checksum: 4: 29e27ea455d6e3819e6063aaf48b6d897e1326dc23eddaad3a6c2fc72b265883
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_zck_init_read: Found matching header in repodata/07e31b86d7da7f17c451a20836e0468d350d3da665ea6bac854fa7ac1135ef8e-updateinfo.xml.zck
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] check_zck: Checking zchunk data checksum: repodata/07e31b86d7da7f17c451a20836e0468d350d3da665ea6bac854fa7ac1135ef8e-updateinfo.xml.zck
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] check_zck: Downloading rest of zchunk body: repodata/07e31b86d7da7f17c451a20836e0468d350d3da665ea6bac854fa7ac1135ef8e-updateinfo.xml.zck
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] find_local_zck_chunks: Cache directory: /home/<USER>/test/thinstation-ng/var/cache/libdnf5

2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] prep_zck_body: Chunks that still need to be downloaded: 488
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] Transfer finished: repodata/9b80e66e5ce9dd76d120c460163d224111fb1b785c40e4486dbc5b0af8249192-primary.xml.zck (Effective url: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/updates/42/Everything/aarch64/repodata/9b80e66e5ce9dd76d120c460163d224111fb1b785c40e4486dbc5b0af8249192-primary.xml.zck)
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] Selecting mirror for: repodata/9b80e66e5ce9dd76d120c460163d224111fb1b785c40e4486dbc5b0af8249192-primary.xml.zck
2025-08-08T00:10:46+0000 [14309] INFO [librepo] Downloading: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/updates/42/Everything/aarch64/repodata/9b80e66e5ce9dd76d120c460163d224111fb1b785c40e4486dbc5b0af8249192-primary.xml.zck
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] Checking checksum: 4: e57d861ff13d57187f098412b4d6ac9cd450e0741b4d62387ae16c9c5e9b46f0
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_zck_init_read: Found matching header in repodata/9b80e66e5ce9dd76d120c460163d224111fb1b785c40e4486dbc5b0af8249192-primary.xml.zck
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] check_zck: Checking zchunk data checksum: repodata/9b80e66e5ce9dd76d120c460163d224111fb1b785c40e4486dbc5b0af8249192-primary.xml.zck
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] check_zck: Downloading rest of zchunk body: repodata/9b80e66e5ce9dd76d120c460163d224111fb1b785c40e4486dbc5b0af8249192-primary.xml.zck
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] find_local_zck_chunks: Cache directory: /home/<USER>/test/thinstation-ng/var/cache/libdnf5

2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] prep_zck_body: Chunks that still need to be downloaded: 4876
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] Transfer finished: repodata/8d67b44dd98f9cbdd5861bd10043e5b0853be29501d276c3c737f5b9b61f546a-comps-Everything.aarch64.xml.zst (Effective url: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/updates/42/Everything/aarch64/repodata/8d67b44dd98f9cbdd5861bd10043e5b0853be29501d276c3c737f5b9b61f546a-comps-Everything.aarch64.xml.zst)
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] check_finished_transfer_checksum: Checksum (sha256) 8d67b44dd98f9cbdd5861bd10043e5b0853be29501d276c3c737f5b9b61f546a is OK
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] Transfer finished: repodata/07e31b86d7da7f17c451a20836e0468d350d3da665ea6bac854fa7ac1135ef8e-updateinfo.xml.zck (Effective url: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/updates/42/Everything/aarch64/repodata/07e31b86d7da7f17c451a20836e0468d350d3da665ea6bac854fa7ac1135ef8e-updateinfo.xml.zck)
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] Checking checksum: 4: 29e27ea455d6e3819e6063aaf48b6d897e1326dc23eddaad3a6c2fc72b265883
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_zck_init_read: Found matching header in repodata/07e31b86d7da7f17c451a20836e0468d350d3da665ea6bac854fa7ac1135ef8e-updateinfo.xml.zck
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] Transfer finished: repodata/9b80e66e5ce9dd76d120c460163d224111fb1b785c40e4486dbc5b0af8249192-primary.xml.zck (Effective url: https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/updates/42/Everything/aarch64/repodata/9b80e66e5ce9dd76d120c460163d224111fb1b785c40e4486dbc5b0af8249192-primary.xml.zck)
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] Checking checksum: 4: e57d861ff13d57187f098412b4d6ac9cd450e0741b4d62387ae16c9c5e9b46f0
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_zck_init_read: Found matching header in repodata/9b80e66e5ce9dd76d120c460163d224111fb1b785c40e4486dbc5b0af8249192-primary.xml.zck
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_handle_perform: Restoring an old SIGINT handler
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_handle_perform: Using dir: /home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_handle_perform: Using own SIGINT handler
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Preparing internal mirrorlist
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Local metalink.xml found at /home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4/metalink.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Parsing metalink.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Mirrors from metalink:
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://d2lzkl7pfhq30w.cloudfront.net/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://mirror.us.mirhosting.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirror.us.mirhosting.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://mirror.us.mirhosting.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://mirror.umd.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://mirror.umd.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirror.umd.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirror.web-ster.com/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://mirror.web-ster.com/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://mirror.web-ster.com/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://fedora.mirror.constant.com/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://fedora.mirror.constant.com/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://mirror.cs.princeton.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirror.cs.princeton.edu/pub/mirrors/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirror.cs.siue.edu/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://mirror.cs.siue.edu/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://146.163.150.1/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://pubmirror1.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://pubmirror1.math.uh.edu/fedora-enchilada/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://pubmirror1.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirrors.rc.rit.edu/fedora/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://mirrors.rc.rit.edu/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirror.uoregon.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://pubmirror2.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://pubmirror2.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://pubmirror2.math.uh.edu/fedora-enchilada/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://mirror.servaxnet.com/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirror.servaxnet.com/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirror.fcix.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://mirror.fcix.net/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://mirror.fcix.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirror.facebook.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://mirror.facebook.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://mirror.facebook.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirror.math.princeton.edu/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://mirror.math.princeton.edu/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://na.edge.kernel.org/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://mirrors.xmission.com/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://mirrors.xmission.com/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirrors.xmission.com/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://mirror.chpc.utah.edu/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirror.chpc.utah.edu/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://mirror.chpc.utah.edu/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://download-ib01.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://download-ib01.fedoraproject.org/fedora-enchilada/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://download-ib01.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://pubmirror3.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://pubmirror3.math.uh.edu/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://pubmirror3.math.uh.edu/fedora-enchilada/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://download-cc-rdu01.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://download-cc-rdu01.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://download-cc-rdu01.fedoraproject.org/fedora-enchilada/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://ftp.usf.edu/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://packages.oit.ncsu.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://packages.oit.ncsu.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://mirrors.mit.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://mirrors.mit.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirrors.mit.edu/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://sjc.mirror.rackspace.com/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://sjc.mirror.rackspace.com/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://sjc.mirror.rackspace.com/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://dl.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://dl.fedoraproject.org/pub/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://muug.ca/mirror/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://muug.ca/fedora/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://muug.ca/mirror/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirror.dst.ca/fedora-linux/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://mirror.dst.ca/fedora-buffet/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://mirror.dst.ca/fedora-linux/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://mirror.cpsc.ucalgary.ca/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://mirror.cpsc.ucalgary.ca/mirror/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirror.cpsc.ucalgary.ca/mirror/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirror.xenyth.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://mirror.xenyth.net/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://mirror.xenyth.net/fedora-linux/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   https://mirror.csclub.uwaterloo.ca/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://mirror.csclub.uwaterloo.ca/fedora/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://mirror.csclub.uwaterloo.ca/fedora-enchilada/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   http://fedora.mirror.iweb.com/linux/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo]   rsync://fedora.mirror.iweb.com/fedora-enchilada/updates/42/Everything/aarch64/repodata/repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_handle_prepare_metalink: Metalink parsed
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing internal mirrorlist
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_handle_prepare_internal_mirrorlist: Finalizing mirrors reported via LRI_MIRRORS
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_handle_perform: Downloading/Locating yum repo
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_yum_use_local: Locating repo..
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_yum_use_local_load_base: Found local metalink: /home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4/metalink.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_yum_use_local_load_base: Parsing repomd.xml
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_yum_use_local_load_base: Repomd revision: 1754526111
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] Found primary_zck so using instead of primary
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] Found updateinfo_zck so using instead of updateinfo
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_yum_use_local: Repository was successfully located
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_yum_check_checksum_of_md_record: Checking checksum of /home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4/repodata/9b80e66e5ce9dd76d120c460163d224111fb1b785c40e4486dbc5b0af8249192-primary.xml.zck (expected: e57d861ff13d57187f098412b4d6ac9cd450e0741b4d62387ae16c9c5e9b46f0 [sha256])
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_yum_check_checksum_of_md_record: Checksum check - Passed
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_yum_check_checksum_of_md_record: Checking checksum of /home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4/repodata/8d67b44dd98f9cbdd5861bd10043e5b0853be29501d276c3c737f5b9b61f546a-comps-Everything.aarch64.xml.zst (expected: 8d67b44dd98f9cbdd5861bd10043e5b0853be29501d276c3c737f5b9b61f546a [sha256])
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_checksum_fd_compare: Using mtime cached in xattr: [user.librepo.checksum.mtime] 1754611846389169936
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_checksum_fd_compare: Using checksum cached in xattr: [user.librepo.checksum.sha256] 8d67b44dd98f9cbdd5861bd10043e5b0853be29501d276c3c737f5b9b61f546a
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_yum_check_checksum_of_md_record: Checksum check - Passed
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_yum_check_checksum_of_md_record: Checking checksum of /home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4/repodata/07e31b86d7da7f17c451a20836e0468d350d3da665ea6bac854fa7ac1135ef8e-updateinfo.xml.zck (expected: 29e27ea455d6e3819e6063aaf48b6d897e1326dc23eddaad3a6c2fc72b265883 [sha256])
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_yum_check_checksum_of_md_record: Checksum check - Passed
2025-08-08T00:10:46+0000 [14309] DEBUG [librepo] lr_handle_perform: Restoring an old SIGINT handler
2025-08-08T00:10:51+0000 [14309] TRACE Writing primary cache for repo "fedora" to "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-c27b7abdec3274a0/solv/fedora.solv.gRfKtQ" (checksum: 0xf5e0353555e8989707479fd692edc2ce683ff9b6c848167a156648215b096422)
2025-08-08T00:10:52+0000 [14309] DEBUG No updateinfo metadata available for repo "fedora"
2025-08-08T00:10:52+0000 [14309] TRACE Cache file "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-c27b7abdec3274a0/solv/fedora-group.solvx" not found
2025-08-08T00:10:52+0000 [14309] DEBUG Loading group extension for repo "fedora" from "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-c27b7abdec3274a0/repodata/be41ebace03b39f50d1973b8c7d44bd695b8e3ce0f9121624f06fa1da4a4f6fb-comps-Everything.aarch64.xml.zst"
2025-08-08T00:10:52+0000 [14309] TRACE Writing group extension cache for repo "fedora" to "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-c27b7abdec3274a0/solv/fedora-group.solvx.dDvaQc"
2025-08-08T00:10:52+0000 [14309] DEBUG No modules metadata available for repo fedora
2025-08-08T00:10:52+0000 [14309] TRACE Cache file "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4/solv/updates.solv" not found
2025-08-08T00:10:52+0000 [14309] DEBUG Loading repomd and primary for repo "updates"
2025-08-08T00:10:53+0000 [14309] TRACE Writing primary cache for repo "updates" to "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4/solv/updates.solv.RpNmZ2" (checksum: 0xe81cb3c17289c2796378b8b38bac0a54a8dfc7503737886cc93ae2a7b811ddae)
2025-08-08T00:10:54+0000 [14309] TRACE Cache file "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4/solv/updates-updateinfo.solvx" not found
2025-08-08T00:10:54+0000 [14309] DEBUG Loading updateinfo extension for repo "updates" from "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4/repodata/07e31b86d7da7f17c451a20836e0468d350d3da665ea6bac854fa7ac1135ef8e-updateinfo.xml.zck"
2025-08-08T00:10:55+0000 [14309] TRACE Writing updateinfo extension cache for repo "updates" to "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4/solv/updates-updateinfo.solvx.4dolQy"
2025-08-08T00:10:55+0000 [14309] TRACE Cache file "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4/solv/updates-group.solvx" not found
2025-08-08T00:10:55+0000 [14309] DEBUG Loading group extension for repo "updates" from "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4/repodata/8d67b44dd98f9cbdd5861bd10043e5b0853be29501d276c3c737f5b9b61f546a-comps-Everything.aarch64.xml.zst"
2025-08-08T00:10:55+0000 [14309] TRACE Writing group extension cache for repo "updates" to "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4/solv/updates-group.solvx.GreGco"
2025-08-08T00:10:55+0000 [14309] DEBUG No modules metadata available for repo updates
2025-08-08T00:10:55+0000 [14309] DEBUG Rewriting repo "@System" with added file provides
2025-08-08T00:10:55+0000 [14309] DEBUG Rewriting repo "fedora-cisco-openh264" with added file provides
2025-08-08T00:10:55+0000 [14309] TRACE Writing primary cache for repo "fedora-cisco-openh264" to "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-cisco-openh264-98ac828bd7db7eab/solv/fedora-cisco-openh264.solv.f746Ic" (checksum: 0x9dae47b9c58011388e9f2f03272c45f647ceb7ec419f59c70ff6da833ef20cea)
2025-08-08T00:10:55+0000 [14309] DEBUG Rewriting repo "fedora" with added file provides
2025-08-08T00:10:55+0000 [14309] TRACE Writing primary cache for repo "fedora" to "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/fedora-c27b7abdec3274a0/solv/fedora.solv.bJpEc3" (checksum: 0xf5e0353555e8989707479fd692edc2ce683ff9b6c848167a156648215b096422)
2025-08-08T00:10:57+0000 [14309] DEBUG Rewriting repo "updates" with added file provides
2025-08-08T00:10:57+0000 [14309] TRACE Writing primary cache for repo "updates" to "/home/<USER>/test/thinstation-ng/var/cache/libdnf5/updates-13de681275b4c4e4/solv/updates.solv.hy2oVn" (checksum: 0xe81cb3c17289c2796378b8b38bac0a54a8dfc7503737886cc93ae2a7b811ddae)
2025-08-08T00:10:57+0000 [14309] DEBUG Rewriting repo "@System" with added file provides
2025-08-08T00:10:58+0000 [14309] ERROR No match for argument: grub2-efi-x64
2025-08-08T00:10:58+0000 [14309] ERROR No match for argument: grub2-tools-efi
2025-08-08T00:10:58+0000 [14309] ERROR No match for argument: shim-x64
2025-08-08T00:10:58+0000 [14309] ERROR No match for argument: microcode_ctl
2025-08-08T00:10:58+0000 [14309] DEBUG Failed to find rpm package of a running kernel in sack
