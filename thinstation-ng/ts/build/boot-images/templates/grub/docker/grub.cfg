set default="0"
set timeout=0
trim=1

insmod progress
#loadfont unicode
#set gfxmode=3840x2160,2880x1800,2560x1600,2560x1440,1920x1200,1920x1080,1680x1050,1600x900,1440x900,1400x1050,1366x768,1280x1024,1280x960,1280x800,1280x768,1280x720,1152x864,1024x768,1024x600,auto
#terminal_output gfxterm

# Try to read the machine-id file
set machine_id_file="/boot/machine-id"
if [ -f $machine_id_file ]; then
	source $machine_id_file
else
	# Fallback to a default machine-id
	set machine_id="10000000000000000000000000000001"
fi

menuentry 'Docker' --class docker --class gnu-linux --class gnu --class os --unrestricted {
	set enable_progress_indicator=1
	linux /boot/vmlinuz $KERNEL_PARAMETERS boot_device=$root machine_id=$machine_id
	initrd /boot/initrd
}
