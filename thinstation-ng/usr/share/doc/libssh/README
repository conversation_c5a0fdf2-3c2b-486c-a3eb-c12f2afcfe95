  _   _   _                          _
 (_) (_) (_)                        (_)
 (_)  _  (_) _         _  _   _  _  (_) _
 (_) (_) (_)(_) _     (_)(_) (_)(_) (_)(_) _
 (_) (_) (_)   (_)  _ (_)  _ (_)    (_)   (_)
 (_) (_) (_)(_)(_) (_)(_) (_)(_)    (_)   (_).org

 The SSH library
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

1* Why ?
-_-_-_-_-_

Why not ? :) I've began to work on my own implementation of the ssh protocol
because i didn't like the currently public ones.
Not any allowed you to import and use the functions as a powerful library, 
and so i worked on a library-based SSH implementation which was non-existing
in the free and open source software world.


2* How/Who ?
-_-_-_-_-_-_-_

If you downloaded this file, you must know what it is : a library for
accessing ssh client services through C libraries calls in a simple manner.
Everybody can use this software under the terms of the LGPL - see the COPYING
file

If you ask yourself how to compile libssh, please read INSTALL before anything.

3* Where ?
-_-_-_-_-_-_

https://www.libssh.org

4* Contributing
-_-_-_-_-_-_-_-_-_

Please read the file 'CONTRIBUTING.md' next to this README file. It explains
our copyright policy and how you should send patches for upstream inclusion.

Have fun and happy libssh hacking!

The libssh Team
