#!/bin/bash

. /etc/thinstation.global

# Ensure the login keyring exists
if [ ! -f ~/.local/share/keyrings/login.keyring ]; then
	printf "[keyring]\npassword=" > ~/.local/share/keyrings/login.keyring
fi

# Unlock the keyring explicitly
dbus-send --session \
	--dest=org.freedesktop.secrets \
	--type=method_call \
	--print-reply \
		/org/freedesktop/secrets \
		org.freedesktop.Secret.Service.Unlock \
		array:string:"login"

if [ -e ~/.proxy ] ; then
	. ~/.proxy
	if [ "$PROXY_HTTP" == "TRUE" ] ; then
		if [ -n "$PROXY_AUTH" ] ; then
			proxyline="--proxy-server=http://${PROXY_AUTH}@$PROXY_SERVER:$PROXY_PORT"
		else
			proxyline="--proxy-server=http://$PROXY_SERVER:$PROXY_PORT"
		fi
	fi
fi

[ "`id -u`" == "0" ] && sandbox="--no-sandbox" || unset sandbox

if [ -n "`echo $@ |grep -e '--kiosk'`" ] && [ -z "`pgrep /opt/google/chrome/chrome`" ]; then
	cp -a /etc/skel/.config/google-chrome ~/.config/.
	chown -R `id` ~/.config/google-chrome
#	if [ -n "$KIOSK_DELAY" ]; then
		# We just killed crash detection, so now sleep incase were in a flapping state, also gives
		# local web servers enough time to get started
#		sleep $KIOSK_DELAY
#	fi
fi

exec /opt/google/chrome/google-chrome $sandbox $proxyline --simulate-outdated-no-au='Tue, 31 Dec 2099 23:59:59 GMT' --user-data-dir "$@"
