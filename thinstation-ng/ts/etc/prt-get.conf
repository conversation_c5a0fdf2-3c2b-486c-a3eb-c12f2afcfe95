###
### prt-get conf
###

# note: the order matters: the package found first is used
prtdir /ts/ports/components
prtdir /ts/ports/static-source
prtdir /ts/ports/gnome
prtdir /ts/ports/xorg
prtdir /ts/ports/xorg-opt
prtdir /ts/ports/xfce
prtdir /ts/ports/kernel-modules
prtdir /ts/ports/ruby
prtdir /ts/ports/binary-core
prtdir /ts/ports/binary-opt
prtdir /ts/ports/proprietary
prtdir /ts/ports/core
prtdir /ts/ports/opt
prtdir /ts/ports/contrib
prtdir /ts/ports/python
prtdir /ts/ports/perl
prtdir /ts/ports/relegated
prtdir /ts/ports/nocache

# the following line enables the user maintained contrib collection
#prtdir /usr/ports/contrib

### use mypackage form local directory
# prtdir /home/<USER>/build:mypackage

### log options:
writelog enabled         # (enabled|disabled)
logmode  overwrite       # (append|overwrite)
rmlog_on_success yes     # (no|yes)
logfile  /var/log/pkgbuild/%n.log
                           # path, %p=path to port dir, %n=port name
                           #       %v=version, %r=release

### use alternate cache file (default: /var/lib/pkg/prt-get.cache
# cachefile /mnt/nfs/cache

### print README information:
# readme verbose           # (verbose|compact|disabled)

### prefer higher versions in sysup / diff
 preferhigher yes      # (yes|no)

### use regexp search
# useregex no        # (yes|no)

### run pre- and post-installs scripts; yes is equivalent to the
### --install-scripts option
# runscripts no            # (no|yes)


### EXPERT SECTION ###

### alternative commands
# makecommand      pkgmk
# addcommand       pkgadd
# removecommand    pkgrm
# runscriptcommand sh

