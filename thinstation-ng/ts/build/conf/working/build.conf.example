################################################
### ---     Thinstation Build Config     --- ###
################################################
#
#
# This is Thinstation s basic setup file. In this file you decide which
# hardware, programs and features to be included in the Thinstation
# boot image generated by running "./build".
#
# You can customize/limit the possibilities in the thinstation.conf
# files later.
#
# Anything after a "#" is a comment. You activate "things" by removing
# the "#" and remove "things" by putting a "#" in front of them.
#
# First we define which modules to include. Modules are hardware
# drivers. NEVER include more than necessary - the more modules, the
# longer boot time.

########################################################
### --- Machine Modules to include in boot image --- ###
########################################################
# A machine profile is a set of modules for a specific set of hardware
# components like a thinclient with adapters. It does not include any
# filesystem modules, those need to be selected individualy. Making a
# a machine profile is a two step process. First you will need to make
# an allmodules build like "./build --allmodules" with the extensions-x
# package included. Once the client machine is booted with that image
# you can open and xterm or telnet in and run "hwlister.sh". hwlister.sh
# will compile some lists and attempt to post them to the pxe server assigned
# to the client. If the pxe server is this workstation and you are hosting
# files directly out of this build env, you can then run
# "./mkmachine SOME-MODEL" and the files will be put into the machine folder
# under that model name. Now all you have to do is add a line to this file
# like "machine SOME-MODEL" and you will no longer have to make
# allmodules builds. If this is not the pxe server, you will have to
# move stuff around on your own, but the most important files are
# modules.list and firmware.list.

################################################
### --- Modules to include in boot image --- ###
################################################
# Any modules may be added as "module" or "module_pkg".
# If defined as a "module", the driver is included in the boot image.
# If defined as a "module_pkg", the driver is added from outside the
# image, such as from a tftp server.  See note below for more details.
#
# Normally you should use "module".  Only use "module_pkg" if you want
# to dynamically load modules.
# Note: If you create a module_pkg network driver, and decide to load it
#       from a tftp server, it won t work.  This is because the image
#       won t have a network driver to get the module_pkg in the first
#       place.

#!Hardware
#!!Machine module list
#machine NT-A3500			# Foxconn AMD E-350 Barebone
#machine NT-535				# Foxconn Intel D525 Barebone
#machine Dell-FX170			# Dell FX170 D525 Atom Machine
#machine VMWare				# A good set of modules for a VmWare Virtual Machine
#machine Virtualbox-4.1			# A good set of modules for a Virtualbox Virtual Machine
machine qemu
#machine classic_generic		# Big list of modules that you can choose by unremarking them
#machine foxconn-nT-330i		# Foxconn nT 330i Barebone
#machine foxconn-nT-435			# Foxconn nT 435 Barebone
#machine foxconn-nT-535			# Foxconn nT 535 Barebone
#machine foxconn-nT-A3800		# Foxconn nT A3800 Barebone
#machine foxconn-nT-i2847		# Foxconn nT i2847 Barebone
#machine foxconn-nT-iBT18		# Foxconn nT iBT18 Barebone
#machine foxconn-nT-n270		# Foxconn nT n270 Barebone
#machine Acer-Revo-M1-601		# Acer Revo M1-601
#machine Lenovo-T420s			# Lenovo T420s Laptop
#machine Lenovo-T430s			# Lenovo T430s Laptop


#!!Wireless Stuff
#package wpa_supplicant
#package wireless
#package wifi-connect
#!!!Misc Modules

#!!Filesystem Support
# Every mounted device needs a filesystem, so choose which ones you need.
module usb-storage
#module autofs4          	# Automount and autofs support
module isofs            	# ISO9960 file system support for CDRoms
module udf			# CDRom UDF file system support
module vfat             	# Fat and VFat file system support
#module ntfs             	# NTFS file system support
#module ext2             	# Ext2 file system support
module ext4			# Ext4 file system support
#module nfs             	# NFS file system support
#module cifs			# CIFS support (use either this or smbfs - not both)

#################################################
### --- Packages to include in boot image --- ###
#################################################

# A package is a program or utility.
# Any packages may be added as "package" or "pkg".
# If defined as a "package", the program is included in the boot image.
# If defined as a "pkg", the program is added from outside the image.
# Normally, you should use "package".  Only use "pkg" if you want to
# dynamically load programs.


# --- Packages to include in boot image
# --- Packages below may be "package" or "pkg"
#!!Miscellaneous
#package overlayfs			# This is needed in order for squashfs compression mode to work.
#package ts-classic              # The classic ts logic for network/netfiles/hostname/mounting/telnetd/telnet/playcd
#package automount
#package netfiles			# Adds only this package instead of the whole ts-classic. netfiles lets you get thinstation.conf.xxx files
							# from the TFTP-server
package udisks
package automount
#package networkmanager
package autonet
#package udisks-glue
#package ntpdate		# Classic utility to obtain date & time from network
#package ntpd			# Modern NTP Daemon for time synchronization
#package sound-esd		# Enable sound-esd or sound-nasd if you want to be able to control
#package sound-nasd		#  sound on your thin client from another computer or
                        	#  your remote session.
package alsa
#package gnome-core
package gnome-control-center
package volumeicon
#package kismet
#package gst-plugins-base
#package gst-plugins-good
#package crystalhd
#package libva
#package iptables		# IP Tables support
package cpufreq
#package cpuspeed
#package wine			# Windows Application Execution Platform

#!!X related
# --- XOrg 7.7
#package xorg7-v4l
package xorg7-vesa		#A fallback driver that works with almost everything. Does allow resolution changes
#package xorg7-vmware
package xorg7-qxl
#package xorg7-ati		#ATI video driver for most modern ATI chipsets. Usually requires several firmwares
#package xorg7-amdgpu
#package xorg7-nouveau		#New opensource nvidia driver
#package xorg7_nvidia		#Proprietary nvidia driver
#package xorg7-openchrome	#Via Video Chipsets driver
#package xorg7-intel		#Intel video driver for most modern chipsets except the GMA3500/3600
#package xorg7-sis

#!!Locale or localization files for keyboard and fonts.  (Language/Country)
#package locale-cs_CZ  # Czech
#package locale-da_DK  # Danish
#package locale-de_DE  # German
#package locale-en_GB  # English-Great Britain
package locale-en_US  # English-US
#package locale-es_ES  # Spanish
#package locale-fi_FI  # Finnish
#package locale-fr_BE  # French Belgium
#package locale-fr_CA  # French Canadian
#package locale-fr_CH  # French Switzerland
#package locale-fr_FR  # French
#package locale-hr_HR  # Croation
#package locale-hu_HU  # Hungarian
#package locale-it_IT  # Italian
#package locale-ja_JP  # Japanese
#package locale-nb_NO  # Norwegian (bokmål)
#package locale-pl_PL  # Polish
#package locale-pt_PT  # Portuguese
#package locale-pt_BR  # Portuguese-Brazil
#package locale-ru_RU  # Russian
#package locale-sv_SE  # Swedish
#package locale-tr_TR  # Turkish

#!Applications
#!!Connection Package types.  Choose *at least* one!
package freerdp		# X RDP Client - fork of rdesktop
#package rdesktop		# X RDP client for Windows Terminal Services (ver 1.7)
#package vncviewer		# VNC client (vncviewer)
#package xorg7vnc		# Remote Control Client Workstation
#package ica			# Citrix ICA client support
#package tarantella		# Tarantella client support
#package xnest			# XDM in a window client
#package urxvt            	# Light Xterm Client (vt102)
#package xterm           	# Xterm Client (vt220)
#package ssh 			# Secure Shell client
#package tn5250          	# 5250 terminal emulator
#package dillo			# Dillo light web browser
#package thinlinc		# Cendio ThinLinc client support
#package nx              	# No Machine NX client
#package 2x			# 2X client
#package spice			# Redhat Spice Client
#package java			# Java runtime
#package firefox		# Firefox current Web Browser
#package chrome			# Google Chrome Web Browser
#package chromium		# Chromium Web Browser
#package kiosk			# Rkiosk Plugin for Firefox
#package flash			# Flash Current
#package filezilla		# FileZilla FTP Client for X
#package git			# GIT Version Control System
#package gparted		# Gnome Partition Manager
#package open-vm-tools
#package vboxguest
#package medit			# Notepad
#package mplayer		# DVD and video player
#package vmview            # VMware Open-View Client, PCOIP
#package evince			# X PDF Viewer
#package openkiosk		# OpenKiosk is a cross platform kiosk web browser based on Mozilla Firefox
#package xdmcp-connect		# Simple connection to XDMCP server

#!!Window Managers. Choose no more than 1 window manager.
# Not needed if you don t want to manage windows.
#package openbox		# A spartin clean/fast window manager.
#package xdesktop-kiosk		# A locked down desktop kiosk using the openbox wm (See packages/xdesktop-kiosk/build/conf for 
				# more help.
#package icewm			# "ICEWM" window manager.  Makes TS a light workstation.
#package icewm-theme-xp
#package icewm-themes		# Extra IceWM themes
#package icewm-theme-bernstein
#package icewm-theme-bluecrux
#package icewm-theme-liquid

package xfwm4
#package xfwm4-extra
package xfce4-power-manager
package terminal		# Terminal emulator for xfwm4 -- Pulls in xfwm4
package thunar			# File Manager for xfwm4 -- Pulls in xfwm4

#!!Window Manager Utils
#package idle-shutdown		# Package that lets you specify automatic shutdown settings and logic.
#package wbar         		# Adds icons to desktop
#package idesk

#!!Other services
#package www 			# Web access to client.  From a browser: "http://<IP number>"
                        	#  The standard page is for general user settings, administrative access
                        	#  is on port 6800.
#package lp_server		# Remote printing daemon (JetDirect compatible)
#package lpr             	# LPR Print Server, for use with samba-server package
#package lprng           	# LPRng Print Server, supports network based printing
#package cups
#package sshd 			# Dropbear secure shell server
#package tftpd 			# Built in tftpd server. Useful for making a tftpserver
				#  for thinstation on a WAN over a slow link.
#package samba-server		# Samba server FS Support, allows you to share
                        	#  local floppy/cdrom/hd/printer to other Windows
				#  PCs. Needs supermount for removeable media.
#package samba-client		# Samba smbclient, gives a shell like environment to access an samba server
#package hdupdate       	# Package for updating TS images on a hardisk over a network connection
#package scp	            	# Add ability to download files over internet using scp.  This package
                        	#  adds some networking based options for downloading configuration files or
                        	#  in using hdupdate package.
#package openvpn                # OpenVPN Client Support
#package vhusbip		# The VirtualHere USB Server enables remote access to USB devices over a network.
				# Install the Generic Build of the VirtualHere Server on an unlimited number of computers
				# and share a single USB device per computer server, with no payment required.
				# To share more than one USB device simultaneously from a single server you must purchase an "Unlimited Device License".


#!!PCSCD Card Readers
#package ccidreader             # Generic USB card reader
#package asedriveiiie-usb	# Drivers for smart cards of readers of AseIIIeUSB and AseIIIeUSB KB

#!!Miscellaneous
package gtk-2.0			# Full gtk-2.0 plus clearlooks theme
package gtk-3.0
package gtk-theme-adwaita
package icons-cursor		# Anti Aliased Mouse Cursor Theme
package icons-hicolor           # Hi-Color icons for some applications and themes. Does not really contain any icons.
#package icons-gnome             # gnome Hi-Color icons theme. Needed by most other icon themes.
package icons-adwaita
#package icons-tango             # tango Hi-Color icons theme
#package icons-neu               # neu Hi-Color icons theme
#package fonts-misc
#package fonts-jis-misc		# Japanese Industrial
#package fonts-util
#package fonts-cyrillic
#package fonts-Speedo-Bitstream
package fonts-TTF-BH		# This one works nicely most of the time and is small.
package fonts-TTF-vera
#package fonts-TTF-MS		# Fonts for rendering documents developed on the MS Platform.
                            # Might want to talk to a lawyer before using these.
package fonts-TTF-liberation
#package fonts-TTF-noto
#package fonts-75dpi-Adobe
#package fonts-75dpi-Adobe-Utopia
#package fonts-75dpi-BH
#package fonts-75dpi-BH-Typewriter
#package fonts-75dpi-Bitstream
#package fonts-100dpi-Adobe
#package fonts-100dpi-Adobe-Utopia
#package fonts-100dpi-BH
#package fonts-100dpi-BH-Typewriter
#package fonts-100dpi-Bitstream
#package fonts-Type1-Adobe-Utopia
#package fonts-Type1-BH
#package fonts-Type1-Bitstream
#package fonts-Type1-IBM
#package fonts-Type1-xfree86
#package lshw				# list hardware
#package e3 				# Basic vi like Editor
#package rox				# Basic File Manager
#package debug				# metapackage to include other debugging packages and stop during bootup to check things.
#package extensions			# Adds various shell commands, will increase image size.
#package extensions-x		# Adds various utility s for the X Environment !!!! INCLUDES hwlister.sh !!!!
#package eGalax				# eGalax TouchKit Drivers/Utility for serial and usb touchscreens by EETI.
#package installer			# Wipes a drive, makes partitions and downloads thinstation from a pxe server
#package devstation			# Starts a tftp server that hosts files from the boot-images/pxe folder
#package ca-bundle			# Adds many standard SSL and PKI root certificats. Include this package if you have issues with certificate trusts.
#package set-resolution		# Allows the user to change the resolution via the start-menu.
#package local-install		# Makes it possible to install Thinstation locally on the machine but still have a central image
							# management via automatic updates and version control when the client boots. For more information
							# see /ts/build/Documentation/README.local-install and /ts/build/thinstation.conf.sample
							# (generated during first build)
#package custom-idle         # Shuts down the client based after X minutes if some applications/sessions are not running.
#package custom-background	# Allows you to dynamically set a diffrent background in the conf-files when the client boots.
                            #     For example have a background-image in the build with an error-message and then in
                            #     thinstation.conf.network set the "regular" background. This way the user will receive an
                            #     error message on the screen if the client was unable to get the config files from the
                            #     Web- or TFTP-server when it booted.
#package hdd-spindown		# Sets hard drives into sleep mode (spin down) when they are idle. See thinstation.conf.sample for info.
#package openssl             # Probably needed by the Citrix Receiver 13.2 version.


# Parameters
########################################
### --- Miscellaneous Parameters --- ###
########################################
#!!Basic

#param fastboot       true				# Mangles the filesystem a special way as to improve boot speed and reduce
							# memory utilization. Cool/Dangerous . Harder to dubug other packages. (Finishing Touch)
							# Set to 'true' to enable or 'lotsofmem' for slightly slower booting but no squash lag on app launch.
param tsuser         tsuser                            # Name of the user that thinstation will run as.
param tsuserpasswd   pleasechangeme			# Do Change! Console/telnet password for non-root

param rootpasswd     pleasechangeme			# Do Change!  Console/telnet password for root
                                        		# If this is enabled, Telnetd will be enabled.
param tsadminpasswd  pleasechangeme			# Admin password of web interface
param xorgvncpasswd  pleasechangeme			# VNC Access Password
param storagepasswd  pleasechangeme			# Password for storage server
param dialuppasswd   pleasechangeme			# Password for dialin account
param sambapasswd    pleasechangeme  			# Password for samba shares when using user mode security
#param kernelcmdline "radeon.modeset=0"                 # Add additional kernel command lines. e.g. disable radeon framebuffer
#param stripelf       yes				# Strip extraneous information from elf binaries (Maybe not Safe)
#param acpisupport    disable				# Tells the kernel not to load acpi modules. (Breaks some Intel Chipsets if disabled)
#param uvesafb        disable				# Disable uvesafb (legacy option like modesetfb)
#param extra_vid      LVDS-1:d				# Add an extra video= parameter to kernel cmd line (good for disabling outputs on video cards)
param bootlogo       true				# Enable or Disable the use of the Boot splash.
param boottheme	     default           			# Backgound picture during boot
#param splash	     verbose				# kernel splash setting (0=off, silent or verbose (default=silent)
param splash	     silent
param fbmtrr         0                                  # MTRR value for uvesafb (default = 0, 4 is the best) grep your log to make sure you have not set it to high
#param fbnocrtc      true                               # This is usually a good thing.
param fbsm           ywrap                              # Window scrolling method (redraw, ypan, ywrap) ywrap is best, but may not work correctly for all people
#param fbvtotal       16                                # Override Video Bios Reported Memory in MB
#param fbmaxhf        67                                # Override Video Negotiated Max Horizontal Frequency
#param fbmaxvf        61                                # Override Video Negotiated Max Vertical Frequency
#param fbmaxclk       155                               # Override Video Negotiated Max Clock Frequency
#param fbnoedid       true                              # Don t do video edid
#param bootresolution 1024x600-32                       # Resolution used during Thinstation boot.
#param bootresolution 1024x768-32                        # You can wright your own resolution mode here, if you know it.
#param bootresolution 1152x864-32
param bootresolution 1280x768-32
#param bootresolution 1366x768-32                       # Otherwise, you may want to refer to the vbe_modes.list you created with hwlister.sh
#param bootresolution 1280x1024-32                      # for modes that your card supports.
#param bootresolution 1400x900-32                       # It is also used by xrandr if no xrandr options are specified.
#param bootresolution 1680x1050-32
#param bootresolution 1920x1080-32
#param bootresolution 1400x1050-32
#param bootresolution 1920x1200-32

param desktop file:./backgrounds/Hive_Lite.jpg		# Custom image to load as desktop background
param defaultconfig  thinstation.conf.buildtime  	# The file with default setup.  No other config file is found
                                                 	#  during boot.
param basename       thinstation     			# Used for all config/tftp/scp file names
param basepath       ts6.3               		# Used to determine path to tftp/scp files
param baseurl        http://www.doncuppjr.net		# Used to determine url to wget files
#param baseurl        http://*************		# Used to determine url to wget/http files - your local web server
#param baseurl        'http://${SERVER_IP}'		# Used to determine url to wget/http files - if you wan't to use a web server on the same server as the TFTP server
#param keyfile        ./id_rsa       			# Path for private key file used for ssh/scp
#param knownhosts     ./known_hosts  			# Path for ssh known_hosts file for ssh/scp
#param localpkgs      false				# to determine is PKG files are to be loaded locally
#param fulllocales    true	      			# Use full locale support for packages
#param icaencryption  false      			# Use ica encryption support, add 290k to image
param haltonerror    false				# Will halt on error, default is true
param hardlinkfs     true
param sametimestmp   true                               # When enabled, the timestamps for all files and folders will be set to 00:00 of todays date in your timezone
param initrdcmd	     "gzip"				# Compression mode and level of initrd file. none, gzip -9, lzma -9 ,bzip2 -9
#package overlayfs
param bootverbosity   3          			# Increased vebosity when booting TS
                                			#   0 no verbose messages
                                			#   1 boot
                                			#   2 network
                                			#   4 init
                                			#   8 kernel
                                			#  16 modules
                                			#  32 packages
                                			#  64 email bootlog file to SMTP server & user set in
                                			#     thinstation.conf file.  This will only work
                                			#     if networking is working.
                                			#
                                			#  Combinations can be used (e.g. 24 does Kernel and Module Messages)

#!!Advanced
# Leave this alone unless you can t download (wget required on your Linux box):
param downloads         /downloads
#param bootserver        "***********"		# Used for the pxe image config files to specify where to download
											# the syslinux image from. Result is generated in
											# /ts/build/boot-images/pxe/boot/lpxelinux/pxelinux.cfg/default
											# so that you can specify APPEND initrd=http://<bootserver>/boot/initrd
#param bootprefix "test/" # Add a prefix directory to /boot for PXE based launch, used to allow multiple builds on same PXE server
                          # Value will be inserted as follows: /${bootprefix}boot
                          # Note: This is incompatible with non-network grub based boot images
param syslinuxtheme     "default"
param grubtheme		"default"
#param httpproxy	http://***********:8080

#param bootfssize     50%                               # Modify the size of the tmpfs filesystem

#package alltimezone
#param allres		true		# Includes a lot of resolution splash images for live-cd s
#param allfirmware	true		# Includes a lot of firmwares for live-cd s
param earlymicrocode	false		# Builds microcode initramfs for early loading
					# by kernel. Needs kernel with MICROCODE_EARLY=y.
param blacklist "snd-pcsp.ko pcspkr.ko"
#param mesa_3d disable			# The Mesa 3D Graphics Library disable
