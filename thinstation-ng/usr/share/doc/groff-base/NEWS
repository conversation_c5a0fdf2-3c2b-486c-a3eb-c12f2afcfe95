    Copyright (C) 1992-2023 Free Software Foundation, Inc.

    Copying and distribution of this file, with or without modification,
    are permitted in any medium without royalty provided the copyright
    notice and this notice are preserved.

This file describes recent user-visible changes in groff.  Bug fixes are
not described.  There are more details in the man and info pages.

VERSION 1.23.0
==============

troff
-----

o The `troffrc` file now loads an English localization file instead of
  directly housing configuration parameters appropriate to the English
  language.  See "Macro Packages" below.

o A new read-only register `.cp` is implemented.  Within a `do` request,
  "\n[.cp]" holds the saved value of compatibility mode.  See
  groff_diff(7) or the groff Texinfo manual for rationale, use case, and
  example.

o New read-only registers `.nm` and `.nn` are implemented.  `.nm` is of
  Boolean sense, reporting the enablement status of output line
  numbering (caused by the `nm` request) irrespective of the temporary
  suspension of numbering with the `nn` request.  `.nn` holds the count
  of numbered output lines still to have that numbering suppressed.
  These registers were introduced because there was no way for the
  formatter (and thus a document) to introspect their state, tbl(1)
  needs to be able to do so, and the writable line number register `ln`
  is not a reliable proxy for this information.

o Type size escape sequences of the form "\sNN", where NN is in the
  range 10-39, are now recognized only in compatibility mode ("groff
  -C"); when encountered, an error diagnostic is emitted.  Otherwise,
  "\sN" is interpreted as setting the type size to the single-digit
  value N (in scaled points), which ends the escape sequence.  This
  change eliminates a quirk in the language grammar that dates back to
  the mid-1970s (AT&T troff by Ossanna) but was not documented in the
  Troff User's Manual until 1992 when Kernighan updated CSTR #54 for
  device-independent AT&T troff.

  The form "\s(NN" is accepted for two-digit sizes in all known troffs.
  The form "\s[NNN]" accepts a numeric expression of variable length; it
  has been supported by groff since version 1.01 (March 1991) or
  earlier, by Heirloom Doctools troff since its 2005-08-16 release, and
  by neatroff, but not by Plan 9 troff.  The form "\s'NNN'" is also
  widely supported.

  Summary: in your documents, rewrite escape sequences beginning with
  "\s1", "\s2", or "\s3" in an unambiguous and portable form.  For
  instance, "\s36" can become any of:
    \s(36
    \s[36]
    \s'36'
  You can use
    grep '\\s[123]'
  to find instances in your documents.

  Those who have changed the escape character with the `ec` request (an
  advanced usage) are expected to be able to cope; ask the development
  team for support if you need it.

o New requests `soquiet` and `msoquiet` are available.  They operate as
  `so` and `mso`, respectively, except that they do not emit a warning
  diagnostic if the file named in their argument does not exist.

o New requests `stringdown` and `stringup` are available.  These change
  the string named in their argument by replacing each of its bytes with
  its lowercase or uppercase version (if any), respectively.  groff
  special characters (see the groff_char(7) man page) in the string will
  often transform in the expected way due to the regular naming
  convention for accented letters.  When they do not, use substrings
  and/or catenation.

o The `ab` request no longer writes "User Abort." to the standard error
  stream if given no arguments.

o The `fp` request no longer accepts file or font names with slashes in
  them as arguments.  All font description files are expected to be
  accessible within the directory of the output device for which they
  were prepared.

nroff
-----

o The new option -P takes an argument to pass to the output driver
  (always grotty(1)).  "-P-i" directs the terminal device to display
  real italic (oblique) characters instead of underlining: it is up to
  your terminal (emulator) to support italics (xterm does since patch
  #314 [2014-12-28]).  "-P-h" can now be used instead of -h; the latter
  may eventually be deprecated and repurposed.

o The new option -V emits the constructed groff command that nroff would
  run to standard output instead of executing it.  Arguments to nroff
  that contain shell metacharacters may not be sufficiently escaped for
  the output of nroff -V to be copied and pasted to the shell prompt;
  this is a historical deficiency of the Bourne shell family not yet
  corrected by the POSIX standard.

o nroff now recognizes the -b, -E, -k, -K, -R, and -z options and passes
  them through to groff.

o nroff now supports spaces and tabs between option flag letters and
  arguments to options, like groff and troff themselves.

groff
-----

o The -I option now implies -g (run the grn(1) preprocessor), and
  supplies grn an -M option with the argument to -I.

eqn
---

o The GNU extension

    delim on

  is now recognized even in AT&T compatibility mode (the -C option) in
  order to reliably integrate with tbl.  Few eqn documents are expected
  to use 'o' and 'n' as left and right delimiters, respectively.  If
  yours does, consider swapping them, or select others.

o The command-line option -D is no longer supported.  It has been
  undocumented, and issued a warning of its obsolescence upon use, for
  30 years, since groff 1.06 (September 1992).

pic
---

o The token `.PY` is now recognized as a synonym of `.PF` to work around
  a name space collision with the m (mm) macro package, which defines
  `PF` as a page footer management macro.  (This problem dates back at
  least to Unix System V Release 2, 1983.)  You should continue to use
  `.PF` to end pictures with flyback unless a similar problem faces your
  document.

tbl
---

o GNU tbl now suspends output line numbering while formatting tables,
  saving and restoring its status before and after each table region,
  including the count of lines for which numbering is suppressed.
  Historical tbl implementations did not, with bizarre consequences when
  text blocks were used in tables.

Macro packages
--------------

o mom version 2.5 is distributed with this release.  New features
  include shaded backgrounds, frames, and colored pages.  Thanks to
  Peter Schaffter.

o English localization has been split into a dedicated macro file,
  `en.tmac`, for better parallelism with other localization files and to
  improve support for multilingual documents.  Those who want a
  different default input language should edit the troffrc file to
  source the desired groff locale macro file (`cs.tmac`, `de.tmac`,
  `den.tmac`, `fr.tmac`, `it.tmac`, `ja.tmac`, `sv.tmac`, or `zh.tmac`)
  instead of `en.tmac`.

  The default hyphenation mode (as given to the `hy` request) for users
  of English has changed from "1", which was inappropriate for the
  TeX-based hyphenation patterns groff has used since at least 1991, to
  "4".  However, invoking ".hy" without an argument remains synonymous
  with ".hy 1".

o The hyphenation patterns for English have been updated using the
  `hyph-en-us.tex` patterns file from the TeX hyph-utf8 project.  The
  new patterns likely _will_ change the automatic hyphenation break
  points of your English documents.

o The `PDFPIC` macro (provided by the `pdfpic` package) no longer aborts
  upon encountering trouble.  Instead, it reports an error and abandons
  processing of its argument(s).  It is also more sensitive to other
  kinds of problems and handles them the same way, by issuing a
  diagnostic and returning.  If you wish `PDFPIC` to abort document
  processing immediately upon error, you can append an `ab` request to
  the package's error-handling macro.

    .am pdfpic@error
    .  ab
    ..

o The pspic package now also has an error hook macro, which you can use
  to make failed image loads fatal (or attempt fallback or recovery).

    .am pspic@error-hook
    .  ab
    ..

o The new rfc1345 macro package, contributed by Dorai Sitaram, defines
  special character identifiers implementing RFC 1345 mnemonics (plus
  some additions from Vim, which itself uses RFC 1345 for its digraphs).
  It is documented in the groff_rfc1345(7) man page.

o The new sboxes macro package, contributed by Deri James, offers a
  simple interface to the new gropdf(1) "background" feature.  Using it,
  ms documents can draw colored rectangles beneath any groff output.
  See "Using PDF boxes with groff and the ms macros", installed as
  `msboxes.ms` and `msboxes.pdf` for instructions and a demonstration.

o The an (man) and doc (mdoc) macro packages no longer remap the -, ',
  and ` input characters to Basic Latin code points on UTF-8 devices,
  but treat them as groff normally does (and AT&T troff before it did)
  for typesetting devices, where they become the hyphen, apostrophe or
  right single quotation mark, and left single quotation mark,
  respectively.  This change is expected to expose glyph usage errors in
  man pages.  See the "PROBLEMS" file for a recipe that will conceal
  these errors.  A better long-term approach is for man pages to adopt
  correct input practices; the man pages groff_man_style(7),
  groff_char(7), and man-pages(7) (subsection "Generating optimal
  glyphs"; from the Linux man-pages project) contain such instructions.
  Doing so also improves man page typography when formatting for PDF.

  If you maintain a generator of man(7) or mdoc(7) documents (such as a
  tool that converts other formats to them), and need assistance, please
  <NAME_EMAIL> mailing list and describe your situation.

o The an (man) macro package can now produce clickable hyperlinks within
  terminal emulators, using the OSC 8 support added to grotty(1) (see
  below).  The groff man(7) extension macros `UR` and `MT`, present
  since 2007, expose this feature.  At present the feature is disabled
  by default in `man.local` pending more widespread recognition of OSC 8
  sequences in pager programs.  The package now recognizes a `U`
  register to enable hyperlinks in any output driver supporting them.

  Use a command like
    printf '\033]8;;man:grotty(1)\033\\grotty(1)\033]8;;\033\\\n' | more
  to check your terminal and pager for OSC 8 support.  If you see
  "grotty(1)" and no additional garbage characters, then you may wish to
  edit "man.local" to remove the lines that disable this feature.

o The an (man) macro package supports a new macro, `MR`, intended for
  use by man page cross references in preference to the font style
  alternation macros historically used.  Where before you would write
    .BR ls (1).
  or
    .IR ls (1).
  you should now write
    .MR ls 1 .
  (the third argument, typically used for trailing punctuation, is
  optional).  Because the macro semantically identifies a man page, it
  can create a clickable hyperlink ("man:ls(1)" for the above example)
  on supporting devices.  Furthermore, a new string, `MF`, defines the
  font to be used for setting the man page topic (the first argument to
  `MR` and `TH`), permitting configuration by distributions, sites, and
  users.

  Inclusion of the `MR` macro was prompted by its introduction to
  Plan 9 from User Space's troff in August 2020.  Its purpose is to
  ameliorate several long-standing problems with man page cross
  references: (1) the package's lack of inherent hyperlink support for
  them; (2) false-positive identification of strings resembling man page
  cross references (as can happen with "exit(1)", "while(1)",
  "sleep(5)", "time(0)" and others) by terminal emulators and other
  programs; (3) the unwanted intrusion of hyphens into man page topics,
  which frustrates copy-and-paste operations (this problem has always
  been avoidable through use of the \% escape sequence, but cross
  references are frequent in man pages and some page authors are
  inexpert *roff users); and (4) deep divisions in man page maintenance
  communities over which typeface should be used to set the man page
  topic (italics, roman, or bold).

o Part of the an (man) macro package has been renamed from "an-old.tmac"
  to "an.tmac", replacing a file that sourced the "andoc.tmac" wrapper.
  This means that the "-man" argument to groff (or nroff, or troff) will
  no longer load the andoc wrapper, and not successfully format mdoc(7)
  man pages.  If you are not sure which macro package a given man page
  uses, or you wish to batch-process a series of man pages written
  variously in the man and mdoc formats, be sure to call the formatter
  with the "-mandoc" option explicitly, as "-man" will no longer do
  this.  The man-db man(1) implementation has, since 2001, used
  "-mandoc" preferentially if available when man-db is configured.

o The an (man) and doc (mdoc) macro packages support a new `AD` string
  to put the default adjustment mode under user control at rendering
  time.  The default is "b" (adjust lines to both margins) as has been
  the Unix man(7) default since 1979.

o The an (man) and doc (mdoc) macro packages support new `CS` and `CT`
  registers to control rendering of man page section headings and topics
  (seen in the page header), respectively, in full capitals.  These
  default off (with no visible effect on pages that already fully
  capitalize such text in man page sources).  The rationale is to
  encourage man page authors to preserve case distinction information in
  (or restore it to) their topics and section headings, while giving
  users (including system administrators, distributors, integrators, and
  maintainers of man(1) implementations) a way to view the rendered page
  elements in full capitals if desired.

o The an (man) macro package no longer honors an `ll` request to set the
  line length on nroff devices prior to processing a man page.  This was
  deprecated in groff 1.18 (July 2002), and all known man program and
  macro package implementations either have set an LL register since
  2002 (man-db man), 2005 (Brouwer/Lucifredi man), or don't let the user
  vary the line length freely (DWB troff, Solaris troff, Plan 9 troff),
  don't permit its configuation via the `ll` request (mandoc), or at all
  (Heirloom Doctools troff).

o The an (man) macro package now interprets the value of the `HY`
  register as a Boolean; using it to set a specific hyphenation mode is
  no longer supported.  The groff command-line option `-rHY=0` continues
  to disable automatic hyphenation of man page text as before.

o The an (man) macro package's `TS` macro no longer inserts vertical
  space.  It was not documented to do so, but had since groff 1.18 (July
  2002).  Man page authors may freely use paragraphing macros around
  tables if vertical space is desired.

o The an (man) macro package no longer attempts to detect misuse of the
  `R` string as a macro.  The `R` string itself is a legacy feature, not
  required in modern man pages; see groff_man_style(7).

o The groff_man(7) man page documenting the groff implementation of the
  an (man) macro package has been split into two pages.  The original
  page remains as a terser reference for experienced users.  A new page,
  groff_man_style(7), is a tutorial and style guide containing the same
  material supplemented with explanations, examples, and advice for the
  reader who is not an expert in *roff systems or in writing man pages.

o The doc (mdoc) macro package now honors the `C`, `FT`, `HY`, `IN`,
  `P`, `SN`, and `X` registers and `HF` string as the an (man) package
  does.

o The doc (mdoc) macro package now renders man page (sub)section cross
  references cited with the `Sx` macro by quoting them instead of
  setting them in italics.

o The e (me) macro package has changed its default line length on
  typesetting devices from 6i to the output device's default (for
  example, 6.5i on the 'ps' and 'pdf' devices).  You can use
  "papersize.tmac" to override this length, as in "groff -d paper=a4l"
  to select A4 paper in landscape orientation, without needing to alter
  the document.

o The e (me) macro package has changed its support for output line
  numbering with the `n1` and `n2` macros to resolve several bugs in the
  previous implementation.  The `n1` macro now accepts an optional `C`
  argument to set the line number at the existing page offset instead of
  reducing the page offset to make the line number appear in the left
  margin.  A second argument to the `n2` macro is no longer supported.
  A new register `no` makes configurable the amount of horizontal space
  to be used for the line number (the default is unchanged).

o The e (me) macro package now uses strings `wa` and `wc` to store the
  terms the package produces in chapter headings created by the `$c`
  macro.  The strings, which default to "Appendix" and "Chapter",
  respectively, ease localization of the package and replacement of the
  terms used without requiring the `$c` macro to be redefined.

o The e (me) macro package has a new macro, `ld`, which "re-localizes
  the date"; if you modify troff registers `dw`, `mo`, and `yr` (to
  record a document's date of revision, for instance), call `ld`
  afterward to update the package's `y2` and `y4` registers and the
  localized strings `dw` and `mo` for the names of the weekday and
  month.  `ld` is also used internally to simplify the use of the
  package with languages other than English; it thus updates the `wa`
  and `wc` strings as well.  If you want to customize these strings, do
  so after any `ld` call you make.

o The e (me) macro package now has a register `sx` that eases the
  configuration of space added to the line height above/below when
  super/subscripting is used.  It defaults to 0.2m, the value used
  literally in past definitions of the super/subscripting strings.
  groff's own 'me' documents redefine it to zero.

o The e (me) macro package's `$v` and `$V` registers have been renamed
  to `tv` and `dv`--they control the vertical spacing used
  for text and displays/annotations, respectively.  The old names are
  still supported as aliases.  The new names reflect the fact that users
  are expected to set them if desired, unlike other registers and
  strings beginning with "$".

o The e (me) and s (ms) macro packages now offer a `PF` macro,
  supporting the pic(1) preprocessor's "flyback" feature.  Thanks to
  Dave Kemper.

o The m (mm) and s (ms) macro packages no longer implement the `IX`
  macro.  This undocumented 4.2BSD ms extension was similarly
  undocumented by groff mm and ms.  No documents applying it are
  attested.  groff mm documents its own indexing feature, `INITI`.  We
  otherwise suggest makeindex(1), which supports troff and is available
  with most TeX distributions, for your mm/ms document indexing needs.

o The m (mm) macro package now adapts to the paper format selected when
  the macro file "papersize.tmac" is used (by specifying the groff "-d
  paper" command-line option).  A consequence is that "groff -mm" and
  "groff -d paper=letter -mm" are _not_ synonymous (when groff is
  configured to use U.S. letter as the default paper format), because
  groff mm(7) uses a page offset of 0.963 inches on typesetting devices
  for compatibility with DWB mm.  If the `W` or `O` registers are also
  set on the command line, the line length and page offset,
  respectively, are not overridden by "papersize.tmac".

o The m (mm) macro package now recognizes a `V` register to set the
  vertical spacing for the document.  Like the existing `S`, it must be
  set from the command line.  Further, both registers are interpreted
  correctly if suffixed with a scaling unit, instead of requiring an
  unscaled value assumed to be points.

o The m (mm) macro package now supports AT&T/DWB mm's `Sm` string.

o The m (mm) macro package now requires a title to be declared when
  memorandum type 5 is used (".MT 5"), just as type 4 has since groff
  1.10 (November 1995).

o The m (mm) and s (ms) macro packages no longer manipulate the set of
  enabled warning categories.  If you want all warnings on, use the
  `warn` request with no arguments in your document or pass "-w w" to
  groff (see troff(1) or the groff Texinfo manual for more on warnings).

o The m (mm) and s (ms) macro packages' `R` macros now work analogously
  to their `B` and `I` macros instead of ignoring their arguments.

o The m (mm) package now offers a `PY` macro, which serves the function
  of `PF` (end pic(1) picture with flyback) from other macro packages.

o The "ptx.tmac" macro file, a counterpart to the GNU coreutils ptx(1)
  command for generating permuted indexes, is now installed.  It has
  long been part of the source distribution.

o The s (ms) macro package now enables the formatter's "no-space mode"
  after ending displays (`DE`), equations (`EN`), tables (`TS`), and
  pictures without flyback (`PE`).  This means that display distance
  spacing (the `DD` register) overrides the spacing that may follow in a
  subsequent paragraph, section heading, or display instead of
  accumulating with that distance.  This change is to make the behavior
  of the package more predictable; you can fine-tune such spacing by
  setting the `DD` register in desired places.  It has also helped us to
  improve groff ms's rendering of historical ms(7) documents such as
  Kernighan & Cherry's "Typesetting Mathematics".

o The s (ms) macro package supports a new string, `FR`, which defines
  the ratio of the footnote line length to the current line length.  The
  default expression is "11/12", eleven twelfths of the normal line
  length, adopted for better compatibility with ms documents prepared
  with AT&T ms or its descendant implementations in Heirloom Doctools
  and neatroff.  This is a change from previous groff releases, which
  used a ratio of five sixths.

  You may wish to set the `FR` string to "1" to align with contemporary
  typesetting practices.  In Unix Version 7 ms, its descendants, and
  groff prior to this release, an `FL` register was used for the line
  length in footnotes; however, setting this register at document
  initialization time had no effect on the footnote line length in
  multi-column arrangements.

  `FR` should be used in preference to the old `FL` register in
  contemporary documents; see the groff Texinfo manual or the "Using
  groff with the ms macros" document, also part of this release.  Thanks
  to T. Kurt Bond.

o The s (ms) macro package has added strings, `<` and `>`, to perform
  subscripting.  They work analogously to the `{` and `}` superscripting
  strings that have been present in groff ms since 1991 or earlier.

o The s (ms) macro package has added a hook macro, `FS-MARK`, which is
  called automatically by the `FS` macro (with the same arguments given
  to `FS`) before any other footnote processing.  It is empty by default
  but can be defined by the user to, for example, place a hyperlink
  anchor so that a link within a footnote can return to its referential
  context.  "Portable Document Format Publishing with GNU Troff",
  distributed with groff as `pdfmark.ms`, uses this technique.  Thanks
  to Keith Marshall.

o The s (ms) macro package's `RP` macro recognizes a new optional
  argument, `no-renumber`, which suppresses the renumbering of the page
  after the cover page as page 1.  It furthermore recognizes the
  optional argument `no-repeat-info`, which has the same effect as `no`;
  the latter will continue to be supported for backward compatibility.
  Optional arguments to `RP` can be given in any order.

o The s (ms) macro package supports new macros `XN` and `XH` to ease the
  input of numbered and unnumbered section headings, respectively.  They
  internally call the `XS` and `XE` macros to produce table of contents
  entries, and lay a foundation for inclusion of PDF bookmarks, all
  without requiring retyping of the heading text as the package
  previously encouraged.  Thanks to Keith Marshall.

o The s (ms) macro package now uses a default line length of 6.5 inches
  by default, resulting in 1-inch left and right margins.  When the
  "papersize.tmac" package is used by employing the "-d paper" groff(1)
  option on typesetting devices, the default page offset and line length
  are adjusted to maintain these margins.

o The "a4.tmac" file has been dropped from the distribution.  Its
  successor, "papersize.tmac", has been present and documented for
  nearly 20 years.  See subsection "Paper format" of groff(1).

o The "safer.tmac" file has been dropped from the distribution.  It was
  present only to support man(1) programs that unconditionally passed
  the formatter the "-msafer" option, and had contained only comments
  for over 20 years.  If your man(1) program has this requirement, you
  can create an empty file of this name in groff's macro file search
  path (see troff(1)) or consider migrating to man-db man(1).

Output drivers
--------------

o On output devices using the Latin-1 character encoding ("groff -T
  latin1" and the X11 devices) the special character escape sequence
  \[oq] (opening quote) is now rendered as code point 0x27 (apostrophe)
  instead of 0x60 (grave accent).  The ISO 8859/ECMA-94 Latin character
  sets do not define any glyphs for directional ("typographer's")
  quotation marks, but the apostrophe is depicted in the defining
  standard as a neutral (vertical) glyph, whereas the grave accent 0x60
  and acute accent 0xB4 are mirror-symmetric diacritical marks.

  This change has no effect on _input_ conventions for roff source
  documents.  You can still get directional single quotes on UTF-8,
  PostScript, PDF, and other output devices supporting them by typing
  sequences like `this' in the input (character remapping with 'char'
  requests and similar notwithstanding).

gropdf
------

o A new device control command, "background", enables boxes to be drawn
  underneath other page content.  The boxes can be shaded with colors,
  drawn with a colored border of configurable thickness, and interrupted
  by page breaks with special support for breaking before footnotes and
  similar material.  For convenience, "pdf.tmac" exposes a new macro,
  `pdfbackground`.  Thanks to Deri James.

grotty
------

o The "utf8" output device now maps the input characters '^' (caret,
  circumflex accent, or "hat") and '~' (tilde) to U+02C6 (modifier
  letter circumflex accent) and U+02DC (small tilde), respectively, for
  consistency with groff's other output devices.  This change is
  expected to expose glyph usage errors in man pages.  See the
  "PROBLEMS" file for a recipe that will conceal these errors.  A better
  long-term approach is for man pages to adopt correct input practices;
  the man pages groff_man_style(7), groff_char(7), and man-pages(7)
  (subsection "Generating optimal glyphs"; from the Linux man-pages
  project) contain such instructions.  Doing so also improves man page
  typography when formatting for PDF.

  If you maintain a generator of man(7) or mdoc(7) documents (such as a
  tool that converts other formats to them), and need assistance, please
  <NAME_EMAIL> mailing list and describe your situation.

o A new device control command, "link", generates OSC 8 hyperlinks.
  This means that groff documents can produce clickable links in the
  terminal window for emulators that support such escape sequences.

o The "sgr" device control command, which dynamically configured support
  for ISO 6429/ECMA-48 SGR escape sequences (and restored traditional
  overstriking behavior if disabled), has been removed.  It took effect
  only at page boundaries.  grotty's "-c" command-line option and the
  GROFF_NO_SGR environment variable remain supported.

Documentation
-------------

o groff's Texinfo manual is included in the distribution archive in
  several formats, including GNU Info, HTML, TeX DVI, PDF, and plain
  text.  Many sections have been extensively revised and corrected, and
  much material added to help the learner acquire the groff formatting
  language (see, for instance, the section/node "Text").

o A compilation of all of groff's man pages in PDF and UTF-8-encoded
  text (with SGR escape sequences) is produced by the build.  Many of
  the documents in this 380+-page document have been heavily revised or
  rewritten, including tbl(1), groff(1), groff_diff(7), groff_font(5),
  groff(7), groff_char(7), and roff(7).  The PDF version uses pdfmark
  extensions to produce an internal bookmark for every man page
  document, section heading, and subsection heading.

o Larry Kollar's "Using groff with the ms Macro Package" has been
  resurrected after 20+ years, revised, and updated.

o Eric Allman's "me Reference Manual" has been revised in detail.

Miscellaneous
-------------

o Because all generated forms of groff's Texinfo manual are now included
  in the distribution archive, building from that archive no longer
  depends on GNU Texinfo or a TeX installation (the latter was required
  only for the "doc" target, which had to be explicitly named).

o Building groff from its distribution archive no longer requires byacc
  (or GNU Bison) to be installed.

o m4 is now required to build.  Any m4 that implements the features
  documented in the Version 7 Unix m4(1) man page, and the `-D` option,
  should suffice.

o New 'configure' options '--{en,dis}able-groff-allocator' control
  whether groff uses its own malloc/free-wrapping allocator to implement
  all C++ new/delete operations.  groff has used this allocator for over
  30 years; C++ implementations are more mature now.  The default is now
  to rely on C++ language runtime support for new/delete.  When building
  groff, use
    configure --enable-groff-allocator
  to re-enable groff's traditional allocator.

o The 'configure' option '--with-appresdir' has been renamed to
  '--with-appdefdir'.

o Italian language input documents are now supported, including
  hyphenation patterns from the hyph-utf8 project and localized strings
  for the ms, me, mm, and mom packages.  Thanks to Edmond Orignac.

o Manual section titles for man pages (those that appear by default in
  the page header, like "General Commands Manual") are now localized for
  Czech, German, French, Italian, and Swedish.

o The semantics of the environment variable SOURCE_DATE_EPOCH to groff,
  support for which was added in 1.22.4, were not established at that
  time with respect to time zone selection, prompting divergent
  interpretations; Debian and distributions derived from it have for
  several years patched groff to implicitly use UTC as the time zone
  when interpreting the current time (or SOURCE_DATE_EPOCH) as a local
  time.  While a convenient and defensible choice for reproducible build
  efforts, it runs against the grain of user expectations.  Systems
  programmers like time zone-invariant, monotonically increasing clocks;
  the broader user base usually prefers a clock that follows an
  applicable civil calendar.  groff programs now reckon
  SOURCE_DATE_EPOCH with respect to the local time zone.  Users of
  SOURCE_DATE_EPOCH may wish to also set the TZ environment variable.

o xtotroff now supports a "-d" option to specify the directory in which
  to generate font description files.

o The 'configure' option '--with-doc' that was introduced in version
  1.22.3 has been deleted again.  Its basic idea was misguided because
  each of the documents is only available in a subset of the output
  formats, so in contrast to the documentation, the option not only
  affected which output formats were generated, but also restricted the
  documentation content the user would get in erratic and surprising
  ways.  The option was also ill-designed insofar as the "examples"
  keyword did not represent an output format.  Some example files were
  controlled by the "examples" keyword alone, some by the respective
  format keywords alone, and some by a combination of both.  The
  implementation of the option was full of bugs, but few, if any, of
  these bugs were ever reported by users, giving the impression that
  few, if any, users ever attempted to use the option, and those who did
  likely remained unaware that doing so deprived them of parts of the
  content of the documentation.  Experience has demonstrated that
  properly maintaining and testing the option exceeds the amount of
  effort the GNU troff team is able to invest.  Finally, GNU standards
  contain no recommendation to support this option, and indeed, few, if
  any, GNU packages apart from groff support it.

o The 'doc' Make target has been eliminated.  'all' (the default Make
  target) assumes responsibility for generating the groff Texinfo manual
  in all formats supported by the build host.  This change is only
  significant when building from a Git checkout or if our Texinfo
  manual's sources are modified; the distribution archive now provides
  copies of the manual in Info, plain text, HTML, DVI, and PDF.

o afmtodit no longer writes file names with directory information in
  them to the "name" directives of the font descriptions it generates.
  (The `fp` request no longer accepts such names; see "troff" above.)

o afmtodit now exits with status 2 (not 1) upon usage errors.

o afmtodit now recognizes a '-w' option to specify the generated font
  description's "spacewidth" parameter (see groff_font(5)).  The
  internal library "libgroff" now emits a diagnostic if a font
  description file is missing such a directive.  Adding this option
  enables a well-formed font description to be produced by the tool
  (without requiring editing by hand).

o pfbtops now exits with status 2 upon usage errors and the standard C
  library's `EXIT_FAILURE` status (usually 1) on operational failures
  instead of vice versa.

o groffer has been deleted from the distribution.

o grog no longer supports the "--warnings" option; the one diagnostic
  message that it enabled has been removed.

o The ditroff(7) man page has been deleted.  The "History" section of
  roff(7) covers the same subject in greater depth.

o The groff_filenames(5) man page has been deleted.  It had inaccuracies
  and spurious content.  The "File name conventions" section of roff(7)
  covers the same subject.

o The lj4_font(5) man page has been deleted.  Its content has moved into
  the "Fonts" subsection of grolj4(1).


VERSION 1.22.4
==============

Troff
-----

o The `hy' request has been extended.  Value 16 enables hyphenation
  before the last character, and value 32 enables hyphenation after the
  first character.

PDFPIC
------

o PDFPIC has been corrected so the behaviour is the same whether you use
  the PostScript or PDF drivers.  However, this means that any documents
  which were written using the old behaviour will not be rendered
  correctly if using the PDF driver with the new version.

  The change would mean that documents which relied on the previous
  behaviour are likely to have a gap underneath the image which was not
  there before.  If you see this effect there are three ways you can
  restore the previous behaviour:

  Add the line ".nr PDFPIC_NOSPACE 1" to the document before the first
  call to .PDFPIC.

  If it is just a single document which exhibits this behaviour you can
  run groff adding "-rPDFPIC_NOSPACE=1" to the command line.

  If you have many documents which rely on the previous behaviour you
  can set an environment variable "export GROFF_PDFPIC_NOSPACE=1" which
  will restore the previous behaviour for all runs.

  This change has no effect if you were using .PDFPIC with the
  PostScript driver--only if you used it with the PDF driver.

Gropdf
------

o Type 1 font loading is fixed to handle newer Ghostscript versions.

o Handling of glyphs above position 255 is improved to allow many more
  glyphs to be used.

o New macros .pdftransition and .pdfpause are introduced to allow
  creation of presentation slides.  Partially backward-compatible with
  present.tmac, specifically the PAUSE, BLOCKS and BLOCKE commands.
  Supports all the transition types introduced in PDF v1.5 (see the
  gropdf man page).

Miscellaneous
-------------

o A new 'configure' option --with-compatibility-wrappers controls how
  groff compatibility wrappers for vendor-provided non-GNU macro sets
  are installed (see ./configure --help).

o eqn2graph, grap2graph, and pic2graph now attempt to adapt to very old
  installed versions of the ImageMagick and GraphicsMagick programs
  "convert".  They search the output of convert's "-help" option, and
  use "-trim" if that string is found; otherwise, the old "-crop 0x0"
  method (which produces incompatible results on versions that _do_
  support "-trim") is used.  The programs emit a warning to standard
  error if the search fails and the old method is used.

o eqn2graph no longer supports the "-unsafe" option.  It did nothing.

o groffer now supports the output of XHTML.  Use the "--xhtml" or
  "--mode=xhtml" command-line options to generate it.

o Much work has been done, and is ongoing, to make groff's man pages
  better examples for man page writers to follow.  groff_man(7) itself
  has been expanded and largely rewritten to more precisely document the
  macro package's behavior and to be more helpful and accessible to man
  page writers who may never read any other groff documentation.


VERSION 1.22.3
==============

Gxditview
---------

o X11 resources for `gxditview', which were previously installed in
  /usr/X11/lib/X11/app-defaults no matter which `prefix' was set, are
  now installed in appresdir=$prefix/lib/X11/app-defaults.  If
  `appresdir' is not a standard X11 resource directory, the environment
  variable XFILESEARCHPATH should be set to this path.  The standard
  default directories depends on the system `libXt'.  Common directories
  include:
 
   /usr/lib/X11/app-defaults
   /usr/share/X11/app-defaults
   /etc/X11/app-defaults

  Note that if the option `--with-appresdir' is passed to `configure',
  the `prefix' will not be added to `appresdir'.

Glilypond
---------

o This new preprocessor (contributed by Bernd Warken) allows embedding
  of code for GNU LilyPond (http://www.lilypond.org), a music
  typesetter.  The data gets automatically processed and embedded as EPS
  images.

Gperl
-----

o Bernd Warken contributed a new preprocessor to handle Perl code that
  can be evaluated and then processed by groff.

Gpinyin
-------

o Another preprocessor from Bernd Warken to pretty-print Pinyin
  syllables like `guo2wang2' as `guówáng'.

Pdfroff
-------

o The pdfroff utility script now activates its `--no-toc-relocation'
  option by default, unless a request similar to:

     .if !\n[PHASE] .tm pdfroff-option:set toc_relocation=enabled

  is invoked during input file processing; (`.if !\n[PHASE] ...' ensures
  that the effect of the `.tm' request is restricted to the document
  setup phase of processing, as pdfroff sets it to 1 or 2 in the output
  phase, but leaves it unset in the setup phase).

  The bundled `spdf.tmac' macro package, which implicitly activates
  `-mpdfmark' for `ms' macro users, ensures that TOC relocation is
  appropriately enabled, when the `.TC' macro is invoked.

Macro Packages
--------------

o New default values for hyphenation.  The previous values were too
  strict, suppressing some hyphenation points unnecessarily.

o The -mom macro package now has full support for eqn, pic, and tbl, as
  well as captioning and labelling of PDF images and preprocessor
  output.  Lists of Figures, Equations, and Tables can now be
  autogenerated.  PDF_IMAGE has a new FRAME option.

o A French introduction to the -me macro package has been added (file
  `meintro_fr.me').

o In -mdoc, command %C is now available, providing a city or place
  reference.


VERSION 1.22.2
==============

Tbl
---

o The character `#' can now be used as an eqn delimiter within tables.

Eqn
---

o A GNU extension

    delim on

  has been added to reactivate delimiters which have been disabled with
  `delim off'.


VERSION 1.22.1
==============

(There was no release 1.22.)

Groff
-----

o A new option `-j' has been added to call the `chem' preprocessor.

Tbl
---

o Improved line numbering support.

Macro Packages
--------------

o Support for the `refer' preprocessor has been added to the -mm macro
  package.

o In -me, the `TH' macro was changed for compatibility with line number
  support in tables.

  `bl' now works inside of blocks.

  The behaviour of centered blocks has been improved.

  Line numbering support has been improved.

o The -mom macro package has reached version 2.0, focusing on PDF output
  with gropdf (using the new `pdfmom' wrapper script).  See the file
  `version-2.html' of the -mom documentation for a list of the many
  changes.

o Some generic Unicode fallback characters (mainly Roman numerals) have
  been added.

Gropdf
------

o A new driver for generating PDF output directly, contributed by Deri
  James <<EMAIL>>.  Note that this driver is written
  in Perl, thus you need a working Perl installation to run this output
  device.

Pdfmom
------

o A new wrapper around groff that facilitates the production of PDF
  documents from files formatted with the -mom macros.


VERSION 1.21
============

Troff
-----

o The new `lsm' request specifies a macro to be invoked when leading
  spaces in an input line are encountered (which are removed then).
  Number registers `lsn' and `lss' hold the number of removed leading
  spaces and the corresponding horizontal space, respectively.

o There is a new warning category `file', enabled by default.  The `mso'
  request emits warnings in this category when the requested macro file
  does not exist.

o The new `class' request assigns a short name to a set of characters
  which can be referred to in the `cflags' request.  This is especially
  useful to control line-breaking and hyphenation rules in CJK
  languages.

o Three new values for the `cflags' request have been added, which are
  needed for proper CJK support.

    128  prohibit before but allow break after character
    256  prohibit after but allow break before character
    512  allow break before and after character

Tbl
---

o A new global option `nowarn' suppresses warnings if tables are longer
  than the current line width.

Afmtodit
--------

o New option `-o' to specify the name of the output file.

Macro Packages
--------------

o A new macro `%U' has been added to the mdoc package to indicate a URL
  reference within an .Rs/.Re environment.

o Rudimentary support for the Japanese script has been added, most
  suitable for man page handling as output by grotty.  The file
  `ja.tmac' contains the necessary setup to allow line breaks before and
  after CJK characters (with proper exceptions).  Note, however, that no
  inter-character spacing is implemented yet -- this usually causes many
  warnings about bad line breaks.


VERSION 1.20.1
==============

A packaging error made it necessary to publish this release.  No
user-visible changes.


VERSION 1.20
============

Groff
-----

o XHTML support has been added to grohtml and can be specified by
  -Txhtml.  This option also utilizes the MathML capability of eqn and
  combines the outputs of both in the final XHTML file.  Users can also
  specify the `-P-V' option together with `-Txhtml' in groff.  This has
  the effect of creating an XHTML validator button at the bottom of each
  page.

o Some options have been added to control a new preprocessor,
  `preconv' (see below): `-k' activates it, `-K' sets the input
  encoding, and `-D' sets the default encoding.

o A new environment variable `GROFF_ENCODING' sets the encoding of input
  files; it implies command option `-k'.

Troff
-----

o Two new requests `device' and `devicem' have been added which are
  equivalents to the \X and \Y escapes, respectively.

o A new read-only number register `.br' is available which is set to 1
  if a macro is called as .foo and to 0 if called as 'foo.  This allows
  to reliably modify requests.

    .als bp@orig bp
    .de bp
    .  tm before bp
    .  ie \\n[.br] .bp@orig
    .  el 'bp@orig
    .  tm after bp
    ..

o A new request `fzoom' has been added to adjust the optical size of a
  font in relation to the others.  The zoom factor is given in integer
  multiples of 1/1000th.  In the following example, the CR font is
  magnified by 10% (the zoom factor is 1.1).

    .fam P
    .fzoom CR 1100
    .ps 12
    Palatino and \f[CR]Courier\f[]

  The new number register `.zoom' holds the zoom value of the current
  font, in multiples of 1/1000th.

o The `cflags' request has been extended with a new flag value 64, to be
  used in combination with values 2 (break before character) and 4
  (break after character).  If set, the hyphenation codes of the
  surrounding characters are ignored.

o A new debugging request, `pev', has been added to print all of the
  current known environments to stderr.  It first prints the state of
  the current environment, then iterates through all of the known
  environments, printing each except the one that is current.

o A new escape `\$^' has been added.  It represents the parameters of a
  macro as if they were an argument to the `ds' request.  This is used
  by `trace.tmac'.

o A new read-only number register `.O' is available which returns the
  current suppression level as set by the `\O' escape.

o The space width emitted by the `\|' and `\^' escape sequences can be
  controlled on a per-font basis.  If there is a glyph named `\|' or
  `\^', respectively (note the leading backslash), defined in the
  current font file, use this glyph's width instead of the default
  value.

  This behaviour is not new, but hasn't been documented before.

Nroff
-----

o Two new command line options `-w' and `-W' are accepted and passed to
  groff to enable and disable warning messages, respectively.

Preconv
-------

o This is a new preprocessor to convert various input encodings to
  something groff understands (this is, ASCII and \[uXXXX] entities,
  with `XXXX' a hexadecimal number with 4 to 6 digits, representing a
  Unicode input code).  Normally, preconv should be invoked with options
  `-k' and `-K' of groff.  See the preconv man page for details.

Pic
---

o int(x) now really behaves as documented: It truncates the non-integer
  part of x, this is, it rounds towards zero and not towards the next
  integer less than or equal to x.

o Pic now supports up to 32 macro arguments (and up to 16 on EBCDIC
  platforms).

o Heinz-Jürgen Örtel contributed code for two new keywords, `xslanted'
  and `yslanted', which can change the shape of boxes into arbitrary
  parallelograms.

Tbl
---

o Latest versions of DWB tbl introduced an `x' column specifier for a
  single column expanded to the line width.  GNU tbl has now been
  extended to support even multiple `x' specifiers within a table.

o To avoid collision with the new `x' specifier, a block formatting
  macro must now be selected with specifier letter `m'.

Eqn
---

o Eric S. Raymond has added a new device type to eqn, MathML.  When
  -TMathML is enabled, eqn now emits MathML formula markup rather than
  groff commands.  The new groff -Txhtml device uses this.

Chem
----

o The preprocessor `chem' was added.  `chem' is a roff language to
  generate chemical structure diagrams.  It generates `pic' output.

Grops
-----

o The PS font definition files have been regenerated with newer AFM
  versions from Adobe's 35 core fonts as present in most Level 2 PS
  printers.  The changes are minor (most notably, the addition of the
  `Euro' glyph and an extended set of kerning values).

  For backwards compatibility, the old set of font definition files is
  still available; for details please read the man page of grops.

Grotty
------

o \D'p...' is now supported if the polygon consists entirely of
  horizontal and vertical lines.

Grohtml
-------

o XHTML support has been added.

o New command line option `-V' (to be used in XHTML mode) to produce an
  XHTML validator button.

o New command line option `-y' to produce a right-justified groff
  signature at the end of the document (in combination with option
  `-V').

Gxditview
---------

o Support for keyboard navigation has been improved.

o Similar to other X11 applications, there are now two resource files,
  `GXditview' and `GXditview-color'.

Groffer
-------

o `groffer' version 1.* exists now in a shell and a Perl version.

Afmtodit
--------

o New option `-c' to output more font information as comments.

o New option `-k' to suppress output of kerning data.

o New option `-f NAME' to set the internal name of the groff font.

Macro Packages
--------------

o Joachim Walsdorff contributed the `hdtbl' package for the generation
  of tables, using a syntax very similar to the HTML table model.  For
  example, a table with two cells and two rows looks like this:

    .TBL cols=2
    .  TR .TD 1*1 .TD 1*2
    .  TR .TD 2*1 .TD 2*2
    .ETB

  Here the same table using a more expanded syntax:

    .TBL cols=2
    .  TR
    .    TD 1*1
    .    TD 1*2
    .  TR
    .    TD 2*1
    .    TD 2*2
    .ETB

  Tables can be nested; `hdtbl' works without a preprocessor so that the
  full capability of groff's macro engine is available.

  This package currently works with `-Tps' only.

o -mandoc now supports multiple man pages (in either man or mdoc
  format).

o Fabrice Ménard contributed locales support.  In particular, it is now
  possible to get French localization of the main macro packages (-ms,
  -mm, -me, and -mom, but not -man and -mdoc which are localized
  differently) by appending `-mfr' to the list of macro packages.
  Example:

    groff -ms -mfr foo > foo.ps

  Note that latin-9 input encoding is used for French (to support the
  `oe' ligature).

o Swedish macro localization (with `-msv') has been added.

o German macro localization (with `-mde' and `-mden' for traditional and
  new orthography, respectively) has been added.

o Czech macro localization (with `-mcs') has been added.

  Note that latin-2 input encoding is used for Czech.

o A new macro `Dx' has been added to the mdoc package which identifies
  the DragonFly OS.

o If mdoc is used to print multiple man pages (together with the -rcR=0
  command line option), each man page now starts a new page.

o -mtrace has been considerably improved, now showing number and string
  register assignments, among other things.  See the groff_trace man
  page for details.

o The PSPIC macro now works with all devices (producing a hollow
  rectangle on devices which don't support inclusion of PS images) and
  is loaded in troffrc at start-up.

o A new auxiliary macro package `62bit' has been added which provides
  some macros for adding, multiplying, and dividing signed 62bit
  integers (mainly to handle normal groff number operations without
  risking overflow errors).

o For -ms, Eric S. Raymond contributed support for ancient Bell Labs
  localisms `.SC', `.UC', `.P1', and `.P2'.  The latter three are
  enabled only after .SC is called.

o A new string, `SN-STYLE', has been added to the ms macros, controlling
  the formatting of section numbers in headings defined by `.NH'.

o The new macro package `ptx' provides a template definition for the
  `.xx' macro as needed by GNU ptx (for creating permuted indices).


VERSION 1.19.2
==============

Troff
-----

o Analogously to the .ft and \f pair, two new requests `gcolor' and
  `fcolor' (which pair with \m and \M, respectively) have been added to
  set the glyph and background colours.

o A new read-only, string-valued register `.sty' returns the name of the
  current style.

o Two new conditional operators `F <name>' and `S <name>' have been
  added.  `F' is true if a font <name> exists.  `S' is true if a style
  <name> has been registered.

o Cyrillic characters have been added to the `utf8' and `html' output
  devices.

Pic
---

o The `by' argument in a `for' loop can now be negative if it is
  additive.  For the multiplicative case, it must be greater than zero.

Eqn
---

o The following keywords aren't new but haven't been documented
  previously:

    undef NAME    (to undefine a macro)
    copy  "FILE"  (a synonym for `include')
    space n       (to modify the vertical spacing before and after
                   an equation)

o The following macros aren't new but haven't been documented
  previously:

    Alpha, ..., Omega   (the same as `ALPHA', ..., `OMEGA')
    ldots               (three dots on the baseline)
    dollar              (a dollar glyph)

o The following keywords have been extended.  Again, this isn't new but
  hasn't been documented previously:

    col n { ... }
    lcol n { ... }
    rcol n { ... }
    ccol n { ... }
    pile n { ... }
    lpile n { ... }
    rpile n { ... }
    cpile n { ... }   (set vertical spacing between rows to N)

Grohtml
-------

o This device driver has been raised to beta stage; its set of tags
  should be stable now.

o New command line option `-s' to set the base point size.

o New command line option `-S' to set the split level while generating
  multiple files.

Grotty
------

o Experimental support for zero-width and double-width characters.

Gxditview
---------

o On platforms which have the X Window System this program is now built
  and installed automatically.

Xtotroff
--------

o This program to create font definition files for xditview isn't new
  but hasn't been installed previously.

Groffer
-------

o A security problem (reported as CAN-2004-0969) has been fixed.

Gdiffmk
-------

o A new script contributed by Mike Bianchi.  It compares two groff,
  nroff, or troff documents and creates an output with added margin
  characters (using `.mc') to indicate the differences.

Pdfroff
-------

o A new wrapper script contributed by Keith Marshall to easily create
  PDF documents with groff.

Macro packages
--------------

o ms.tmac

  . Support for fractional point sizes: A value for the `PS', `VS',
    `FPS', and `VPS' register larger than or equal to 1000 is always
    divided by 1000.  For example, `.nr PS 10250' sets the document's
    font size to 10.25 points.

  . The `Ds' and `De' macros provided in ms since groff version 1.19
    have been removed; the equivalent `DS' and `DE' macros should be
    used instead.  X11 documents which actually use `Ds' and `De' always
    load a specific macro file from the X11 distribution (`macros.t')
    which provides proper definitions for the two macros.

  . The following registers have been added for improving layout
    control:

    PORPHANS
      Defines number of lines following `LP', `PP', `QP', `IP' or `XP'
      which must be kept together, before any automatic page break.

    HORPHANS
      Sets number of lines of following paragraph which must be kept
      with a heading, defined by `NH' or `SH', before any automatic page
      break.

    GROWPS
      Sets the first level of heading (set with `NH') which keeps the
      same point size as body text.

    PSINCR
      Sets the point size increment for each level of heading (set with
      `NH'), below the threshold level set by `GROWPS'; e.g., if
      \n[PS] = 10, \n[GROWPS] = 3 and \n[PSINCR] = 2.0p, then `.NH 1'
      produces 14pt headings, `.NH 2' produces 12pt, and all other
      levels remain at 10pt (because \n[PS] = 10).

  . The `SH' macro now accepts a numeric argument, to make heading size
    match that of `NH' with same argument value when the
    `GROWPS'/`PSINCR' feature is enabled.

  Please refer to the documentation of the ms package for other, minor
  improvements.

o me.tmac

  The section type set with the `++' request is available in the `_M'
  register.  This isn't new but hasn't been documented before.

o www.tmac

  The `HR' macro no longer causes an empty line for non-HTML devices.

  A new macro `HEAD' has been added to directly add data to the
  <head>...</head> block.

  New macros `OLS' and `OLE' to start and end an ordered list.

  New macros `DLS' and `DLE' to start and end a definition list.

Pdfmark
-------

o A new macro package contributed by Keith Marshall which implements PDF
  marks.  This is in alpha stage currently.

Miscellaneous
-------------

o Two new keywords to the DESC file have been added which are needed for
  grohtml: `image_generator' and `unscaled_charwidths'.  The former
  gives the name of the program which creates PNG images, and the latter
  makes troff always use unscaled character widths.


VERSION 1.19.1
==============

Groff
-----

o The argument of the command line option `-I' is now also passed to
  troff and grops, specifying a directory to search for files on the
  command line, files named in `so' and `psbb' requests, and files named
  in \X'ps: file' and \X'ps: import' escapes.

o If option `-V' is used more than once, the commands are both printed
  on standard error and run.

Troff
-----

o Two new read-only, string-valued registers `.m' and `.M' return the
  name of the current drawing and background color, respectively.

o New read-only register `.U' which is set to 1 if in unsafe mode, and 0
  otherwise.

o An input encoding file for latin-5 (a.k.a. ISO 8859-9) has been added.
  Example use:

    groff -Tdvi -mlatin5 my_file > my_file.dvi

  Note that some output devices don't support all glyphs of this
  encoding.

o If the `return' request is called with an argument, it exits twice,
  namely the current macro and the macro one level higher.  This is used
  to define a wrapper macro for `return' in trace.tmac.

o For completeness, two new requests have been added: `dei1' and `ami1'.
  They are equivalent to `dei' and `ami', respectively, but the macros
  are executed with compatibility mode off (similar to `de1' and `am1').

o New command line option `-I' to specify a directory for files (both
  those on the command line and those named in `psbb' requests).  This
  is also handled by the groff wrapper program.

o Since version 1.19 you can say `.vs 0'.  Older versions emit a warning
  and convert this to `.vs \n[.V]'.

  This hasn't been documented properly.  Note that `.vs 0' isn't saved
  in a diversion since it doesn't result in vertical motion.

Pic
---

o Dashed and dotted ellipses have been implemented.

Tbl
---

o New specifier `x' to make tbl call a user-defined macro on a table
  cell.  Patch by Heinz-Jürgen Oertel <<EMAIL>>.

Grap2graph
----------

o A new script contributed by Eric S. Raymond <<EMAIL>>.  It
  converts a grap diagram into a cropped image.  Since it uses gs and
  the PNM library, virtually all graphics formats are available for
  output.  [Note that the grap program itself isn't part of the groff
  package; see the file MORE.STUFF how to obtain grap.]

Grohtml
-------

o New option `-j' to emit output split into multiple files.

Grops
-----

o New command line option `-I' to specify a directory to search for
  files on the command line and files named in \X'ps: import' and \X'ps:
  file' escapes.  This is also handled by the groff wrapper program.

o The default value for the `broken' keyword in the DESC file is now 0.

Grolj4
------

o A new man page `lj4_font(5)' documents how fonts are accessed with
  grolj4.

o The built-in fonts for LJ4 and newer PCL 5 devices have been
  completely revised, mainly to access as much glyphs as possible.  The
  provided metric files should be compatible with recent PCL 5 printers
  also.  Additionally, font description files have been added for the
  Arial and Times New Roman family, the MS symbol, and Wingdings fonts.

Afmtodit
--------

o New option `-x' to prevent use of built-in Adobe Glyph List.

Hpftodit
--------

o Completely revised to handle HP TrueType metric files also.  See the
  hpftodit manual page for more details.

Groffer
-------

o This version is a rewrite of groffer in many parts, but it is kept in
  the old single script style.

  New options: --text, --mode text, --tty-viewer, --X, --mode X,
  --X-viewer, --html, --mode html, --html-view, --apropos-data,
  --apropos-devel, --apropos-progs.

  New documentation file: README_SH.

  Enhancement of the configuration files and the `apropos' handling.

Macro Packages
--------------

o www.tmac: New macro `JOBNAME' to split output into multiple files.

o In mdoc, multiple calls to `.Lb' are now supported in the LIBRARY
  section.


VERSION 1.19
============

Troff
-----

o Input encoding files for latin-9 (a.k.a. latin-0 and ISO 8859-15) and
  latin-2 (ISO 8859-2) have been added.  Example use:

    groff -Tdvi -mlatin9 my_file > my_file.dvi

  You still need proper fonts with the necessary glyphs.  Out of the
  box, the groff package supports latin-9 only for -Tps, -Tdvi, and
  -Tutf8, and latin-2 only for -Tdvi and -Tutf8.

o Composite glyphs are now supported.  To do this, a subset of the Adobe
  Glyph List (AGL) Algorithm as described in

    http://partners.adobe.com/public/developer/opentype/index_glyph.html

  is used to construct glyph names based on Unicode character codes.
  The existing groff glyph names are frozen; no glyph names which can't
  be constructed algorithmically will be added in the future.

  The \[...] escape sequence has been extended to specify multiple glyph
  components.  Example:

    \[A ho]

  this accesses a glyph with the name `u0041_0328'.

  Some groff glyphs which are useful as composites map to `wrong'
  Unicode code points.  For example, `ho' maps to U+02DB which is a
  spacing ogonek, whereas a non-spacing ogonek U+0328 is needed for
  composite glyphs.  The new request

    .composite from to

  changes the mapping while a composite glyph name is constructed.
  To make \[A ho] yield the expected result,

    .composite ho u0328

  is needed.  [The new file `composite.tmac' loaded at start-up already
  contains proper calls to `.composite'.]

  Please refer to the info pages of groff and to the groff_char man page
  for more details.

o A new request `fschar' has been added to define font-specific fallback
  characters.  They are searched after the list of fonts declared with
  the `fspecial' request but before the list of fonts declared with
  `special'.

o Fallback characters defined with `fschar' can be removed with the
  new `rfschar' request.

o A new request `schar' has been added to define global fallback
  characters.  They are searched after the list of fonts declared with
  the `special' request but before the already mounted special fonts.

o In groff versions 1.18 and 1.18.1, \D'f ...' didn't move the current
  point horizontally.  Despite of being silly, this change has been
  reverted for backwards compatibility.  Consequently, the intermediate
  output command `Df' also moves the position horizontally again.

  \D'f ...' is deprecated since it depends on the horizontal motion
  quantum of the output device (given with the `hor' parameter in the
  DESC file).  Use the new \D'Fg ...' escape instead.

o For orthogonality, new \D subcommands to change the fill color are
  available:

    \D'Fr ...' (rgb)
    \D'Fc ...' (cmy)
    \D'Fg ...' (gray)
    \D'Fk ...' (cmyk)
    \D'Fd'     (default color)

  The arguments are the same as with the `defcolor' request.  The
  current position is *not* changed.

o The values set with \H and \S are now available in number registers
  \n[.height] and \n[.slant], respectively.

o The `.pe' number register isn't new but hasn't been documented before.
  It is set to 1 during a page ejection caused by the `bp' request.

o The new glyph symbol `tno' is a textual variant of `no'.

o The new glyph symbol `+e' represents U+03F5, GREEK LUNATE EPSILON
  SYMBOL.  (Well, it is not really new since it has been previously
  supported by grolj4.)  The mapping for both the dvi and lj4 symbol
  font has been changed accordingly so that Greek small letter epsilon,
  `*e', has the same glyph shape as with other devices.

Grops
-----

o The font `freeeuro.pfa' has been added to provide various default
  glyph shapes for `eu' and `Eu'.

o It is now possible to access all glyphs in a Type 1 font, not only 256
  (provided the font file created by afmtodit has proper entries).
  grops constructs additional encoding vectors on the fly if necessary.

o The paper size is now emitted via the %%DocumentMedia and PageSize
  mechanisms so that it is no longer required to tell `gv' or `ps2pdf'
  about the paper size.  The `broken' flag value 16 omits this feature
  (the used PostScript command `setpagedevice' is a LanguageLevel 2
  extension) -- if you intend to further process grops output to get an
  encapsulated PS (EPS) file you must also use this option.

  Patch by Egil Kvaleberg <<EMAIL>>.

o Non-slanted PostScript metrics have been changed again; they no longer
  contain negative left italic correction values.  This assures correct
  spacing with eqn.

Grodvi
------

o The font cmtex10 has been added as the special font `SC' to the DVI
  fonts.  It is used as a font-specific special font for CW and CWI.

o New options -l and -p to set landscape orientation and the paper size.
  grodvi now emits a `papersize' special which is understood by DVI
  drivers like dvips.

  Consequently, the DESC file should contain a `papersize' keyword.

o The glyph shapes for \[*f] and \[*e] have been exchanged with \[+f]
  and \[+e], respectively, to be in sync with all other devices.

o Glyphs \[HE] and \[DI] have been replaced with \[u2662] and \[u2661],
  respectively, since the former two glyphs have a black (filled) shape
  which grodvi doesn't provide by default (it never has actually).

Grolj4
------

o The glyphs \[*e] and \[+e] have been exchanged to be in sync with all
  other devices.

o The glyph \[~=] is now called \[|=].  Similar to other devices, \[~=]
  is now another name for glyph \[~~].

Grotty
------

o New option `-r'.  It is similar to the -i option except it tells
  grotty to use the `reverse video' attribute to render italic fonts.

Pic
---

o New command `figname' to set the name of a picture's output box in TeX
  mode.

Refer
-----

o The environment variable `REFER' to override the name of the default
  database isn't new but hasn't been documented before.

Soelim
------

o New option `-r' to avoid emission of `.lf' lines.

o New option `-t' to emit TeX comment lines (giving current file and the
  line number) instead of `.lf' lines.

Afmtodit
--------

o Unencoded glyphs in an AFM file are output also (since grops can now
  emit multiple encoding vectors for a single font).

o New option `-m' to prevent negative left italic correction values.

o The mapping and encoding file together with file `DESC' are now
  searched in the default font directory also.  Please refer to the man
  page of afmtodit for more details.

Macro Packages
--------------

o Larry Kollar <<EMAIL>> and others made the man macros more
  customizable.

  . New command line options -rFT, -rIN, and -rSN to set the vertical
    location of the footer line, the body text indentation, and the
    sub-subheading indentation.

  . New command line option -rHY (similar to the ms macros) to control
    hyphenation.

  . New macros `.PT' and `.BT' to print the header and footer strings.
    They can be replaced with a customized version in `man.local'.

  . The string `HF' now holds the typeface to print headings and
    subheadings.

  . Similar to the ms macros, the LT register now defaults to LL if not
    explicitly specified on the command line.

o troff's start-up file `troffrc' now includes `papersize.tmac' to set
  the paper size with the command line option `-dpaper=<size>'.

  Possible values for `<size>' are the same as the predefined
  `papersize' values in the DESC file (only lowercase; see the
  groff_font man page) except a7-d7.  An appended `l' (ell) character
  denotes landscape orientation.  Examples: `a4', `c3l', `letterl'.

  Most output drivers need additional command line switches `-p' and
  `-l' to override the default paper length and orientation as set in
  the driver specific DESC file.

  For example, use the following for PS output on A4 paper in landscape
  orientation:

    groff -Tps -dpaper=a4l -P-pa4 -P-l -ms foo.ms > foo.ps


VERSION 1.18.1
==============

Troff
-----

o The non-slanted PostScript font definition files have been regenerated
  to include left and right italic correction values.  Applying those to
  a glyph (this is, prepending the glyph with `\,' and appending `\/' to
  the glyph) sets the glyph width to the real value given by the
  horizontal bounding box values.  Without those escapes, the advance
  width for the particular glyph is used (which can differ
  considerably).

  Most users will neither need this feature nor notice a difference in
  existing documents (provided \, and \/ is used as advertised, namely
  for italic fonts only); its main goal is to improve image generation
  with grohtml.

  This is an experimental change, and feedback is welcome.

Tbl
---

o Added global option `nospaces' to ignore leading and trailing spaces
  in data items.

Grolbp
------

o The option -w (--linewidth) has been added (similar to other device
  drivers) to set the default line width.

Grn
---

o Support for b-spline and Bezier curves has been added.

Groffer
-------

o New option `--shell' to select the shell under which groffer shall
  run.

Macro Packages
--------------

o The string `Am' (producing an ampersand) has been added to mdoc for
  compatibility with NetBSD.

o `.IX' is now deprecated for mom; you should use `.IQ' (Indent Quit)
  instead.

o In mom, new inlines `FWD', `BCK', `UP', and `DOWN' deal with
  horizontal and vertical movements; please refer to contrib/mom/NEWS
  for more details.

o New macro ENDNOTES_HDRFTR_CENTER for mom to better control headers.

Miscellaneous
-------------

o The `papersize' keyword in the DESC file now accepts multiple
  arguments.  It is scanned from left to the right, and the first valid
  argument is used. This makes it possible to provide a fallback paper
  size.

  Example:

    papersize /etc/papersize a4

o A local font directory has been prepended to the default font path; it
  defaults to /usr/local/share/groff/site-font.  Similar to the normal
  font searching process, files must be placed into a devXXX
  subdirectory, e.g.

    /usr/local/share/groff/site-font/devps/FOO

  for a PostScript font definition file FOO.


VERSION 1.18
============

************************************************************************
 PLEASE READ THE CHANGES BELOW REGARDING GROTTY, GROFF'S TTY FRONTEND.
************************************************************************

Troff
-----

o Color support has been added to troff and pic (and to the device
  drivers grops, grodvi, grotty, and grohtml -- other preprocessors and
  drivers will follow).  A new function `defcolor' defines colors; the
  escape sequence `\m' sets the drawing color, the escape sequence `\M'
  specifies the background color for closed objects created with \D'...'
  commands.  `\m[]' and `\M[]' switch back to the previous color.  `\m'
  and `\M' correspond to the new troff output command sets starting with
  `m' and `DF'.  The device-specific default color is called `default'
  and can't be redefined.

  Use the `color' request to toggle the usage of colors (default is on);
  the read-only register `.color' is 0 if colors are not active, and
  non-zero otherwise.

  The old `Df' output command is mapped onto `DFg'; all color output
  commands don't change the current font position (consequently, `Df'
  doesn't either).

  Outputting color can be disabled in troff and groff with the option -c
  (it is always disabled in compatibility mode).  See the section on
  grotty for the GROFF_NO_SGR environment variable also.

  For defining color components as fractions between 0 and 1, a new
  scaling indicator `f' has been introduced: 1f = 65536u.  For testing
  whether a color is defined (with .if and .ie), a new conditional
  operator `m' is available.

  More details can be found in the groff_diff.7 manual page and in
  groff.texinfo.

o Similar to \m and \M, \f[] switches back to the previous font.  \fP
  (and \f[P]) is still valid for backwards compatibility.

o The new escape \F is the same as `.fam'; \F[] switches back to
  previous family -- \F[P] selects family `P'.

o Two new glyph symbols are available: `eu' is the official Euro symbol;
  `Eu' is a font-specific glyph variant.

o The new glyph symbols `t+-', `tdi', and `tmu' are textual variants of
  `+-', `di', and `mu', respectively.

o Latin-1 character 181 (PS name `mu', Unicode name U+00B5 MICRO SIGN)
  has got the troff glyph name `mc'.

o -Tutf8 is now available on EBCDIC hosts.

o Strings can take arguments, using this syntax: \*[foo arg1 arg2 ...].
  Example:

    .ds xxx This is a \\$1 test.
    \*[xxx nice]

o It is now possible to have whitespace between the first and second dot
  (or the name of the ending macro) to end a macro definition.  Example:

    .de !
    ..
    .
    .de foo
    .  nop Hello, I'm `foo'.
    .  nop I will now define `bar'.
    .  de bar !
    .    nop Hello, I'm `bar'.
    .  !
    ..

o `.fn' is a new string-valued register that returns the resolved font
  font name; a font family and abstract style are catenated.

o Three new read/write registers `seconds', `minutes', and `hours'
  contain the current time, set at start-up of troff.  Use the `af'
  request to control their output format.

o The new request `fchar' can be used to provide fallback characters.
  It has the same syntax as the `char' request; the only difference is
  that a character defined with `.char' hides the glyph with the same
  name in the current font, whereas a character defined with `.fchar' is
  checked only if the particular glyph isn't found in the current font.
  This test happens before checking special fonts.

o In analogy to the `tmc' request, `.writec' is the same as `.write' but
  doesn't emit a final newline.

o The new request `itc' is a variant of `.it' for which a line
  interrupted with \c counts as one input line.

o Two new requests `ds1' and `as1' which are similar to `ds' and `as'
  but with compatibility mode disabled during expansion of strings
  defined by them.

o The syntax of the `substring' request has been changed: The first
  character in a string now has index 0, the last character has index
  -1.  Note that this is an incompatible change.

o To emit strings directly to the intermediate output, a new `output'
  request has been added; it is similar to `\!' used at the top level.

o `.hpf' has been extended.  It can now handle most TeX hyphenation
  pattern files without modification.  To do that, the commands
  \patterns, \hyphenation, and \endinput are recognized.  Please refer
  to groff_diff.7 for more information.

o `hpfcode' is a new request to provide an input encoding mapping for
  the `hpf' request.

o The new request `hpfa' appends hyphenation patterns (`hpf' replaces
  already existing patterns).

o A new request `ami' (append macro indirect) has been added.  The first
  and second parameter of `ami' are taken from string registers rather
  than directly; this very special request is needed to make
  `trace.tmac' independent from the escape character (which might even
  be disabled).

o The new request `sizes' is similar to the `sizes' command in DESC
  files.  It expects the same syntax; the data must be on a single line,
  and the final `0' can be omitted.

o `trin' (translate input) is a new request which is similar to `tr'
  with the exception that the `asciify' request uses the character code
  (if any) before the character translation.  Example:

    .trin ax
    .di xxx
    a
    .br
    .di
    .xxx
    .trin aa
    .asciify xxx
    .xxx

  The result is `x a'.  Using `tr', the result would be `x x'.

o The request `pvs' isn't new, but hasn't been documented before.  It
  adds vertical space after a line has been output.  This makes it an
  alternative to the `ls' request to produce double-spaced documents.
  The read-only register `.pvs' holds the current amount of the
  post-vertical line space.

o For compatibility with plan 9's troff, multiple `pi' requests are
  supported:

    .pi foo
    .pi bar

  is now equivalent to

    .pi foo | bar

o A new escape sequence `\O' is available to disable and enable glyph
  output.  Please see groff_diff.7 and groff.texinfo for more details.

o The escapes `\%', `\&', `\)', and `\:' no longer cause an error in \X;
  they are ignored now.  Additionally `\ ' and `\~' are converted to
  single space characters.

o The default tab distance in nroff mode is now 0.8i to be compatible
  with Unix troff.

o Using the latin-1 input character 0xAD (soft hyphen) for the `shc'
  request was a bad idea.  Instead, it is now translated to `\%', and
  the default hyphenation character is again \[hy].  Note that the glyph
  \[shc] is not useful for typographic purposes; it only exists to have
  glyph names for all latin-1 characters.

Macro Packages
--------------

o Peter Schaffter <<EMAIL>> has contributed a new major macro
  package called `mom', mainly for non-scientific writers, which takes
  care of many typographic issues.  It comes with a complete reference
  (in HTML format) and some examples.  `mom' has been designed to format
  documents for PostScript output only.

o Two macros `AT' (AT&T) and `UC' (Univ. of California) have been added
  to the man macros for compatibility with older BSD releases.

o Both the man and mdoc macro packages now use the LL and LT registers
  for setting the line and title length, respectively (similar to those
  registers in the ms macro package).  If not set on the command line or
  in a macro file loaded before the macro package itself, they default
  to 78n in nroff mode and 6.5i in troff mode.

o The `-xwidth' specifier in the mdoc macro package has been removed.
  Its functionality is now integrated directly into `-width'.
  Similarly, `-column' has been extended to provide this functionality
  also.

o A new macro `Ex' has been added to the mdoc macro package to document
  an exit status.

o The PSPIC macro has been extended to work with DVI output
  (`pspic.tmac' is now automatically loaded for -Tdvi), using a dvips
  special to load the EPS file.

o The trace.tmac package now traces calls to `am' also.  Additionally,
  it works in compatibility mode.

o `troff.1' has been split.  Differences to Unix troff are now
  documented in the new man page `groff_diff.7'.

o `groff_mwww.7' has been renamed to `groff_www.7'.  The file mwww.tmac
  has been removed.

o `groff_ms.7' has been completely rewritten.  It now contains a
  complete reference to the ms macros.

o `groff_trace.7' documents the trace macro package.

o Changes in www.tmac:

    Note that HTML support is still in alpha change, so it is rather
    likely that both macro names and macro syntax will change.  Some of
    the macros mentioned below aren't really new but haven't been
    documented properly before.

    The following macros have been renamed:

        MAILTO     -> MTO
        IMAGE      -> IMG
        LINE       -> HR

    For consistency, the macros `URL', `FTL', and `MTO' now all have the
    address as the first parameter followed by the description.

    By default, grohtml generates links to all section headings at the
    top of the document.  Use the new `LK' macro to specify a different
    place.

    For specifying the background color and a background image, use the
    new macros `BCL' and `BGIMG', respectively.

    The macro `NHR' has been added; it suppresses the generation of top
    and bottom rules which grohtml emits by default.

    The new macro `HX' determines the cut-off point for automatic link
    generation to headings.

    The image position parameter names in `IMG' have been changed to
    `-L', `-R', and `-C'.

    New macro `PIMG' for inclusion of a PNG image (it automatically
    converts it into an EPS file if not -Thtml is used).

    New macro `MPIMG' for putting a PNG image into the left or right
    margin (it automatically converts it into an EPS file if not -Thtml
    is used).

    New macros `HnS', `HnE' to start and end a header line block.

    New macro `DC' to produce dropcap characters.

    New macro `HTL' to generate an HTML title line only but no H1
    heading.

    New macros `ULS' and `ULE' to start and end an unordered list.  The
    new macro `LI' inserts a list item.

Groff
-----

o The new command line option `-c' disables color output (which is
  always disabled in compatibility mode).

Nroff
-----

o Two new command line options `-c' and `-C'; the former passes `-c' to
  grotty (switching to the old output scheme); the latter passes `-C' to
  groff (enabling compatibility mode).

Pic
---

o New keywords `color' (or `colour', `colored', `coloured'), `outline'
  (or `outlined'), and `shaded' are available.  `outline' sets the color
  of the outline, `shaded' the fill color, and `color' sets both.

  Example:

    circle shaded "green" outline "black" ;

  Filled arrows always use the outline color for filling.

  Color support for TeX output is not implemented yet.

Pic2graph
---------

o A new script contributed by Eric S. Raymond <<EMAIL>>.  It
  converts a PIC diagram into a cropped image.  Since it uses gs and the
  PNM library, virtually all graphics formats are available for output.

Eqn2graph
---------

o A new script contributed by Eric S. Raymond <<EMAIL>>.  It
  converts an EQN diagram into a cropped image.  Since it uses gs and
  the PNM library, virtually all graphics formats are available for
  output.

Groffer
-------

o A new script contributed by Bernd Warken <<EMAIL>>.  It
  displays groff files and man pages on X and tty, taking care of most
  parameters automatically.

Grog
----

o Documents using the mom macro package are recognized.

Grops
-----

o Color support has been added.

o A new option `-p' is available to select the output paper size.  It
  has the same syntax as the new `papersize' keyword in the DESC file.

Grodvi
------

o By default, font sizes are now available in the range 5-10000pt,
  similar to PS fonts. If you want the old behaviour (i.e., font sizes
  at discrete values only), insert the following at the start of your
  document:

    .if '\*[.T]'dvi' \
    .  sizes *********** 800 900 1000 1095 1200 1400 1440 1600 \
             1728 1800 2000 2074 2200 2400 2488 2800 3600

o A new font file HBI (using cmssbxo10; this is slanted sans serif bold
  extended) has been added.

o Two font families are now available: `T' and `H'.

o EC and TC fonts have been integrated.  Use `-mec' (calling the file
  ec.tmac) to switch to them.  Those fonts give a much better coverage
  of the symbols defined by groff than the CM fonts.

  Note that ec.tmac must be called before any language-specific files;
  it doesn't take care of hcode values.

o Color support has been added.  For drawing commands, colors are
  translated to gray values currently.

Grotty
------

o Color support has been added, using the SGR (ISO 6429, sometimes
  called ANSI color) escape sequences.

o SGR escape sequences are now used by default for underlining and bold
  printing also, no longer using the backspace character trick.  To
  revert to the old behaviour, use the `-c' switch.

  Note that you have to use the `-R' option of `less' to make SGR
  escapes display correctly.  On the other hand, terminal programs and
  consoles like `xterm' which support SGR sequences natively can
  directly display the output of grotty.  Consequently, the options
  `-b', `-B', `-u', and `-U' work only in combination with `-c' and are
  ignored silently otherwise.

  For the `man' program, it may be necessary to add the `-R' option of
  `less' to the $PAGER environment variable (or $MANPAGER, depending on
  the used `man' program); alternatively, you can use `man's `-P' option
  (or adapt its configuration file accordingly).  See man(1) for more
  details.

o If the environment variable GROFF_NO_SGR is set, SGR output is
  disabled, reverting to the old behaviour.

o A new special \X'tty: sgr n' has been added; if n is non-zero or
  missing, enable SGR output (the default).

o If the new option `-i' is used (only in SGR mode), grotty sends escape
  sequences to set the italic font attribute instead of the underline
  attribute for italic fonts.  Note that many terminals don't have
  support for this (including xterm).

Grohtml
-------

o Color support for glyphs has been added.

o New option `-h' to select the style of headings in HTML output.

o New option `-b' to set the background colour to white.

o New options `-a' and `-g' to control the number of bits for
  anti-aliasing used for text and graphics, respectively.  Default value
  is 4; 0 means no anti-aliasing.

o groff character/glyph entities now map onto HTML 4 character entities.

Grolbp
------

o Valid paper sizes are now specified as with the new `papersize'
  keyword in the DESC file.  Specifically, the old custom paper type
  format `custAAAxBBB' is no longer supported.

Miscellaneous
-------------

o A new manual page `ditroff.7' is available.

o The groff texinfo manual is installed now, together with a bunch of
  examples.

o A new keyword `papersize' has been added to the DESC file format.  Its
  argument is either

  . a predefined paper format (e.g. `A4' or `letter')

  . a file name pointing to a file which must contain a paper size
    specification in its first line (e.g. `/etc/papersize')

  . a custom paper size definition like `35c,4i'

  See groff_font(5) for more details.  This keyword only affects the
  physical dimensions of the output medium; grops, grolj4, and grolbp
  use it currently.  troff completely ignores it.


VERSION 1.17.2
==============

This is major bug-fixing release which should replace 1.17.1.

Troff
-----

o The `IMAGE' macro in www.tmac has changed: Now the optional 2nd
  parameter gives the horizontal image location (left, centered, or
  right), and the optional 3rd and 4th parameter the image dimensions.


VERSION 1.17.1
==============

This is mainly a bug-fixing release.

Troff
-----

o Two new requests `de1' and `am1' which are similar to `de' and `am'
  but with compatibility mode disabled during expansion of macros
  defined by them.

o Added request `brp'.  This is the same as `\p'.

o Similar to other versions of troff, the `ns' request now works in all
  diversions, not only in the top-level one.

o New read-only number register `.ns'.  Returns 1 if in no-space mode, 0
  otherwise.

Nroff
-----

o Options -p (pic) and -t (tbl) added.

o The environment variable GROFF_BIN_PATH is now checked before PATH for
  finding groff.

Grohtml
-------

o New option `-D dir' to specify a directory in which all images are
  placed.

o New option `-I stem' to specify an image name stame.  If not given,
  `grohtml-XXX' is used (`XXX' is the process ID).


VERSION 1.17
============

Groff
-----

o `-mFOO' now searches first for `FOO.tmac' and then for `tmac.FOO'.
  The old behaviour has been changed to overcome problems with platforms
  which have an 8+3 file name limit, and platforms which have other
  versions of troff installed also.  Additionally, all macro files have
  been renamed using the latter scheme to avoid 8+3 name clashes.

o The new environment variable GROFF_BIN_PATH is checked for programs
  groff is calling (preprocessors, troff, and output devices) before
  PATH.  If not set, it defaults to the directory where the groff binary
  is located.  Previously, it was PATH only.  The nroff script only uses
  GROFF_BIN_PATH to find the groff binary but passes both the
  GROFF_BIN_PATH and PATH environment variables to groff.

Troff
-----

o The mdoc package has been completely rewritten, using the full power
  of GNU troff to remove limitations of Unix troff (which is no longer
  supported).  Most important changes are:

  . No argument limit
  . Almost all macros are parsed and callable (if it makes sense)
  . `.Lb': prints library names
  . `.Nm <punctuation>' now works as expected; `.Nm "" <punctuation>'
    has been withdrawn
  . Updated `.St' command
  . `.Fx': prints FreeBSD
  . `.Ox': prints OpenBSD
  . `.Bsx': prints BSD/OS
  . `.Brq', `.Bro', `.Brc': brace enclosure macros
  . `.Bd -centered': center lines
  . `.Bl -xwidth <string>': interpret <string> and use the resulting
    width
  . Support for double-sided printing (-rD1 command line switch)
  . Support for 11pt and 12pt document sizes (-rS11, -rS12 command line
    switches)

  `groff_mdoc.7' replaces `groff_mdoc.samples.7'; it now completely
  documents the mdoc package.

  Great care has been taken to assure backwards compatibility.  If you
  encounter any abnormal results, please report them to
  <EMAIL>.  [2018 UPDATE: This address no longer accepts bug
  reports; please use the GNU Savannah bug tracker at
  http://savannah.gnu.org/bugs/?group=groff.]

o A new command line option for the `man' macros (similar to the `mdoc'
  package) has been implemented: `-rcR=1' (now the default in nroff
  mode) produces one single, very long page instead of multiple pages.
  `-rcR=0' deactivates it.

o The `return' request has been added to return immediately from a
  macro.

o A new request `nop' (no operation) has been added which is similar to
  `if 1'.  For example,

    .if t \{\
    Hallo!
    .\}

  can now be written as

    .if t \{\
    .  nop Hallo!
    .\}

o `box' and `boxa' are two new requests which behave similarly to `di'
  and `da' but don't include a partially filled line (which is restored
  after ending the diversion).

o The `asciify' request has been extended to `unformat' space characters
  and some other escape sequences also.

  `\ ' is no longer unformatted as a space but remains an unpaddable,
  unbreakable space character.

o The new `unformat' request is similar to `asciify' but only handles
  space characters and tabs specially if the diversion is reread,
  retaining font information.  This makes it possible to reformat
  diversions; for example the following

    .ll 3i
    .
    a01 a02 a03 a04 a05 a06 a07 a08 a09 a10.
    .
    .box box1
    .ev 1
    .nf
    \f[B]b01 b02 b03 b04 b05 b06 b07 b08 b09 b10.\f[P]
    .br
    .ev
    .box
    .
    c01 c02 c03 c04 c05 c06 c07 c08 c09 c10.
    .
    .unformat box1
    .box1

  gives

    a01  a02  a03  a04 a05 a06 a07
    a08 a09 a10.  c01 c02 c03  c04
    c05  c06 c07 c08 c09 c10.  b01
    b02 b03 b04 b05  b06  b07  b08
    b09 b10.

  Without the `unformat' request, space characters are converted to word
  space nodes which are no longer stretchable, and the result would be

    a01  a02  a03  a04 a05 a06 a07
    a08 a09 a10.  c01 c02 c03  c04
    c05  c06 c07 c08 c09 c10.  b01
    b02 b03 b04 b05 b06 b07 b08
    b09 b10.

o The new request `linetabs' controls the `line-tabs' mode.  In
  line-tabs mode, tab distances are computed relative to the (current)
  output line.  Otherwise they are taken relative to the input line.
  For example, the following

    .ds x a\t\c
    .ds y b\t\c
    .ds z c
    .ta 1i 3i
    \*x
    \*y
    \*z

  yields

    a         b         c

  In line-tabs mode, the same code gives

    a         b                   c

  The new read-only number register `.linetabs' returns 1 if in
  line-tabs mode, and 0 otherwise.

o Two new requests `tm1' and `tmc' have been added to improve writing
  messages to the terminal.  `tm1' is similar to `tm' but allows leading
  whitespace.  `tmc' is similar to `tm1' but doesn't emit a final
  newline.

o For compatibility with sqtroff, the request `output' has been added.
  The behaviour is similar to `\!' at the top-level, that is, it
  directly inserts its argument into the intermediate output format.
  The syntax is similar to .tm1, allowing leading whitespace.

o The new `spreadwarn' request makes troff warn if spaces in an output
  line are widened by a given limit or more.

o Use `warnscale' to change the scaling indicator troff uses for warning
  messages.

o A new request `dei' (define indirect) has been added.  The first and
  second parameter of `dei' are taken from string registers rather than
  directly; this very special request is needed to make `trace.tmac'
  independent from the escape character (which might even be disabled).

o It is now possible to save and restore the escape character with two
  new requests `ecs' and `ecr'.

o The new escape sequence \B'...' is an analogon to `\A': If the string
  within the delimiters is a valid numeric expression, return character
  `1', and `0' otherwise.

o The new escape sequence `\:' inserts a zero-width break point.  This
  is similar to `\%' but without a soft hyphen character.

o The `tr' request can now map characters onto `\~'.

o Calling the `fam' request without an argument switches back to the
  previous font family.

o The new read-only register `.int' is set to a positive value if the
  last output line is interrupted (i.e., if the input line contains
  `\c').

o The `writem' request is not new, but hasn't been documented before.
  This is similar to `write' but instead of a string the contents of a
  given macro or string is written to a stream.

o The read/write number register `hp' to get/set the current horizontal
  position relative to the input line isn't new but hasn't been
  documented properly before.

o `\X' and `\Y' are now transparent for end-of-sentence recognition.

o The `cu' request in nroff mode now works as documented (i.e., it
  underlines spaces also).

Grog
----

o The grog script now works in non-compatibility mode also (which is the
  default).  As usual, use the `-C' option to activate compatibility
  mode.

Grops
-----

o A new option `-P' resp. a new environment variable `GROPS_PROLOGUE'
  has been added to select a different prologue file.

o The effect of the former `-mpsnew' option to access more Type 1
  characters is now the default and no longer available.  To get the old
  behaviour (i.e., emulation of some glyphs by composition) use
  `-mpsold'.

Miscellaneous
-------------

o For security reasons the following changes have been done:

  . The tmac.safer file has been replaced with a built-in solution;
    .open, .opena, .pso, .sy, and .pi are completely disabled in safer
    mode (which is the default); to enable these requests the `-U'
    command line flag must be used.

  . Files specified with the .mso request or given with the `-m' command
    line option, and hyphenation patterns loaded with `.hpf' are no
    longer searched in the current directory by default (besides the
    usual tmac path).  Instead, the home directory is used.  To add the
    current directory, either use the `-U' or `-M' command line option
    or set the GROFF_TMAC_PATH environment variable to an appropriate
    value.

  . troffrc, troffrc-end, and eqnrc are neither searched in the current
    nor in the home directory (even if -U is given).  Use -M or
    GROFF_TMAC_PATH to change that.

  . Similarly, the current directory is no longer part of the font path.
    Use the `-F' command line option or the GROFF_FONT_PATH environment
    variable if you really need the current directory.

o groff now installs its data files into
  /usr/local/share/groff/<version> by default, following the GNU
  standard.  Additionally, a local tmac directory (by default
  /usr/local/share/groff/site-tmac) is scanned before the standard tmac
  directory.  Wrapper files for system-specific macro packages (if
  necessary) are put into /usr/local/lib/groff/site-tmac; this directory
  is searched before the local tmac directory.

o All programs now have option `-v' to show the version number; they
  exit immediately afterwards, following the GNU standards.
  Additionally, `--version' and `--help' have been added, doing the
  obvious actions.


VERSION 1.16.1
==============

Bug fixes only; no user-visible changes.


VERSION 1.16
============

Groff
-----

The anachronism of calling the man macro package with `-man' has been
fixed; now you can say `-m man' also. The same is true for `ms', `me',
`markup', `mandoc', and `mdoc'.

A new switch `-g' for calling `grn' is available.

A new switch `-G' for calling `grap' is available.

EBCDIC support for tty devices has been added.  On such hosts, IBM code
page 1047 is available with -Tcp1047 instead of -Tascii and -Tlatin1
(and, for the moment, -Tutf8).  Note that non-tty devices are not yet
supported (but installed).

Troff
-----

A new command line option to the `man' macros is available: `-rSxx'
(with `xx' either 10, 11, or 12) to set the base document font size to
`xx' points.  Additionally, `.SH' now produces larger headings than
`.SS'.

To solve a problem with the .PSPIC macro which needs the `-U' switch of
troff to access an external program (psbb), a new request .psbb is now
available to get the bounding box of a PostScript image file.  The
values (in PostScript units) are returned in the new read-only number
registers `llx', `lly', `urx', and `ury'.  Consequently, .PSPIC has been
adapted to use the new request, and the psbb program has been removed.

A new predefined writable number register, `year', has been added.  It
contains the current year.

A new read-only register, `.Y', has been added.  It contains the
revision number of the groff package.

`\fP' now behaves as expected in situations like the following where the
font `foo' is undefined:

  .B bold text
  normal text \f[foo]bar\fP normal text

Previously, the text after \fP appeared as bold.

The `substring' request is not new, but hasn't been documented before.

The predefined `.T' string register (which holds the name of the output
device) is not new, but hasn't been documented before.

A new request `length' computes the length of a string and returns it in
a number register.

The macro files `tmac.a4' (for specifying A4 paper format) and
`tmac.trace' (a debugging aid) are now installed also.

A new resource file, `troffrc-end', is now available.  It is invoked
after all user-specified macros.  Currently used by the html device to
include tmac.html; thus no need for users to specify -mhtml anymore.

The soft hyphen character now has a glyph name: `shc'.

The latin-1 character 173 (PS name `periodcentered') has got the troff
glyph name `pc' and is no longer intermixed with the symbol character
`md' (PS name `mathdot').

ASCII character 34 (PS name `quotedbl') has got the troff glyph name
`dq' (which is an alias to character `"').

ASCII character 39 (PS name `quoteright') has got the troff glyph name
`cq' (which is an alias to character "'").

Some additions to the font description files have been implemented for
better support of HTML output:

  The new format of lines in the `charset' subsection of font
  description files is

     name metrics type code [entity_name] [-- comment]

  Currently, only the font description files in devhtml use the optional
  entity_name string to define glyph entities in HTML.  Everything after
  the entity_name field is ignored; in case this field isn't used, two
  hyphen characters are now necessary to start a comment.

  Two new requests are available in DESC files (currently used only with
  grohtml):

    use_charnames_in_special
      This command indicates that troff should encode named characters
      inside special commands.

    pass_filenames
      requests that troff tells the driver the source file name being
      processed.  This is achieved by another tcommand: `F filename'.

Grotty
------

Bruno Haible <<EMAIL>> contributed support for UTF8
output.

Grohtml
-------

Added .LINE macro to tmac.arkup.

The obsolete `.LINK' macro has been removed.

.URL, .FTP, and .MAILTO macros now accept an optional third argument
which is immediately appended to the second argument (to be used with
punctuation, for example).

Grodvi
------

The font size 11pt has been changed to 10.95pt (as used in LaTeX 2e).

A new font file CWI (using cmitt10; this is typewriter italic) has been
added.

Grolbp
------

A new driver for Canon CaPSL printers (LBP-4 and LBP-8 series laser
printers).  This code has been contributed by Francisco Andres Verdu
<<EMAIL>>.

Grn
---

A new preprocessor to process gremlin pictures.  It is based on the
original Berkeley implementation of grn, written by David Slattengren
and Barry Roitblat, and has been adapted to groff by Daniel Senderowicz
<<EMAIL>> and Werner Lemberg <<EMAIL>>.

Pic
---

Added the `srand' command to set the seed for a new sequence of
pseudo-random numbers to be returned by `rand'.

Gxditview
---------

Simplified installation: The Imakefile is now configured (by groff's
configure script).

Documentation
-------------

Three new man pages are available: groff_tmac.5 (documenting how troff
macros are accessed and where they are found), groff.7 (a short
reference of the GNU roff language), and roff.7 (a general survey on GNU
troff).

Miscellaneous
-------------

A partial port to win32 (for use with Microsoft Visual C++ 6.0) is now
part of the distribution.  It has been contributed by Blake McBride
<<EMAIL>>.

More information about programs, macros, documentation, etc., which is
related to groff has been collected in the file `MORE.STUFF'.


VERSION 1.13, 1.14, 1.15
========================

Bug fixes only; no user-visible changes.


VERSION 1.12
============

Finally, there are new maintainers for groff.  Mailing lists and a
development repository are available also. See the file README for
details.  Not all reported bugs could be fixed, so please send mails
again if something is still not working.

Most of the installation problems should have vanished now (most notably
the $(tmac_wrap) bug).

There is now a man page called groff_man.7 which documents the basics of
the -man macros.  It has been originally written by Susan G. Kleinmann
<<EMAIL>>.

A (still incomplete) groff reference manual in texinfo format originally
contributed by Trent A. Fisher <<EMAIL>>.

me.man and msafer.man have been renamed to groff_me.man resp.
groff_msafer.man for consistency.

Default strings for macros in doc-common resp. tmac.an no longer contain
the word `UNIX'.

groff should now be Y2k safe (fixes contributed by Paul Eggert
<<EMAIL>>).

Following the GNU standards, groff now uses the prefix `/usr/local/' as
the default instead of replacing an existent groff binary.

groff, troff, nroff, and pic now support the -U flag to activate unsafe
behaviour (without -msafer); the -S flag for using the -msafer macros is
now the default.

Grohtml
-------

This is a new output device for producing HTML output contributed by
Gaius Mulley <<EMAIL>>.  It is still very alpha but has been
included into the distribution so that a lot of people have a chance to
test it.  Bug reports are highly welcome.

Grolj4
------

Duplex printing support has been contributed by Jeffrey Copeland
<<EMAIL>>.

Soelim
------

Added -I option for defining include paths (patch contributed by Peter
Miller <<EMAIL>>).

Gxditview
---------

Fallback resources added (patch contributed by Larry Jones
<<EMAIL>>).

Will now support 8 gray levels.

mm
--

New version 1.32 (contributed by Joergen Haegg <<EMAIL>>).


VERSION 1.11
============

Complete documentation for pic is now in the file doc/pic.ms.  It was
contributed by Eric S. Raymond, <<EMAIL>>, who is emphatically
*not* volunteering to take over groff as he is way overworked with half
a dozen other projects.


VERSION 1.10
============

The directory where data files are installed has been changed from
/usr/local/lib/groff to /usr/local/share/groff to comply with the latest
GNU coding standards.

By default groff programs with Unix equivalents are installed with a "g"
prefix unless there is an existing (non-groff) troff installation.

A new approach is used to make system macro packages available to groff.
Instead of simply including /usr/lib/tmac in the list of directories
searched by groff, the installation process creates for each system
macro package a wrapper macro package in the groff macro directory that
references the system macro package.  The groff macro packages are now
installed with a leading "g" prefix if there is a system version of the
same macro package, and otherwise without the "g" prefix, with the
exception that the groff version of -me which is always installed as
-me.

There is a new device, lj4, for the HP LaserJet 4 (and PCL5
compatibles).

Groff
-----

groff has a -S option that prevents the use of unsafe features in pic
and troff.  This uses a new -S option of pic and the -msafer macros for
troff.

Troff
-----

The `blm' request specifies a macro to be invoked when a blank line is
encountered.

Pic
---

A -S (safer) option disables the sh command.

Grops
-----

The -m option enables manual feed.


VERSION 1.09
============

\(rn now produces a character that has the traditional metrics, and form
corners with \(ul and \(br.  This means that it does not align properly
with \(sr.  Instead there's a new character \[radicalex] which aligns
with \(sr; this is used by eqn for doing square roots.

Troff
-----

The `pso' request allows you to read from the standard output of a
command.

Grops
-----

The PSPIC macro has options to allow the horizontal alignment of the
graphic to be specified.


VERSION 1.08
============

Troff
-----

The escape sequence \V[xxx] interpolates the value of the environment
variable xxx.

Tbl
---

The decimalpoint option can be used to specify the character to be
recognized as the decimal point character in place of the default
period.


VERSION 1.07
============

Groff
-----

The environment variable GROFF_COMMAND_PREFIX can be used to control
whether groff looks for `gtroff' or `troff' (similarly for the
preprocessors.)

Troff
-----

Multilingual hyphenation is supported by new `hpf' and `hla' requests,
and by a `\n[.hla]' number register.  The -H option has been removed.
Files of hyphenation patterns can have comments.

When a font cannot be found, troff gives a warning (of type `font',
enabled by default) instead of an error.

There's a new request `trnt' that's like `tr' except that it doesn't
apply to text transparently throughput into a diversion with \!.

Tbl
---

There is a `nokeep' option which tells tbl not to use diversions to try
to keep the table on one page.

Eqn
---

Setting the parameter `nroff' to a non-zero value causes `ndefine' to
behave like `define' and `tdefine' to be ignored.  This is done by eqnrc
when the current device is ascii or latin1.  There's a `neqn' script
that just does `eqn -Tascii'.

Grotty
------

grotty uses whatever page length was specified using the `pl' request
rather than using the paperlength command in the DESC file.  The
paperwidth command in the DESC file is also ignored.


VERSION 1.06
============

The programs in groff that have Unix counterparts can now be installed
without a leading `g' prefix.  See the `g' variable in the Makefile.

The g?nroff script simulates the nroff command using groff.

New special characters \(+h, \(+f, \(+p, \(Fn, \(Bq, \(bq, \(aq, \(lz,
\(an.  See groff_char(7).

^L is now a valid input character.

Groff
-----

The Xps pseudo-device has disappeared.  Instead there is a new -X option
that tells groff to use gxditview instead of the usual postprocessor.
(So instead of -TXps, use -XTps or just -X if your default device is
ps.)

The postprocessor to be used for a particular device is now specified by
a `postpro' command in the DESC file rather than being compiled into
groff.  Similarly the command to be used for printing (with the -l
option) is now specified by a `print' command in the DESC file.

The groff command no longer specifies eqnchar as an input file for eqn.
Instead eqn automatically loads a file `eqnrc'.  The groff command no
longer passes the -D option to eqn.  Instead eqnrc sets the draw_lines
parameter.

The groff command no longer tells troff to load a device-specific macro
file.  This is handled instead by the `troffrc' file, which is always
loaded by troff.

The shell script version of groff has been removed.

Troff
-----

The `rchar' request removes a character definition established with
`char'.

Compatibility mode is disabled and the escape character is set to `\'
while a character definition is being processed.

The `\#' escape sequence is like `\"' except that the terminating
newline is ignored.

The `shc' request tells troff which character to insert (instead of the
default \(hy) when a word is hyphenated at a line break.

A font name of 0 (zero) in the DESC file causes no font to be mounted on
the corresponding font position.  This is useful for arranging that
special fonts are mounted on positions on which users are not likely
explicitly to mount fonts.  All groff devices now avoid initially
mounting fonts on positions 5-9.

The `do' request allows a single request or macro to be interpreted with
compatibility mode disabled.

troff automatically loads a file `troffrc' before any other input file.
This can be prevented with the -R option.  This file is responsible for
loading the device-specific macros.

Pic
---

The -x option has been removed and a -n option has been added.  By
default, pic now assumes that the postprocessor supports groff
extensions.  The -n option tells pic to generate output that works with
ditroff drivers.  The -z option now applies only to TeX mode.

The -p option has been removed. Instead if the -n option is not
specified, pic generates output that uses \X'ps: ...' if the \n(0p
register is non-zero and tmac.ps sets this register to 1.

In places where you could 1st or 5th you can now say `i'th or `i+1'th
(the quotes are required).

Eqn
---

Eqn now automatically reads a file `eqnrc' from the macro directory.
This performs the same role that the eqnchar files used to.  This can be
prevented by the -R option.

Setting the draw_lines parameter to a non-zero value causes lines to be
drawn using \D rather than \l.  The -D option is now obsolete.

`uparrow', `downarrow' and `updownarrow' can be used with `left' and
`right'.

The amount of extra space added before and after lines containing
equations can be controlled using the `body_height' and `body_depth'
parameters.

Grops
-----

Font description files have been regenerated from newer AFM files.  You
can get access to the additional characters present in the text fonts in
newer PostScript printers by using -mpsnew.

The default value of the -b option is specified by a `broken' command in
the DESC file.

With the -g option, grops generates PostScript code that guesses the
page height.  This allows documents to be printed on both letter
(8.5x11) and A4 paper without change.

Grodvi
------

ISO Latin-1 characters are available with -Tdvi.  Format groff_char(7)
with groff -Tdvi for more information.

Grotty
------

The -mtty-char macros contain additional character definitions for use
with grotty.

Macros
------

In previous releases the groff -me macros treated the $r and $R number
registers in a way that was incompatible with the BSD -me macros.  The
reason for this was that the approach used by the BSD -me macros does
not work with low resolution devices such as -TX75 and -TX100.  However,
this caused problems with existing -me documents.  In this release, the
vertical spacing is controlled by the $v and $V registers which have the
same meaning as $r and $R in earlier groff releases.  In addition, if
the $r or $R register is set to a value that would be correct for the
BSD -me macros and a low resolution device is not being used, then an
appropriate value for the $v or $V register is derived from the $r or $R
register.

The groff -me macros work with -C and (I think) with Unix troff.

For backward compatibility with BSD -me, the \*{ and \*} strings are
also available as \*[ and \*].  Of course, \*[ is only usable with -C.

The \*T string has been deleted.  Use \*(Tm instead.

Xditview
--------

The `n', Space and Return keys are bound to the Next Page action.  The
`p', BackSpace and Delete keys are bound to the Previous Page action.
The `q' key is bound to the Quit action.

The `r' key is bound to a rerasterize action that reruns groff, and
redisplays the current page.


VERSION 1.05
============

Pic
---

There is a alternative assignment operator `:=' which interacts
differently with blocks.

There is a new command `command', which allows the values of variables
to be passed through to troff or TeX.

The `print' command now accepts multiple arguments.

String comparison expressions (using `==' or `!=') are allowed in more
contexts.

Grotty
------

Horizontal and vertical lines drawn with \D'l ...' are rendered using -,
| and + characters.  This is intended to give reasonable results with
boxed tables.  It won't work well with pic.

Macros
------

The -mdoc macros have been upgraded to the version in the second
Berkeley networking release.  This version is not completely compatible
with earlier versions; the old version is still available as -mdoc.old.
The grog script has been enhanced so that it can usually determine
whether a document requires the old or new versions.

With -TX75, -TX100 and -TXps, the PSPIC macro produces a box around
where the picture would appear with -Tps.


VERSION 1.04
============

An implementation of the -mm macros is included.

The directory in which temporary files are created can be controlled by
setting the GROFF_TMPDIR or TMPDIR environment variables.

Pic
---

Some MS-DOS support (see pic/make-dos-dist).

Grops
-----

There are two new \X commands (\X'ps: invis' and \X'ps: endinvis') which
make it possible to have substitute characters that are displayed when
previewing with -TXps but ignored when printing with grops.

Xditview
--------

Support for scalable fonts.


VERSION 1.03
============

No changes other than bug fixes.


VERSION 1.02
============

There is an implementation of refer and associated programs.  groff -R
preprocesses with grefer; no mechanism is provided for passing arguments
to grefer because most grefer options have equivalent commands which can
be included in the file.  grog also supports refer.

There is an alternative perl implementation of the grog script.

The code field in lines in the charset section of font description files
is now allowed to contain an arbitrary integer (previously it was
required to lie between 0 and 255).  Currently grops and grodvi use only
the low order 8 bits of the value.  Grodvi uses the complete value;
however, this is unlikely to be useful with traditional TeX tools (.tfm
files only allow 8 bit character codes.)

Left and right double quotes can be obtained with \(lq and \(rq
respectively.

There is a new program called pfbtops which translates PostScript fonts
in pfb format to ASCII.

A slightly modified version of the Berkeley tmac.doc is included.

Troff
-----

In long escape names the closing ] is now required to be at the same
interpolation depth as the opening [.

The \A'S' escape sequence returns 1 or 0 according as S is or is not
suitable for use as a name.

\~ produces an unbreakable space that can be stretched when the line is
adjusted.

The `mso' request is like the `so' request except that it searches for
the file in the same directories in which tmac.X is searched for when
the -mX option is given.

The escape sequence `\R' is similar to the `nr' request.

Eqn
---

A new `special' primitive allows you to add new types of unary
constructs by writing a troff macro.

Pic
---

The implementation no longer uses gperf.

Grops
-----

The compile-time -DBROKEN_SPOOLER option has been replaced by a
BROKEN_SPOOLER_FLAGS option.  This allows more precise control over how
grops should workaround broken spoolers and previewers.  There is a new
-b option that can change this at run-time.

Grops now generates PostScript that complies with version 3.0 of the
Document Structuring Convention.

The resource management component of grops (the part that deals with
imported documents and downloadable fonts) has been rewritten and now
supports version 3.0 of the Document Structuring Conventions.  The
%%DocumentFonts comment is no longer supported; you must use the
%%Document{Needed,Supplied}{Fonts,Resources} comments instead
(or as well.)

tmac.psatk contains some macros that support the mechanism used by the
Andrew Toolkit for including PostScript graphics in troff documents.

Xditview
--------

Parts of xditview have been rewritten so that it can be used with the
output of gtroff -Tps.  groff -TXps runs gtroff -Tps with gxditview.

There is a new menu entry `Print' which brings up a dialog box for
specifying a command with which the file being previewed should be
printed.

Xditview now uses imake.


VERSION 1.01
============

The groff command now understands the gtroff `-a' and `-i' options.

With the `m' and `n' scaling indicators, the scale factor is rounded
horizontally before being applied.  This makes (almost) no difference
for devices with `hor' equal to 1, but it makes groff with -Tascii or
-Tlatin1 behave more like nroff in its treatment of these scale
indicators.  Accordingly tmac.tty now calls the `nroff' request so that
the `n' condition is true.

The device-specific macros (tmac.ps, tmac.dvi, tmac.tty and tmac.X) have
been made to work at least somewhat with -C.  In particular the special
characters defined by these macros now work with -C.

groff -Tdvi -p now passes pic the -x flag; this enables filling of
arrowheads and boxes, provided that your dvi driver supports the latest
version of the tpic specials.

Eqn
---

There is a new `-N' option that tells eqn not to allow newlines in
delimiters.  This allows eqn to recover better from missing closing
delimiters.  The groff command passes on a `-N' option to eqn.

Grops
-----

You can now use psfig with grops.  See the file ps/psfig.diff.  I do not
recommend using psfig for new documents.

The command \X'ps: file F' is similar to \X'ps: exec ...' except that
the PostScript code is read from the file F instead of being contained
within the \X command.  This was added to support psfig.

Grodvi
------

There are font files HB and HI corresponding to cmsssbx10 and cmssi10.

Macros
------

The groff -me macros now work with the -C option.  As a result, they may
also work with Unix nroff/troff.

In -me, the $r and $R number registers now contain the line spacing as a
percentage of the pointsize expressed in units (normally about 120).
The previous definition was useless with low resolution devices such as
X75 and X100.


VERSION 1.00
============

A -ms-like macro-package is now included.

The name for the Icelandic lowercase eth character has been changed from
\(-d to \(Sd.

Troff
-----

There is a new request `nroff', which makes the `n' built-in condition
true and the `t' built-in condition false; also a new request `troff'
which undoes the effect of the `nroff' request.  This is intended only
for backward compatibility: it is usually better to test \n(.H or \n(.V
or to use the `c' built-in condition.

The \R escape sequence has been deleted.  Use \E instead.

There are `break' and `continue' requests for use with the `while'
request.

There is a request `hym' that can ensure that when the current
adjustment mode is not `b' a line is not hyphenated if it is no more
than a given amount short, and a request `hys' that can ensure that when
the current adjustment mode is `b' a line is not hyphenated if it can be
justified by adding no more than a given amount of extra space to each
word space.

There is a request `rj' similar to `ce' that right justifies lines.

A warning of type `space' is given when a call is made to an undefined
request or macro with a name longer than two characters, and the first
two characters of the name make a name that is defined.  This is
intended to find places where a space has been omitted been a request or
macro and its argument.  This type of warning is enabled by default.

Pic
---

A comma is permitted between the arguments to the `reset' command.

For use with TeX, there is a new `-c' option that makes gpic treat lines
beginning with `.' in a way that is more compatible with tpic (but
ugly).

Eqn
---

It is no longer necessary to add `space 0' at the beginning of
complicated equations inside pictures.

`prime' is now treated as an ordinary character, as in Unix eqn.  The
previous behaviour of `prime' as an operator can now be obtained using
`opprime'.

Xditview
--------

There are two new devices X75-12 and X100-12 which are the same as X75
and X100 except that they are optimized for documents that use mostly 12
point text.


VERSION 0.6
===========

The installation process has been refined to make it easy for you to
share groff with someone who has the same type of machine as you but
does not have a C++ compiler.  See the end of the INSTALL file for
details.

There is a man page for the tfmtodit program which explains how to use
your own fonts with groff -Tdvi.

There is a man page for afmtodit which explains how to use your own
PostScript fonts with groff -Tps.

The \N escape sequence is now fully supported.  It can now be used to
access any character in a font by its output code, even if it doesn't
have a groff name.  This is made possible by a convention in the font
files that a character name of `---' refers to an unnamed character.
The drivers now all support the `N' command required for this.  The font
description files have been updated to include unnamed characters.

The `x' command in font description files has been removed: instead any
unknown commands are automatically made available to the drivers.  If
you constructed your own font files with an earlier version of tfmtodit
or afmtodit, you must construct them again using the current version.

Characters between 0200 and 0237 octal are no longer valid input
characters.  Note that these are not used in ISO 8859.

A command called `grog' has been added, similar to the `doctype' command
described in Kernighan and Pike.

Groff
-----

The groff command has some new options: -V prints the pipeline instead
of executing it; -P passes an argument to the postprocessor, -L passes
an argument to the spooler.

There is a C++ implementation of the groff command.  This handles some
things slightly better than the shell script.  In particular, it can
correctly handle arguments containing characters that have a special
meaning to the shell; it can give an error message when child processes
other than the last in the pipeline terminate abnormally; its exit
status can take account of the exit statuses of all its child processes;
it is a little more efficient; when geqn is used, it searches for the
eqnchar file in the same way that font metric files are searched for,
rather than expecting to find it in one particular directory.

Gtroff
------

There is font translation feature: For example, you can tell gtroff to
use font `HR' whenever font `H' is requested with the line
  .ftr H HR
This would be useful for a document that uses `H' to refer to Helvetica.

There are some new number registers: `.kern' contains the current kern
mode, `.lg' the current ligature mode, `.x' the major version number,
`.y' the minor version number, `.ce' the number of lines to be centered
in the current environment, `.trunc' the amount of vertical space
truncated by the most recently sprung vertical position trap, `.ne' the
amount of vertical space needed in the last `ne' request that caused a
vertical position trap to be sprung.

The `cf' request now behaves sensibly in a diversion.  If used in a
diversion, it now arranges for the file to be copied to the output when
the diversion is reread.

There is a new request `trf' (transparent file) similar to `cf', but
more like `\!'.

There is a new escape sequence `\Y[xxx]', roughly equivalent to
`\X'\*[xxx]'', except that the contents of string or macro xxx are not
interpreted, and xxx may contain newlines.  This requires an output
format extension; the drivers have been modified to understand this.
Grops has also been modified to cope with newlines in the arguments to
\X commands; grops has a new \X command mdef, which is like def except
that it has a first argument giving the number of definitions.

There is a new warning category `escape' which warns about unknown
escape sequences.

The `fp' request now takes an optional third argument giving the
external name of the font.

The `\_' character is now automatically translated to `\(ul' as in
troff.

The environment variable `GROFF_HYPHEN' gives the name of the file
containing the hyphenation patterns.

There is a `\C'xxx'' escape sequence equivalent to `\[xxx]'.

Characters ", ', ), ], *, \(dg are now initially transparent for the
purposes of end of sentence recognition.

There is an anti-recursion feature in the `char' request, so you can say
`.char \(bu \s+2\(bu\s-2'.

The limit on the number of font positions has been removed.  Accordingly
`\n[.fp]' never returns 0.

The restriction on the number of numbered environments has been removed.

There is a new escape sequence `\E' that makes it possible to guarantee
that an escape sequence won't get interpreted in copy-mode.  The `\R'
escape sequence is accordingly now deprecated.

Gpic
----

Arguments of the form `X anything X' (in the `copy thru', `sh', `for',
`if' and `define' constructs) can now be of the form `{ anything }'.

If the `linethick' variable is negative (as it now is initially), lines
are drawn with a thickness proportional to the current point size.

The `rand' function now takes no arguments and returns a number between
0 and 1.  The old syntax is still supported.

`^' can be used in expressions to indicate exponentiation.

In the `for' construct the argument to the by clause can be prefixed by
`*' to indicate that the increment is multiplicative.

A bare expression may be used as an attribute.  If the current direction
is `dir', then an attribute `expr' is equivalent to `dir expr'

There is a `sprintf' construct that allows numbers to be formatted and
used wherever a quoted string can be used.

The height of a text object without an explicit height attribute is the
number of text strings associated with the object times the value of the
`textht' variable.

The maximum height and width of a picture is controlled by the
`maxpswid' and `maxpsht' variables.

Gtbl
----

Gtbl can now handle gracefully the situation where the `ce' request has
been applied to a table.

Geqn
----

The `ifdef' primitive has been generalized.

A tilde accent can be put underneath a box using `utilde'.  This defined
using a general `uaccent' primitive.

Grops
-----

There is a new PostScript font downloading scheme which handles font
downloading for imported illustrations.  Previously, the name of the
file containing the font was given in the `x download' line in the groff
font metric file.  Now, there is a `download' file which says for each
PostScript font name which file contains that font.  Grops can also now
handle inter-font dependencies, where one downloadable font depends on
some other (possibly downloadable) font.

The `T' font has been removed.  The characters it used to provide are
now provided by `char' definitions in tmac.ps. TSymbol.ps has also been
removed, and the tweaks it provided are now provided by `char'
definitions.


##### Editor settings
Local Variables:
coding: latin-1
fill-column: 72
mode: text
version-control: never
End:
# vim: set autoindent textwidth=72:
