.\" cp1047.tmac
.\"
.do nr *groff_cp1047_tmac_C \n[.cp]
.cp 0
.\" char65 (no-break space) is translated on input
.trin \[char66]\[^a]
.trin \[char67]\[:a]
.trin \[char68]\[`a]
.trin \[char69]\['a]
.trin \[char70]\[~a]
.trin \[char71]\[oa]
.trin \[char72]\[,c]
.trin \[char73]\[~n]
.trin \[char74]\[ct]
.trin \[char81]\['e]
.trin \[char82]\[^e]
.trin \[char83]\[:e]
.trin \[char84]\[`e]
.trin \[char85]\['i]
.trin \[char86]\[^i]
.trin \[char87]\[:i]
.trin \[char88]\[`i]
.trin \[char89]\[ss]
.trin \[char98]\[^A]
.trin \[char99]\[:A]
.trin \[char100]\[`A]
.trin \[char101]\['A]
.trin \[char102]\[~A]
.trin \[char103]\[oA]
.trin \[char104]\[,C]
.trin \[char105]\[~N]
.trin \[char106]\[bb]
.trin \[char112]\[/o]
.trin \[char113]\['E]
.trin \[char114]\[^E]
.trin \[char115]\[:E]
.trin \[char116]\[`E]
.trin \[char117]\['I]
.trin \[char118]\[^I]
.trin \[char119]\[:I]
.trin \[char120]\[`I]
.trin \[char128]\[/O]
.trin \[char138]\[Fo]
.trin \[char139]\[Fc]
.trin \[char140]\[Sd]
.trin \[char141]\['y]
.trin \[char142]\[Tp]
.trin \[char143]\[t+-]
.trin \[char144]\[de]
.trin \[char154]\[Of]
.trin \[char155]\[Om]
.trin \[char156]\[ae]
.trin \[char157]\[ac]
.trin \[char158]\[AE]
.trin \[char159]\[Cs]
.trin \[char160]\[mc]
.trin \[char170]\[r!]
.trin \[char171]\[r?]
.trin \[char172]\[-D]
.trin \[char174]\[TP]
.trin \[char175]\[rg]
.trin \[char176]\[tno]
.trin \[char177]\[Po]
.trin \[char178]\[Ye]
.trin \[char179]\[pc]
.trin \[char180]\[co]
.trin \[char181]\[sc]
.trin \[char182]\[ps]
.trin \[char183]\[14]
.trin \[char184]\[12]
.trin \[char185]\[34]
.trin \[char186]\['Y]
.trin \[char187]\[ad]
.trin \[char188]\[a-]
.trin \[char190]\[aa]
.trin \[char191]\[tmu]
.\" char202 (soft hyphen) is translated on input
.trin \[char203]\[^o]
.trin \[char204]\[:o]
.trin \[char205]\[`o]
.trin \[char206]\['o]
.trin \[char207]\[~o]
.trin \[char218]\[S1]
.trin \[char219]\[^u]
.trin \[char220]\[:u]
.trin \[char221]\[`u]
.trin \[char222]\['u]
.trin \[char223]\[:y]
.trin \[char225]\[tdi]
.trin \[char234]\[S2]
.trin \[char235]\[^O]
.trin \[char236]\[:O]
.trin \[char237]\[`O]
.trin \[char238]\['O]
.trin \[char239]\[~O]
.trin \[char250]\[S3]
.trin \[char251]\[^U]
.trin \[char252]\[:U]
.trin \[char253]\[`U]
.trin \[char254]\['U]
.cp \n[*groff_cp1047_tmac_C]
.do rr *groff_cp1047_tmac_C
.
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set filetype=groff textwidth=72:
