#! /bin/sh
. /etc/thinstation.global

if [ -z "$INTERFACE" ]; then
	echo "No interface specfied"
	exit
fi

. /run/log/net/$INTERFACE

#set -x
#exec </dev/null >>/run/log/lease.log  2>&1

case $1 in
	deconfig)
		ifconfig $INTERFACE 0.0.0.0
		;;
	bound|renew)
		if [ -n "$dns" ]; then
			resolvectl dns $INTERFACE $dns
		fi
		if [ -n "$domain" ]; then
			resolvectl domain $INTERFACE $domain
		fi
		ifconfig $interface $ip netmask $subnet
		route add default gw $router
		if [ -n "$hostname" ] ;then
			CLIENT_NAME=$hostname
		fi
		echo "DEVTYPE=$DEVTYPE" > /run/log/net/$INTERFACE
		echo "CLIENT_NAME=$CLIENT_NAME" >> /run/log/net/$INTERFACE
		echo "CLIENT_MAC=$CLIENT_MAC" >> /run/log/net/$INTERFACE
		echo "CLIENT_IP=$ip" >> /run/log/net/$INTERFACE
                echo "SUBNET=$subnet" >> /run/log/net/$INTERFACE
                echo "boot_file=$boot_file" >> /run/log/net/$INTERFACE
#		env >> /run/log/lease_dhcp
#		echo "local-pac-server=$local-pac-server" >> /run/log/net/$INTERFACE
		if [ -n "$siaddr" ] ; then
                        SERVER_IP=$siaddr
                else
                        SERVER_IP=$tftp
                fi
		if [ -n "$NET_FILE_ALTERNATE" ] ; then
			SERVER_IP=$NET_FILE_ALTERNATE
		fi
                echo "SERVER_IP=$SERVER_IP" >> /run/log/net/$INTERFACE
                echo "NETWORKUP=TRUE" >> /run/log/net/$INTERFACE
		echo "NETMASK_SIZE=$mask" >> /run/log/net/$INTERFACE
		if [ -n "$tftp" ] ; then
			echo "SERVER_NAME=$tftp" >> /run/log/net/$INTERFACE
		else
			echo "SERVER_NAME=$sname" >> /run/log/net/$INTERFACE
		fi
		if [ -n $NET${IFINDEX} ]; then
			let NET${IFINDEX}=$INTERFACE
			echo "NET${IFINDEX}=$INTERFACE" >> $TS_RUNTIME
		fi
	;;
esac
