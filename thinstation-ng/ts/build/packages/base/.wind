#94db193ad7a2f9f39c64e46bb8fb07e6  .dna
rm /ts/build/packages/base/bin/vmtouch
rm /ts/build/packages/base/bin/ps
rm /ts/build/packages/base/bin/getent
rm /ts/build/packages/base/etc/adjtime
rm /ts/build/packages/base/etc/authselect/nsswitch.conf
rm /ts/build/packages/base/etc/authselect/password-auth
rm -f /ts/build/packages/base/etc/pam.d/password-auth
rm /ts/build/packages/base/etc/authselect/postlogin
rm -f /ts/build/packages/base/etc/pam.d/postlogin
rm /ts/build/packages/base/etc/authselect/system-auth
rm -f /ts/build/packages/base/etc/pam.d/system-auth
rm /ts/build/packages/base/etc/authselect/fingerprint-auth
rm -f /ts/build/packages/base/etc/pam.d/fingerprint-auth
rm /ts/build/packages/base/etc/authselect/smartcard-auth
rm -f /ts/build/packages/base/etc/pam.d/smartcard-auth
rm /ts/build/packages/base/etc/authselect/authselect.conf
rm /ts/build/packages/base/etc/authselect/dconf-db
rm /ts/build/packages/base/etc/authselect/dconf-locks
rm /ts/build/packages/base/etc/pam.d/other
rm /ts/build/packages/base/etc/pam.d/chfn
rm /ts/build/packages/base/etc/pam.d/chsh
rm /ts/build/packages/base/etc/pam.d/remote
rm /ts/build/packages/base/etc/pam.d/runuser
rm /ts/build/packages/base/etc/pam.d/runuser-l
rm /ts/build/packages/base/etc/pam.d/su
rm /ts/build/packages/base/etc/pam.d/su-l
rm /ts/build/packages/base/etc/pam.d/login
rm /ts/build/packages/base/etc/pam.d/config-util
rm /ts/build/packages/base/etc/pam.d/passwd
rm /ts/build/packages/base/etc/pam.d/vlock
rm /ts/build/packages/base/etc/pam.d/ksu
rm /ts/build/packages/base/etc/security/access.conf
rm /ts/build/packages/base/etc/security/sepermit.conf
rm /ts/build/packages/base/etc/security/pwquality.conf
rm /ts/build/packages/base/etc/security/pam_env.conf
rm /ts/build/packages/base/etc/security/group.conf
rm /ts/build/packages/base/etc/security/limits.conf
rm /ts/build/packages/base/etc/security/namespace.conf
rm /ts/build/packages/base/etc/security/namespace.init
rm /ts/build/packages/base/etc/security/time.conf
rm /ts/build/packages/base/etc/shells
rm /ts/build/packages/base/etc/environment
rm /ts/build/packages/base/etc/inputrc
rm /ts/build/packages/base/etc/pam_debug
rm /ts/build/packages/base/sbin/ldconfig
rm /ts/build/packages/base/sbin/setcap
rm /ts/build/packages/base/sbin/getcap
rm /ts/build/packages/base/sbin/lspci
rm /ts/build/packages/base/sbin/agetty
rm /ts/build/packages/base/bin/blkid
rm -f /ts/build/packages/base/sbin/blkid
rm /ts/build/packages/base/bin/swapon
rm -f /ts/build/packages/base/sbin/swapon
rm /ts/build/packages/base/bin/login
rm /ts/build/packages/base/lib64/security/pam_pwquality.so
rm /ts/build/packages/base/bin/unix_chkpwd
rm /ts/build/packages/base/lib64/security/pam_sepermit.so
rm -f /ts/build/packages/base/lib64/security/pam_selinux_permit.so
rm /ts/build/packages/base/lib64/security/pam_access.so
rm /ts/build/packages/base/lib64/security/pam_deny.so
rm /ts/build/packages/base/lib64/security/pam_env.so
rm /ts/build/packages/base/lib64/security/pam_faildelay.so
rm /ts/build/packages/base/lib64/security/pam_keyinit.so
rm /ts/build/packages/base/lib64/security/pam_lastlog.so
rm /ts/build/packages/base/lib64/security/pam_limits.so
rm /ts/build/packages/base/lib64/security/pam_localuser.so
rm /ts/build/packages/base/lib64/security/pam_loginuid.so
rm /ts/build/packages/base/lib64/security/pam_namespace.so
rm /ts/build/packages/base/lib64/security/pam_nologin.so
rm /ts/build/packages/base/lib64/security/pam_permit.so
rm /ts/build/packages/base/lib64/security/pam_rootok.so
rm /ts/build/packages/base/lib64/security/pam_shells.so
rm /ts/build/packages/base/lib64/security/pam_succeed_if.so
rm /ts/build/packages/base/lib64/security/pam_umask.so
rm /ts/build/packages/base/lib64/security/pam_unix.so
rm /ts/build/packages/base/lib64/security/pam_usertype.so
rm /ts/build/packages/base/lib64/security/pam_warn.so
rm /ts/build/packages/base/lib64/security/pam_wheel.so
rm /ts/build/packages/base/lib64/security/pam_xauth.so
rm /ts/build/packages/base/lib64/security/pam_securetty.so
rm /ts/build/packages/base/lib64/security/pam_selinux.so
rm /ts/build/packages/base/lib64/ld-linux-x86-64.so.2
rm /ts/build/packages/base/lib64/libgcc_s.so.1
rm /ts/build/packages/base/sbin/pwconv
rm /ts/build/packages/base/sbin/grpconv
rm /ts/build/packages/base/sbin/unix_chkpwd
rm /ts/build/packages/base/bin/mount
rm /ts/build/packages/base/bin/find

