#!/bin/sh
export PACKAGE=chrome
export RPM=`basename $1`
repackage -e

INSTALLDIR=/build/packages/chrome
rm -rf $INSTALLDIR/etc/cron.daily

chmod 4755 $INSTALLDIR/opt/google/chrome/chrome-sandbox
returnval=$?

VERSION=`$INSTALLDIR/opt/google/chrome/chrome --no-sandbox --product-version |cut -d '.' -f1`
cat $INSTALLDIR/build/'Local State' |jq ".browser.last_whats_new_version = $VERSION" > $INSTALLDIR/etc/chrome/'Local State'

exit $returnval
