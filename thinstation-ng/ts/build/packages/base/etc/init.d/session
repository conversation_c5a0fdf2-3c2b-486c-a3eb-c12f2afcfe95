#!/bin/sh

. `dirname $0`/common

# Set Geometry Variables for, needed for certain session types

screen_res
#let COLS=`echo $SCREEN_RESOLUTION | cut -f1 -dx`/6-1
#let ROWS=`echo $SCREEN_RESOLUTION | cut -f2 -dx`/14

if is_enabled $X_RES_SEED; then
	echo "SCREEN_RESOLUTION=\"$SCREEN_RESOLUTION\"" >>$TS_RUNTIME
fi

#echo "COLS=$COLS" >> $TS_RUNTIME
#echo "ROWS=$ROWS" >> $TS_RUNTIME

#let x=-1
#let y=-1

# Add each Applications configuration parameters
for x in `seq 0 10`; do
#while [ $x -le $y ]; do
#   let x=x+1

   # Define Default Values. Can be overriden by session parameters.
   SCREEN=0
   SCREEN_POSITION=1
   WORKSPACE=1
   AUTOSTART=On
   ICON=Off
   CONFIG=.
   SERVER=.
   OPTIONS=.
   USER=.
   PASS_ENABLE=.
   GATE=.
   GATE_USER=.

  # Get Session details
#   . $TS_GLOBAL
   SESSION_TYPE=`eval echo '$SESSION_'$x'_TYPE'`
   session_type=`make_caps $SESSION_TYPE`
   SESSION_TITLE=`eval echo '$SESSION_'$x'_TITLE'`
   SESSION_SCREEN=`eval echo '$SESSION_'$x'_SCREEN'`
   SESSION_SCREEN_POSITION=`eval echo '$SESSION_'$x'_SCREEN_POSITION'`
   SESSION_WORKSPACE=`eval echo '$SESSION_'$x'_WORKSPACE'`
   SESSION_AUTOSTART=`eval echo '$SESSION_'$x'_AUTOSTART'`
   SESSION_CUSTOM_CONFIG=`eval echo '$SESSION_'$x'_CUSTOM_CONFIG'`
   SESSION_ICON=`eval echo '$SESSION_'$x'_ICON'`
   SESSION_APPLICATION_SET=`eval echo '$SESSION_'$x'_'$session_type'_APPLICATION_SET'`
   SESSION_SERVER=`eval echo '$SESSION_'$x'_'$session_type'_SERVER'`
   SESSION_OPTIONS=`eval echo '$SESSION_'$x'_'$session_type'_OPTIONS'`
   SESSION_USER=`eval echo '$SESSION_'$x'_'$session_type'_USER'`
   SESSION_PASS_ENABLE=`eval echo '$SESSION_'$x'_'$session_type'_PASS_ENABLE'`
   SESSION_GATE=`eval echo '$SESSION_'$x'_'$session_type'_GATE'`
   SESSION_GATE_USER=`eval echo '$SESSION_'$x'_'$session_type'_GATE_USER'`

   # Set Default Values
   if [ -z "$SESSION_TITLE" ];		then SESSION_TITLE=$SESSION_TYPE	; fi
   if [ -z "$SESSION_SCREEN" ];		then SESSION_SCREEN=$SCREEN		; fi
   if [ -z "$SESSION_SCREEN_POSITION" ];then SESSION_SCREEN_POSITION=$SCREEN_POSITION; fi
   if [ -z "$SESSION_WORKSPACE" ]; 	then SESSION_WORKSPACE=$WORKSPACE	; fi
   if [ -z "$SESSION_AUTOSTART" ]; 	then SESSION_AUTOSTART=$AUTOSTART	; fi
   if [ -z "$SESSION_CUSTOM_CONFIG" ]; 	then SESSION_CUSTOM_CONFIG=$CONFIG	; fi
   if [ -z "$SESSION_ICON" ];		then SESSION_ICON=$ICON			; fi
   if [ -z "$SESSION_SERVER" ];		then SESSION_SERVER=$SERVER		; fi
   if [ -z "$SESSION_OPTIONS" ];	then SESSION_OPTIONS=$OPTIONS		; fi
   if [ -z "$SESSION_USER" ];		then SESSION_USER=$USER			; fi
   if [ -z "$SESSION_PASS_ENABLE" ];	then SESSION_PASS_ENABLE=$PASS_ENABLE   ; fi
   if [ -z "$SESSION_GATE" ];		then SESSION_GATE=$GATE			; fi
   if [ -z "$SESSION_GATE_USER" ];	then SESSION_GATE_USER=$GATE_USER	; fi

   SESSION_TYPE=`make_lower $SESSION_TYPE`
   if [ -e /etc/cmd/$SESSION_TYPE.change_server_type ] ; then
	if [ -n "$SESSION_TITLE" ] ; then
	   	SESSION_SERVER=`replace_char "$SESSION_TITLE" " " "_"`
	elif [ -n "$SESSION_APPLICATION_SET" ] ;then
		SESSION_SERVER=$SESSION_APPLICATION_SET
		SESSION_TITLE=$SESSION_APPLICATION_SET
	else
		SESSION_SERVER="$SESSION_TYPE"_"$x"
   	fi
   fi
   SESSION_AUTOSTART=`make_caps $SESSION_AUTOSTART`
   SESSION_ICON=`make_caps $SESSION_ICON`
   SESSION_SCREEN_POSITION=`make_caps $SESSION_SCREEN_POSITION`
   SESSION_CUSTOM_CONFIG=`make_caps $SESSION_CUSTOM_CONFIG`


   if [ "$SESSION_SCREEN_POSITION" != "1" ] ; then
   	if [ "$DUALHEAD" != "ENABLED" ] ; then
	   DUALHEAD=ENABLED
	   echo "DUALHEAD=$DUALHEAD" >> $TS_RUNTIME
  	fi
   fi
   mkdir -p $WKDIR
   if [ -n "$SESSION_TYPE" ] ;then
 #    let y=y+1
     echo $SESSION_TYPE  \
	$(replace_invalid "$SESSION_TITLE")  \
	$SESSION_SCREEN  \
	$SESSION_SCREEN_POSITION  \
	$SESSION_WORKSPACE  \
	$SESSION_AUTOSTART  \
	$SESSION_CUSTOM_CONFIG  \
	$SESSION_ICON  \
	$(replace_invalid "$SESSION_SERVER")  \
	$(replace_invalid "$SESSION_OPTIONS") \
	$(replace_invalid "$SESSION_USER") \
	$SESSION_PASS_ENABLE \
	$SESSION_GATE \
	$SESSION_GATE_USER >> $WKDIR/session
   fi
done

if [ ! -e $WKDIR/session ] ; then
  echo_log -d "\n\nError, no valid session type!!!"
  echo_log -d "Boot aborted, please check your thinstation config files are correct"
  if pkg_initialized debug ; then
	  echo_log -d "Debug Enabled...continuing"
  else
	  halt
  fi
fi
