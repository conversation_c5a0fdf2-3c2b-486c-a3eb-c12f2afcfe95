README for GNU DIFF

This directory contains the GNU diff, diff3, sdiff, and cmp utilities.
Their features are a superset of the Unix features and they are
significantly faster.

Please see the file COPYING for copying conditions.

Please see the file doc/version.texi for version information.

Please see the file doc/diffutils.texi (or doc/diffutils.info) for
documentation that can be printed with TeX, or read with the 'info'
program or with Emacs's 'M-x info'.  Brief man pages are in man/*,
but they are no substitute for the documentation.

Please see the file ABOUT-NLS for notes about translations.

Please see the file INSTALL for generic compilation and installation
instructions.  Briefly, you can run "./configure; make install".  The
command "./configure --help" lists the supported --enable and --with
options.

If you have a problem with internationalization, you might be able to
work around it as described in ABOUT-NLS by invoking './configure
--disable-nls'.  Many of the problems arise from dynamic linking
issues on non-GNU platforms (e.g. with the iconv library).  Such
problems tend to be shared by other GNU applications on these
platforms, and can usually be fixed by carefully tweaking your non-GNU
installation.  If you have an older version of libiconv, please
upgrade to the latest one; see <ftp://ftp.gnu.org/gnu/libiconv/>.  If
the problem seems isolated to diffutils, though, please report a bug.

This program requires a Standard C compiler (C89 or later).  If you
have a nonstandard compiler, please install GCC first.

If you make changes to the source code, you may need appropriate
versions of GNU build tools to regenerate the intermediate files.  The
following versions were used to generate the intermediate files in
this distribution:

* Autoconf 2.59   <ftp://ftp.gnu.org/gnu/autoconf/autoconf-2.59.tar.gz>
* Automake 1.8.3  <ftp://ftp.gnu.org/gnu/automake/automake-1.8.3.tar.gz>
* gettext 0.14.1  <ftp://ftp.gnu.org/gnu/gettext/gettext-0.14.1.tar.gz>
* help2man 1.33   <ftp://ftp.gnu.org/gnu/help2man/help2man-1.33.1.tar.gz>
* Texinfo 4.7     <ftp://ftp.gnu.org/gnu/texinfo/texinfo-4.7.tar.gz>

For any copyright year range specified as YYYY-ZZZZ in this package
note that the range specifies every single year in that closed interval.

Please report bugs to <<EMAIL>>.

-----

Copyright (C) 1992, 1998, 2001-2002, 2004, 2009-2013, 2015-2025 Free Software
Foundation, Inc.

This file is part of GNU Diffutils.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with no
Invariant Sections, with no Front-Cover Texts, and with no Back-Cover
Texts.  A copy of the license is included in the "GNU Free
Documentation License" file as part of this distribution.
