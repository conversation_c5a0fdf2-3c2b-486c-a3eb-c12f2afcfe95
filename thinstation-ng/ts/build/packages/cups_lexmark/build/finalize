#lexmark 90

ABS_ROOT=/usr/local/Lexmark/ppd/Lexmark-UPD-PPD-Files
REL_ROOT=/usr/local/Lexmark/ppd/Lexmark-UPD-PPD-Files
OEM=Lexmark
P_RELEASE=Lexmark-UPD-PPD-Files

TYPE=1 #For other machines i.e Suse/SuseServer/Redhat/Fedora both for 32 & 64 bit
file /bin/bash | grep "ARM" >/dev/null 2>&1
if [ $? -eq 0 ]; then
	file /bin/bash | grep "32-bit" >/dev/null 2>&1
	if [ $? -eq 0 ]; then
		TYPE=2 #for future use. 2 - Ubuntu ARM 32 bit
		echo "This is ARM 32 bit system and this driver doesn't support ARM 32 bit architecture."
		exit 1
	else
		TYPE=3 #3 - Ubuntu ARM 64 bit
	fi
else
	if [ -f "/etc/debian_version" ];then
                if [ -f "/etc/lsb-release" ];then
			file /bin/bash | grep "32-bit" >/dev/null 2>&1
			if [ $? -eq 0 ]; then
				TYPE=4 #4 - Ubuntu INTEL 32 bit
			else
				TYPE=5 #5 - Ubuntu INTEL 64 bit
			fi
		fi
	fi
fi

#default path
PPD_PATH=/usr/local/${OEM}/ppd/${P_RELEASE}
QUTIL_PATH=/usr/local/${OEM}/ppd
FOOMATIC_PPD=$PPD_PATH/foomatic/UTF-8
GLOBAL_PPD_1_2=$PPD_PATH/GlobalPPD_1.2
GLOBAL_PPD_1_4=$PPD_PATH/GlobalPPD_1.4

FOO_DIR="/usr/share/foomatic/db/source"
FOO_PRT_DIR="/usr/share/foomatic/db/source/printer"
FOO_OPT_DIR="/usr/share/foomatic/db/source/opt"
FOO_DRV_DIR="/usr/share/foomatic/db/source/driver"

DEST_FAX_PNH_DIR="/usr/lib/cups/filter"
FAXPNHDIR="Filter"
FAXPNHFILTER="LexFaxPnHFilter"
REROUTEFILTER="rerouteprintoption"
CUPSVERSION="cupsversion"
QUEUECREATIONUTIL="QueueCreationUtility"
QUEUECREATIONUTILSUSE="SuseExe"
COMMANDFILEFILTER="LexCommandFileFilterG2"
HBPFILTER="LexHBPFilter"
OPENSOURCELICENSEFILES_PATH=/usr/local/${OEM}
OPENSOURCELICENSEFILES="OpenSource_License"
QUEUEAUTOCONFIGURE="queueCreation.sh"
DEST_QUEUE_AUTOCONF_DIR="/usr/local/${OEM}"

MACHINE_TYPE_A=`uname -m`
MACHINE_TYPE_B=`uname -i`
echo "${MACHINE_TYPE_A} / ${MACHINE_TYPE_B}"

CAT_CMD=`which cat | awk '{print $1}'`
COPY_CMD=`which cp | awk '{print $1}'`
TAR_CMD=`which tar | awk '{print $1}'`
MAKE_DIR_CMD=`which mkdir | awk '{print $1}'`
RM_CMD=`which rm | awk '{print $1}'`
PRINTF_CMD=`which echo | awk '{print $1}'`
GREP_CMD=`which grep | awk '{print $1}'`
CHMOD_CMD=`which chmod | awk '{print $1}'`
SED_CMD=`which sed | awk '{print $1}'`
ECHO_CMD=`which echo | awk '{print $1}'`

if [ -f "/etc/SuSE-release" ];then
	SYS_TYPE="SUSE"
	SHOW_FOO="NO"
	SHOW_ENC="YES"
elif [ -f "/etc/lindowsos-version" -o -f "/etc/linspire-version" ];then
	SYS_TYPE="LINSPIRE"
	SHOW_FOO="NO"
	SHOW_ENC="NO"
	PPD_LINK="YES"
elif [ -f "/etc/debian_version" ];then
	SHOW_FOO="YES"
	SHOW_ENC="YES"
	PPD_LINK="YES"
        if [ -f "/etc/lsb-release" ];then
            SYS_TYPE="UBUNTU"
        else
	    SYS_TYPE="DEBIAN"
        fi
	if [ -d "/usr/share/ppd" ];then
		CUPS_PPD_ROOT="/usr/share/ppd"
	fi
elif [ -f "/etc/fedora-release" ];then
	SYS_TYPE="FEDORA"
	SHOW_FOO="YES"
	SHOW_ENC="YES"
	PPD_LINK="YES"
elif [ -f "/etc/pclinuxos-release" ];then
	SYS_TYPE="PCLinux"
elif [ -f "/etc/redhat-release" ];then
	COUT=`cat /etc/redhat-release`
	SHOW_FOO="YES"
	
	${PRINTF_CMD} "${COUT}\n" | ${GREP_CMD} " 3 " >/dev/null 2>&1
		
	if [ $? -eq 0 ];then
		SYS_TYPE="REDHAT_3"
	else
		${PRINTF_CMD} "${COUT}\n" | ${GREP_CMD} " 4 " >/dev/null 2>&1
		if [ $? -eq 0 ];then
			SYS_TYPE="REDHAT_4"
		else
                        ${PRINTF_CMD} "${COUT}\n" | ${GREP_CMD} " 5 " >/dev/null 2>&1
			if [ $? -eq 0 ];then
				SYS_TYPE="REDHAT_5"
				SHOW_FOO="NO"
			else
				SYS_TYPE="REDHAT_x"
			fi
		fi
	fi
	SHOW_ENC="YES"
else
	SYS_TYPE="OTHER"
	SHOW_FOO="YES"
	SHOW_ENC="YES"
fi

get_cups_version()
{
    if [ ${MACHINE_TYPE_A} = 'x86_64' -o ${MACHINE_TYPE_B} = 'x86_64' ]; then
        CUPSVERSION_PATH=$GLOBAL_PPD_1_4/lib64/$CUPSVERSION 
    elif [ ${MACHINE_TYPE_A} = 'aarch64' -o ${MACHINE_TYPE_B} = 'aarch64' ]; then
        CUPSVERSION_PATH=$GLOBAL_PPD_1_4/libarm64/$CUPSVERSION
    else
        CUPSVERSION_PATH=$GLOBAL_PPD_1_4/lib/$CUPSVERSION
    fi

    $CUPSVERSION_PATH ${TYPE}> /dev/null 2>&1  

    CUPS_VERSION=$?
}

set_ppd()
{
    if [ $CUPS_VERSION -lt 2 ]; then
        CUPS_VERS="FOO"
    elif [ $CUPS_VERSION -ge 2 -a $CUPS_VERSION -le 3 ]; then
        CUPS_VERS="PPD_1_2"
    elif [ $CUPS_VERSION -ge 4 ]; then
        CUPS_VERS="PPD_1_4"
        if [ -f "$GLOBAL_PPD_1_4/${FAXPNHFILTER}" ]; then
            ${COPY_CMD} $GLOBAL_PPD_1_4/${FAXPNHFILTER} ${DEST_FAX_PNH_DIR}
            ${CHMOD_CMD} 755 ${DEST_FAX_PNH_DIR}/${FAXPNHFILTER}
        fi
        if [ -f "$GLOBAL_PPD_1_4/${REROUTEFILTER}" ]; then
            ${COPY_CMD} $GLOBAL_PPD_1_4/${REROUTEFILTER} ${DEST_FAX_PNH_DIR}
            ${CHMOD_CMD} 755 ${DEST_FAX_PNH_DIR}/${REROUTEFILTER}
        fi
        MACHINE_TYPE_A=`uname -m`
        MACHINE_TYPE_B=`uname -i`

        if [ ${MACHINE_TYPE_A} = 'x86_64' -o ${MACHINE_TYPE_B} = 'x86_64' ]; then
           ${COPY_CMD} $GLOBAL_PPD_1_4/lib64/${COMMANDFILEFILTER} ${DEST_FAX_PNH_DIR}
	   if [ -f $GLOBAL_PPD_1_4/lib64/${HBPFILTER} ]; then
           	${COPY_CMD} $GLOBAL_PPD_1_4/lib64/${HBPFILTER} ${DEST_FAX_PNH_DIR}
        	${CHMOD_CMD} 755 ${DEST_FAX_PNH_DIR}/${HBPFILTER}
	   fi
        elif [ ${MACHINE_TYPE_A} = 'aarch64' -o ${MACHINE_TYPE_B} = 'aarch64' ]; then
           ${COPY_CMD} $GLOBAL_PPD_1_4/libarm64/${COMMANDFILEFILTER} ${DEST_FAX_PNH_DIR}
	   if [ -f $GLOBAL_PPD_1_4/libarm64/${HBPFILTER} ]; then
           	${COPY_CMD} $GLOBAL_PPD_1_4/libarm64/${HBPFILTER} ${DEST_FAX_PNH_DIR}
                ${CHMOD_CMD} 755 ${DEST_FAX_PNH_DIR}/${HBPFILTER}
	   fi
        else
           ${COPY_CMD} $GLOBAL_PPD_1_4/lib/${COMMANDFILEFILTER} ${DEST_FAX_PNH_DIR}
	   if [ -f $GLOBAL_PPD_1_4/lib/${HBPFILTER} ]; then
           	${COPY_CMD} $GLOBAL_PPD_1_4/lib/${HBPFILTER} ${DEST_FAX_PNH_DIR}
        	${CHMOD_CMD} 755 ${DEST_FAX_PNH_DIR}/${HBPFILTER}
	   fi
        fi
		${MAKE_DIR_CMD} -p $OPENSOURCELICENSEFILES_PATH/${OPENSOURCELICENSEFILES}
		${CHMOD_CMD} 777 $OPENSOURCELICENSEFILES_PATH/${OPENSOURCELICENSEFILES}
        ${CHMOD_CMD} 755 ${DEST_FAX_PNH_DIR}/${COMMANDFILEFILTER}
	USER_NAME=`whoami`
	if [ -f "$GLOBAL_PPD_1_4/${QUEUEAUTOCONFIGURE}" ]; then
            ${COPY_CMD} $GLOBAL_PPD_1_4/${QUEUEAUTOCONFIGURE} ${DEST_QUEUE_AUTOCONF_DIR}
            ${CHMOD_CMD} 755 ${DEST_QUEUE_AUTOCONF_DIR}/${QUEUEAUTOCONFIGURE}
        fi

    fi
    
    echo "CUPS Version: ${CUPS_VERS}"

    ${RM_CMD} -fr CUPS_VERSION
}

get_cups_root()
{
    if [ -d "/usr/share/ppd" ]; then
        CUPS_PPD_ROOT="/usr/share/ppd"
    else
        CUPS_PPD_ROOT="/usr/share/cups/model"
    fi
}

get_contone_stat()
{
    PSTOPDF="false"
    if [ -f "/usr/lib/cups/filter/pstopdf" ]; then
        PSTOPDF="true"
    else
        PSTOPDF="false"
    fi
}

update_driver_xml()
{
    if [ ! -f "${FOO_DRV_DIR}/${OEM_TITLE_CASE}_PPD.xml" ];then
        ${COPY_CMD} ${FOOMATIC_PPD}/driver/*.xml $FOO_DRV_DIR
    fi

    ${GREP_CMD} ${PRINTER_ID} ${FOO_DRV_DIR}/${OEM_TITLE_CASE}_PPD.xml >/dev/null 2>&1
        if [ $? -ne 0 ];then
            GrepOut=`${GREP_CMD} -n "</printers>" ${FOO_DRV_DIR}/${OEM_TITLE_CASE}_PPD.xml 2>&1`
            LINE=`${ECHO_CMD} ${GrepOut} | awk -F: '{print $1}'`
            INFO="\ \ \  <printer><id>printer/${PRINTER_ID}</id></printer>"
            #echo ${SED_CMD} -e "${LINE}i  ${INFO}"
            ${CAT_CMD} ${FOO_DRV_DIR}/${OEM_TITLE_CASE}_PPD.xml | ${SED_CMD} -e "${LINE}i  ${INFO}" > ${FOO_DRV_DIR}/${OEM_TITLE_CASE}_PPD.xml.tmp
            ${COPY_CMD} -f ${FOO_DRV_DIR}/${OEM_TITLE_CASE}_PPD.xml.tmp ${FOO_DRV_DIR}/${OEM_TITLE_CASE}_PPD.xml
            ${RM_CMD} -f ${FOO_DRV_DIR}/${OEM_TITLE_CASE}_PPD.xml.tmp
        fi
}

################################
#
# Post-Installation begins here
#
################################


get_cups_version
set_ppd
get_cups_root
get_contone_stat

#cat ${PPD_PATH}/License.txt

if [ "$CUPS_VERS" = "FOO" ]; then #foomatic PPD
    if [ "$SYS_TYPE" = "REDHAT_4" ]; then #foomatic PPD for RHEL 4
        cd ${FOOMATIC_PPD} 
        ${TAR_CMD} xf ${FOOMATIC_PPD}/*.tar 

        PRINTER_ID=`ls ${FOOMATIC_PPD}/printer/`
        PRINTER_ID=`basename ${PRINTER_ID} .xml` 
        OEM_TITLE_CASE=`ls ${FOOMATIC_PPD}/driver/`
        OEM_TITLE_CASE=`basename ${OEM_TITLE_CASE} _PPD.xml` 

        #${COPY_CMD} ${FOOMATIC_PPD}/driver/*.xml $FOO_DRV_DIR 
        update_driver_xml

        ${COPY_CMD} ${FOOMATIC_PPD}/opt/*.xml $FOO_OPT_DIR    
        ${COPY_CMD} ${FOOMATIC_PPD}/printer/*.xml $FOO_PRT_DIR
    fi
elif [ "$CUPS_VERS" == "PPD_1_2" ]; then	# PS PPD for CUPS1.2
    ${MAKE_DIR_CMD} -p $CUPS_PPD_ROOT/${F_OEM}_PPD
    ${COPY_CMD} ${GLOBAL_PPD_1_2}/*.ppd $CUPS_PPD_ROOT/${F_OEM}_PPD
elif [ "$CUPS_VERS" == "PPD_1_4" ]; then 
    ${MAKE_DIR_CMD} -p $CUPS_PPD_ROOT/${F_OEM}_PPD
	if [ "${SYS_TYPE}" = "DEBIAN" -o "${SYS_TYPE}" = "SUSE" ]; then
		if [ "${F_OEM}" = "Printer" ]; then
			if [ -f $GLOBAL_PPD_1_4/startQueueCreationUtil.sh ]; then
				if [ ! -d /usr/share/ppd/PPD_PPD ]; then
					mkdir -p /usr/share/ppd/PPD_PPD
				fi
				if [ ! -f /usr/share/ppd/PPD_PPD/ppd.dummy_ ]; then
					touch /usr/share/ppd/PPD_PPD/ppd.dummy_
				fi
    			if [ ${MACHINE_TYPE_A} = 'x86_64' -o ${MACHINE_TYPE_B} = 'x86_64' ]; then
    			    QEXE_PATH=$GLOBAL_PPD_1_4/lib64/$QUEUECREATIONUTIL 
    			    QEXE_PATH_SUSE=$GLOBAL_PPD_1_4/lib64/$QUEUECREATIONUTILSUSE 
    			else
    			    QEXE_PATH=$GLOBAL_PPD_1_4/lib/$QUEUECREATIONUTIL
    			    QEXE_PATH_SUSE=$GLOBAL_PPD_1_4/lib/$QUEUECREATIONUTILSUSE 
    			fi
				if [ -f /etc/init.d/startQueueCreationUtil.sh ]; then
					/etc/init.d/startQueueCreationUtil.sh stop >> $QUTIL_PATH/queueCreationLog.txt 2>&1 
				fi
    			${COPY_CMD} $QEXE_PATH $QUTIL_PATH
				if [ "${SYS_TYPE}" = "SUSE" ]; then
    				${COPY_CMD} $QEXE_PATH_SUSE $QUTIL_PATH
				fi
    			${COPY_CMD} $GLOBAL_PPD_1_4/queueUtil.sh $QUTIL_PATH
    			${COPY_CMD} $GLOBAL_PPD_1_4/startQueueCreationUtil.sh /etc/init.d
				if [ "${SYS_TYPE}" = "DEBIAN" ]; then
					echo "This is Debian"
					update-rc.d startQueueCreationUtil.sh defaults
				elif [ "${SYS_TYPE}" = "SUSE" ]; then
					echo "This is Suse"
					if [ -f /etc/init.d/boot.local ]; then
						grep "startQueueCreationUtil.sh start" /etc/init.d/boot.local > /dev/null 2>&1
						if [ $? -ne 0 ]; then
							echo "nohup /etc/init.d/startQueueCreationUtil.sh start >> $QUTIL_PATH/queueCreationLog.txt 2>&1 &" >> /etc/init.d/boot.local
						fi
					elif [ -f /etc/init.d/after.local ]; then
						grep "startQueueCreationUtil.sh start" /etc/init.d/after.local > /dev/null 2>&1
						if [ $? -ne 0 ]; then
							echo "nohup /etc/init.d/startQueueCreationUtil.sh start >> $QUTIL_PATH/queueCreationLog.txt 2>&1 &" >> /etc/init.d/after.local
							#systemctl enable after-local.service > /dev/null 2>&1
						fi
					else
						chkconfig --list | grep "startQueueCreationUtil.sh" > /dev/null 2>&1
						if [ $? -ne 0 ]; then
							chkconfig --add startQueueCreationUtil.sh > /dev/null
							chkconfig --level 23456 startQueueCreationUtil.sh on > /dev/null
						fi
					fi
				else
					echo "This is not supported ditros, not sure what will happen"
					chkconfig --list | grep "startQueueCreationUtil.sh" > /dev/null 2>&1
					if [ $? -ne 0 ]; then
						chkconfig --add startQueueCreationUtil.sh > /dev/null
						chkconfig --level 23456 startQueueCreationUtil.sh on > /dev/null
					fi
				fi
				nohup /etc/init.d/startQueueCreationUtil.sh start >> $QUTIL_PATH/queueCreationLog.txt 2>&1 &
			fi
		fi
	fi
    ${COPY_CMD} $GLOBAL_PPD_1_4/*.ppd $CUPS_PPD_ROOT/${F_OEM}_PPD
fi
