%!PS-Adobe-3.0 Resource-Font
%%DocumentNeededResources: font Symbol
%%EndComments
% newfontname matrix oldfontname MakeTransformedFont
/MakeTransformedFont{
findfont dup maxlength dict begin
{
exch dup dup/FID ne exch/UniqueID ne and{
exch def
}{
pop pop
}ifelse
}forall
% first copy FontBBox
/FontBBox
% FontBBox sometimes seems to have the executable
% attribute set
% so to get the array on the stack, we have to do this
currentdict/FontBBox get
4 array copy def
% now transform it
FontBBox aload pop
4 index transform 4 2 roll
4 index transform 4 2 roll
FontBBox astore pop
% matrix
% now transform FontMatrix
FontMatrix exch matrix concatmatrix
/FontMatrix exch def
dup/FontName exch def
currentdict end
definefont pop
}bind def
%%IncludeResource: font Symbol
/Symbol-Slanted
[.89 0.0 15.5 dup sin exch cos div .89 0.0 0.0]
/Symbol
MakeTransformedFont
