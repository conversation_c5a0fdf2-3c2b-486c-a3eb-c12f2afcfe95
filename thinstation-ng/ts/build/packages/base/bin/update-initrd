#!/bin/sh

. /etc/thinstation.global
bootdir=/boot/boot
set -x
show_help()
{
echo "
  Usage:	-h --help	Show this help message
		-l --local	Copy from the local build env
		-s --server	Download from the dhcp assigned tftp server
		-sh --server-h	Download from the dhcp assigned tftp server over http
		-w --web	Download the default image from the web
		-p --path	Copy from a user specified path
"
                exit 255
}

exit_everything_already_current()
{
	echo "Everything is Up to Date"
	echo 0 > /tmp/update.status
	exit 0
}

exit_update_completed()
{
        echo "Update Complete"
        echo 1 > /tmp/update.status
	exit 1
}

exit_failed_no_md5()
{
	echo "Could not check md5"
	echo 2 > /tmp/update.status
	exit 2
}

exit_invalid_path()
{
	echo "Invalid Path or missing vmlinuz and initrd"
        echo 3 > /tmp/update.status
	exit 3
}

exit_copy_failed()
{
	echo "Something went wrong a file copy. Either permissions or space."
	echo 4 > /tmp/update.status
	exit 4
}

exit_transfer_failed()
{
	echo "Something went wrong with a download from the internet. Are you connected?"
	echo 5 > /tmp/update.status
	exit 5
}

exit_unwind_failed()
{
	echo "Something went wrong with the unwind of the update archive. Possibly a space problem or lack of memory"
	echo 6 > /tmp/update.status
	exit 6
}

init()
{
LPATH=""
	if [ -z "$1" ] ; then show_help ; fi
        until [ -z "$1" ] ; do
		case $1 in
			-p|--path)	METHOD=PATH LPATH="$2" ;;
			-l|--local)     METHOD=LOCAL ;;
			-h|--help)	show_help ;;
			-w|--web)	METHOD=WEB ;;
			-s|--server)	METHOD=SERVER ;;
			-sh|--server-h)	METHOD=SH ;;
			*)		echo "Invalid Option: $1" ; exit 255 ;;
                esac
		shift $#
	done
}
init $@

case $METHOD in
	LOCAL)
		cd $bootdir
		cp --update /thinstation/build/boot-images/initrd/image.md5 check.md5 || \
		exit_failed_no_md5
		if ! md5sum -c check.md5 ; then
			echo "Your installation is out of date. Performing the update"
			cp -f /thinstation/build/boot-images/initrd/initrd initrd || \
				exit_copy_failed
			cp -f /thinstation/build/boot-images/initrd/vmlinuz vmlinuz || \
				exit_copy_failed
			if [ -e /thinstation/build/boot-images/initrd/lib.squash ]; then
				cp -f /thinstation/build/boot-images/initrd/lib.squash lib.update || \
				exit_copy_failed
			fi
			exit_update_completed
		else
			exit_everything_already_current
		fi
	;;
	SERVER)
		cd $bootdir
		tftp -g -l check.md5 -r boot/image.md5 -b $TFTP_BLOCKSIZE $SERVER_IP || \
		exit_failed_no_md5
		if ! md5sum -c check.md5 ; then
                        echo "Your installation is out of date. Performing the update"
			if [ -e initrd ]; then
				rm -f initrd
				rm -f vmlinuz
			fi
			tftp -g -l initrd -r boot/initrd -b $TFTP_BLOCKSIZE $SERVER_IP || \
				exit_transfer_failed
			tftp -g -l vmlinuz -r boot/vmlinuz -b $TFTP_BLOCKSIZE $SERVER_IP || \
				exit_transfer_failed
			tftp -g -l lib.update -r boot/lib.squash -b $TFTP_BLOCKSIZE $SERVER_IP || \
				exit_transfer_failed
			exit_update_completed
		else
			exit_everything_already_current
		fi
	;;
        SH)
                cd $bootdir
                wget http://$SERVER_IP/boot/image.md5 || \
		exit_failed_no_md5
                if ! md5sum -c check.md5 ; then
			echo "Your installation is out of date. Performing the update"
                        if [ -e initrd ]; then
                                rm -f initrd
                                rm -f vmlinuz
                        fi
                        wget http://$SERVER_IP/boot/initrd || \
				exit_transfer_failed
                        wget http://$SERVER_IP/boot/vmlinuz || \
				exit_transfer_failed
                        wget http://$SERVER_IP/boot/lib.squash lib.update || \
				exit_transfer_failed
			exit_update_completed
                else
			exit_everything_already_current
                fi

	;;
	WEB)
		cd /tmp
		wget ${WEBUPDATEROOT}/image-$TS_VERSION.md5 -O check.md5 || \
		exit_failed_no_md5
		cd $bootdir
		if ! md5sum -c /tmp/check.md5 ; then
			echo "Your installation is out of date. Performing the update"
			cd /tmp
			echo "Downloading a Default Image"
			wget ${WEBUPDATEROOT}/thindev-default-$TS_VERSION.tar.xz || \
				exit_transfer_failed
			cd $bootdir
			tar -xvf /tmp/thindev-default-$TS_VERSION.tar.xz || \
				exit_unwind_failed
			rm /tmp/thindev-default-$TS_VERSION.tar.xz
			exit_update_completed
		else
			exit_everything_already_current
		fi
	;;
	PATH)
		if [ -e $LPATH/image.md5 ] ;then
			cd $bootdir
			cp --update $LPATH/image.md5 check.md5
			if ! md5sum -c check.md5 ; then
				echo "Your installation is out of date. Performing the update"
				if [ -e $LPATH/initrd ] && [ -e $LPATH/vmlinuz ]; then
	       	 			cp --update $LPATH/initrd initrd || \
						exit_copy_failed
	        			cp --update $LPATH/vmlinuz vmlinuz || \
						exit_copy_failed
	  		      		if [ -e $LPATH/lib.squash ]; then
	                			cp --update $LPATH/lib.squash lib.update || \
						exit_copy_failed
					fi
					exit_update_completed
				else
					exit_invalid_path
				fi
			else
				exit_everything_already_current
			fi
		else
			exit_failed_no_md5
		fi
	;;
esac
