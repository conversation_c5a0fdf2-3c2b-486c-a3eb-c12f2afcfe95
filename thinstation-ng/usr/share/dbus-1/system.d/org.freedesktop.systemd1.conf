<?xml version="1.0"?> <!--*-nxml-*-->
<!DOCTYPE busconfig PUBLIC "-//freedesktop//DTD D-BUS Bus Configuration 1.0//EN"
        "https://www.freedesktop.org/standards/dbus/1.0/busconfig.dtd">

<!--
  SPDX-License-Identifier: LGPL-2.1-or-later

  This file is part of systemd.

  systemd is free software; you can redistribute it and/or modify it
  under the terms of the GNU Lesser General Public License as published by
  the Free Software Foundation; either version 2.1 of the License, or
  (at your option) any later version.
-->

<busconfig>

        <policy user="root">
                <allow own="org.freedesktop.systemd1"/>

                <!-- Root clients can do everything -->
                <allow send_destination="org.freedesktop.systemd1"/>
                <allow receive_sender="org.freedesktop.systemd1"/>

                <!-- systemd may receive activator requests -->
                <allow receive_interface="org.freedesktop.systemd1.Activator"
                       receive_member="ActivationRequest"/>
        </policy>

        <policy context="default">
                <deny send_destination="org.freedesktop.systemd1"/>

                <!-- Completely open to anyone: org.freedesktop.DBus.* interfaces -->

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.DBus.Introspectable"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.DBus.Peer"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.DBus.Properties"
                       send_member="Get"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.DBus.Properties"
                       send_member="GetAll"/>

                <!-- Completely open to anyone: org.freedesktop.systemd1.Manager interface -->

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="GetUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="GetUnitByPID"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="GetUnitByInvocationID"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="GetUnitByControlGroup"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="GetUnitByPIDFD"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="LoadUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="GetUnitProcesses"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="GetJob"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="GetJobAfter"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="GetJobBefore"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="ListUnits"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="ListUnitsFiltered"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="ListUnitsByPatterns"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="ListUnitsByNames"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="ListJobs"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="Subscribe"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="Unsubscribe"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="Dump"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="DumpByFileDescriptor"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="DumpUnitsMatchingPatterns"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="DumpUnitsMatchingPatternsByFileDescriptor"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="ListUnitFiles"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="ListUnitFilesByPatterns"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="GetUnitFileState"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="GetDefaultTarget"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="GetUnitFileLinks"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="LookupDynamicUserByName"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="LookupDynamicUserByUID"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="GetDynamicUsers"/>

                <!-- Completely open to anyone: org.freedesktop.systemd1.Unit interface -->

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Service"
                       send_member="GetProcesses"/>

                <!-- Completely open to anyone: org.freedesktop.systemd1.Slice interface -->

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Slice"
                       send_member="GetProcesses"/>

                <!-- Completely open to anyone: org.freedesktop.systemd1.Scope interface -->

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Scope"
                       send_member="GetProcesses"/>

                <!-- Completely open to anyone: org.freedesktop.systemd1.Socket interface -->

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Socket"
                       send_member="GetProcesses"/>

                <!-- Completely open to anyone: org.freedesktop.systemd1.Mount interface -->

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Mount"
                       send_member="GetProcesses"/>

                <!-- Completely open to anyone: org.freedesktop.systemd1.Swap interface -->

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Swap"
                       send_member="GetProcesses"/>

                <!-- Managed via polkit or other criteria: org.freedesktop.systemd1.Manager interface -->

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="StartUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="StartUnitReplace"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="StopUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="ReloadUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="RestartUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="TryRestartUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="ReloadOrRestartUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="ReloadOrTryRestartUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="BindMountUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="MountImageUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="KillUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="QueueSignalUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="ResetFailedUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="SetUnitProperties"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="RefUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="UnrefUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="StartTransientUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="AttachProcessesToUnit"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="CancelJob"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="ClearJobs"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="ResetFailed"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="Reload"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="Reexecute"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="EnableUnitFiles"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="EnableUnitFilesWithFlags"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="DisableUnitFiles"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="DisableUnitFilesWithFlags"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="DisableUnitFilesWithFlagsAndInstallInfo"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="ReenableUnitFiles"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="LinkUnitFiles"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="PresetUnitFiles"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="PresetUnitFilesWithMode"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="MaskUnitFiles"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="UnmaskUnitFiles"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="RevertUnitFiles"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="SetDefaultTarget"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="PresetAllUnitFiles"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="AddDependencyUnitFiles"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Manager"
                       send_member="SetShowStatus"/>

                <!-- Managed via polkit or other criteria: org.freedesktop.systemd1.Job interface -->

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Job"
                       send_member="Cancel"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Job"
                       send_member="GetAfter"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Job"
                       send_member="GetBefore"/>

                <!-- Managed via polkit or other criteria: org.freedesktop.systemd1.Unit interface -->

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Unit"
                       send_member="Start"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Unit"
                       send_member="Stop"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Unit"
                       send_member="Reload"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Unit"
                       send_member="Restart"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Unit"
                       send_member="TryRestart"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Unit"
                       send_member="ReloadOrRestart"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Unit"
                       send_member="ReloadOrTryRestart"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Unit"
                       send_member="Kill"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Unit"
                       send_member="QueueSignal"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Unit"
                       send_member="ResetFailed"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Unit"
                       send_member="SetProperties"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Unit"
                       send_member="Ref"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Unit"
                       send_member="Unref"/>

                <!-- Managed via polkit or other criteria: org.freedesktop.systemd1.Service interface -->

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Service"
                       send_member="AttachProcesses"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Service"
                       send_member="BindMount"/>

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Service"
                       send_member="MountImage"/>

                <!-- Managed via polkit or other criteria: org.freedesktop.systemd1.Scope interface -->

                <allow send_destination="org.freedesktop.systemd1"
                       send_interface="org.freedesktop.systemd1.Scope"
                       send_member="AttachProcesses"/>

                <allow receive_sender="org.freedesktop.systemd1"/>
        </policy>

</busconfig>
