
##
# --- Proxy Options
#
# PROXY_MODE			 Can be set to 'auto' or 'manual'.
# PROXY_AUTOCONFIG		 Set to a url for a PAC file for automatic proxy configuration. Also sets mode to 'auto'. 
# PROXY_HOST             DNS Name or IP Address of HTTP Proxy Server
# PROXY_PORT             TCP/IP Port to use on HTTP Proxy Server
# PROXY_IGNORE		 	 Comma seperated list of hosts for which to bypass the proxy. "localhost,127.0.0.1/8" by default
# PROXY_MANDATORY		 True/False, If enabled, will use mandatory settings found in /gnome-core/etc/gconf/gconf.xml.mandatory
#						 otherwise, will use default settings found in /gnome-core/etc/gconf/gconf.xml.defaults
# PROXY_USER             HTTP Proxy Authentication User
# PROXY_PASSWORD         HTTP Proxy User Password
# PROXY_USE_SAME         True/False, If enabled, HTTP Proxy settings will be used for all Protocols
# PROXY_SECURE_HOST      HTTPS Proxy Host
# PROXY_SECURE_PORT      HTTPS Proxy Port
# PROXY_SOCKS_HOST       SOCKS Proxy Host
# PROXY_SOCKS_PORT       SOCKS Proxy Port
# PROXY_FTP_HOST         FTP Proxy Host
# PROXY_FTP_PORT         FTP Proxy Port


#PROXY_HOST=***********
#PROXY_PORT=8080
#PROXY_USE_SAME=true
#PROXY_IGNORE="localhost,127.0.0.1/8"
