2.3.1: <PERSON><PERSON><PERSON>
24 December 2024
- Install a default /etc/default/pcscd file
- auth.c: implement polkit support for FreeBSD
- meson:
  . also build static version of libpcsclite
  . add options to disable polkit and libsystemd
  . add "filter_names" in features when needed
- Doxygen: document dwCurrentState use for "\\?PnP?\Notification"
- Some other minor improvements

2.3.0: <PERSON><PERSON><PERSON>
3 August 2024
- SCardGetStatusChange(): add the number of reader events
- Add Appstream metainfo announcing HW support
- meson: specify minimum meson version to use
- fix formats under musl libc
- Send libpcsclite.so logs to stderr instead of stdout
- Some other minor improvements

2.2.3: <PERSON><PERSON><PERSON>
26 May 2024
- meson:
  . Fix build on Slackware 15
  . fail if both libusb and libudev are used
- Fix memory leak on exit
- libpcscspy: dump an output buffer only if the call succeeded
- Some code cleanup

2.2.2: <PERSON><PERSON><PERSON>
20 May 2024
- Serial support is ENABLED by default

2.2.1: <PERSON><PERSON><PERSON>
8 May 2024
- fix meson related issues
- Some code cleanup

2.2.0: <PERSON><PERSON><PERSON>
3 May 2024
- provide files for meson build tool (replaces autoconf/auoomake)
- fix a missing symbol in libpcscspy (bug introduced by the previous version)
- fix shutdown issues with hotplug_libusb
- update pcsc-spy manpage
- update copyright date
- Some other minor improvements

2.1.0: Ludovic Rousseau
12 April 2024
- LIBPCSCLITE_DELEGATE is used to redirect to another libpcsclite library
- setup_spy.sh displays the LIBPCSCLITE_DELEGATE value to use for spying
- provides libfake.c as a sample source code
- Some other minor improvements

2.0.3: Ludovic Rousseau
3 March 2024
- add SCARD_E_UNKNOWN_RES_MNG back

2.0.2: Ludovic Rousseau
3 March 2024
- SCardConnect() & SCardReconnect(): restrict the protocol used
- negotiate PTS also for the backup protocol
- pcscd.8:
  . document --disable-polkit
  . add "CONFIGURATION FILE" section
- Some other minor improvements

2.0.1: Ludovic Rousseau
24 November 2023
- SCardConnect(): return SCARD_W_SECURITY_VIOLATION when needed (polkit)
- SCardCancel(): return SCARD_S_SUCCESS even if the client already finished
- polkit is enabled by default
- libpcscspy: fix a crash with NULL pointers
- Doxygen: fix SCardBeginTransaction() documentation
- fix pcscd internal thread safety issues (clang -fsanitize=thread)
- Some other minor improvements


9 June 2023
2.0.0: Ludovic Rousseau
9 June 2023
- Adjust USB drivers path at run-time via environment variable PCSCLITE_HP_DROPDIR
- Add '--disable-polkit' option
- Reset eventCounter when a reader is removed
- Add "polkit" in "pcscd -v" output if enabled
- Doxygen: document SCARD_E_INVALID_VALUE for some functions
- use secure_getenv(3) if available
- Some other minor improvements


1.9.9: Ludovic Rousseau
11 September 2022
- SCardEstablishContext() may return SCARD_W_SECURITY_VIOLATION if refused by Polkit
- Fix SCardReleaseContext() failure on orphan handles
- Fix SCardDisconnect() on orphan handle
- pcsc-spy: log the pioSendPci & pioRecvPci SCardTransmit() parameters
- Improve the log from pcscd: log the return code in text instead of hex
- Some other minor improvements


1.9.8: Ludovic Rousseau
11 June 2022
- Install install_spy.sh & uninstall_spy.sh scripts in docdir
- SCardTransmit(): do not fail if receive buffer is "too large"
- SCardControl(): do not fail if receive buffer is "too large"
- fix some memory leaks on shutdown
- use a better random number generator
- Some other minor improvements


1.9.7: Ludovic Rousseau
13 May 2022
- disable strict compilation by default
- fix 3 warnings


1.9.6: Ludovic Rousseau
11 May 2022
- do not fail reader removal in some specific cases (USB/Thunderbolt port)
- improve documentation regarding /etc/reader.conf.d/
- SCardGetStatusChange: speedup the case DISABLE_AUTO_POWER_ON
- configure:
  . add --disable-strict option
   By default the compiler arguments are now:
   -Wall -Wextra -Wno-unused-parameter -Werror ${CFLAGS} 
  . fail if flex is not found
- fix different data races
- pcscdaemon: -v displays internal constants values: 
  MAX_READERNAME & PCSCLITE_MAX_READERS_CONTEXTS
- Some other minor improvements


1.9.5: Ludovic Rousseau
4 December 2021
- pcscd: autoexit even if no client connects
- Fix variable substitution in systemd units
- fix potential race conditions with powerState handling
- Add and use tag TAG_IFD_DEVICE_REMOVED
- UnitaryTests: port code to Python 3


1.9.4: Ludovic Rousseau
1 October 2021
- fix a memory leak when libusb is used for hotplug (i.e. non-Linux
  systems)


1.9.3: Ludovic Rousseau
6 August 2021
- fix a stupid regression with systemd introduced in the previous version


1.9.2: Ludovic Rousseau
3 August 2021
- improve NetBSD support
- pcsc-spy: version 1.1
  . add option -t|--thread
  . x10 speed increase
  . correctly exit at end-of-file
  . remove, now useless, support of macOS
- systemd:
  . use /etc/default/pcscd as EnvironmentFile
  . use $PCSCD_ARGS to specify more arguments
- SetProtocol: Handle IFD_NOT_SUPPORTED from the driver
- hotplug_libudev.c: sanitize interface name
- pcsc_demo: change licence from GPLv3 to BSD
- use Python 3 for Python scripts (psc-spy, UnitaryTests)
- Some other minor improvements


1.9.1: Ludovic Rousseau
16 February 2021
- Do not (possibly) lock a reader if allocating hCard fails
- Fix a hang in SCardTransmit()
- Do not report an error if the wrong interface is used by the driver
- Update reader state when a card is removed during an exchange
- readerfactory: Make sure a freed Reader Context is not accessed
- PHSetProtocol(): supports T=0&1 cards on T=0 reader
- hotplug-libusb:
  . support CCIDCLASSDRIVER
  . add interface name to reader name
  . remove obsolete libhal scheme
- Some other minor improvements


1.9.0: Ludovic Rousseau
14 June 2020
- SCardEndTransaction(): greatly improve performances (x300)
- tokenparser: accept any Unicode character in a reader name
- Use /run instead of /var/run by default
- Fix a memory leak from a polkit call
- Some other minor improvements


1.8.26: Ludovic Rousseau
3 January 2020
- Use poll() instead of select() to allow file descriptor higher than FD_SETSIZE
- Enable reader filtering by default
- pcsc-spy:
  . Do not read output buffer after error
  . Adjust code to handle autoallocated buffers
  . fix year-2038 issue by using long instead of int
- Android: fix compilation
- if client/server protocol mismatch:
  . log an explicit message
  . SCardEstablishContext() returns SCARD_E_SERVICE_STOPPED
- polkit: log the error message if polkit_authority_get_sync() fails
- Exit with EXIT_SUCCESS on shutdown to please systemd
- Doxygen: fix minor issues in the documentation
- Add --disable-documentation option
- Fix a minor memory leak


1.8.25: Ludovic Rousseau
25 March 2019
- Fix a socket issue when pcscd is used inside LXC container
- pcsc-spy: always provide a total time of execution
- Fix resource leak if SCardEstablishContext() fails
- Fix realloc(3) error handling (possible memory leak)
- Remove usage of function chmod(2) to use fchmod(2) (fix race condition)


1.8.24: Ludovic Rousseau
12 October 2018
- the project moved to https://pcsclite.apdu.fr/
- SCardGetStatusChange(): Fix a rare race condition
- SCardReleaseContext(): do not release a lock owned by another context
- SCardReconnect(): suspend card auto power off
- Allow "=" in serial driver filenames
- Add the thread id in the pcscd log lines
- pcsc-spy: correctly handle incomplete log file
- Simclist: avoid to divide by zero in list_findpos()
- Some other minor improvements


1.8.23: Ludovic Rousseau
18 December 2017
- use libsystemd instead sd-daemon.{c,h}
- install_spy.sh: add support of Ubuntu
- spy: add support SCardStatus() with NULL pcbAtrLen
- Some other minor improvements


1.8.22: Ludovic Rousseau
17 June 2017
- SCardCancel() was broken in 1.8.21. The call was blocking.
- Enable use of info level logging for pcscd using -i/--info


1.8.21: Ludovic Rousseau
20 May 2017
- SCardCancel():
  . fix a race condition bug
  . fix a possible use after free
  . improve Doxygen documentation
- SCardGetStatusChange(): fix a race condition when a reader is removed
- SCardDisconnect(): fix status update for SCARD_UNPOWER_CARD
- pcsc_stringify_error(): use Thread-local storage
- pcsc_stringify_error() now returns a const char *
- SCardControl() may return SCARD_E_INSUFFICIENT_BUFFER when
  pbRecvBuffer is not big enough to receive the card response.
- Fix compilation error with GCC 4.9
- UnitaryTests: add support of Python 3
- pcsc-spy: add support of Python 3
- Some other minor improvements


1.8.20: Ludovic Rousseau
30 December 2016
- Fix a crash and potential security issue in pcscd


1.8.19: Ludovic Rousseau
9 December 2016
- SCardGetStatusChange(): Fix a (rare) race condition
- Doxygen:
  . SCardGetStatusChange() may return SCARD_E_UNKNOWN_READER
  . SCardConnect() and SCardReconnect() will never return SCARD_E_NOT_READY
- pcsc-spy:
  . fix display of execution time
  . log the thread number in the results
- Some other minor improvements


1.8.18: Ludovic Rousseau
10 August 2016
- SCardDisconnect(): much faster with SCARD_UNPOWER_CARD
- SCardConnect(): Fix a possible duplicated hCard context
- Fix compilation on FreeBSD
- Fix compilation on Solaris
- Some other minor improvements


1.8.17: Ludovic Rousseau
29 May 2016
- Fix SCardEndTransaction() issue with a SCARD_SHARE_EXCLUSIVE connection
- Fix an issue when used with systemd (problem in signal handler)
- SCardGetAttrib(): set pcbAttrLen when buffer is too small
- Doxygen: SCardGetAttrib() pbAttr can be NULL
- Doxygen: SCardGetAttrib() *pcbAttrLen contains the buffer size
- fix compilation warnings and link errors on SunOS
- Some other minor improvements


1.8.16: Ludovic Rousseau
20 March 2016
- SCardCancel() was not correctly handled
  When a SCardGetStatusChange() was cancelled then a next PC/SC call
  after the SCardGetStatusChange() may fail with a strange error code if
  the event waited in SCardGetStatusChange() occurs.
- Doxygen: fix different documentation issues
- SCARD_SCOPE_GLOBAL is now defined in a public header (even if never used)
- Enable Trace and Profile features using compiler flags and without
  modifying the source code
- Some other minor improvements and bug corrections


1.8.15: Ludovic Rousseau
25 December 2015
- Add support of remove and/or customize PC/SC reader names using
  PCSCLITE_FILTER_IGNORE_READER_NAMES and PCSCLITE_FILTER_EXTEND_READER_NAMES
  See http://ludovicrousseau.blogspot.fr/2015/12/remove-andor-customize-pcsc-reader-names.html
- Some other minor improvements and bug corrections


1.8.14: Ludovic Rousseau
5 August 2015
- Threading: lock the PC/SC context in a safe way
- Threading: lock the card context in a safe way
- SCardGetStatusChange(): fix card movement rare bug
- Doxygen:
  . SCardTransmit() may return SCARD_E_INSUFFICIENT_BUFFER
  . SCardEndTransaction() The disposition IS used and the dwDisposition
    parameter HAS an effect.
  . SCardReconnect() do not release locks
  . fix typos
- Move the source code repository from subversion to git
- Use asprintf(3) instead of strlcat(3) and strlcpy(3)
- Allow to use pcscd in a remote session (polkit issue)
- Some other minor improvements and bug corrections


pcsc-lite-1.8.13: Ludovic Rousseau
7 November 2014
- fix a systemd + libudev hotplug bug introduced in version 1.8.12.
  The list of readers was not (yet) available just after the start of pcscd
- Make the license more 3-clause BSD like
- fix a rare race condition in the (non default) libusb hotplug
- Some other minor improvements and bug corrections


pcsc-lite-1.8.12: Ludovic Rousseau
24 September 2014
- make hotplug using libudev (default) more robust
- add ReiserFS file system support (for configuration files)
- add musl libC support (increase the thread stack)
- Some other minor improvements and bug corrections


pcsc-lite-1.8.11: Ludovic Rousseau
14 February 2014
- Add polkit support. See doc/README.polkit
- /etc/reader.conf: CHANNELID and DEVICENAME are both optional but not
  at the same time
- Some other minor improvements and bug corrections


pcsc-lite-1.8.10: Ludovic Rousseau
19 October 2013
- do not export the internal log_msg symbol from libpcsclite.so.1


pcsc-lite-1.8.9: Ludovic Rousseau
16 October 2013
- SCardEndTransaction(): Return an error if is called with no
  corresponding SCardBeginTransaction()
- SCardGetAttrib(): Add support of SCARD_ATTR_DEVICE_SYSTEM_NAME
- SCardGetAttrib(): Fix bug in SCARD_ATTR_DEVICE_FRIENDLY_NAME
- SCardBeginTransaction() was not correctly releasing a mutex when the
  hCard handle was invalidated
  The problem was that SCardGetStatusChange() was blocked because
  SCardBeginTransaction() had not released the context mutex.
- PCSC/reader.h: Use C99 flexible array member
  The structures PIN_MODIFY_STRUCTURE and PIN_VERIFY_STRUCTURE now use a
  C99 flexible array member when available for abData field.
  uint8_t abData[];
- Add support of --reader-name-no-serial and --reader-name-no-interface
  It is now possible to NOT add the USB serial number of the reader
  using --reader-name-no-serial
  It is now possible to NOT add the CCID interface name of the reader
  using --reader-name-no-interface
- Add support of serialconfdir pkg-config variable
  It is now possible to use pkg-config to get the directory used by
  pcscd to fetch serial drivers configurations.
  $ pkg-config libpcsclite --variable=serialconfdir
  /etc/reader.conf.d
- pcsc-spy: Try to display the thread in the order they appear in the log
- pcsc-spy: Add SCARD_ATTR_DEVICE_SYSTEM_NAME
- Check the Info.plist file is (a minimum) correct
- Update PROTOCOL_VERSION_MINOR from 2 to 3
  We broke the API between version 1.8.3 and 1.8.4 by changing the value
  of MAX_READERNAME. This change should have been made before releasing
  version 1.8.4 to make mix of versions clearly non working instead of
  failing with strange errors.
- hotplug_libudev.c: Fix a memory leak in case of error
- Fix OpenBSD 5.2 compilation regarding dlopen
- correctly manage thread safe multi-slot readers
- Do not use pthread_atfork() any more (fix problem on FreeBSD)
- fix memory leaks.
  This was not really a problem unless you embed pcscd in another
  process and do init/deinit pcscd without exiting the process (as maybe
  used on Android or iOS).
- pcscd.8 manpage: add documentation for --max-thread,
  --max-card-handle-per-thread, --max-card-handle-per-reader,
  --force-reader-polling, --error, --critical and --color
- Some other minor improvements and bug corrections


pcsc-lite-1.8.8: Ludovic Rousseau
16 January 2013
- /etc/reader.conf: handle FRIENDLYNAME with no " delimiters (serial readers)
- Info.plist: Correctly handle reader names containing & (USB readers)


pcsc-lite-1.8.7: Ludovic Rousseau
28 November 2012
- Fix a problem when a reader is unplugged (and the reader is still in use)


pcsc-lite-1.8.6: Ludovic Rousseau
30 August 2012
- Fix a problem when only serial drivers are used (no hotplug/USB
  driver)
- increase log buffer size from 160 to 2048. Some "long" log lines where
  truncated.
- Fix redirection of stdin, stdout and stderr to /dev/null when pcscd is
  started as a daemon (default)
- Some other minor improvements and bug corrections


pcsc-lite-1.8.5: Ludovic Rousseau
4 August 2012
- Fix crash when a reader is unplugged while pcscd is in the middle of a
  PC/SC function
- SCardBeginTransaction(): fix a bug introduced in version 1.8.4
  related to sharing
- Some other minor improvements and bug corrections


pcsc-lite-1.8.4: Ludovic Rousseau
26 June 2012
- Add [ and ] in the list of accepted characters for a reader name
- truncates the reader name if it is too long instead of rejecting the
  reader
- The restriction to have to call SCardEstablishContext() in each thread
  has been removed. Threads could now share a PC/SC context.
- Fix compiler failure for static driver
- Update IFDHandler API Doxygen regarding the "libusb-1.0" naming scheme
- Some other minor improvements and bug corrections


pcsc-lite-1.8.3: Ludovic Rousseau
30 March 2012
- ignore directories and hidden (.*) files when parsing a configuration
  directory (like /etc/reader.conf.d/)
- add Mac OS X for PC/SC spy tool
- fix a bug in PC/SC spy tool when loading of the real library fails
- add PCSCv2_PART10_PROPERTY_dwMaxAPDUDataSize,
  PCSCv2_PART10_PROPERTY_wIdVendor and PCSCv2_PART10_PROPERTY_wIdProduct
  from PC/SC v2 part 10 release 2.02.09 (not yet published)
- Some other minor improvements and bug corrections


pcsc-lite-1.8.2: Ludovic Rousseau
18 January 2012
- rename pcsc-spy.py to pcsc-spy and install it as a normal binary (in
  /usr/local/bin by default)
- write a pcsc-spy.1 manpage
- fix a bug with a multi-slot reader
- Info.plist parser: avoid a buffer read overflow in &amp; management
- Some Doxygen improvements


pcsc-lite-1.8.1: Ludovic Rousseau
25 November 2011
- Distribute missing files from src/spy/


pcsc-lite-1.8.0: Ludovic Rousseau
19 November 2011
- PC/SC spy tool
- Support systemd socket activation (the auto start of pcscd from the
  library has been removed. Use systemd instead)
- SCardGetStatusChange(): check all the readers are already known and
  return SCARD_E_UNKNOWN_READER if a reader name is not present.
  Windows XP has this behavior.
- SCardEstablishContext(): Invalidate all the handles in the son after a
  fork
- Add define of FEATURE_EXECUTE_PACE from PCSC v2 Part 10 Amendment 1
  2011-06-03
- Fix some memory leaks reported by Coverity
- Enable silent build by default
- log_line(): correctly calculate delta time when no color is used
  The update of last_time was only done in case of colorization
  (LogDoColor). So on unsupported consoles the time was wrong.
- log_xxd_always(): Use a variable-length array
  The debug message buffer is no more with a fixed size (around 600
  bytes of buffer to log) but uses a variable-length array.
  It is now possible to log extended APDU of 64kB.
  The variable-length array feature is available in GCC in C90 mode and
  is mandatory in C99 standard.
- Some other minor improvements and bug corrections


pcsc-lite-1.7.4: Ludovic Rousseau
23 June 2011
- Fix a stupid bug from the previous version. T=1 cards were not
  working.


pcsc-lite-1.7.3: Ludovic Rousseau
22 June 2011
- COPYING: Add my name as copyright holder
- hotplug libudev: support libudev >= 171
- hotplug libusb: Fix a memory leak
- pcscd: exit immediately in case of SIGTERM
  Closes Debian bug #620305 "pcscd slows down shutdown/restart"
- Send logs to stdout instead of stderr
  It is now possible to use tee(1) to redirect logs in a file without
  first redirecting stderr to stdout
- Add command line option -T, --color: force use of colored logs
  The idea is to have colored logs even if they are redirected to a file
  or a pipe.
- Define g_rgSCardT?Pci as const structures to be more Windows like
  I do not expect a regression or compilation problem in WinSCard API
  users but how knows...
- log at level PCSC_LOG_DEBUG instead of PCSC_LOG_ERROR to avoid filling
  the system log file
- Remove the deprecated define FEATURE_MCT_READERDIRECT (replaced by
  FEATURE_MCT_READER_DIRECT)
- better Hurd support
- some other minor improvements and bug corrections


pcsc-lite-1.7.2: Ludovic Rousseau
31 March 2011
- fix a crash if a specific driver fails to work and no class driver is
  available

pcsc-lite-1.7.1: Ludovic Rousseau
30 March 2011
- use libudev only on Linux and libusb elsewhere. The configuration now
  works by default on GNU/kFreeBSD systems
- Try to use a (CCID) class driver if a specific driver fails to use the
  reader.
- fix a potential crash


pcsc-lite-1.7.0: Ludovic Rousseau
9 March 2011
- use libudev instead of (the deprecated) libhal


pcsc-lite-1.6.7: Ludovic Rousseau
22 February 2011
- better Mac OS X support
- Fix Alioth bug [#312960] SCardDisconnect when other context has transaction
- add support of multi-interfaces readers with libusb and not just libhal
- add a API tracing feature in the client side (#define DO_TRACE)
- allow the use of tracing and profiling features from different
  application threads
- fix a problem with a multi-slots reader
- fix minor problems detected by the clang tool
- some other minor improvements and bug corrections


pcsc-lite-1.6.6: Ludovic Rousseau
12 December 2010
- SCardGetStatusChange(): fix a bug on 64-bits systems
- Fix another bug because of a regression in internal list manager


pcsc-lite-1.6.5: Ludovic Rousseau
3 December 2010
- Power on the card _only_ if an application requests a connection.
  You can disable the feature using DISABLE_ON_DEMAND_POWER_ON in
  src/pcscd.h.in
  If DISABLE_AUTO_POWER_ON is defined then do not automatically power on
  the card. The card will be powered on on the first SCardConnect()
  See http://ludovicrousseau.blogspot.com/2010/10/card-auto-power-on-and-off.html
- SCardReconnect(): return SCARD_E_NO_SMARTCARD when card is removed and
  SCARD_W_UNRESPONSIVE_CARD when card is unresponsive instead of
  SCARD_E_PROTO_MISMATCH
- Install pcscd as sgid pcscd instead of suid root
  See http://ludovicrousseau.blogspot.com/2010/09/pcscd-auto-start.html
- SCardSetTimeout() is no more provided. This function is not provided
  by Microsoft and is deprecated since 2004 in pcsc-lite. 
- SCardCancelTransaction() is no more provided. This function is not
  provided by Microsoft and is deprecated since 2005 in pcsc-lite.
- Parsing the CCID Info.plist (159 readers supported) was, on a i386
  machine, done in 264306 Âµs and is now done 5547 Âµs => gain x47 or 4600%
  See http://ludovicrousseau.blogspot.com/2010/08/ram-and-cpu-improvements-in-pcsc-lite.html
- It is now possible to configure the local socket name to use using the
  environment variable PCSCLITE_CSOCK_NAME
  See http://ludovicrousseau.blogspot.com/2010/11/pcsc-client-and-server-on-two-different.html	  
- Wait until all connected readers have a chance to power up a possibly
  inserted card before accepting clients.
- restrict pcscd features when not run by root (so using suid): APDU
  logging or setting parameters are disabled for example
- fix compilation problem on kfreebsd-* systems
- PCSC/reader.h: HOST_TO_CCID_16() and HOST_TO_CCID_32() are now
  identity functions
  Since libccid 1.4.1 (revision 5252) the byte order is no more important
- If you want to use IFDHCreateChannel() instead of
  IFDHCreateChannelByName() then do not use any DEVICENAME line in the
  configuration file. IFDHCreateChannel() will then be called with the
  CHANNELID parameter.
- the CHANNELID parameter can also be a decimal number.
- Remove the support of IFDHandler v1 API. I don't know any driver using
  this API.
  See http://ludovicrousseau.blogspot.com/2010/10/ifdhandler-version-1-support-removed.html
- avoids a buffer overflow with badly formed ATR
- some other minor improvements and bug corrections


pcsc-lite-1.6.4: Ludovic Rousseau
15 August 2010
- Do not use sysconfdir as configuration directory but
  "${sysconfdir}/reader.conf.d" instead.
  Use --enable-confdir=DIR if you want to set a specific value without
  the "reader.conf.d" appended.


pcsc-lite-1.6.3: Ludovic Rousseau
15 August 2010
- "/reader.conf.d" is only appended to sysconfdir if no value of
  sysconfdir is provided
- Define LPSCARD_READERSTATE since this is used in the MSDN prototype.
  Use LPSCARD_READERSTATE in winscard.h instead of (SCARD_READERSTATE *)
  to mimic the MSDN API.
- fix a pcscd crash when the application uses a PCSC handle after a
  fork. The crash was with openvpn.
- some other minor improvements and bug corrections

pcsc-lite-1.6.2: Ludovic Rousseau
4 August 2010
- implement a "Forced suicide" mechanism.
  After 3 Ctrl-C without much reaction from pcscd (in fact the drivers)
  we force the suicide. Sometimes libusb is blocked in a kind of
  dead-lock and kill -9 was the only option.
- Add support of TAG_IFD_STOP_POLLING_THREAD to request the stop of the
  driver polling function.
- Avoid a division by 0. Closes [#312555] "simclist bug in pcsc-lite"
- if pcscd is stared by libpcsclite then close all file handles except
  stdin, stdout and stderr so that pcscd does not confiscate resources
  allocated by the application
- in case of auto exit create a new session so that Ctrl-C on the
  application will not also quit pcscd
- src/hotplug_libusb.c: port from libusb-0.1 to libusb-1.0
- default configuration is now $sysconfdir/reader.conf.d
- fix crash with empty config dir
- src/PCSC/winscard.h: Remove definitions of SCARD_READERSTATE_A
  PSCARD_READERSTATE_A and LPSCARD_READERSTATE_A types
- some other minor improvements and bug corrections


pcsc-lite-1.6.1: Ludovic Rousseau
4 June 2010
- SCardControl(): do not check for card events since we are talking to
  the reader not the card. A smart card removal should not make
  SCardControl() fail with SCARD_W_REMOVED_CARD
- pcscd do not timeout any more after 2 minutes of inactivity. If the
  other side of the socket dies we will get an error from the kernel.
  The problem was that if a client does nothing during
  PCSCLITE_READ_TIMEOUT (120 seconds by default) then pcscd considers it
  as a dead client and closes the connection. I guess this problem was
  present since the first version of pcsc-lite but nobody complained
  before.
- pcscd: do not return before most of the initialisation are done
  correctly. The idea is that pcscd can return an error code if the
  daemon fails to start correctly (hald not started for example).
  Before the patch pcscd became a daemon, then returned 0 (success) and
  then continued with the initialisation. If the initialisation failed
  it was too late to return an error code. The /etc/init.d/pcscd script
  was not aware of the failure.
  Closes https://bugzilla.redhat.com/show_bug.cgi?id=580321
  "/usr/sbin/pcscd exit codes broken"
- src/hotplug_libusb.c: Add a synchronisation so that if pcscd is auto
  started the initial reader list is available before the server takes
  commands from clients.
  Before the change early calls of SCardListReaders() returned an empty
  list of readers even if a reader was connected.
  Thanks to Patrice Angelini for the bug report
- SCardConnect() & SCardReconnect(): do not reset the cardProtocol in
  SCARD_SHARE_DIRECT case since the card has _not_ been reset. A new
  PPS negotiation would fail.
- Do not install files in /etc any more. Serial drivers are rare now.
- Avoids a crash if a client sends a unknown command.
  Thanks to Martin Vogt for the bug report
- some other minor improvements and bug corrections


pcsc-lite-1.6.0: Ludovic Rousseau
5 May 2010
- redesign the client/server communication:
  * no more shared memory used (allow pcscd and libpcsclite1.so to be on
  different computer and talk over a network)
  * no more difference between short and extended APDU
  * no more use of a /var/run/pcscd/pcscd.events/ directory. events are
  sent through the socket
  * simpler command format between client and server
  The side effect is that you are not able to mix an old pcscd with a
  new libpcsclite1.so or the reverse. SCardEstablishContext() will fail
  unless you update both sides of the communication.
- Use lists instead of fixed size arrays to store handles.
  It is now possible to have:
  - 200 simultaneous PC/SC clients instead of 16
  - 200 SCardConnect per client instead of 16
  - 200 clients per reader instead of 16
  The default value of 200 can be changed by giving an argument to pcscd
  --max-thread --max-card-handle-per-thread --max-card-handle-per-reader
  Thanks to Jean-Luc Giraud for the big patch
- Make SCardReconnect(), SCardStatus() and SCardTransmit() block instead
  of returning SCARD_E_SHARING_VIOLATION immediately. These functions
  will then behave like on Windows.
  This can happen if these functions are called when the reader is
  locked by a PCSC transaction
  (SCardBeginTransaction/SCardEndTransaction).
  You can define the environment variable PCSCLITE_NO_BLOCKING to use
  the old behavior.
  Thanks to Jean-Luc Giraud for the patch.
  http://archives.neohapsis.com/archives/dev/muscle/2010-q1/0041.html
- SCardEstablishContext(): try to start the pcscd daemon if not already
  running.
  . pcscd will suicide itself after 60 seconds of inactivity if it is
  started using --auto-exit. This is the default behavior when pcscd is
  started by libpcsclite
  . Set PCSCLITE_PCSCD_ARGS with the argument you want to pass to pcscd in
  autostart Only one argument is passed. The space character is not a
  separator. example: export PCSCLITE_PCSCD_ARGS=-dfa
- SCardListReaders(): can use SCARD_AUTOALLOCATE
- SCardGetAttrib(): return SCARD_E_INSUFFICIENT_BUFFER if the driver
  returns IFD_ERROR_INSUFFICIENT_BUFFER
  . add support of SCARD_ATTR_DEVICE_FRIENDLY_NAME as it is better
  implemented in pcscd (it knows the friendly name)
- SCardGetStatusChange(): Calling with cReaders == 0 will now just
  return SCARD_S_SUCCESS
  . Use the special reader name "\\?PnP?\Notification" to wait for a
  reader event notification
- SCardTransmit(): do not limit the minimum size of an APDU to 4 bytes.
  non ISO 7816-4 compliant cards (like Mifare DESFIRE) may use shorter
  commands
- SCardStatus(): returns SCARD_E_SHARING_VIOLATION if the reader is
  already used More conform to Windows
- PCSC/reader.h: update struct PIN_PROPERTIES_STRUCTURE to be conform
  with Revision 2.02.06, April 2009 of PCSCv2 part 10 Fields
  wLcdMaxCharacters and wLcdMaxLines have been removed
  . rename FEATURE_MCT_READERDIRECT in FEATURE_MCT_READER_DIRECT to be
  conform with ch. 2.3 of PCSC v2 part 10
  . add FEATURE_GET_TLV_PROPERTIES and FEATURE_CCID_ESC_COMMAND from
  PC/SC part 10 v2.02.07 March 2010
  . Add PCSCv2_PART10_PROPERTY_* defines
- SCardControl() return SCARD_E_UNSUPPORTED_FEATURE if the driver
  returned IFD_ERROR_NOT_SUPPORTED or IFD_NOT_SUPPORTED This is used to
  separate an unsupported value of ControlCode from a general error
- Use the standard --sysconfdir=DIR ($prefix/etc by default) instead of
  --enable-confdir=DIR for defining the directory containing reader.conf
- remove SCF support (PC/SC over Smart Card Framework). I never used
  this feature and SCF is now dead and replaced by JSR 268
  (javax.smartcardio)
- Better handling of PCSCLITE_STATIC_DRIVER as can be used on platforms
  using ÂµClinux (without dynamic loader).  This is used to statically
  link the reader driver to pcscd. Since the link is static you must
  define the IFDHandler API version at compilation time. Either define
  IFDHANDLERv1, IFDHANDLERv2 or IFDHANDLERv3
- Use dynamic instead of static allocation for the driver library
  filename. The filename is no more limited to 100 characters.
  Closes: [#312332] MAX_LIBNAME too short?
- force the return codes SCARD_* to be long since the SCard* functions
  return a LONG type
- Add the ability to parse all the configuration files of a directory
  instead of just one configuration file. update-reader.conf is then now
  obsolete.
- Add --enable-embedded (default is no) to build pcsc-lite for an
  embedded system. This will activate the NO_LOG option to disable
  logging and limit RAM and disk consumption.
- If NO_LOG is defined then no log are displayed. The idea is to limit
  the binaries size on disk and RAM consumption at execution time.
  With NO_LOG defined we gain 26% (17 kB) for the .text segment of pcscd
  and 15% (4 kB) for the .text segment of libpcsclite.so (for i386)
- Define a minimal pcsc_stringify_error() if NO_LOG is defined. Only the
  error code in hex is displayed in this case.
  Gain: 2kB of .text (10%) for libpcsclite
- Add --disable-serial and --disable-usb options
  --disable-serial removes support of /etc/reader.conf gain: 8.0kB of
  .text (12%) and 160 bytes of .bss (4%) for pcscd
  --disable-usb removes support of USB hotplug gain: 9.7kB of .text
  (14%) and 960 bytes of .bss (23%) for pcscd
  If you use both options (and use a static driver configuration) gain:
  17.7kB of .text (26%) and 1152 bytes of .bss (28%) for pcscd
- Better support of Android
- some other minor improvements and bug corrections


pcsc-lite-1.5.5: Ludovic Rousseau
28 July 2009
- add the reader interface name if provided by the device
- SCardTransmit(): return SCARD_E_UNSUPPORTED_FEATURE if
  SCARD_PROTOCOL_RAW is requested by unsupported
- SCardConnect() and SCardReconnect(): set dwActiveProtocol to
  SCARD_PROTOCOL_UNDEFINED if SCARD_SHARE_DIRECT is used (conform to
  MSDN). Contrary to Windows winscard behavior, the reader is accessed in
  shared mode and not exclusive mode if SCARD_SHARE_DIRECT is used.
- SCardControl(): correctly check for buffer overflow (bug introduced in
  pcsc-lite 1.5.4)
- some other minor improvements and bug corrections


pcsc-lite-1.5.4: Ludovic Rousseau
24 June 2009
- SCardGetStatusChange() works again. It was broken in some cases since
  version 1.5.2
- detect buffer overflows if pcscd if used by a rogue client
- force access rights on /var/run/pcscd to be sure it can be used by a
  libpcsclite client without privileges
- create the PCSCLITE_EVENTS_DIR directory with the sticky bit so only
  root or the owner of the event files can remove them
- if RFAddReader() fails with the libhal scheme then we try with the
  (old) libusb scheme.  This patch should allow proprietary drivers to
  work even if pcsc-lite is compiled with libhal support.
- give a higher priority to a specific driver over the CCID Class
  driver. This should allow proprietary drivers to be used instead of
  libccid when possible
- some other minor improvements and bug corrections


pcsc-lite-1.5.3: Ludovic Rousseau
29 April 2009
- SCardEstablishContext(): check we do not reuse an already allocated
  hContext
  Thanks to Daniel Nobs for the bug report and patch
- pcsclite.h: add missing SCARD_E_* and SCARD_W_* return code. They are
  unused by pcsc-lite but defined on Windows
- reader.h: add PIN_PROPERTIES_STRUCTURE structure and FEATURE_IFD_PIN_PROPERTIES
  Thanks to Martin Paljak for the patch
- remove powermgt_macosx.c since it is using APSL version 1.1 instead of
  the BSD-like licence like the other files
  Thanks to Stanislav Brabec for the bug report
- avoid a possible crash due to a race condition
  Thanks to Matheus Ribeiro for the patch
- change default log level from PCSC_LOG_INFO to PCSC_LOG_ERROR to limit
  syslog pollution
- CardDisconnect(): call RFUnlockAllSharing() instead of
  RFUnlockSharing() to release all nested locks. The problem occurs if
  SCardBeginTransaction() are made without corresponding
  SCardEndTransaction().  OpenSC "pkcs11-tool -I" exhibits such a
  behavior.
  Thanks to Marc Rios Valles for the bug report
- some other minor improvements and bug corrections


pcsc-lite-1.5.2: Ludovic Rousseau
6 February 2009
- SCardGetStatusChange(): return if the state of the reader changed
  since the previous call. Thanks to Thomas Harning for the patch
- SCardCancel() no works as expected. It got broken in version 1.5.0.
  Closes: [#311342] SCardCancel does not cancel an outstanding
  SCardGetStatusChange
- log TxBuffer and RxBuffer if the SCardControl() command failed.
  Closes: [#311376] PCSC_LOG_VERBOSE via -dd; print details of "Card not
  transacted"
- add a mutex to avoid a race condition
  Closes: [#311377] Race condition in SCardBeginTransaction
- SCardGetStatusChange() may not return if the reader was removed.
- some other minor improvements and bug corrections


pcsc-lite-1.5.1: Ludovic Rousseau
7 January 2009
- Extended APDU of more than 2048 bytes were corrupted. The problem was
  introduced in version 1.3.3 (2 years ago) by making the code compile
  with Sun Studio 11.
  Thanks to Eric Mounier for the patch
- some other minor improvements and bug corrections


pcsc-lite-1.5.0: Ludovic Rousseau
18 November 2008
- correctly handle up to PCSCLITE_MAX_READERS_CONTEXTS readers (instead
  of PCSCLITE_MAX_READERS_CONTEXTS-1)
- SCardGetStatusChange()
  . now returns SCARD_E_TIMEOUT instead of SCARD_S_SUCCESS if dwTimeout
    == 0 (conform to Windows XP)
  . add support of reader name \\?PnP?\Notification to detect reader
    insertion/removal (conform to Windows XP)
  . if a reader disappear also set SCARD_STATE_UNAVAILABLE in
    dwEventState (more conform to Windows XP)
- SCardStatus(): add support of SCARD_AUTOALLOCATE for pcchReaderLen and
  pcbAtrLen
- SCardGetStatusChange() now uses asynchronous events instead of polling
- more and/or better Doxygen documentation
- SCardTransmit(): correctly pass the pioRecvPci parameter
- SCardConnect() and SCardReconnect(): correct a bug when two
  applications were calling SCardConnect() or SCardReconnect() at the
  exact same time
- pcscd logs the command name sent by the application (when in debug mode)
- some other minor improvements and bug corrections


pcsc-lite-1.4.102: Ludovic Rousseau
27 June 2008
- pcscd -v now displays the enabled features
- add support of SCARD_AUTOALLOCATE in SCardListReaders(),
  SCardListReaderGroups() and SCardGetAttrib
- add SCardFreeMemory()
- try to use the reader polling thread also for the other slots on a
  multi-slots reader
- solve a possible crash with SCardCancel() in multithreading environment
- SCardConnect(), SCardReconnect(): do not check the parameter
  dwPreferredProtocols if dwShareMode == SCARD_SHARE_DIRECT
  This is used on contactless readers to talk to the reader without any
  card and "random" value of dwPreferredProtocols
- better support of driver termination (when pcscd exits)
- kill the driver polling thread only if the driver supports it
  (declared using TAG_IFD_POLLING_THREAD_KILLABLE)
- generate a .tar.bz2 archive (smaller than the .tar.gz archive)
- some other minor improvements and bug corrections


pcsc-lite-1.4.101: Ludovic Rousseau
30 April 2008
- support for fork(). Handles are now invalid in the child process
- SCardStatus() returns SCARD_W_REMOVED_CARD instead of
  SCARD_W_RESET_CARD when a card has been removed and inserted
- Doxygen improvements
- add support for DragonFly BSD
- some other minor improvements and bug corrections


pcsc-lite-1.4.100: Ludovic Rousseau
23 March 2008
- add libhal support to avoid polling the USB bus.  libusb is still
  supported but libhal is now the default
- improve performances in SCardConnect(), SCardReconnect(),
  SCardDisconnect(). Thanks to Sean Wykes for the patch
- SCardListReaders(): returns SCARD_E_NO_READERS_AVAILABLE when no
  reader are available.  Thanks to Thomas Harning for the bug report
- add support of TAG_IFD_POLLING_THREAD to use an asynchronous card
  movements detection instead of an active polling. The reader driver
  need to support TAG_IFD_POLLING_THREAD to use this feature
- CardCheckDaemonAvailability(): lower the priority of the log message
  in case of "PCSC Not Running" or "PCSC restarted" so that nothing is
  logged by default.  PCSCLITE_DEBUG can be defined to see the message.
  Programs linked with libpcsclite will not display anything if pcscd is
  not running. Solves Red Hat bug 428299.
- default log level is PCSC_LOG_CRITICAL+1 so that NO log is sent to
  stderr by default. You need to explicitly set PCSCLITE_DEBUG to have
  logs. (in a library stderr(2) can be any file opened with fd=2 so
  should not be used)
- ifdhandler-3.tex: more details about deviceName argument of
  IFDHCreateChannelByName()
- some other minor improvements and bug corrections


pcsc-lite-1.4.99: Ludovic Rousseau
9 January 2008
- add support of mix 32/64 bits platforms.  Thanks to Jacob Berkman for
  the big patch
- increase MAX_READERNAME from 52 to 100
- default ipcdir is /var/run/pcscd instead of /var/run so the directory
  can be shared locally between 32/64 bits systems on chroots
- display time delta between two lines of logs when printed to stderr
- return EXIT_SUCCESS (instead of EXIT_SUCCESS) if parsing
  /etc/reader.conf fails.
- performance improvement when powering a card after insertion
- some other minor improvements and bug corrections


pcsc-lite-1.4.4: Ludovic Rousseau
14 August 2007
- do not call a Log function in a signal handler and do hotplug
  synchronously. See Debian bug #430492 Thanks to Russell Stuart
- support LSB init script format
- better support of Solaris and Mac OS X
- some other minor improvements and bug corrections


pcsc-lite-1.4.3: Ludovic Rousseau
19 June 2007
- correctly handle lock (SCardBeginTransaction) when the locked card is
  removed.
- correct a buffer overflow introduced in 1.4.2 when extended APDU are
  used
- some other minor improvements and bug corrections


pcsc-lite-1.4.2: Ludovic Rousseau
23 May 2007
- add a Lock counter so that SCardBeginTransaction/SCardEndTransaction
  can be nested
- SCardDisconnect(): do not block if dwDisposition == SCARD_LEAVE_CARD.
  We block only for SCARD_UNPOWER_CARD, SCARD_RESET_CARD and
  SCARD_EJECT_CARD since that would impact other running transactions
- LPTSTR and LPCTSTR types are no more deprecated since they are found
  in many applications. And using them is not a problem.
- some other minor improvements and bug corrections


pcsc-lite-1.4.1: Ludovic Rousseau
16 May 2007
- do not limit the execution of an APDU to 2 minutes (Thanks to Harsh
  Sangal for the bug report)
- if the daemon is restarted we invalidate all the existing handles so
  SCard functions returns SCARD_E_INVALID_HANDLE
- SCardReconnect(): block instead of returning SCARD_E_SHARING_VIOLATION
- clean the data buffer for SCardTransmit() to clean the APDU buffer to
  remove any possible PIN or secret value (Thanks to Nils Larsch for the
  patch)
- SCardGetStatusChange(): add a counter in the upper word of
  dwEventState so it is possible to detect a card movement between two
  calls to SCardGetStatusChange() (Thanks to Matheus Ribeiro for the
  patch)
- SCardGetStatusChange(): do not check for SCARD_STATE_ATRMATCH,
  SCARD_STATE_EXCLUSIVE or SCARD_STATE_INUSE bits when the card is not
  present.  (thanks to Matheus Ribeiro for the bug report)
- some other minor improvements and bug corrections


pcsc-lite-1.4.0: Ludovic Rousseau
13 February 2007
- great speed improvements for SCardBeginTransaction(), SCardReconnect()
  and SCardDisconnect()
- SCardConnect(): return SCARD_W_UNPOWERED_CARD if the card is mute
  instead of returning SCARD_E_PROTO_MISMATCH because the requested
  protocol is not supported by the (mute) card
- Ctrl-C works again to stop pcscd on FreeBSD
- USB polling was not active even if a driver does not support
  IFD_GENERATE_HOTPLUG.
- split pcsclite.h in pcsclite.h and an internal pcscd.h. Some
  application compilations may fail but should not
- move RESPONSECODE definition from wintypes.h to ifdhandler.h since it
  should only be used as return type of IFDHandler functions. Some
  driver compilations may fail
- some other minor improvements and bug corrections


pcsc-lite-1.3.3: Ludovic Rousseau
19 January 2007
- add -H --hotplug argument to ask the pcscd daemon to rescan the
  available readers
- add support for IFD_GENERATE_HOTPLUG bit in driver Info.plist
  ifdCapabilities
- add --force-reader-polling to ignore the IFD_GENERATE_HOTPLUG bit in
  driver Info.plist ifdCapabilities (pcscd will poll the USB bus every 1
  second as in previous versions)
- SCardConnect() & SCardDisconnect(): wait until any transaction
  finishes before going on. This avoids the possibility to reset a card
  in the middle of a transaction Thanks to Martin Paljak for the bug
  report
- the tools installifd and formaticc are now completely outdated and
  should not be used anymore.
- PCSC/ifdhandler.h: add IFD_NO_SUCH_DEVICE for readers supporting
  hotplug
- PCSC/pcsclite.h: add SCARD_E_NO_READERS_AVAILABLE
- SCardControl(): do not limit cbSendLength to MAX_BUFFER_SIZE bytes
  since we now transparently support up to MAX_BUFFER_SIZE_EXTENDED
  bytes.  Thanks to Martin FÃ¼hrlinger for the bug report
- SCardGetAttrib()/SCardSetAttrib(): if the driver returns IFD_ERROR_TAG
  we return SCARD_E_UNSUPPORTED_FEATURE instead of the generic error
  code SCARD_E_NOT_TRANSACTED
- implement SCardIsValidContext() PC/SC call
- some other minor improvements and bug corrections


pcsc-lite-1.3.2: Ludovic Rousseau
11 August 2006
- add support of extended APDU in the standard configuration and in a
  backward compatible way: pcscd 1.3.2 can be used with libpcsclite <=
  1.3.2
- define MAX_BUFFER_SIZE_EXTENDED as the maximal size allowed for a
  extended APDU (64KB)
- LPCTSTR and LPTSTR types are deprecated. Use LPCSTR and LPSTR instead
- Dual licence src/error.c so it can be used by OpenSC. It is now
  BSD-like, see the COPYING file and GNU Lesser General Licence 2.1 or
  (at your option) any later version
- document that the 4 bytes field value in PCSC_TLV_STRUCTURE is always
  in big endian as documented in PCSC v2 part 10 ch 2.2 page 2. You can
  use ntohl() to convert the value. Thanks to Ulrich Vogl for the bug
  report
- some other minor improvements and bug corrections


pcsc-lite-1.3.1: Ludovic Rousseau
22 April 2006
- improve support of Solaris
- correct a bug when two clients are connecting at the same time
- better documentation for ./configure arguments
- doc/ifdhandler-3.tex: improve IFD handler documentation
- doc/pcsc-lite.tex: document VERIFY_PIN and MODIFY_PIN commands using
  PCSCv2 part 10 instead of the "proprietary" mechanism now unsupported
- doc/pcsc-lite.tex: document log_msg and log_xxd
- use fprintf(stderr,) instead of syslog(3) to log messages from libpcsclite
- use PCSCLITE_DEBUG to activate the debug messages in libpcsclite.
  MUSCLECARD_DEBUG is now used for libmusclecard only
- add the reader serial number in the reader name only if
  ADD_SERIAL_NUMBER is defined
- some other minor improvements and bug corrections


pcsc-lite-1.3.0: Ludovic Rousseau
3 March 2006
- new official stable version since 1.2.0 in October 2003. Many thanks
  to all the bug reporters and bug fixers
- libmusclecard is now in a independent package
- (re)allow compilation on Solaris
- SCardReleaseContext(): do not check that the thread releasing the
  context is the one that established it. This check is not performed on
  Windows and creates portability problems See
  http://archives.neohapsis.com/archives/dev/muscle/2006-q1/0134.html
- automatically call SCardUnload() when the libpcsclite library is
  unloaded Thanks to Najam Siddiqui. See
  http://archives.neohapsis.com/archives/dev/muscle/2006-q1/0177.html
- some other minor improvements and bug corrections


pcsc-lite-1.2.9-beta10: Ludovic Rousseau
3 February 2006
- if the USB reader defines a serial number then include it in the
  reader name (between parenthesis)
- the library libpcsclite.so.1 only exports the symbols defined by the
  API (http://pcsclite.alioth.debian.org/pcsc-lite/). This is needed to
  be able to use the library in an application that also uses flex (like
  muscleTool). The problem only occurs with GCC >= 4.0
- some other minor improvements and bug corrections


pcsc-lite-1.2.9-beta9: Ludovic Rousseau
27 November 2005
- add/improve support of PIN pad readers
  . define HOST_TO_CCID_16() and HOST_TO_CCID_32() macro to convert 16 and
    32-bits data to the CCID format (replace HOST_TO_CCID)
- add support of SUN C compiler and try to avoid GCC specific features
  (Heiko Nardmann)
- SCardGetStatusChange():
  . exists if the list of readers changed (one reader added) so that the
    application can update its list of readers (Najam Siddiqui)
  . correct a bug when two contexts where used (Najam Siddiqui)
- add support of Solaris 10 IFDhandler (Douglas E. Engert)
- allow pcsc-lite to be compiled without (f)lex installed
- add a TODO file. Help/money needed here.
- improve Doxygen documentation
- some other minor improvements and bug corrections


pcsc-lite-1.2.9-beta8: Ludovic Rousseau
6 September 2005
- correct a crash on Fedora Core 4 (off by 1 buffer overflow)
- do not silently truncate the reader, library or device name if they
  are too long but display an error message instead
- reinclude musclecard library in pcsc-lite package since a lot of code
  is shared. The separation was a mistake
- add colorization of the logs when sent to stderr. The color depends on
  the priority level
- restrict the number of symbols (function names) exported from
  libpcsclite.so to limit symbol conflicts with other libraries. Only
  the PC/SC API symbols should be exported
- add Doxygen documentation. Thanks to Luiz Reuter Silva Torro.
  HTML pages available at
  http://pcsclite.alioth.debian.org/doxygen/html/index.html
- SCardControl(): a 0 byte long pbSendBuffer is no more rejected since
  the command is in dwControlCode. Thanks to Martin Paljak for the patch
- provide a reader.h file (by default in /usr/local/include/PCSC/reader.h)
  that contains definitions shared between an application and a smart
  card driver (like SCARD_CTL_CODE, CM_IOCTL_GET_FEATURE_REQUEST,
  FEATURE_* and HOST_TO_CCID)
- pcscd: allow a serial hotplug by sending a SIGUSR1 signal. The
  /etc/reader.conf file is re-read and reader presence/absence is updated
- musclecard library: small bug fixes
- pcsc-lite SCF: small bug fixes
- some other minor improvements and bug corrections


pcsc-lite-1.2.9-beta7: Ludovic Rousseau
2 March 2005
- SCARD_PROTOCOL_ANY is defined as (SCARD_PROTOCOL_T0|SCARD_PROTOCOL_T1)
  You should not use SCARD_PROTOCOL_ANY since it is not defined in
  Windows PC/SC. It is just defined for backward source code compatibility
- define SCARD_STATE_UNPOWERED even it is a state never used so source
  code using it can compile
- SCardStatus(): pdwState and pdwProtocol parameters may be NULL (mimic
  Windows)
- provide a script update-reader.conf to update the /etc/reader.conf
  file from /etc/reader.conf.d/* files
  This script is called by /etc/init.d/pcscd before starting the daemon
- add support of SCardGetAttrib() with a NULL pbAttr parameter to only
  get the needed length in pcbAttrLen
- SCardReconnect() now works after a card movement.  Previously
  SCardReconnect() returned "Card was removed" even if the new card is
  reset.
- SCardGetStatusChange(): greatly improve performances. Thanks to Oivind
  H. Danielsen
- SCardControl(): check if the pbSendBuffer is NULL or no bytes are sent
  for driver API v2 only. With API v3 we can use dwControlCode as the
  only data to send.
- Implement the dynamic level logging in pcscd
  The new command line options are:
    -d, --debug           display lower level debug messages
        --info            display info level debug messages (default level)
        --error           display error level debug messages
        --critical        display critical only level debug messages
- some documentation update


pcsc-lite-1.2.9-beta6: Ludovic Rousseau
15 August 2004
- The debug messages are now displayed by default. This prevented error
  messages generated by a wrong /etc/reader.conf parsing to be displayed
- thread management:
  . threads are now created with the PTHREAD_CREATE_DETACHED attribute
    so that resources are released when the thread ends. Thanks to
    Michael Gold for the patch.
    Previous versions of pcscd just died after 256 clients connections.
- remove PCSC/ from the header files path since the path is given by
  pkg-config --cflags libpcsclite or the correct -I directive
- src/winscard_svc.c:
  . MSGCleanupClient(): always reset all the fields. Thanks to Michael
    Gold for the patch.
- src/configfile.l:
  . if the file referenced by DEVICENAME can't be use (because it is not
    a correct filename) we now display:
    You should use 'DEVICENAME /dev/null' if your driver does not use this field
- src/winscard.c:
  . Avoid generating a PPS request that would not be just after a power
    up. The previous code worked only when the card was _reset_ at
    SCardDisconnect() but not when SCARD_LEAVE_CARD was used.
- correctly manage multi-slots readers
- etc/pcscd.init:
  . updated to reflect the RPM version. Thanks to Ville SkyttÃ¤ & Fritz Elfert
- doc/example/pcsc_demo.c:
  . add SCardTransmit() code sample
- src/testpcsc.c:
  . add a "(don't panic)" after a "Transaction failed." if it is not a
    critical failure.
- doc/pcsc-lite.tex:
  . the function SCardSetTimeout() is deprecated and does nothing
- doc/ifdhandler-3.tex:
  . add documentation for IFDHGetCapabilities(..., TAG_IFD_THREAD_SAFE, ...),
    IFDHGetCapabilities(..., TAG_IFD_SLOT_THREAD_SAFE, ...) and
    IFDHSetCapabilities(..., TAG_IFD_SLOTNUM, ...)
- some internal changes and bugs corrections


pcsc-lite-1.2.9-beta5: Ludovic Rousseau
16 July 2004
- src/PCSC/wintypes.h:
  . add type LPSTR (again) so that "old" drivers and applications can
    compile but mark the type deprecated.
    See http://gcc.gnu.org/onlinedocs/gcc-3.1/gcc/Type-Attributes.html
- src/tokenfactory.c:
  . TPSearchBundlesForAtr(): use "%s/%s" instead of "%s%s" when
    generating the MuscleCard bundle name on MacOSX
- src/winscard.c:
  . SCardControl() (new API) can now be used with a IFDHandler v2.0 or v3.0.
    The previous code was broken and only worked with an IFDHandler v3.0
    (crashed with a v2.0).
- src/PCSC/ifdhandler.h:
  . IFDHControl(): use PUCHAR instead of LPCVOID and LPVOID so the
    driver can use TxBuffer[x] without needing a cast.
  . it is now possible to compile a IFDHandler v2.0 with this include
    file.  Just #define IFDHANDLERv2 in your source code before
    #include <ifdhandler.h>
    By default it is setup for for most recent version of the API (V3.0)


pcsc-lite-1.2.9-beta4: Ludovic Rousseau
3 July 2004
- src/libmusclecard.pc.in and src/libpcsclite.pc.in
  . includedir is now @includedir@/PCSC
  . add pthread flags for compilation and link
  . thanks to Ville SkyttÃ¤ for these patches.
    They are small but are mandatory to (re)compile muscleTools (and
    others) without modification to their Makefile.


pcsc-lite-1.2.9-beta3: Ludovic Rousseau
30 June 2004
- src/hotplug_libusb.c:
  . use a dynamic array for available USB drivers:
   - avoid a buffer overflow (argh!)
   - allow to use more than 16 drivers/supported readers
- src/Makefile.am:
  . install mscdefines.h, winscard.h, musclecard.h, pcsclite.h and wintypes.h
    in /usr/include/PCSC/ to not pollute /usr/include/
  . install ifdhandler.h, debuglog.h and parser.h in /usr/include/PCSC/ so
    drivers can use them for compilation.
  Modify your source code or add -I/usr/include/PCSC to CFLAGS in your
  Makefile or, better, use CFLAGS=`pkg-config libpcsclite --cflags`
- doc/pcsc-lite.tex:
  . dwPreferredProtocols is a bit mask of acceptable protocols
  . SCARD_SHARE_DIRECT can be used to talk to the reader without a card
    inserted
  . add "Some SCardControl commands" section (IFD_EXCHANGE and VERIFY_PIN)
  . add documentation for LTPBundleFindValueWithKey(), debug_msg() and
    debug_xxd()
- src/configfile.l: (/etc/reader.conf parser):
  . allow ':' in DEVICENAME. use ':' to indicate a non-real device (pcscd does
    not test for its existence as a file).  You can use this in something like
    "net://1.2.3.4/foobar" to indicate a reader on a remote machine like an
    X11 terminal.
- src/PCSC/wintypes.h:
  . Change the names of the types LPCSTR to LPCTSTR and LPSTR to LPTSTR to be
    compliant with the Microsoft SCard API. You will have to update your
    source codes.
- src/readerfactory.c:
  . rework RFSetReaderName() to simply the code and always start with the
    lowest number available (like in previous pcsc-lite version)
- src/prothandler.c:
  . completely redesign the function so that IFDSetPTS() is always called to
    tell the driver which protocol to use and to initialise its internal
    state. The driver now knows which protocol (T=0 or T=1) the application
    wants to use even if the card only support only one protocol.
- src/atrhandler.c:
  . add support of specific mode by the presence of TA2 (protocol not
    negotiable)
- src/utils/Makefile.am:
  . install bundleTool and installifd in [...]/sbin/ instead of [...]/bin/
- doc/example/Makefile.am:
  . do not install pcsc_demo since it is just a sample code for developers
    that is not supposed (the code) to do anything useful
- doc/example/pcsc_demo.c:
  . replace SCARD_PROTOCOL_ANY by SCARD_PROTOCOL_T0 | SCARD_PROTOCOL_T1 since
    SCARD_PROTOCOL_ANY is _not_ defined by Microsoft PC/SC and is not equals
    to SCARD_PROTOCOL_T0 | SCARD_PROTOCOL_T1 in pcsc-lite
- and lots of other minor and/or internal only changes

pcsc-lite-1.2.9-beta2: Ludovic Rousseau
11 May 2004
- change libmusclecard current version to 1 so that old libmusclecard0 and new
  libmusclecard1 can cohabit on the same system and will not break existing
  programs during upgrades.

  This is because libmusclecard is provided in the same distrib/package as
  libpcsclite. If we want two libpcsclite to cohabit we must also ensure
  that the two libmusclecard can also cohabit.
- src/pcscdaemon.c: print pcsc-lite version number during startup
- src/winscard.c: in SCardReconnect(), SCardDisconnect() and
  SCardEndTransaction() SCARD_UNPOWER_CARD is "Power down the card and reset
  it (Cold Reset)" so we really power down _and_ then power up the card.
- doc/pcsc-lite.tex: add the chapter "Multithreading and contexts"
- doc/ifdhandler-3.tex: IFD Handler API in LaTeX format
- correct some minor typos in documentation and output texts


pcsc-lite-1.2.9-beta1: Ludovic Rousseau
6 May 2004
- configure: add support of --enable-extendedapdu argument to allow the use of
  big APDUs (APDU size between 256-Bytes and 128-KBytes)
- API changes:
  . SCardControl() API changed to be similar to the Microsoft PC/SC
    implementation
  . add SCardGetAttrib()/SCardSetAttrib() functions
  . the driver need to be compliant to IFDHandler API v3 to support these new
    functions
- multithreading:
  Damien Sauveron added the support of multiplexing multi-readers
  communications. In the previous version the communications were serialized
  and not simultaneous even if it was not needed. Thanks Damien.
- src/pcscdaemon.c:
  . remove the warning if no /etc/reader.conf is found. It is normal to not
    have an /etc/reader.conf for USB readers only.
  . send debug to syslog by default
  . send debug to stderr if --foreground|-f is used (no need to add
    --debug stderr anymore)
- doc/pcsc-lite.pdf: doc improved to add the new functions and API. The doc is
  now generated from a LaTeX file (easier to maintain, html version available,
  much more nice, etc.)
- src/testpcsc.c:
  . added tests for the new functions SCardGetAttrib(), SCardSetAttrib() and
    SCardControl() and also the old SCardListReaderGroups()
  . do not ask for reader number if only one reader is present
  . some more debug
- pcscd and libpcsclite now exchange a protocol version to know what API to
  use. This will be useful if/when the API change again
- change library version to 1:0:0 since the interface changed
- hotplug: the daemon pcscd do not try to restart drivers anymore when it is
  exiting
- src/hotplug_libusb.c:
  . do not try to restart a USB driver if the first execution fails (because
    of a bug in the driver or whatever) since the next execution of the driver
    will, with a great probability, also fail. The user has to unplug/replug
    the reader to restart the driver. This prevents to fill the system logs
    with an error message every 1 second.
  . use deviceName defined as usb:idVendor/idProduct:libusb:busname:filename
    in HPAddHotPluggable(). This should avoid wrong USB enumeration when used
    in IFDHCreateChannelByName()
- src/hotplug_macosx.c:
  . buffer overflow: the driver list was not terminated and caused crashes.
- src/winscard.c:
  . remove the code to map SCARD_UNPOWER_CARD on IFD_RESET.
    SCARD_UNPOWER_CARD is power down and power up (cold reset) => IFD_POWER_DOWN
    SCARD_RESET_CARD is just power up (warm reset) => IFD_RESET
- src/winscard.h:
  . use SCARDCONTEXT, DWORD, LPSCARDHANDLE, etc. instead of long, unsigned
    long, long *, etc. to be more Windows PC/SC compliant
- doc/reader.conf.5.in: new manpage to make it clear that this configuration
  file SHOULD not be used for USB readers
- src/configfile.l:
  . print a warning if the LIBPATH contains ".bundle". USB drivers SHOULD NOT
    be declared in reader.conf
  . Check that DEVICENAME and LIBPATH files exist and if an error occurs
    during the parsing the reader is not added and pcscd exit.
- doc/example/pcsc_demo.c:
  . recode the readers enumeration to avoid the use of
    PCSCLITE_MAX_READERS_CONTEXTS. You do not and should not need
    PCSCLITE_MAX_READERS_CONTEXTS. Use a dynamic management instead.
  . add SCardReconnect() sample code
  . allow to select the reader number instead of always selecting the first
    reader
  . some minor debug
- src/musclecard.c:
  . Initialize currentToken->tokenType in MSCListTokens(). Closes "[ #300607 ]
    MSCListTokens should set tokenType to know state before OR operation"
- src/tokenfactory.c:
  . correct atrString[] buffer size. The ATR is in ASCII so it must be
    MAX_ATR_SIZE*2 +1. This was problematic for cards with a "long" ATR.
- src/readerfactory.c:
  . initialize vHandle field to NULL in RFAllocateReaderSpace() (caused a
    crash under MacOS X)
- aclocal/acx_pthread.m4:
  . new upstream version to avoid checking for pthread.h which does not exist
    on *BSD
- src/libmusclecard.pc.in, src/libpcsclite.pc.in:
  . add muscledropdir (in libmusclecard) and usbdropdir (in libpcsclite) so we
    can use `pkg-config libpcsclite --variable=usbdropdir` to find the
    directory to use. Should be used by drivers and plugins installation.
    Thanks to Ville SkyttÃ¤ for the patch.
- many other minor patches and corrections. Read ChangeLog.cvs for a complete
  list


pcsc-lite-1.2.0: Ludovic Rousseau
27 october, 2003
- the 1.2.0 version is the same as 1.2.0-rc3 version

pcsc-lite-1.2.0-rc3: Ludovic Rousseau
15 october, 2003
- src/winscard_msg.c: perform a round-robin among clients to avoid starvation
  under heavy load. Patch from Bettina Martelli.
- src/winscard_clnt.c: send debug to stdout only if the environment variable
  MUSCLECARD_DEBUG is defined
- src/libmusclecard.pc.in: add a new pkg-config file for application using
  libmusclecard.
- a lot of code and build clean up by Antti Tapaninen
- some code clean up and debug by Damien Sauveron


pcsc-lite-1.2.0-rc2: Ludovic Rousseau
4 September, 2003
- removed a very _stupid_ bug that linked libpcsclite with libusb. Any
  application linked with libpcsclite was also linked with libusb.
- generate a new library libmusclecard and remove MuscleCard code from
  libpcsclite. An application using MuscleCard functions needs to explicitly
  link with libmusclecard.
- src/winscard_clnt.c: add a new function SCardUnload() to free allocated
  resources. It is mandatory only if you use dlopen/dlclose to often
  load/unload the library.  Otherwise you will exhaust the resources
  available and get a crash.  Thanks to Guy Moreillon for the patch.
- src/muscletest.c: code cleaning


pcsc-lite-1.2.0-rc1: Ludovic Rousseau
26 August, 2003
- configure.in: --enable-usb is now deprecated and off by default.
  --enable-libusb is selected by default and will be used if libusb is
  installed in /usr. If libusb is installed in /usr/local use
  --enable-libusb=/usr/local
- src/hotplug_macosx.c: Add support of reader aliases using <array></array> in
  driver Info.plist on MacOS X.
  The old syntax did not work since pcscd on MacOS X wants a correct XML file.
  The new syntax is:
    <key>ifdVendorID</key>
    <array>
        <string>0x08E6</string> <!-- 1, Gemplus -->
        <string>0x04E6</string> <!-- 2, SCM Microsystems -->
        <string>0x076B</string> <!-- 3, OmniKey -->
        <string>0x0783</string> <!-- 4, C3PO -->
    </array>

    <key>ifdProductID</key>
    <array>
        <string>0x3437</string> <!-- 1 -->
        <string>0x5115</string> <!-- 2 -->
        <string>0x3021</string> <!-- 3 -->
        <string>0x0003</string> <!-- 4 -->
    </array>

    <key>ifdFriendlyName</key>
    <array>
        <string>GemPC Twin</string>   <!-- 1 -->
        <string>SCR 335</string>      <!-- 2 -->
        <string>CardMan 3121</string> <!-- 3 -->
        <string>LTC31</string>        <!-- 4 -->
    </array>
- src/powermgt_macosx.c, src/hotplug_macosx.c and some others: Add support of
  PCMCIA for MacOS X. Thanks to Stephen M. Webb.
- src/hotplug_libusb.c: Add support of libusb. Allow to use USB readers on
  *BSD or any plateform supported by libusb. Thanks to Toni Andjelkovic for
  the great job.
  I also redesigned the code to support up to PCSCLITE_MAX_READERS readers
  whatever the driver they use.
- src/musclecard.c:
  . fix an initialisation problem of pConnection->shareMode (thanks to Wan-Teh
  Chang)
  . avoid a memory leak (Toni Andjelkovic)
- doc/example: sample demo application using pcsc-lite API
- src/winscard.c:
  . pcscd reported card is present when there is no card in the reader
  . return SCARD_E_INVALID_PARAMETER for APDU of less than 4 bytes
- src/winscard_svc.c: Sometimes unknown APDUs were being transmitted when
  'SCardStatus' function was invoked
- some code cleanup and compilation problems removed.


pcsc-lite-1.1.2beta5: Ludovic Rousseau
30 May, 2003
- src/winscard.c: SCardStatus(): do not return before filling buffers
- src/winscard_clnt.c: SCardStatusTH(): simplify code and return correct
  results when buffers are too short or NULL is used
- src/libpcsclite.pc.in: used to generated a pkg-config ".pc" file (thanks to
  Andreas Jellinghaus for the idea and the patch)
- configure.in, src/Makefile.am, src/utils/installifd.c, doc/bundleTool.8.in,
  doc/pcscd.8.in, etc/reader.conf.in:
  . correct typos and include path configured by ./configure appear in the
  docs (thanks to Ville SkyttÃ¤)
- src/utils/installifd.c:
  . replace gets() by fgets() to avoid buffer overflow.
  . Use sizeof() instead of constants.
  . do not use strdup() since it is useless.
  . print error messages when needed.


pcsc-lite-1.1.2beta4: Ludovic Rousseau
13 Apr, 2003
- src/debuglog.c:
  . DebugLogSetLogType() set flags and not just _or_ them (allow unset)
  . use strncpy to avoid a possible buffer overflow
- src/hotplug_linux.c:
  . code cleanup
  . support for driver aliases in Info.plist. The syntax is:
        <key>ifdProductString</key>
        <string>GemPC430</string>

        <key>ifdVendorID</key>
        <string>0x08E6</string>
        <string>0x08E6</string>
        <string>0x08E6</string>

        <key>ifdProductID</key>
        <string>0x0430</string>
        <string>0x0432</string>
        <string>0x0435</string>

        <key>ifdFriendlyName</key>
        <string>GemPC430</string>
        <string>GemPC432</string>
        <string>GemPC435</string>
    So this driver will be used by the three possible readers.
- src/Makefile.am:
  . support for driver aliases in Info.plist (use tokenparser.l instead of
    driverparser.l)
  . support for xBSD back
- src/pcsclite.h:
  . rename "pcsc.pub" and "pcsc.comm" in "pcscd.pub" and "pcscd.comm"
- src/tokenparser.l:
  . code reindentation
  . use DebugLogB()/DebugLogC with correct number of arguments
- src/tokenparser.l:
  . regenerated from src/tokenparser.l


pcsc-lite-1.1.2beta3: Ludovic Rousseau, David Corcoran
7 Nov, 2002
- Support for Sun Microsystems' SCF
- Patches from Dmitry Djachenko to:
  . init g_rgSCard??Pci variable at compile time
  . return more meaningful error codes in SCardReconnect()
  . return more information in SCardStatus()
  . accept pioRecvPci == NULL in SCardTransmit()
    according to MSDN (July 2002) : SCardTransmit description
     pioRecvPci
      [in, out] Pointer to the protocol header structure for the instruction,
      followed by a buffer in which to receive any returned protocol control
      information (PCI) specific to the protocol in use. This parameter may be
      NULL if no returned PCI is desired.
- correct an overflow in SCardGetStatusChange(). Thanks to Michael Nidd
- configure.in, src/pcscdaemon.c, src/pcsclite.h:
  . add --enable-ipcdir=DIR option. Default is now /var/run/ instead of
  /tmp/pcsc/
- src/Makefile.am:
  . reorganise conditionals to to have a src/Makefile.in file 142 (yes 142)
  times smaller (33 KB instead of 4.7 MB)
- src/eventhandler.c:
  . add filename and error message to error logs for /var/run/pcsc.pub
- src/debuglog.h:
  . DebugLogC() uses 3 parameters not 2 (use DebugLogB() for that)


pcsc-lite-1.1.2beta2: Ludovic Rousseau, David Corcoran, Jean-Luc Giraud
12 Oct, 2002
- OpenBSD 3.1 and FreeBSD 4.7RC2 debug and testing
- MacOSX debug and improvement
- support multiple identical USB readers under GNU/Linux
- more debug
- Bug in winscard.c for protocol when ANY is chosen

pcsc-lite-1.1.2beta1: (Ludovic Rousseau <<EMAIL>>)
6 Sep, 2002
- src/tokenfactory.c, configure.in and bundleTool.c:
  . add support for --enable-muscledropdir=DIR
- README:
  . The licence file is COPYING and not LICENSE (thanks to Juha Tuomala)
- doc/formaticc.1:
  . add formaticc.1 manpage from Debian
- doc/bundleTool.1:
  . update and rename from .8 to .1
- src/utils/formaticc.c:
  . lots of debug and buffer overflow removal
- src/ifdwrapper.c, src/debuglog.c, doc/pcscd.8:
  . add support for --apdu|-a
- src/pcscdaemon.c:
  . add support for --apdu, test if the pcscd is still running before
    complaining that /tmp/pcsc is present
  . print an error message if the --debug argument is not known
- src/Makefile.am:
  . add -Wl,--export-dynamic link option
- src/hotplug_linux.c:
  . add support for --enable-usbdropdir=DIR


pcsc-lite-1.1.1: (David Corcoran <<EMAIL>>)
5 Jun, 2002
- src/readerfactory.c
  . Fixed multiple slot handling by adding dwFeeds to multiple slots
- src/winscard_clnt.c
  . Fixed multi Establish/Release Context problem by removing CleanupClient


pcsc-lite-1.1.0: (David Corcoran <<EMAIL>>)
28 May, 2002
- src/mscdefines.h added
- src/musclecard.c/.h added
  . Added client side token/card plugin interface
- src/tokenfactory.c added
- src/powermgt_macosx.h added
- src/powermgt_macosx.c added
  . Support for sleep mode on OS X
- src/tokenparser.l added
- src/dyn_hpux modified with new include <errno.h>
- src/winscard_svc.c
  . Added session checking so rogue clients cannot steal hCard values
- src/readerfactory.c
  . removed world writable files in /tmp/pcsc
- configure.in
  . Added support for SCF with SCF plugin
- Added command line arguments for debug/daemon mode <<EMAIL>>
- Previous additions from Ludovic Rousseau <<EMAIL>>

pcsc-lite-1.0.2beta5: (Ludovic Rousseau <<EMAIL>>)
10 Mar, 2002
- src/pcscdaemon.c:
   . move the pid file creation earlier (before drivers loading)
   . allow to properly kill pcscd before or during driver loading
     this is useful when the driver init is buggy
- src/pcsclite.h: change PCSCLITE_VERSION_NUMBER to 1.0.2.beta5
- configure.in: change version number to 1.0.2.beta5


pcsc-lite-1.0.2beta4: (Ludovic Rousseau <<EMAIL>>)
13 Feb, 2002
- configure.in: change version number to 1.0.2.beta4
(from Douglas Atique bugs report and patch)
- etc/Makefile.am
   . add the files makeFMStyle, makeSTDStyle and moveFMCode to the
     distribution archive
- src/Makefile.am
   . add the files sys_solaris.c, sys_hpux.c and powermgt_macosx.c to the
     distribution archive
- src/pcscdaemon.c
   . ignore SIGHUP signal
   . remove pid file if USE_RUN_PID is defined
- src/winscard_msg.c
   . move #ifdef PCSC_TARGET_SOLARIS _after_ the inclusion of config.h since
     PCSC_TARGET_SOLARIS is defined in config.h


pcsc-lite-1.0.2beta3: (Ludovic Rousseau <<EMAIL>>)
10 Jan, 2002
(from Carlos Prados Debian package)
- configure.in
   . add AC_PREFIX_DEFAULT(/usr/local/pcsc) to install in /usr/local/pcsc by
   default
- etc/
   . add makeFMStyle, makeSTDStyle and moveFMCode scripts
- src/utils/Makefile.am
   . add EXTRA_DIST = sample.in sample.ibm sample.out
- src/dyn_bsd.c
   . try without a leading '_' in cas of failure (needed by FreeBSD) thanks to
   Toni Andjelkovic <<EMAIL>> for the patch)
- src/test.c
   . commented declarations on unused variables
   . corrected three %x to %lx for long arguments
- README
   . add documentation for --enable-confdir and --enable-runpid
- src/README_INTERNALS.txt: new file containing some documentation for source
  code hackers
- src/Makefile.am: add EXTRA_DIST = README_INTERNALS.txt
- src/pcscdaemon.c: the daemon now cleany stops the drivers before exiting
   . the global variable AraKiri is set in signal_trap()
   . this variable is checked in the main loop of SVCServiceRunLoop()
   . RFCleanupReaders() is then called if AraKiri
- src/readerfactory.c: add RFCleanupReaders() to do the cleaning job at exit
- src/test.c: change %x to %02X for the ATR bytes
- src/debuglog.c and src/debuglog.h:
   . rewrote the DebugLog[ABCD] function
   . the function are now defines than include __FILE__ and __LINE__
   . the test #ifdef USE_SYSLOG if moved from the source code to the
     src/debuglog.c function only -> the source is more readable
   . the new log functions are now used everywhere
- src/configfile.l:
   . changed syslog() to DebugLog()
   . add #include "debuglog.h"
- README: add a supported platform: OpenBSD 3.0 (with libc.so.28.2)
- Makefile.am: add HELP file to EXTRA_DIST


pcsc-lite-1.0.2beta2: (Ludovic Rousseau <<EMAIL>>)
20 Dec, 2001
- bootstrap: added --verbose
- reconf: added --verbose and --enable-debug
- configure.in:
   . changed release number to 1.0.2.beta2
   . added -Wall to CFLAGS to compile with all the warnings ON. This change
	 implied many small corrections: mainly addition of include file to add
	 functions prototyping, adding return values when needed, removing unused
	 variables.
   . add doc/ directory (from Carlos Prados Debian package)
   . add --enable-confdir=DIR (default to /etc)
     inspiration from Shell Hin-Lik Hung, OpenBSD pcsc-lite port
   . add --enable-runpid=FILE to store the pcscd pid
     inspiration from Carlos Prados, Debian package maintainer
- many C source files includes <pcsclite.h> (or similar). I changed to
  "pcsclite.h" since the local .h should be more recent and with less
  bugs than the one in /usr/local/include/
- src/dyn_bsd.c, src/dyn_unix.c: in DYN_LoadLibrary() changed 0 to
  NULL, "char *" to "const char *"
- src/dyn_hpux.c: removed declared but unused variables
- src/ifdwrapper.c: initialize IFD_? functions pointer to NULL
- src/pcscdaemon.c:
   . removed declaration of errno (already made in errno.h)
   . exit with code value
   . more explicit error message when /tmp/pcsc/ already exist
   . test if VERSION and PCSCLITE_VERSION_NUMBER are the same
     release numbers
   . create a file containing the PID (see configure --enable-runpid=FILE)
     ("stolen" from Carlos Prados Debian package)
- src/readerfactory.c
   . removed unused variables
   . dwSlot is a long, use %ld instead of %d
   . changed "SCARD_F_UNKNOWN_ERROR;" to "return SCARD_F_UNKNOWN_ERROR;"
- src/readerfactory.h: corrected "RVAllocateReaderSpace" to
  "RFAllocateReaderSpace" (RVA -> RFA)
- sys_*.c: removed declaration of errno
- src/sys_unix.c: add "return 0;" in SYS_Initialize()
- winscard.c: in SCardReconnect()
   . initialize dwAction to 0
   . remove unused variables dwReaderLen and dwProtocol
- src/winscard_clnt.c: commented out declaration and definition of
  SCardSetupThreadSafety(). It is not used anywhere.
- src/winscard_msg.c:
   . add the error message "strerror(errno)" returned by the socket
     functions in the debug message logged
   . removed unused variables
   . removed (fd_set *) cast in select calls. (compiles OK without)
- src/winscard_svc.c: add a "return 0;" in MSGCleanupClient()
- src/winscard_svc.h: add prototype for MSGCleanupClient()
- doc/Makefile.am: ("stolen" from Carlos Prados Debian package)
   . add this directory in dist files
- Makefile.am: ("stolen" from Carlos Prados Debian package)
   . add doc directory
   . add reconf bootstrap in dist files
- src/Makefile.am: ("stolen" from Carlos Prados Debian package)
   . add utils directory
   . remove libpcsclite-core.la from install target
   . add dyn_bsd.c dyn_hpux.c to EXTRA_libpcsclite_core_la_SOURCES
- src/utils/Makefile.am: ("stolen" from Carlos Prados Debian package)
   . converted LIBS line into formaticc_LDADD line
- src/hotplug_linux.c
   . add some includes files
   . commented useless declarations of hpManu_id, hpProd_id, bundleArraySize
- src/utils/installifd.c
   . rv, dwRecvLength and dwSendLength are long, use %ld instead of %d
   . commented useless declarations of many variables
   . add some (int *) casts
- etc/ ("stolen" from Carlos Prados Debian package)
   . added SmartcardServices
   . added StartupParameters.plist
- src/debuglog.c:
   . put the ATR on just one line even with syslog
   . changed LOG_DEBUG to LOG_INFO since the LOG_DEBUG level messages are
     seldom logged
- src/pcsclite.h: test if USE_READER_CONF is defined (see
     --enable-confdir=DIR)

pcsc-lite-1.0.2beta: (Ludovic Rousseau)
29 Nov, 2001
- stop if /tmp/pcsc already exists
- clean and remove /tmp/pcsc on exit using signal() and atexit()
- renamed SYS_Exit to exit in many places to use the atexit() defined
  cleaning function
- removed \n in debug messages (syslog does not need it)
- some minor print cosmetic modification in atrhandler.c
- use a define DEBUG_BUFFER_LENGTH instead of 150 in debuglog.c
- use snprintf() instead of sprintf() in debuglog.c
- add a error on compile in pcscdaemon.c
   You must use '--enable-syslog' when also using '--enable-daemon' or
   you will not get any message
- renamed SYS_Stat to SYS_Fstat since the system call is fstat and stat
  is another one
- use PCSCLITE_IPC_DIR instead of repeating "/tmp/pcsc/" in pcsclite.h

pcsc-lite-1.0.1:
added -fno-common to CCFLAGS
fixed syslog error on ATR
lengthened the default client timeout on commands
fixed bus error when vendir/id not found
Added support for HP-UX 11 and OpenBSD 2.9
Removed callback socket from server
Fixed GetStatusChange bug on small timeouts
Fix bug in transmit where reader sends back
wrong length causes client to overwrite buffer

pcsc-lite-1.0.0b:
Fixed numerous NULL pointer errors
Fixed many, many behaviors
Tried Windows compatibility testing
Switched to sockets based implementation
Basically, it's had 2 months testing

pcsc-lite-0.9.3:
Changed test.c
Added buffered read/write layer
Shortened timeouts on outgoing commands
Added support for Solaris 2.6 2.8
Fixed autoconf problems
Added an option for client side thread safety
Fixed '-' problem for bundleparser

pcsc-lite-0.9.1:
Switched to named pipes for transport
Fixed SCARD_POWERED tag when card inserted upside down
Added non-polling request blocking
Switched to autoconf thanks to some help
Added timeouts for client/server

pcsc-lite-0.8.7:
Added kernel IoKit notifications for USB devices on OS X
Added polling support for USB devices on Linux
Added XML parsing for bundles on Linux
Fixed GetStatusChange's SCARD_READERSTATE structure problem
Fixed random generation in EstablishContext ( Carl-Magnus Pettersson )
Fixed RPC Channel leak in EstablishContext  ( Carl-Magnus Pettersson )
Updated documentation to 0.8.7

pcsc-lite-0.8.6:
Added empty file powermgt_macosx.c for future power management
Added ability for multiple identical readers on OS X
Fixed bug enabling SCardTransmit to pass memory card functions
by allowing sSendPci to be NULL
Fixed some possible buffer overrun exploits

pcsc-lite-0.8.5:
Moved to 'fat client' architecture
Mapped status to clients so events are immediate
Removed fork() from server, and wait() calls
Added multi readers to GetStatusChange
Added block for reader in GetStatusChange
Added Reader Lun naming mechanism for friendlynames
Updated the documentation

pcsc-lite-0.8.2:
Added more error checking to all winscard functions.
Added the ability to use Domain Sockets on Linux, Solaris.
Increased the select sleep time to conserve cpu time.
Added the ability to recover from errors by reloading drivers/etc.
Pre-allocated bundle array list to save cpu time and memory leakage.
Added SECURITY file.

pcsc-lite-0.8.0:
Added Hot Pluggable search function to select loop.
Fixed Makefiles to do copies not moves
Changed some filenames.

pcsc-lite-0.7.8:
Fixed race condition between pcscusb and eventhandler.c
Fixed core dump on error startup.
Added T=1 detection to test.c.
Added SCardControl()

pcsc-lite-0.7.5:
Dynamically addable readers.
Multiple dynamically addable readers.
More startup error checking.
Full support for OS X

pcsc-lite-0.7.3:
Added stringified error responses.
Fixed some error returning bugs.
Support for dynamic readers such as USB readers.
More support for OS X

pcsc-lite-0.7.1:
Fixed bugs causing problems in Redhat regarding mutex pointers.
Fixed exiting problem causing random zombie processes.
Removed thread libs and dyn libs from local library.
Abstracted thread libs in thread_generic.h
Added support for Mac OS X.

pcsc-lite-0.7.0:
Support for ifd handler 2.0 specifications.

pcsc-lite-0.6.9:
Fixed bug in readerfactory.c that only allowed 2 applications
to run simultaneously.
Fixed bug in rpc/winscard.c so that some applications which
while looped on Begin/End could not cause starvation.


pcsc-lite-0.6.8
Fixed mutex bug in readerfactory.c
Added SCardListReaderGroups for compatibility
Fixed NULL in for ListReaders
Fixed NULL as pioRecvPci in Transmit
Added multi-sys Makefiles
Pulled Windows(r) defs from pcsclite.h
Added INFINITE to GetStatusChange.

pcsc-lite-0.6.6
Added application event notification on calls
Added SCardReconnect

pcsc-lite-0.6.5
Added -v option for version checking
Added security module for future encryption/decryption
Added function to allow only from localhost
Added default values to all variables
Proper freeing of all pointer values
Fixed Reset on Disconnect
Removed any dangerous pointer references

pcsc-lite-0.6.4
Shortened the thread zombie cleanup time.

pcsc-lite-0.6.3
Fixed readerfactory assignment of ID problem

pcsc-lite-0.6.2
Using pthreads for status poll.
Using pthread mutexes instead of test and set.
Fixed problem in GetStatusChange.
Fixed SCardBeginTransaction.

pcsc-lite-0.6.0
Removed SCardReadMemory, SCardWriteMemory
Added Mutex locks around all driver calls
Added ability to do startup allocation
Fixed memory leak in RFAddReader
Removed some warnings

pcsc-lite-0.5.5
Added multi-slot support
Fixed T=0/1 protocol definition

pcsc-lite-0.5.2
Fixed protocol negotiation
Modified defines to work with OCF

pcsc-lite-0.5.0
Fixed the Makefiles to include
symbolic links to external libraries

pcsc-lite-0.4.9
Added some objects to the client's
library so it can be used with ssp-lite

pcsc-lite-0.4.7
Added Server State Machine
Kills dead clients and frees resources
Added SCardSetTimeout
Fixed Makefile for non-rpc
Added BSD style Makefile for BSD compilation
Fixed SCardRead/Write functions

pcsc-lite-0.4.0
Fixed SCardStatus Function
Added IFD Wrapper Abstraction Layer
Began support for multiple slots
Fixed bug in SCardStatus with overflow
Added code to remove zombie processes
Added support for connecting to multiple
readers/resource managers from the client.

pcsc-lite-0.3.0:
Added RPC abstraction layer.
Added Server Forking on Transmit and GetStatusChange.
Fixed overflowed buffer in atrhandler.c
Fixed Disconnect Bug.

pcsc-lite-0.2.4:
Added ATR Handling.
Added support for T=1
Added support for PTS negotiation
Added abstraction for more slots/terminal
Added correct ATR size return

pcsc-lite-0.2.2:
Added some defines in pcsclite.h
Added support for Memory cards.
Added support for SCardCancel.

pcsc-lite-0.2.0:
Fixed powering up error in GetStatusChange.
Added some type defines in pcsclite.h

pcsc-lite-0.1.0:
Fixed bug in readerfactory.c line 117 lpcReaders[p] = 0
to lpcTReaders[p] = 0.  This was giving strange errors.
Fixed return rv at SCardStatus function to return SCARD_S_SUCCESS.
Added 7 more defines to pcsclite.h

pcsc-lite-0.0.2:
Added functions Status, and GetStatusChange
Fixed Connect to look for card
Fixed return values for functions
