#!/usr/bin/sh

# Copyright (c) 2006 SUSE LINUX Products GmbH, Nuernberg, Germany.
# Copyright (c) 2009 Fedora Project
# Copyright (c) 2014-2015 Red Hat
# This file and all modifications and additions to the pristine
# package are under the same license as the package itself.
#
# Please submit bugfixes or comments via http://bugzilla.redhat.com/
#
# <PERSON> <<EMAIL>> 2006
# <PERSON> <<EMAIL>> 2006
# <PERSON> <<EMAIL>> 2009
# Pavol R<PERSON>k <<EMAIL>> 2009
# <PERSON> <<EMAIL>> 2014
#
# use proper binary (pinentry-qt, pinentry-gnome, pinentry-gtk-2 or pinentry-curses)

kde_running=
arg=
display=
# look for a --display option
for opt in "$@"; do
    if [ "$opt" = "--display" ]; then
        arg=1
    elif [ -n "$arg" ]; then
        display="$opt"
    else
        arg=
    fi
done

# export DISPLAY if pinentry is meant to be run on a different display
# check the KDE_FULL_SESSION variable otherwise
if [ -n "$display" ] && [ "$DISPLAY" != "$display" ]; then
    export DISPLAY="$display"
elif [ -n "$KDE_FULL_SESSION" ]; then
    kde_running=1
fi

# Check for presence of xprop binary
type xprop >/dev/null 2>/dev/null
XPROP=$?

if [ -n "$DISPLAY" ] && [ $XPROP -eq 0 ]; then
    xprop -root | grep "^KDE_FULL_SESSION" >/dev/null 2>/dev/null
    if test $? -eq 0; then
        kde_running=1
    fi
fi

# if a user supplied a pinentry binary, use it
if [ -n "$PINENTRY_BINARY" ];
then
	export PINENTRY_BINARY="$PINENTRY_BINARY"
# if KDE is detected and pinentry-qt exists, use pinentry-qt
elif [ -n "$kde_running" ] && [ -x /usr/bin/pinentry-qt ]
then
	export PINENTRY_BINARY="/usr/bin/pinentry-qt"
# otherwise test if pinentry-gnome3 is installed
elif [ -n "$DISPLAY" ] && [ -x /usr/bin/pinentry-gnome3 ]
then
	export PINENTRY_BINARY="/usr/bin/pinentry-gnome3"
# otherwise test if pinentry-gtk-2 is installed
elif [ -n "$DISPLAY" ] && [ -x /usr/bin/pinentry-gtk-2 ]
then
	export PINENTRY_BINARY="/usr/bin/pinentry-gtk-2"
# otherwise test if pinentry-qt exists although KDE is not detected
elif [ -n "$DISPLAY" ] && [ -x /usr/bin/pinentry-qt ]
then
	export PINENTRY_BINARY="/usr/bin/pinentry-qt"
# use pinentry-tty if installed
elif [ -x /usr/bin/pinentry-tty ]
then
	export PINENTRY_BINARY="/usr/bin/pinentry-tty"
# pinentry-curses is installed by default
else
	export PINENTRY_BINARY="/usr/bin/pinentry-curses"
fi
exec $PINENTRY_BINARY "$@"
