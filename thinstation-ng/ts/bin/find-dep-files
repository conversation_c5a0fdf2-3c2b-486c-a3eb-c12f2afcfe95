#!/bin/bash
dependency=$1

fdump()
{
	if objdump -x "$1" 2>/dev/null |grep -e NEEDED |grep -e $dependency >/dev/null; then
                echo "$1"
        fi

}

if [ ! -e /tmp/binlist ] || [ -n "$2" ]; then
	rm -f /tmp/binlist
	if [ -n "$2" ]; then
		>&2 echo "Finding files in $2"
		location=$2
	else
		>&2 echo "Finding all files"
		location=/{bin,boot,etc,home,lib,opt,root,sbin,share,usr,var,www}
	fi
	eval find $location -type f > /tmp/filelist
	>&2 echo "Determining which files are ELF"
	file -m /ts/etc/elf.mgc -f /tmp/filelist |grep -e ELF |grep -E "shared|executable" |cut -d: -f1 >> /tmp/binlist
fi

>&2 echo "Looking for dependent binaries on $dependency"
export dependency
export procs=`nproc`
export -f fdump

cat /tmp/binlist | xargs -P $procs -I {} bash -c 'fdump "$@"' _ {}
