# SSSD Configuration Example
# You will have to setup a prstnt mount that maps to /var/prstnt, as the krb5.keytab must be persisted.
# sssd already brings in 'package persistent-files', so just figure out how to partition stuff.
# This is not an autojoin. You will need to run "realm join --user=<user> <domain>" on the client and reboot.
# This can be automated by something like ansible but you will need a prstnt store.

# Enable Fully Qualified Domain Names
# Using FQDN helps ensure that user names are always used with their domain parts, which is useful in multi-domain environments.
# Default is False, only the username is required.
# Usage: SSSD_FQDN=True or False
#SSSD_FQDN=True

# Allowed Groups
# Specify which groups are allowed to authenticate via SSSD. Useful for restricting access to specified groups only.
# Not required, any domain user can login, but they may net get far unless they also belong to a group that is mapped by SSSD_USER_GROUP.
# Usage: SSSD_ALLOWED_GROUPS="group1,group2"
#SSSD_ALLOWED_GROUPS="Domain Users"

# Allowed Users
# Specify which users are allowed to authenticate via SSSD. This setting provides an additional layer of access control.
# Not required.
# Usage: SSSD_ALLOWED_USERS="<EMAIL>,<EMAIL>"
#SSSD_ALLOWED_USERS="<EMAIL>"

# User Primary Group
# This variable sets a default group for users who authenticate via SSSD, and maps them to the unix 'users' group.
# You likely need this at the very least.
# Usage: SSSD_USER_GROUP="default_group"
SSSD_USER_GROUP="Domain Users"
