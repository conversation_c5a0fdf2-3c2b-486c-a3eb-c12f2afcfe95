.\" andoc.tmac
.\"
.\" Load either an.tmac or doc.tmac.  Multiple man pages can be handled.
.\"
.\"
.\" Copyright (C) 1991-2020 Free Software Foundation, Inc.
.\"      Written by <PERSON> (<EMAIL>),
.\"      based on a patch from <PERSON><PERSON><PERSON>.
.\"
.\" This file is part of groff.
.\"
.\" groff is free software; you can redistribute it and/or modify it
.\" under the terms of the GNU General Public License as published by
.\" the Free Software Foundation, either version 3 of the License, or
.\" (at your option) any later version.
.\"
.\" groff is distributed in the hope that it will be useful, but WITHOUT
.\" ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
.\" or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public
.\" License for more details.
.\"
.\" You should have received a copy of the GNU General Public License
.\" along with this program.  If not, see
.\" <http://www.gnu.org/licenses/>.
.\"
.
.if !\n(.g \
.  ab andoc.tmac: macros require groff extensions; aborting
.
.do nr *groff_andoc_tmac_C \n[.cp]
.cp 0
.
.als andoc-em em
.als andoc-bp bp
.als andoc-ne ne
.
.
.\" We must not use '.de1' for 'reload-doc' or 'reload-man'!  'doc.tmac'
.\" unconditionally switches compatibility mode off, but '.de1' would
.\" ignore this, restoring the mode that was active before.  Similarly,
.\" we have to switch back to the original compatibility mode for man
.\" documents in case there is a mix of mdoc and man input files.
.\"
.\" Due to a bug in GNU troff it necessary to have a no-op line between
.\" '.do' and '\*'.
.
.
.de reload-doc
.  \" Flush any partially collected output line and write page footer in
.  \" continuous rendering mode.
.  do if d an-end \
.    do an-end
.
.  \" Remove traps planted by an.tmac.
.  do ch an-header
.  do ch an-break-body-text
.  do ch an-footer
.
.  do als em andoc-em
.  do als bp andoc-bp
.  do als ne andoc-ne
.  do blm            \" no blank line trap
.  do lsm            \" no leading space trap
.  em                \" no end-of-input trap
.
.  do rm Dd          \" force reinitialization of doc.tmac
.  do mso doc.tmac
.
.  do als TH reload-man
.
\\*(Dd\\
..
.
.de reload-man
.  \" Flush any partially collected output line and write page footer in
.  \" continuous rendering mode.
.  do if d doc-end-macro \
.    do doc-end-macro
.
.  \" Remove traps planted by mdoc/doc-{common,{n,dit}roff}.
.  do ch doc-break-body-text
.  do ch doc-header
.  do ch doc-footer
.
.  do als em andoc-em
.  do als bp andoc-bp
.  do als ne andoc-ne
.  do blm            \" no blank line trap
.  em                \" no end-of-input trap
.
.  do rm TH          \" force reinitialization of an.tmac
.  do mso an.tmac
.
.  do als Dd reload-doc
.
\\*(TH\\
..
.
.als TH reload-man
.als Dd reload-doc
.
.\" dummy equation macros -- eqnrc is read before .TH or .Dd is parsed
.de EQ
..
.de EN
..
.
.cp \n[*groff_andoc_tmac_C]
.do rr *groff_andoc_tmac_C
.
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set filetype=groff textwidth=72:
