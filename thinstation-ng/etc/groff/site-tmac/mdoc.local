.\" This file is loaded by mdoc.tmac.
.\"
.\" Put local modifications to groff_mdoc(7)'s behavior here.
.\"
.\" Adopting Debian's decision to preserve old mapping for UTF-8
.\" Debian: As of groff 1.23.0, the "-", "'", "`", "^", and "~" input
.\" characters are mapped to non-Basic Latin code points in output from
.\" the "utf8" device, for consistency with other output devices such as
.\" PDF.  Unfortunately in practice this still causes far too much
.\" disruption for innocent users of manual pages who just want to be able
.\" to search for characters normally, so preserve the old behaviour.
.\" Comment this out if you want to make sure that manual pages you're
.\" writing are clear of these problems.
.if '\*[.T]'utf8' \{\
.  char - \-
.  char ' \[aq]
.  char ` \[ga]
.  char ^ \[ha]
.  char ~ \[ti]
.\}
.\" "CW" is not a portable font name, but some man pages use it anyway.
.\" Uncomment this to suppress warnings produced by such pages.  This
.\" test remaps the font to roman ("R") on nroff (terminal) devices. You
.\" might prefer to remap it to bold ("B") instead.
.\" .if n .ftr CW R
.\"
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
