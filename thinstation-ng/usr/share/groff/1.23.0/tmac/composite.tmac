.\" composite.tmac
.
.do composite ga u0300
.do composite `  u0300
.do composite aa u0301
.do composite '  u0301
.do composite a^ u0302
.do composite ^  u0302
.do composite a~ u0303
.do composite ~  u0303
.do composite a- u0304
.do composite -  u0304
.do composite ab u0306
.do composite a. u0307
.do composite .  u0307
.do composite ad u0308
.do composite :  u0308
.do composite ao u030A
.do composite a" u030B
.do composite "  u030B
.do composite ah u030C
.do composite ac u0327
.do composite ,  u0327
.do composite ho u0328
.
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set filetype=groff textwidth=72:
