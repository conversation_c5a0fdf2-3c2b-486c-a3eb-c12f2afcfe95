#! /bin/sh
# Fix the path name of an AFM file.
if test -f "$1"
then
	echo "$1"
else
	echo `dirname $1`/`basename $1 .afm | awk '
/^AvantGarde-Book$/ { print "avangbk" }
/^AvantGarde-BookOblique$/ { print "avangbko" }
/^AvantGarde-Demi$/ { print "avangd" }
/^AvantGarde-DemiOblique$/ { print "avangdo" }
/^Bookman-Demi$/ { print "bookmd" }
/^Bookman-DemiItalic$/ { print "bookmdi" }
/^Bookman-Light$/ { print "bookml" }
/^Bookman-LightItalic$/ { print "bookmli" }
/^Courier$/ { print "couri" }
/^Courier-Bold$/ { print "courib" }
/^Courier-BoldOblique$/ { print "couribo" }
/^Courier-Oblique$/ { print "courio" }
/^Helvetica$/ { print "helve" }
/^Helvetica-Bold$/ { print "helveb" }
/^Helvetica-BoldOblique$/ { print "helvebo" }
/^Helvetica-Narrow$/ { print "helven" }
/^Helvetica-Narrow-Bold$/ { print "helvenb" }
/^Helvetica-Narrow-BoldOblique$/ { print "helvenbo" }
/^Helvetica-Narrow-Oblique$/ { print "helveno" }
/^Helvetica-Oblique$/ { print "helveo" }
/^NewCenturySchlbk-Bold$/ { print "newcsb" }
/^NewCenturySchlbk-BoldItalic$/ { print "newcsbi" }
/^NewCenturySchlbk-Italic$/ { print "newcsi" }
/^NewCenturySchlbk-Roman$/ { print "newcsr" }
/^Palatino-Bold$/ { print "palatb" }
/^Palatino-BoldItalic$/ { print "palatbi" }
/^Palatino-Italic$/ { print "palati" }
/^Palatino-Roman$/ { print "palatr" }
/^Symbol$/ { print "symbol" }
/^Times-Bold$/ { print "timesb" }
/^Times-BoldItalic$/ { print "timesbi" }
/^Times-Italic$/ { print "timesi" }
/^Times-Roman$/ { print "timesr" }
/^ZapfChancery-MediumItalic$/ { print "zapfcmi" }
/^ZapfDingbats$/ { print "zapfd" }
' `.afm
fi
