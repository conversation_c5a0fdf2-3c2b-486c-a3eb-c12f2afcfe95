#!/bin/bash

# Ensure the script was called with an argument
if [[ "$#" -ne 1 ]]; then
    echo "Usage: $0 <blue|green|alt>"
    exit 1
fi

OPTION=$1
BOOT_GRUB_PATH="/boot/boot/grub"

# Extract BOOT_IMAGE from /proc/cmdline
BOOT_IMAGE=$(cat /proc/cmdline | grep -o 'BOOT_IMAGE=[^ ]*' | cut -d'=' -f2)

# Determine the current environment based on BOOT_IMAGE
CURRENT_ENV=""
if [[ "$BOOT_IMAGE" =~ -blue ]]; then
    CURRENT_ENV="blue"
elif [[ "$BOOT_IMAGE" =~ -green ]]; then
    CURRENT_ENV="green"
fi

# Determine the target environment
TARGET_ENV=""
if [ "$OPTION" == "alt" ]; then
    if [ "$CURRENT_ENV" == "blue" ]; then
        TARGET_ENV="green"
    elif [ "$CURRENT_ENV" == "green" ]; then
        TARGET_ENV="blue"
    else
        echo "Could not determine the current environment from BOOT_IMAGE."
        exit 1
    fi
else
    TARGET_ENV="$OPTION"
fi

# Validate the target environment
if [[ "$TARGET_ENV" != "blue" && "$TARGET_ENV" != "green" ]]; then
    echo "Invalid argument: $TARGET_ENV. Please specify 'blue', 'green', or 'alt'."
    exit 1
fi

# Activate the target environment by creating a marker file
echo "Activating $TARGET_ENV environment..."
rm -f "$BOOT_GRUB_PATH/blue" "$BOOT_GRUB_PATH/green"  # Clean up any existing markers
touch "$BOOT_GRUB_PATH/$TARGET_ENV"  # Create a new marker for the target environment

echo "$TARGET_ENV environment activated."

# Ensure the filesystem changes are written
sync

# Countdown before reboot
echo "Rebooting in 5 seconds to activate the new environment..."
for i in $(seq 5 -1 1); do
    echo -ne "\r\033[K$i..."
    sleep 1
done

# Reboot the system
echo "Rebooting now!"
reboot
