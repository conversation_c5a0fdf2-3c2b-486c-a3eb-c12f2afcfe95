#base 00
systemd-sysusers
systemd-tmpfiles --create

rm -rf /lib64/doc

dbus-uuidgen > /etc/machine-id
chmod 0444 /etc/machine-id

sed -i '/session[[:space:]]\+include[[:space:]]\+system-auth/a session    optional     pam_hooks.so /sbin/session $PAM_USER' /etc/pam.d/login

for var in $(compgen -v | grep '^MOUNT_'); do
    mount="${!var}"
    if [ -n "$mount" ]; then
        echo -e "$(echo "$mount" | sed -e 's/[ ]\+/\t/g')" >> /etc/fstab
    fi
done

for var in $(compgen -v | grep '^CRYPT_'); do
    crypt="${!var}"
    if [ -n "$crypt" ]; then
        echo -e "$(echo "$crypt" | sed -e 's/[ ]\+/\t/g')" >> /etc/crypttab
    fi
done

for i in /*; do
  if [ $i != "/dev" ] && [ $i != "/proc" ] && [ $i != "/tmp" ]; then
#    chown -R root:root $i
    chmod -R g-w,o-w $i
  fi
done

#if [ "$SESSION_0_TYPE" == "sh" ]; then
#        rm /lib/systemd/system/default.target
#        ln -sf multi-user.target /lib/systemd/system/default.target
#        if is_enabled $SESSION_0_AUTOSTART; then
#		mkdir -p /etc/systemd/system/<EMAIL>.d
#                cat <<EOF> /etc/systemd/system/<EMAIL>.d/override.conf
#[Service]
#ExecStart=
#ExecStart=-/sbin/agetty -a tsuser tty1 115200 linux
#EOF
#        fi
#else
#rm -rf /etc/systemd/system/getty.target.wants
#fi

if [ "$TSUSER" == "root" ]; then
        sed -i "/deleteme/d" /etc/passwd
	sed -i "/deleteme/d" /etc/shadow
        sed -i "/deleteme/d" /etc/group
        sed -i "/deleteme/d" /etc/gshadow
fi
sed -i -e "s/tsuser/$TSUSER/g" /etc/passwd
sed -i -e "s/tsuser/$TSUSER/g" /etc/shadow
sed -i -e "s/tsuser/$TSUSER/g" /etc/group
sed -i -e "s/tsuser/$TSUSER/g" /etc/gshadow

sed -i -e "s/\-a tsuser/\-a $TSUSER/g" /etc/systemd/system/display-manager.service
if [ -e /etc/systemd/system/<EMAIL>.d/override.conf ]; then
	sed -i -e "s/\-a tsuser/\-a $TSUSER/g" /etc/systemd/system/<EMAIL>.d/override.conf
fi
if [ -e /etc/pam.d/login ]; then
        sed -i -e "s/user = root/user = $TSUSER/g" /etc/pam.d/login
fi

if [ "`readlink /bin/su`" == "/bin/busybox" ]; then
	chmod u+s /bin/busybox
else
	chmod u+s /bin/su
fi
chmod 640 /etc/securetty
chmod 1777 /var/tmp
ssl=-1
crypto=-1
for i in 2 1 0; do
	if [ -e /lib/libssl.so.1.0.$i ]; then
		if [ $ssl != -1 ]; then
			rm /lib/libssl.so.1.0.$i;
			ln -s /lib/libssl.so.1.0.$ssl /lib/libssl.so.1.0.$i
		else
			ssl=$i
		fi
	fi
	if [ -e /lib/libcrypto.so.1.0.$i ]; then
		if [ $crypto != -1 ]; then
			rm /lib/libcrypto.so.1.0.$i;
			ln -s /lib/libcrypto.so.1.0.$crypto /lib/libcrypto.so.1.0.$i
		else
			crypto=$i
		fi
	fi
done
systemd-hwdb update

write_release(){
	cat <<EOF> /etc/os-release
NAME=ThinStation
VERSION="$TSVER"
ID=thinstation
VERSION_ID=$TSVER
PRETTY_NAME="ThinStation $TSVER $CODENAME"
ANSI_COLOR="0;34"
CPE_NAME="cpe:/o:thinstation:thinstation:$TSVER"
HOME_URL="https://www.thinstation.org/"
SUPPORT_URL="https://www.github.com/thinstation/thinstation-ng"
BUG_REPORT_URL="https://www.github.com/thinstation/thinstation-ng"
EOF

	cat <<EOF> /etc/thinstation-release
ThinStation release $TSVER $CODENAME
EOF

	if [ ! -e /etc/issue ]; then
		cat <<'EOF'> /etc/issue
\S
Kernel \r on an \m (\l)
EOF
	fi

	for link in release system-release system-release-cpe; do
		ln -sf thinstation-release /etc/$link
	done
}
write_release

chmod 644 /etc/busybox.conf
chmod u+s /bin/busybox.shared /bin/su
chmod u+s /sbin/unix_chkpwd

/sbin/pwconv
	wait
/sbin/grpconv
	wait
chmod 000 /etc/shadow
chmod 000 /etc/gshadow

ldconfig

systemctl enable tsinit.target
systemctl enable debug
systemctl enable crond
systemctl enable crontab
systemctl enable rtc_cmos
systemctl enable pkg
systemctl enable post-udev
systemctl enable profile-setup
systemctl enable session
systemctl enable session-setup
systemctl enable fastboot
