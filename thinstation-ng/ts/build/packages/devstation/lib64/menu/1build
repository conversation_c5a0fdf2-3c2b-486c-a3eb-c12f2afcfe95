package="devstation"; icon="DevStation"; needs="x11"; title="Build Image"; command="sudo -i devcmd build"; menu="DevStation"; nodesktop="true"
package="devstation"; icon="media-optical"; needs="x11"; title="CD"; command="sudo -i devcmd test-cd"; menu="DevStation::Test-Build"; nodesktop="true"
package="devstation"; icon="media-optical"; needs="x11"; title="CD-EFI"; command="sudo -i devcmd test-cd-efi"; menu="DevStation::Test-Build"; nodesktop="true"
package="devstation"; icon="media-flash"; needs="x11"; title="HD"; command="sudo -i devcmd test-hd"; menu="DevStation::Test-Build"; nodesktop="true"
package="devstation"; icon="media-flash"; needs="x11"; title="HD-EFI"; command="sudo -i devcmd test-hd-efi"; menu="DevStation::Test-Build"; nodesktop="true"
package="devstation"; icon="network-wired"; needs="x11"; title="NET"; command="sudo -i devcmd test-net"; menu="DevStation::Test-Build"; nodesktop="true"
#package="devstation"; icon="network-wired"; needs="x11"; title="NET-EFI"; command="sudo -i devcmd test-net-efi"; menu="DevStation::Test-Build"; nodesktop="true"
package="devstation"; icon="DevStation"; needs="x11"; title="Edit Configs"; command="sudo -i devcmd configure"; menu="DevStation"; nodesktop="true"
package="devstation"; icon="DevStation"; needs="x11"; title="DNS/DHCP/Router"; command="sudo -i dnsmasq_gui"; menu="DevStation"; nodesktop="true"
package="devstation"; icon="DevStation"; needs="x11"; title="Toggle All Modules"; command="sudo -i devcmd toggle-modules"; menu="DevStation"; nodesktop="true"
package="devstation"; icon="DevStation"; needs="x11"; title="Toggle Console"; command="sudo -i devcmd toggle-con"; menu="DevStation"; nodesktop="true"
package="devstation"; icon="DevStation"; needs="x11"; title="Toggle PXE Read/Write"; command="sudo -i devcmd toggle-pxe"; menu="DevStation"; nodesktop="true"
package="devstation"; icon="DevStation"; needs="x11"; title="Make Machine Profile"; command="sudo -i devcmd make-machine"; menu="DevStation"; nodesktop="true"
package="devstation"; icon="DevStation"; needs="x11"; title="Tweek DevStation"; command="geany -c /etc/geany-build -s $HOME/thinstation.conf.user"; menu="DevStation"; nodesktop="true"
package="devstation"; icon="DevStation"; needs="x11"; title="Factory Reset"; command="sudo -i devcmd reset"; menu="DevStation"; nodesktop="true"
package="devstation"; icon="DevStation"; needs="x11"; title="Update DevStation"; command="sudo -i devcmd update"; menu="DevStation"; nodesktop="true"
package="terminal"; icon="Terminal"; needs="x11"; title="Terminal Emulator(root)"; command="sudo -i xfce4-terminal"; menu="Utilities"; metric=30
package="terminal"; icon="Terminal"; needs="x11"; title="Terminal Emulator"; command="xfce4-terminal"; menu="Utilities"; metric=30
package="devstation"; icon="Terminal"; needs="x11"; title="Terminal Chroot"; command="sudo -i devcmd terminal"; menu="Utilities"; metric=40
package="devstation"; icon="drive-harddisk"; needs="x11"; title="File System(root)"; command="sudo -i thunar"; menu="Utilities"; metric=1; nomenu="true"
