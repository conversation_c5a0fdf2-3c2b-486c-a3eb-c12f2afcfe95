Noteworthy changes in version 1.3.1 (2024-07-03)
------------------------------------------------

 * qt: Install and use pinentry icon.  [rPf9c252a8d9]

 * qt: Small fix for Qt5.  [rP844360c9c9]

 * qt: Fix Windows build of Qt6.  [rP34019f954a]

 * New envvar PINENTRY_KDE_USE_WALLET to enable the secret storage
   integration on KDE.  [rP23753cfb03]

 Release-info: https://dev.gnupg.org/T7046


Noteworthy changes in version 1.3.0 (2024-03-18)
------------------------------------------------

 * qt: Add new Qt6 frontend.  [rP1e79123c38]

 * qt: Set parent window on Wayland.  [T6930]

 * qt: Fix capslock detection on Wayland.  [rP7dfc60a70d]

 * qt: Fix window icon on Wayland.  [T6887]

 * qt: Add support for external password manager with libsecret.
   [T6801]

 * qt: Remove focus indication by text selection.  [T5863]

 * qt: Use same focus indication for labels as Kleopatra.  [T5863]

 * qt: Improve accessibility.  [T5863]

 * gnome3: Prefer gcr-4.  [rP069c219223]

 * curses: Fix timeout handling.  [rP08408498b3]

 * curses: Add SETREPEATOK and quality bar colors.  [rP2f109972e4]

 * curses: Add password quality meter.  [rP2923707e75]

 * curses,tty: Upon SIGINT, let pinentry exit gracefully.  [T6641]

 * w32: Fix non-focused window and simplify code.  [rPabbecc67d9]

 * Disable secret storage integration when running on KDE Plasma.
   [rPefb6de7f]

 * The Windows CE support has been removed.


 Release-info: https://dev.gnupg.org/T7046


Noteworthy changes in version 1.2.1 (2022-08-24)
------------------------------------------------

 * qt: Support building with Qt 5.9.  [T5592]

 * curses: Handle an error at curses initialization.  [T5623]

 * curses: Specify fg/bg when an extention of Ncurses is not available.
   [T5631]

 * qt: Fix translation of context menu entries.  [T5786]

 * qt: Further improve the accessibility.  [T5863]

 * qt: Fix moving focus to second input field when pressing Enter in
   first input field.  [T5866]

 * qt: Update the cursor position when reformatting the text.  [T5972]

 * qt: Use foreground raising code also with the confirm prompt.
   [T6134]

 * Make the legacy qt4 version build again.  [T5569]

 * Make sure an entered PIN is always cleared from memory.  [T5977]

 * Build fixes for Windows.  [T5893]


Noteworthy changes in version 1.2.0 (2021-08-25)
------------------------------------------------

 * qt: Show a warning if Caps Lock is on on Windows, X11 (requires
   libX11 and Qt5X11Extras), and Wayland (requires KF5WaylandClient).
   [T4950]

 * qt: Support password formatting.  This makes generated passwords
   easier to transcript. [T5517]

 * qt: Fix showing of pinentry window on Wayland.  [T5528]

 * qt: Check passphrase constraints before accepting passphrase if
   passphrase constraints are requested to be enforced.  [T5532]

 * qt: Improve detection of running in a GUI session.  [T3659]

 * qt: Improve accessibility when entering new password.  [T5543]

 Release-info: https://dev.gnupg.org/T5566


Noteworthy changes in version 1.1.1 (2021-01-21)
------------------------------------------------

  * A EFL-based pinentry has been contributed.

  * Disable echoing in backspace key is pressed first
    (GTK, Qt, TQt, and ncurses pinentries).

  * Support line editing in TTY pinentry.

  * Remove support for old GTK+2 (< 2.12.0).

  * Various minor fixes.


Noteworthy changes in version 1.1.0 (2017-12-03)
------------------------------------------------

 * A FLTK1.3-based pinentry has been contributed.

 * A TQt3-based pinentry has been contributed.

 * New option --ttyalert for pinentry-curses to alert the user.

 * Don't show "save passphrase" checkbox if secret service is
   unavailable.

 * The GTK Pinentry shows on Linux some information anout the process
   which invoked the Pinentry.

 * The GTK Pinentry does not anymore show tooltips when keyboard
   grabbing is enabled.

 * Fixed various minor problems.


Noteworthy changes in version 1.0.0 (2016-11-22)
------------------------------------------------

 * Qt pinentry now supports repeat mode in one dialog.

 * Qt and GTK pinentries now make it possible to show the entered
   value.

 * Qt pinentry now only grabs the keyboard if an entry field is
   focused.

 * Fixed foreground handling in pinentry-qt if compiled with Qt5 for
   Windows.

 * Fixed potential crash in Qt qualitybar calculation.

 * GTK keyboard grabbing is now a bit more robust.  The cursor is
   changed to a big dot as a visual indication that a pinentry has
   popped up and is waiting for input.

 * The GNOME pinentry now falls back to curses if it can't use the
   GCR system prompter or a screenlock is active.

 * Fixed error output for cached passwords.

 * A show/hide passphrase button or checkbox is now available with
   some pinentry flavors.

 * Improved diagnostics and error codes.


Noteworthy changes in version 0.9.7 (2015-12-07)
------------------------------------------------

 * Fix regressions in the Qt pinentry.

 * Fix minor problems pinnetyr-tty.

 * New option --invisible-char.


Noteworthy changes in version 0.9.6 (2015-09-10)
------------------------------------------------

 * Many improvements for the dump tty pinentry.

 * Use the standard GTK+-2 text entry widget instead of our outdated
   and back-then-it-was-more-secure text widget.

 * Use the standard Qt text widget.

 * Allow for building a static Qt variant.

 * Fix regression in w32 pinentry.


Noteworthy changes in version 0.9.5 (2015-07-01)
------------------------------------------------

 * Replaced the internal Assuan and gpg-error code by the standard
   libassuan and libgpg-error libraries.

 * Add a new Emacs pinentry and use as fallback for GUI programs.

 * gnome3: The use-password-manager checkbox does now work.

 * Gtk: Improved fallback to curses feature.

 * curses: Recognize DEL as backspace.


Noteworthy changes in version 0.9.4 (2015-06-05)
------------------------------------------------

 * Fix regression in GTK+ and curses pinentries.


Noteworthy changes in version 0.9.3 (2015-06-01)
------------------------------------------------

 * Improved documentation

 * New pinentry-gnome3

 * More improvements for pinentry-tty.

 * Fixes for pinentry-curses including support for Ctrl-W, Ctrl-U,
   Ctrl-H, Ctrl-L, and Alt-Backspace

 * New Assuan command to request clearing an external cache.

 * Fixed problems linking to ncursesw.

 * All kind of other minor fixes.


Noteworthy changes in version 0.9.2 (2015-05-11)
------------------------------------------------

 * Support for saving the passphrase with libsecret.

 * Escape key works in the Gtk+ pinentry.

 * Improvements for pinentry-tty.

 * Minor cleanups for the native Windows pinentry.


Noteworthy changes in version 0.9.1 (2015-03-18)
------------------------------------------------

 * Fixed build problems for systems without ncurses.

 * Reworked the option parser to allow building on systems without
   getopt_long.

 * Fixed Qt4 build problems.


Noteworthy changes in version 0.9.0 (2014-10-26)
------------------------------------------------

 * New command SETREPEAT.  Currently only supported for Gtk+-2.

 * Gtk+-2: Pasting using the mouse is now supported.

 * curses: Check that it is actually connected to a tty.

 * Removed the old qt-3 and gtk+-1 pinentries.


Noteworthy changes in version 0.8.4 (2014-09-18)
------------------------------------------------

 * New pinentry-tty version for dumb terminals.

 * Qt4: New option to enable pasting the passphrase from clipboard

 * Qt4: Improved accessiblity

 * Qt4: Raise confirm message windows into foreground

 * Qt4 (Windows): Improve the way pinentry-qt raises itself in the
   foreground.

 * Improved the build system.


Noteworthy changes in version 0.8.3 (2013-04-26)
------------------------------------------------

 * Build fixes for newer mingw32 toolchains.

 * Add SETTIMEOUT command for the gtk+-2 pinentry.


Noteworthy changes in version 0.8.2 (2012-08-08)
------------------------------------------------

 * New SETTIMEOUT command for the qt4 pinentry.

 * Wide character support for the curses pinentry.

 * Various bug fixes.


Noteworthy changes in version 0.8.1 (2010-12-16)
------------------------------------------------

 * The W32 pinentry now supports WindowsCE.

 * The GTK pinentry now always sticks to the top and properly grabs
   the keyboard.

 * The protocol options default-cancel and default-ok now work for the
   pinentry-gtk2 and pinentry-qt (that is QT3).


Noteworthy changes in version 0.8.0 (2010-03-03)
------------------------------------------------

 * Beautified the qt4 pinentry

 * Minor enhancements.


Noteworthy changes in version 0.7.6 (2009-06-19)
------------------------------------------------

 * Make Gtk+-2 pinentry transient to the root window.

 * Add Qt4 pinentry.

 * Add native W32 pinentry.

 * Fix utf-8 problem in Qt pinentries.

 * Return GPG_ERR_CANCELED if during a "CONFIRM" command the user
   closed the window.

 * Add quality bar.

Noteworthy changes in version 0.7.5 (2008-02-15)
------------------------------------------------

 * Fix cross compilation for Gtk+-2 pinentry.

 * New Assuan command GETINFO with subcommands "version" and "pid".


Noteworthy changes in version 0.7.4 (2007-11-29)
------------------------------------------------

 * Pinentry-gtk-2 and pinentry-qt now support a simple passphrase
   quality indicator.


Noteworthy changes in version 0.7.3 (2007-07-06)
------------------------------------------------

 * New command MESSAGE and --one-button compatibility option to
   CONFIRM.

 * New Assuan option touch-file to set a file which will be touched
   after ncurses does not need the display anymore.

 * New option --colors=FG,BG,SO to set the colors for the curses
   pinentry.

 * Pinentry-w32 does now basically work.  It needs some finishing
   though.  For example the buttons should resize themself according
   to the size of the text.


Noteworthy changes in version 0.7.2 (2005-01-27)
------------------------------------------------

 * Remove bug in configure script that would use installed version of
   Qt even if another path was explicitely specified with QTDIR.

 * Honor the rpath setting for Qt.

 * Add GTK+-2 pinentry.

 * Install a symbolic link under the name "pinentry" that defaults to
   pinentry-gtk, pinentry-qt, pinentry-gtk-2, or pinentry-curses, in
   that order.


Noteworthy changes in version 0.7.1 (2004-04-21)
------------------------------------------------

 * Removed unneeded Assuan cruft.

 * Fixes for *BSD.


Noteworthy changes in version 0.7.0 (2003-12-23)
------------------------------------------------

 * Make UTF8 description (prompt, error message, button texts) work.

 * Make sure that secmem_term is called before program termination.

 * Make assuan in Gtk and Curses pinentry use secure memory for
   storage.

 * Fixed a bug that would occur if a canceled GETPIN was immediately
   followed by a CONFIRM.

 * Disabled undo/redo in Qt pinentry.

 * Print diagnostics for locale problems and return a new error code
   in that case.


Noteworthy changes in version 0.6.8 (2003-02-07)
------------------------------------------------

 * Bug fix in pinentry-qt.

Noteworthy changes in version 0.6.7 (2002-11-20)
------------------------------------------------

 * Workaround for a bug in the curses version which led to an infinite
   loop.

Noteworthy changes in version 0.6.6 (2002-11-09)
------------------------------------------------

 * Fixed handling of DISPLAY and --display for the sake of the curses
   fallback.

 * UTF-8 conversion does now work for the GTK+ and CURSES version.


Noteworthy changes in version 0.6.5 (2002-09-30)
------------------------------------------------

  * Handle Assuan options in the qt version.

Noteworthy changes in version 0.6.4 (2002-08-19)
------------------------------------------------

  * Handle CONFIRM command in the qt version.

Noteworthy changes in version 0.6.3 (2002-06-26)
------------------------------------------------

  * Minor bug fixes to the qt version.

Noteworthy changes in version 0.6.2 (2002-05-13)
------------------------------------------------

  * Error texts can now be percent-escaped.

  * The Curses pinentry supports multi-line error texts.

  * The GTK+ and Qt pinentry can fall back to curses if no display is
    available.

Noteworthy changes in version 0.6.1 (2002-04-25)
------------------------------------------------

  * The Curses pinentry supports user-provided button texts via the
    new SETOK and SETCANCEL commands.

  * The Curses pinentry supports setting the desired character set
    locale with --lc-ctype and correctly translates the UTF-8 strings
    into that.

Noteworthy changes in version 0.6.0 (2002-04-05)
------------------------------------------------

  * Merged all pinentry frontends into a single module.

  * There is now a Curses frontend.

  * The curses pinentry supports --ttyname and --ttytype options to
    set the desired input/output terminal and its type.

Noteworthy changes in version 0.5.1 (2002-02-18)
------------------------------------------------

  * CONFIRM command works

Noteworthy changes in version 0.5.0 (2002-01-04)
------------------------------------------------

 * Window layout is somewhat nicer

 * percent escape sequences do now work for SETDESC and SETERROR
