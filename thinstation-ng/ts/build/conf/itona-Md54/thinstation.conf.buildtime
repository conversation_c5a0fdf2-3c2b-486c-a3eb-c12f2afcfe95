AUDIO_LEVEL=90
MIC_LEVEL=0
HD_MOUNT=true
#X_DPI=100
#USE_XRANDR=TRUE
#XRANDR_OPTIONS="-s 1680x1050"
#MEMORY_CONSTRAINED="TRUE"
SESSION_0_TYPE=xfwm4
SESSION_1_TYPE=chrome
SESSION_0_AUTOSTART=on
SESSION_1_AUTOSTART=off
SAMBA_SECURITY=user
SAMBA_WORKGROUP=WORKGROUP
LOCALE=en_US
#SESSION_1_FIREFOX_HOMEPAGE=http://www.doncuppjr.net/projects
SESSION_1_CHROME_HOMEPAGE=http://www.doncuppjr.net/projects
GTK_THEME=Adwaita
XFWM4_THEME=Default
XTERM_CMD=xfce4-terminal
DESKTOP_ICON_SIZE=48
DESKTOP_FONT_SIZE=8
DESKTOP_SHOWTRASH=false
DEKSTOP_SHOWHOME=true
DESKTOP_SHOWREMOVEABLE=false
DESKTOP_SHOWFILESYSTEM=true
# PANEL_AUTOHIDE=false
PANEL_PAGER=false
PANEL_REV_CLOCKSYSTRAY=true
PANEL_USER=false
# XFWM4_START_POSITION="BOTTOM"
XFWM4_START_TITLE="Start"
XFWM4_START_ICON="applications-system"
XFWM4_COMPOSITING="true"
NO_XORG_CMD="/bin/sh"
#ICONS_COMPOSITE_THEME_ORDER="gnome Tango Neu"
ICONS_CUT_SIZES="256x256 scalable"
#SERVER=**************
#MENU_SHOWRES=off
#MENU_SHOWAPPS=off
#MENU_SHOWABOUT=off
#MENU_SHOWLOGOUT=off
#HIDE_CONTROLS=on
#SCREEN_BLANK_TIME=0
#SCREEN_STANDBY_TIME=0
#SCREEN_SUSPEND_TIME=0
#SCREEN_OFF_TIME=0
#SCREEN_HORIZSYNC="30-70 | *"
#DONT_VT_SWITCH_STATE=TRUE
#DONT_ZAP_STATE=TRUE
#ALWAYS_ENTER_SERVER=TRUE
#FASTBOOT_URL=http://*************
NET_USE=BOTH
NET_USE_DHCP=on
NET_HOSTNAME=ts-*
NET_TELNETD_ENABLED=ON
#DEBUGPAUSE=TRUE
#EGALAX_DEVICE=/dev/ttyS1
#EGALAX_DEVICE=usbauto
#EGALAX_PARAMFILE=/boot/eeti.param
#RECONNECT_PROMPT=AUTO
TIME_ZONE=America/Los_Angeles
NET_TIME_SERVER=us.pool.ntp.org
NET_REMOTE_ACCESS_FROM="0.0.0.0"
#X_DRIVER_OPTION1="HWcursor False"
#X_DRIVER_OPTION1="PanelSize 1920x1080"
X_DRIVER_NAME=via
#WIRELESS_ESSID="Example"
#WIRELESS_MODE="managed"
#WIRELESS_WPAKEY="3x@mpl39@559hr@53"
#WIRELESS_DRIVER="wext"
TFTP_BLOCKSIZE=8192
#TFTP_BLOCKSIZE=1024
BIND_MOUNT0="home:/root"
BIND_MOUNT1="boot:/boot"
BIND_MOUNT2="tsdev:/thinstation"
BIND_MOUNT3="TEST:/TEST"
USB_STORAGE_SYNC=on
USB_MOUNT_DIR=
BASE_MOUNT_PATH=/media
