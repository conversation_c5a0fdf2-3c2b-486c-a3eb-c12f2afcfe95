#!/bin/sh

echo "Ensuring all devices are available..."

# Parse /etc/fstab for device specifications
while read -r line; do
    # Skip comments and empty lines
    [ -z "$line" ] || [[ "$line" =~ ^# ]] && continue

    # Extract the first column (device specifier)
    device=$(echo "$line" | awk '{print $1}')

    if [[ "$device" =~ ^LABEL= ]]; then
        # Handle LABEL= entries
        label=${device#LABEL=}
        while [ -z "$(blkid -L "$label")" ]; do
            echo "Waiting for device with LABEL=$label..."
            sleep 1
        done
    elif [[ "$device" =~ ^UUID= ]]; then
        # Handle UUID= entries
        uuid=${device#UUID=}
        while [ -z "$(blkid -U "$uuid")" ]; do
            echo "Waiting for device with UUID=$uuid..."
            sleep 1
        done
    elif [[ "$device" =~ ^/dev/ ]]; then
        # Handle direct device paths
        while [ ! -e "$device" ]; do
            echo "Waiting for device $device..."
            sleep 1
        done
    else
        # Unknown device specification, log a warning
        echo "Unknown device specifier: $device (skipping)"
    fi
done < /etc/fstab

echo "All devices are available."

mount -a
systemd-tmpfiles --create

