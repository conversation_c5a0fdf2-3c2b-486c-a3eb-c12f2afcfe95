#!/bin/bash

pkg=$1
name=$(echo $pkg |tr [A-Z] [a-z])
mkdir -p /ts/ports/python/$name

cat << EOF > /ts/ports/python/$name/Pkgfile
# Description: A language that lets writing C extensions for the Python language.
# URL: http://www.cython.org/
# Maintainer: <PERSON>, monster dot romster at gmail dot com
# Packager: <PERSON>, pitillo at ono dot com
# Depends on: setuptools

name=$name
pname=$pkg
version=pip
release=1
source=()

build() {
        pip2 install \$name --download=\$SRC --no-binary :all:
        mkdir -p \$SRC/\$name
        tar -xvf \$SRC/\$pname*.tar.* -C \$SRC/\$name || unzip -d \$SRC/\$name \$pname-*.zip
        cd \$SRC/\$name/*

        python ./setup.py install --root \$PKG
}
EOF
