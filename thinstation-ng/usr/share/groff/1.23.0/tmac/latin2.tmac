.\" latin2.tmac
.\"
.do nr *groff_latin2_tmac_C \n[.cp]
.cp 0
.
.if '\*[.T]'latin1' \{\
.\" Replace characters that ISO Latin-1 has but Latin-2 doesn't.
.char \[r!] \ \" space
.char \[ct] \ \" space
.char \[Po] \ \" space
.char \[Ye] \ \" space
.char \[bb] \ \" space
.char \[co] \ \" space
.char \[Of] \ \" space
.char \[Fo] \ \" space
.char \[no] \ \" space
.char \[rg] \ \" space
.char \[a-] \ \" space
.char \[+-] \ \" space
.char \[S2] \ \" space
.char \[S3] \ \" space
.char \[mc] \ \" space
.char \[ps] \ \" space
.char \[pc] \ \" space
.char \[S1] \ \" space
.char \[Om] \ \" space
.char \[Fc] \ \" space
.char \[14] 1/4
.char \[12] 1/2
.char \[34] 3/4
.char \[r?] \ \" space
.char \[`A] A
.char \[~A] A
.char \[oA] A
.char \[AE] AE
.char \[`E] E
.char \[^E] E
.char \[`I] I
.char \[:I] I
.char \[-D] \ \" space
.char \[~N] N
.char \[`O] O
.char \[~O] O
.char \[/O] O
.char \[`U] U
.char \[^U] U
.char \[TP] \ \" space
.char \[`a] a
.char \[~a] a
.char \[oa] A
.char \[ae] ae
.char \[`e] e
.char \[^e] e
.char \[`i] i
.char \[:i] i
.char \[Sd] \ \" space
.char \[~n] n
.char \[`o] o
.char \[~o] o
.char \[/o] o
.char \[`u] u
.char \[^u] u
.char \[tp] \ \" space
.char \[:y] y
.\" Map characters that ISO Latin-2 has and Latin-1 doesn't to their
.\" numeric code points.
.\" 0xA0
.char \[A ho] \N'161'
.char \[ab] \N'162'
.char \[/L] \N'163'
.char \[L ah] \N'165'
.char \[S aa] \N'166'
.char \[vS] \N'169'
.char \[S ac] \N'170'
.char \[T ah] \N'171'
.char \[Z aa] \N'172'
.char \[vZ] \N'174'
.char \[Z a.] \N'175'
.\" 0xB0
.char \[a ho] \N'177'
.char \[ho] \N'178'
.char \[/l] \N'179'
.char \[l ah] \N'181'
.char \[s aa] \N'182'
.char \[ah] \N'183'
.char \[vs] \N'185'
.char \[s ac] \N'186'
.char \[t ah] \N'187'
.char \[z aa] \N'188'
.char \[a"] \N'189'
.char \[vz] \N'190'
.char \[z a.] \N'191'
.\" 0xC0
.char \[R aa] \N'192'
.char \[A ab] \N'195'
.char \[L aa] \N'197'
.char \[C aa] \N'198'
.char \[C ah] \N'200'
.char \[E ho] \N'202'
.char \[E ah] \N'204'
.char \[D ah] \N'207'
.\" 0xD0
.char \[u0110] \N'208'
.char \[N aa] \N'209'
.char \[N ah] \N'210'
.char \[O a"] \N'213'
.char \[R ah] \N'216'
.char \[U ao] \N'217'
.char \[U a"] \N'219'
.char \[T ac] \N'222'
.\" 0xE0
.char \[r aa] \N'224'
.char \[a ab] \N'227'
.char \[l aa] \N'229'
.char \[c aa] \N'230'
.char \[c ah] \N'232'
.char \[e ho] \N'234'
.char \[e ah] \N'236'
.char \[d ah] \N'239'
.\" OxF0
.char \[u0111] \N'240'
.char \[n aa] \N'241'
.char \[n ah] \N'242'
.char \[o a"] \N'245'
.char \[r ah] \N'248'
.char \[u ao] \N'249'
.char \[u a"] \N'251'
.char \[t ac] \N'254'
.char \[a.] \N'255'
.\} \" using -Tlatin1
.
.\" Translate eight-bit input characters.
.\" 0xA0
.\" char160 (no-break space) is translated on input
.trin \[char161]\[A ho]
.trin \[char162]\[ab]
.trin \[char163]\[/L]
.trin \[char164]\[Cs]
.trin \[char165]\[L ah]
.trin \[char166]\[S aa]
.trin \[char167]\[sc]
.trin \[char168]\[ad]
.trin \[char169]\[vS]
.trin \[char170]\[S ac]
.trin \[char171]\[T ah]
.trin \[char172]\[Z aa]
.\" char173 (soft hyphen) is translated on input
.trin \[char174]\[vZ]
.trin \[char175]\[Z a.]
.\" 0xB0
.trin \[char176]\[de]
.trin \[char177]\[a ho]
.trin \[char178]\[ho]
.trin \[char179]\[/l]
.trin \[char180]\[aa]
.trin \[char181]\[l ah]
.trin \[char182]\[s aa]
.trin \[char183]\[ah]
.trin \[char184]\[ac]
.trin \[char185]\[vs]
.trin \[char186]\[s ac]
.trin \[char187]\[t ah]
.trin \[char188]\[z aa]
.trin \[char189]\[a"]
.trin \[char190]\[vz]
.trin \[char191]\[z a.]
.\" C0
.trin \[char192]\[R aa]
.trin \[char193]\['A]
.trin \[char194]\[^A]
.trin \[char195]\[A ab]
.trin \[char196]\[:A]
.trin \[char197]\[L aa]
.trin \[char198]\[C aa]
.trin \[char199]\[,C]
.trin \[char200]\[C ah]
.trin \[char201]\['E]
.trin \[char202]\[E ho]
.trin \[char203]\[:E]
.trin \[char204]\[E ah]
.trin \[char205]\['I]
.trin \[char206]\[^I]
.trin \[char207]\[D ah]
.\" 0xD0
.trin \[char208]\[u0110]
.trin \[char209]\[N aa]
.trin \[char210]\[N ah]
.trin \[char211]\['O]
.trin \[char212]\[^O]
.trin \[char213]\[O a"]
.trin \[char214]\[:O]
.trin \[char215]\[tmu]
.trin \[char216]\[R ah]
.trin \[char217]\[U ao]
.trin \[char218]\['U]
.trin \[char219]\[U a"]
.trin \[char220]\[:U]
.trin \[char221]\['Y]
.trin \[char222]\[T ac]
.trin \[char223]\[ss]
.\" 0xE0
.trin \[char224]\[r aa]
.trin \[char225]\['a]
.trin \[char226]\[^a]
.trin \[char227]\[a ab]
.trin \[char228]\[:a]
.trin \[char229]\[l aa]
.trin \[char230]\[c aa]
.trin \[char231]\[,c]
.trin \[char232]\[c ah]
.trin \[char233]\['e]
.trin \[char234]\[e ho]
.trin \[char235]\[:e]
.trin \[char236]\[e ah]
.trin \[char237]\['i]
.trin \[char238]\[^i]
.trin \[char239]\[d ah]
.\" 0xF0
.trin \[char240]\[u0111]
.trin \[char241]\[n aa]
.trin \[char242]\[n ah]
.trin \[char243]\['o]
.trin \[char244]\[^o]
.trin \[char245]\[o a"]
.trin \[char246]\[:o]
.trin \[char247]\[tdi]
.trin \[char248]\[r ah]
.trin \[char249]\[u ao]
.trin \[char250]\['u]
.trin \[char251]\[u a"]
.trin \[char252]\[:u]
.trin \[char253]\['y]
.trin \[char254]\[t ac]
.trin \[char255]\[a.]
.cp \n[*groff_latin2_tmac_C]
.do rr *groff_latin2_tmac_C
.
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set filetype=groff textwidth=72:
