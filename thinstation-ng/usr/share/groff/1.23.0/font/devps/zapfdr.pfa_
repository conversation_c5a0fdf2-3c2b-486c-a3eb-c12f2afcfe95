%!PS-Adobe-3.0 Resource-Font
%%DocumentNeededResources: font ZapfDingbats
%%EndComments
%%IncludeResource: font ZapfDingbats
/ZapfDingbats findfont[-1 0 0 1 0 0]makefont
dup length 1 add dict begin
{
exch dup dup/FID ne exch/UniqueID ne and{
exch def
}{
pop pop
}ifelse
}forall
/FontName/ZapfDingbats-Reverse def
/Metrics 202 dict dup begin
/space[-0 -278]def
/a1[-939 -974]def
/a2[-927 -961]def
/a202[-939 -974]def
/a3[-945 -980]def
/a4[-685 -719]def
/a5[-755 -789]def
/a119[-755 -790]def
/a118[-761 -791]def
/a117[-655 -690]def
/a11[-925 -960]def
/a12[-904 -939]def
/a13[-516 -549]def
/a14[-820 -855]def
/a15[-876 -911]def
/a16[-899 -933]def
/a105[-876 -911]def
/a17[-909 -945]def
/a18[-938 -974]def
/a19[-721 -755]def
/a20[-811 -846]def
/a21[-727 -762]def
/a22[-727 -761]def
/a23[-571 -571]def
/a24[-642 -677]def
/a25[-728 -763]def
/a26[-726 -760]def
/a27[-725 -759]def
/a28[-720 -754]def
/a6[-460 -494]def
/a7[-517 -552]def
/a8[-503 -537]def
/a9[-542 -577]def
/a10[-657 -692]def
/a29[-751 -786]def
/a30[-752 -788]def
/a31[-753 -788]def
/a32[-756 -790]def
/a33[-759 -793]def
/a34[-759 -794]def
/a35[-782 -816]def
/a36[-787 -823]def
/a37[-754 -789]def
/a38[-807 -841]def
/a39[-789 -823]def
/a40[-798 -833]def
/a41[-782 -816]def
/a42[-796 -831]def
/a43[-888 -923]def
/a44[-710 -744]def
/a45[-688 -723]def
/a46[-714 -749]def
/a47[-756 -790]def
/a48[-758 -792]def
/a49[-661 -695]def
/a50[-741 -776]def
/a51[-734 -768]def
/a52[-757 -792]def
/a53[-725 -759]def
/a54[-672 -707]def
/a55[-672 -708]def
/a56[-647 -682]def
/a57[-666 -701]def
/a58[-791 -826]def
/a59[-780 -815]def
/a60[-754 -789]def
/a61[-754 -789]def
/a62[-673 -707]def
/a63[-651 -687]def
/a64[-661 -696]def
/a65[-655 -689]def
/a66[-751 -786]def
/a67[-752 -787]def
/a68[-678 -713]def
/a69[-756 -791]def
/a70[-751 -785]def
/a71[-757 -791]def
/a72[-838 -873]def
/a73[-726 -761]def
/a74[-727 -762]def
/a203[-727 -762]def
/a75[-725 -759]def
/a204[-725 -759]def
/a76[-858 -892]def
/a77[-858 -892]def
/a78[-754 -788]def
/a79[-749 -784]def
/a81[-403 -438]def
/a82[-104 -138]def
/a83[-242 -277]def
/a84[-380 -415]def
/a97[-357 -392]def
/a98[-357 -392]def
/a99[-633 -668]def
/a100[-634 -668]def
/a89[-356 -390]def
/a90[-355 -390]def
/a93[-283 -317]def
/a94[-283 -317]def
/a91[-242 -276]def
/a92[-242 -276]def
/a205[-475 -509]def
/a85[-475 -509]def
/a206[-375 -410]def
/a86[-375 -410]def
/a87[-199 -234]def
/a88[-199 -234]def
/a95[-299 -334]def
/a96[-299 -334]def
/a101[-697 -732]def
/a102[-488 -544]def
/a103[-508 -544]def
/a104[-875 -910]def
/a106[-633 -667]def
/a107[-726 -760]def
/a108[-758 -760]def
/a112[-741 -776]def
/a111[-560 -595]def
/a110[-659 -694]def
/a109[-591 -626]def
/a120[-754 -788]def
/a121[-754 -788]def
/a122[-754 -788]def
/a123[-754 -788]def
/a124[-754 -788]def
/a125[-754 -788]def
/a126[-754 -788]def
/a127[-754 -788]def
/a128[-754 -788]def
/a129[-754 -788]def
/a130[-754 -788]def
/a131[-754 -788]def
/a132[-754 -788]def
/a133[-754 -788]def
/a134[-754 -788]def
/a135[-754 -788]def
/a136[-754 -788]def
/a137[-754 -788]def
/a138[-754 -788]def
/a139[-754 -788]def
/a140[-754 -788]def
/a141[-754 -788]def
/a142[-754 -788]def
/a143[-754 -788]def
/a144[-754 -788]def
/a145[-754 -788]def
/a146[-754 -788]def
/a147[-754 -788]def
/a148[-754 -788]def
/a149[-754 -788]def
/a150[-754 -788]def
/a151[-754 -788]def
/a152[-754 -788]def
/a153[-754 -788]def
/a154[-754 -788]def
/a155[-754 -788]def
/a156[-754 -788]def
/a157[-754 -788]def
/a158[-754 -788]def
/a159[-754 -788]def
/a160[-860 -894]def
/a161[-803 -838]def
/a163[-981 -1016]def
/a164[-422 -458]def
/a196[-698 -748]def
/a165[-890 -924]def
/a192[-698 -748]def
/a166[-884 -918]def
/a167[-892 -927]def
/a168[-891 -928]def
/a169[-893 -928]def
/a170[-799 -834]def
/a171[-838 -873]def
/a172[-791 -828]def
/a173[-889 -924]def
/a162[-889 -924]def
/a174[-882 -917]def
/a175[-896 -930]def
/a176[-896 -931]def
/a177[-429 -463]def
/a178[-848 -883]def
/a179[-802 -836]def
/a193[-802 -836]def
/a180[-832 -867]def
/a199[-832 -867]def
/a181[-661 -696]def
/a200[-661 -696]def
/a182[-840 -874]def
/a201[-840 -874]def
/a183[-725 -760]def
/a184[-911 -946]def
/a197[-736 -771]def
/a185[-830 -865]def
/a194[-736 -771]def
/a198[-853 -888]def
/a186[-932 -967]def
/a195[-853 -888]def
/a187[-796 -831]def
/a188[-838 -873]def
/a189[-891 -927]def
/a190[-931 -970]def
/a191[-884 -918]def
end def
/ZapfDingbats-Reverse currentdict end definefont pop
