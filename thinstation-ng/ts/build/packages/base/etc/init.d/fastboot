#!/bin/sh

. `dirname $0`/common

if [ -n "$DEBUG_BOOT" ]; then
	set -x
	exec </dev/null >/var/log/fastboot.log  2>&1
fi

search()
{
	if [ -e $1 ]; then
		return 0
	fi
	if [ -z $squash_location ]; then
		filename=`basename $1`
		if [ -e /boot/boot/$filename ]; then
			squash_location="boot/"
		fi
	fi
	if [ -z $squash_location ]; then
		return 1
	else
		FILE=/boot/"$squash_location"$lib_file
		UPDATE=/boot/"$squash_location"lib.update
		if [ -e $FILE ] || [ -e $UPDATE ]; then
			return 0
		else
			return 1
		fi
	fi
}

disk() {
    # Parse /proc/cmdline to find the BOOT_IMAGE parameter
    BOOT_IMAGE=$(cat /proc/cmdline | sed 's/.*BOOT_IMAGE=\([^ ]*\).*/\1/')
    echo "Boot Image: $BOOT_IMAGE"

    # Extract extension after "vmlinuz"
    extension=$(echo "$BOOT_IMAGE" | sed -n 's/.*vmlinuz\(-[a-zA-Z0-9_-]*\)/\1/p')

    # Append the extracted extension to lib.squash if found, otherwise use default
    if [ -n "$extension" ]; then
        lib_file="lib.squash$extension"
    else
        lib_file="lib.squash"
    fi

    if [ -z "$HDVOLNAME" ]; then
	if [ -e /dev/disk/by-label/THINSTATION ]; then
		HDVOLNAME=THINSTATION
	elif [ -e /dev/disk/by-label/BOOT ]; then
                HDVOLNAME=BOOT
	else
                HDVOLNAME=boot
	fi
    fi

    if ! mountpoint /boot; then
	    systemd-mount --fsck=no -o "x-mount.mkdir,defaults" /dev/disk/by-label/$HDVOLNAME /boot
    fi
    FILE=/boot/"$squash_location"$lib_file
    UPDATE=/boot/"$squash_location"lib.update
    local timeout=15
    while [ "$BOOTABLE" != "TRUE" ] && [ "$timeout" -ne "0" ]; do
        if search "$FILE" || search "$UPDATE" ; then
            BOOTABLE=TRUE
        else
            BOOTABLE=FALSE
            let timeout-=1
            printf "\rWaiting for Storage -- %s" "$timeout"
            sleep .5
            mount -a 2>/dev/null
        fi
        printf "\n"
    done
    if [ "$BOOTABLE" == "FALSE" ]; then
        printf "\rCould not find the storage volume!"
	exit 1
    fi
    if [ -e "$UPDATE" ]; then
        rm "$FILE"
        mv "$UPDATE" "$FILE"
    fi
}

pxe()
{
	MEMORY_CONSTRAINED=TRUE
	FILE=/tmp/lib.squash
	cd /tmp
	if [ -z "$FASTBOOT_URL" ] && [ ${initrd:0:4} == "http" ]; then
		FASTBOOT_URL=${initrd%/*}
	fi
	if [ -n "$FASTBOOT_URL" ]; then
		wget $FASTBOOT_URL/lib.squash 2>&1
	else
                local timeout=15
                while [ -z "$SERVER_IP" ] && [ "$timeout" -ne "0" ]; do
                        sleep .1
                        . /etc/thinstation.global
                        let timeout-=1
                done
		FILE_NET="$squash_server$squash_location"lib.squash
		tftp -g -l $FILE -r $FILE_NET -b $TFTP_BLOCKSIZE $SERVER_IP 2>&1
	fi
}

iso()
{
	timeout=150
        if ! mountpoint /mnt/cdrom0; then
		while [ ! -e /dev/disk/by-label/$CDVOLNAME ] && [ "$timeout" -ne "0" ]; do
			sleep .1
			let timeout-=1
		done
		systemd-mount --fsck=no -o x-mount.mkdir,defaults /dev/disk/by-label/$CDVOLNAME /mnt/cdrom0
	fi
	FILE=$BASE_MOUNT_PATH/cdrom0/"$squash_location"lib.squash
	timeout=150
	while ! /bin/busybox.shared mountpoint $BASE_MOUNT_PATH/cdrom0 && [ $timeout -ne 0 ]; do
		sleep .1
		let timeout-=1
	done
	if [ ! -e $FILE ]; then
		if [ -z $squash_location ]; then
			filename=lib.squash
			if [ -e $BASE_MOUNT_PATH/cdrom0/boot/$filename ]; then
				squash_location="boot/"
			fi
		fi
		if [ -z $squash_location ]; then
			echo "Could not find the image on the CDROM!"
			exit 1
		else
			FILE=$BASE_MOUNT_PATH/cdrom0/"$squash_location"lib.squash
		fi
	fi
}

squash_loc()
{
	eval "`cat /proc/cmdline |cut -d ' ' -f1`"
	initrd="`dirname $BOOT_IMAGE`"/initrd
	initrd=${initrd:=/boot/initrd}
	squash_location=`dirname $initrd |sed -e 's|::/boot|/boot|g'`
	if [ "$squash_location" == "." -o "$squash_location" == "/." ]; then
		unset squash_location
	elif [ "`echo $squash_location |cut -c 1`" == "/" ]; then
		squash_location="`echo $squash_location |cut -c 2-`/"
	elif [ "`echo $squash_location |cut -c 1`" == "." ]; then
		squash_location="`echo $squash_location |cut -c 3-`/"
	else
		squash_location="$squash_location/"
	fi
}

mount_squash()
{
	if [ "$FASTBOOT" == "lotsofmem" ] ; then
		/sbin/unsquashfs -f -d / -f $FILE
		if [ -e /tmp/lib.squash ] ; then
			rm /tmp/lib.squash
		fi
	else
		if is_enabled $MEMORY_CONSTRAINED ; then
			systemd-mount --fsck=no -o ro,loop -t squashfs $FILE /lib64
		else
			if [ ! -e /tmp/lib.squash ] ; then
				cp $FILE /tmp/lib.squash
			fi
			systemd-mount --fsck=no -o ro,loop -t squashfs /tmp/lib.squash /lib64
		fi
	fi
}
# This has to be a double negative test. Only is_disabled() will
# return false with a non-null value that isn't explicitly disabled.
if ! is_disabled $FASTBOOT; then
	export FASTBOOT
	if [ -n "$DEBUG_INIT" ] ; then
		echo_log "Doing Fastboot"
	fi
	squash_loc
	# Check if $boot_device is set
	if [[ -z "$boot_device" ]]; then
		echo "Error: boot_device is not set."
		exit 1
	fi

	# Continue based on the value of $boot_device
	if [[ "$boot_device" == cd* || "$boot_device" == hd96 || "$boot_device" == hd31 ]]; then
		echo "LM=iso" >> /etc/thinstation.runtime
		iso
	elif [[ "$boot_device" == hd* ]]; then
		disk
		echo "LM=hd" >> /etc/thinstation.runtime
	elif [[ "$boot_device" == *tftp* || "$boot_device" == *http* ]]; then
		pxe
		echo "LM=pxe" >> /etc/thinstation.runtime
	else
		echo "boot_device UNKNOWN!"
	fi

	mount_squash
else
	exit 0
fi
