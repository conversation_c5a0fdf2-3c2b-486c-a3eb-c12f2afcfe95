<?xml version="1.0" encoding="UTF-8"?> <!--*-nxml-*-->
<!DOCTYPE policyconfig PUBLIC "-//freedesktop//DTD PolicyKit Policy Configuration 1.0//EN"
        "https://www.freedesktop.org/standards/PolicyKit/1/policyconfig.dtd">

<!-- SPDX-License-Identifier: LGPL-2.1-or-later -->

<policyconfig>

        <vendor>The systemd Project</vendor>
        <vendor_url>https://systemd.io</vendor_url>

        <action id="org.freedesktop.home1.create-home">
                <description gettext-domain="systemd">Create a home area</description>
                <message gettext-domain="systemd">Authentication is required to create a user's home area.</message>
                <defaults>
                        <allow_any>auth_admin_keep</allow_any>
                        <allow_inactive>auth_admin_keep</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
        </action>

        <action id="org.freedesktop.home1.remove-home">
                <description gettext-domain="systemd">Remove a home area</description>
                <message gettext-domain="systemd">Authentication is required to remove a user's home area.</message>
                <defaults>
                        <allow_any>auth_admin_keep</allow_any>
                        <allow_inactive>auth_admin_keep</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
        </action>

        <action id="org.freedesktop.home1.authenticate-home">
                <description gettext-domain="systemd">Check credentials of a home area</description>
                <message gettext-domain="systemd">Authentication is required to check credentials against a user's home area.</message>
                <defaults>
                        <allow_any>auth_admin_keep</allow_any>
                        <allow_inactive>auth_admin_keep</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
        </action>

        <action id="org.freedesktop.home1.update-home">
                <description gettext-domain="systemd">Update a home area</description>
                <message gettext-domain="systemd">Authentication is required to update a user's home area.</message>
                <defaults>
                        <allow_any>auth_admin_keep</allow_any>
                        <allow_inactive>auth_admin_keep</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
        </action>

        <action id="org.freedesktop.home1.update-home-by-owner">
                <description gettext-domain="systemd">Update your home area</description>
                <message gettext-domain="systemd">Authentication is required to update your home area.</message>
                <defaults>
                        <allow_any>auth_admin_keep</allow_any>
                        <allow_inactive>auth_admin_keep</allow_inactive>
                        <allow_active>yes</allow_active>
                </defaults>
        </action>

        <action id="org.freedesktop.home1.resize-home">
                <description gettext-domain="systemd">Resize a home area</description>
                <message gettext-domain="systemd">Authentication is required to resize a user's home area.</message>
                <defaults>
                        <allow_any>auth_admin_keep</allow_any>
                        <allow_inactive>auth_admin_keep</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
        </action>

        <action id="org.freedesktop.home1.passwd-home">
                <description gettext-domain="systemd">Change password of a home area</description>
                <message gettext-domain="systemd">Authentication is required to change the password of a user's home area.</message>
                <defaults>
                        <allow_any>auth_admin_keep</allow_any>
                        <allow_inactive>auth_admin_keep</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
        </action>

        <action id="org.freedesktop.home1.activate-home">
                <description gettext-domain="systemd">Activate a home area</description>
                <message gettext-domain="systemd">Authentication is required to activate a user's home area.</message>
                <defaults>
                        <allow_any>auth_admin_keep</allow_any>
                        <allow_inactive>auth_admin_keep</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
        </action>
</policyconfig>
