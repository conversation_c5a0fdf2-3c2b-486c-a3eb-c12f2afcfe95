# -- NX Options

#NX_NOPASSWORD="ON"      # disables the functionality to save the username and password
                        # in the NX session file. This is handy when an NX terminal has
                        # multiple users and the administrator needs to enforce the keying
                        # in of the NX user credentials before each session. It ensures
                        # that only pre-established users can access the NX server via NX client.
        
#NX_NOEXIT="ON"          # disables all Close buttons in the NX client GUI.  When the user closes
                        # or loses a running session, a new NX client dialog box automatically
                        # appears on the desktop, ready for the next user to start a new session
                        # or for the current user to re-connect to his lost session. This is
                        # welcome where client terminals have multiple users who need to access
                        # their session quickly and easily.
        
#NX_NOCONFIG="ON"        # disables all user possibilities to configure new session setups or
                        # modify existing ones. This allows the administrator to completely lock
                        # down the types of sessions a user can run. This feature works only when
                        # the NX client is started with the --session or --plugin option.

#Anything you can set in ~/.nx/config/<session>.nxs can be put here by the syntax:
#SESSION_#_NX_<group name>_<option name>="<option value>"
#See nomachine.com's NX documentation for details.

#SESSION_#_TYPE=nx
#SESSION_#_TITLE="Gnome"
#SESSION_#_NX_ADVANCED_ENABLE_SSL_ENCRYPTION="true"
#SESSION_#_NX_GENERAL_DESKTOP="Gnome"
#SESSION_#_NX_GENERAL_SERVER_HOST="nx-server-ip"
#SESSION_#_NX_LOGIN_USER="Thinstation"
#SESSION_#_NX_GENERAL_LINK_SPEED="lan"           #Link speed: modem, isdn, adsl (default), wan or lan
#SESSION_#_NX_GENERAL_REMEMBER_PASSWORD="true"
#SESSION_#_NX_GENERAL_RESOLUTION="fullscreen"
#SESSION_#_NX_GENERAL_SESSION="Unix"

#SESSION_#_TYPE=nx
#SESSION_#_TITLE="Firefox"
#SESSION_#_NX_ADVANCED_ENABLE_SSL_ENCRYPTION="true"
#SESSION_#_NX_GENERAL_DESKTOP="Console"
#SESSION_#_NX_GENERAL_SERVER_HOST="nx-server-ip"
#SESSION_#_NX_GENERAL_REMEMBER_PASSWORD="true"
#SESSION_#_NX_GENERAL_COMMAND_LINE="/usr/local/firefox/firefox"
#SESSION_#_NX_GENERAL_SESSION="Unix"
#SESSION_#_NX_GENERAL_ONLY_CONSOLE="false"

#SESSION_#_TYPE=nx
#SESSION_#_TITLE="Windows"
#SESSION_#_NX_ADVANCED_ENABLE_SSL_ENCRYPTION="true"
#SESSION_#_NX_GENERAL_DESKTOP="RDP"
#SESSION_#_NX_GENERAL_RESOLUTION_VALUE="fullscreen"
#SESSION_#_NX_GENERAL_SERVER_HOST="nx-server-ip"
#SESSION_#_NX_GENERAL_SESSION="Windows"
#SESSION_#_NX_WINDOWS_SESSION_SERVER="windows-server-ip"
#SESSION_#_NX_WINDOWS_SESSION_AUTHENTICATION="1"

#SESSION_0_TYPE=nx
#SESSION_0_TITLE="xfce4"
#SESSION_0_NX_ADVANCED_ENABLE_SSL_ENCRYPTION="true"
#SESSION_0_NX_GENERAL_DESKTOP="console"
#SESSION_0_NX_GENERAL_CUSTOM_UNIX_DESKTOP="application"
#SESSION_0_NX_GENERAL_SERVER_HOST="********"
#SESSION_0_NX_GENERAL_REMEMBER_PASSWORD="true"
#SESSION_0_NX_GENERAL_COMMAND_LINE="/usr/bin/startxfce4"
#SESSION_0_NX_GENERAL_SESSION="Unix"
#SESSION_0_NX_GENERAL_ONLY_CONSOLE="false"

