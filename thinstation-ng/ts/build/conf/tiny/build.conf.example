################################################
### ---     ThinStation Build Config     --- ###
################################################
#
#
# This is ThinStation s basic setup file. In this file you decide which
# hardware, programs and features to be included in the ThinStation
# boot image generated by running "./build".
#
# You can customize/limit the possibilities in the thinstation.conf
# files later.
#
# Anything after a "#" is a comment. You activate "things" by removing
# the "#" and remove "things" by putting a "#" in front of them.
#
# First we define which modules to include. Modules are hardware
# drivers. NEVER include more than necessary - the more modules, the
# longer boot time.

########################################################
### --- Machine Modules to include in boot image --- ###
########################################################
# A machine profile is a set of modules for a specific set of hardware
# components like a thinclient with adapters. It does not include any
# filesystem modules, those need to be selected individually. Making a
# a machine profile is a two step process. First you will need to make
# an allmodules build like "./build --allmodules" with the extensions-x
# package included. Once the client machine is booted with that image
# you can open and xterm or ssh in and run "hwlister.sh". hwlister.sh
# will compile some lists and attempt to post them to the pxe server assigned
# to the client. If the pxe server is this workstation and you are hosting
# files directly out of this build env, you can then run
# "./mkmachine SOME-MODEL" and the files will be put into the machine folder
# under that model name. Now all you have to do is add a line to this file
# like "machine SOME-MODEL" and you will no longer have to make
# allmodules builds. If this is not the pxe server, you will have to
# move stuff around on your own, but the most important files are
# modules.list and firmware.list.

################################################
### --- Modules to include in boot image --- ###
################################################
# Any modules may be added as "module" or "module_pkg".
# If defined as a "module", the driver is included in the boot image.
# If defined as a "module_pkg", the driver is added from outside the
# image, such as from a tftp server.  See note below for more details.
#
# Normally you should use "module".  Only use "module_pkg" if you want
# to dynamically load modules.
# Note: If you create a module_pkg network driver, and decide to load it
#       from a tftp server, it won t work.  This is because the image
#       won t have a network driver to get the module_pkg in the first
#       place.

###############################
### --- Machine Profile --- ###
###############################
# Specify the machine profile to load relevant modules for specific hardware.
# Add your new machine profiles here.

#machine NT-A3500
#machine NT-535
#machine Dell-FX170
machine qemu                 # Default profile for QEMU virtual machines
#machine Virtualbox-4.1       # A good set of modules for VirtualBox
#machine VMWare               # A good set of modules for VMWare

###################################
### --- File System Modules --- ###
###################################
# File system modules for handling storage and mounting devices.

module ext4                  # Ext4 file system support
module vfat                  # FAT and VFAT file system support
module isofs                 # ISO9660 file system for CD-ROMs
module udf                   # Universal Disk Format for CD/DVDs
module fuse                  # Filesystem in Userspace
module fuse3                 # Updated FUSE version
#module ntfs                  # NTFS file system support
#module cifs                  # CIFS/SMB file system support
#module autofs4               # Automount file system support

#########################################
### --- Hardware-related Packages --- ###
#########################################
# Essential hardware modules for power, storage, and device management.

#package acpi                 # ACPI power and thermal management
#package lvm2                 # Logical Volume Manager for storage management
#package cpupower             # CPU frequency and power management

####################################
### --- ThinStation Packages --- ###
####################################
#package automount          # Automatically mount storage volumes during boot
#package netfiles           # Package that downloads additional runtime config files during boot.
#package idle-shutdown		# Package that lets you specify automatic shutdown settings and logic.

#####################
### --- Sound --- ###
#####################
# Select sound-related packages.

#package alsa                 # Advanced Linux Sound Architecture
#package pipewire             # Modern sound server
#package pavucontrol          # PulseAudio mixer application
#package pasystray            # Systray volume icon

#####################
### --- Video --- ###
#####################
#package mplayer              # Multimedia Application
#package gstreamer            # Gstreamer framework
#package gst-plugins-base     # GStreamer base plugins for audio and video
#package gst-plugins-good     # GStreamer good plugins for extended media support
#package gst-plugins-bad      # Additional GStreamer plugins
#package gst-plugins-ugly     # Plugins with potential licensing issues
#package gst-libav            # FFmpeg-based GStreamer plugins

########################
### --- Printing --- ###
########################
# Include printing-related packages for network or local printing.

#package cups                  # Common Unix Printing System
#package gutenprint            # High-quality printer drivers
#package hplip                 # HP Linux Imaging and Printing (for HP printers)
#package ghostscript           # Interpreter for PostScript and PDF files
#package system-config-printer # Printer configuration utility

##########################
### --- Networking --- ###
##########################
# Networking-related packages and utilities.

package autonet              # Legacy automatic network configuration
#package networkmanager       # Modern network management service
#package sssd                 # LDAP/AD authentication for a multiuser system
#package bluetooth            # Bluetooth support and applets
#package wireless             # Wireless tools and drivers
#package wpa_supplicant       # WPA/WPA2/802.1X supplicant for Wi-Fi
#package nftables             # Netfilter tables for firewalling
#package avahi                # Service discovery using mDNS/DNS-SD (e.g., local printers and file shares)

###############################
### --- Network Clients --- ###
###############################

#package ssh                  # SSH client
#package wget                 # Command-line HTTP and FTP client
#package curl                 # Command-line tool for transferring data with URLs

#########################
### --- Utilities --- ###
#########################
# Miscellaneous utilities and tools.

#package e2fsprogs            # Filesystem utilities for ext2/3/4
#package parted               # Command-line partition manager
#package gparted              # Graphical partition manager
#package git                  # Version control system
#package nano                 # Lightweight text editor
#package sudo                 # Privileged command execution
#package yad                  # Yet Another Dialog - GTK-based dialog boxes
#package extensions           # Extra system utilities
#package extensions-x         # Extra X11 utilities and tools
#package jq                   # Lightweight JSON processor
#package file-roller          # Archive manager for compressed files
#package nmap                 # Network analysis tool

############################
### --- Applications --- ###
############################
# End-user applications and software.

#package chrome               # Google Chrome browser
#package firefox              # Mozilla Firefox browser
#package filezilla            # FileZilla FTP client
#package meld                 # Visual diff and merge tool
#package geany                # Lightweight IDE

###########################################
### --- Remote Desktop Applications --- ###
###########################################
#package freerdp              # RDP client
#package ica                  # Citrix ICA, this one might need a bit for work for ctxusb

##############################
### --- Virtualization --- ###
##############################
# Virtualization tools and utilities.

#package open-vm-tools        # VMware guest tools
#package vboxguest            # VirtualBox guest additions

#######################
### --- Locales --- ###
#######################
# Localization files for keyboard, fonts, and translations.

#param fulllocales    true    # Generate extra locales for each language. Usually not needed.

package locale-en_US         # English (US)

#############################
### --- Date and Time --- ###
#############################
#package alltimezones         # Include all zoneinfo files instead of only what is defined in .buildtime
#package tzupdate             # Automatically update the timezone based on network location
#package ntpd                 # Syncronize time with NTP protocal

#######################
### --- Servers --- ###
#######################
# Server services and tools for hosting and networking.

#package lighttpd             # Lightweight web server
#package tftpd                # TFTP server for PXE booting
#package dnsmasq              # DNS and DHCP server
#package sshd                 # Secure shell server for remote login

####################
### --- Xorg --- ###
####################
# Include Xorg and related video drivers.

#package xorg7                # X.Org server and core utilities with KMS drivers
#package xorg7-amdgpu         # AMD/ATI open-source GPU drivers
#package xorg7-intel          # Intel open-source GPU drivers
#package xorg7-nouveau        # NVIDIA open-source GPU drivers
#package xorg7-qxl            # QXL drivers for QEMU/SPICE virtual machines
#package xorg7-vmware         # VMware GPU drivers

# Optional proprietary drivers
# package xorg7_nvidia       # NVIDIA proprietary driver
# package xorg7_fglrx        # AMD proprietary driver

###############################
### --- Window Managers --- ###
###############################
# Choose only one window manager.

#package xfwm4                # XFCE window manager
#package xfce4-power-manager  # Power management for XFCE

# Display Manager
#package lightdm              # Lightweight display manager
#package light-locker         # Session locker for LightDM

# Utilities
#package terminal             # Terminal emulator for XFCE
#package thunar               # File manager for XFCE

########################################
### --- Fonts, Icons, and Themes --- ###
########################################
# Packages for font rendering, icon sets, and GTK themes.

#package fonts-TTF-liberation # Liberation TrueType fonts
#package fonts-TTF-noto       # Google Noto fonts for wide Unicode support

#package icons-cursor         # Cursor theme
#package icons-gnome          # GNOME icon set
#package icons-hicolor        # Hicolor icon theme
#package icons-adwaita        # Adwaita icon theme

#package gtk-theme-adwaita    # Adwaita GTK theme

######################################
### --- Build Control Settings --- ###
######################################
# Adjust parameters for fine-tuning the build process.

param desktop        file:./backgrounds/Hive_Lite.jpg # Custom image to load as desktop background

param grubtheme		"default"

#param tsuser         tsuser
param tsuserpasswd   pleasechangeme
param rootpasswd     pleasechangeme
#param xorgvncpasswd  pleasechangeme			# VNC Access Password
#param storagepasswd  pleasechangeme			# Password for storage server
#param sambapasswd    pleasechangeme			# Password for samba shares when using user mode security

param defaultconfig  thinstation.conf.buildtime
param baseurl        https://www.thinstation.net
param basename       thinstation                # Used for all config/tftp/scp file names
param basepath       ts7.2                      # Used to determine path to tftp/scp files
#param keyfile        ./id_rsa       			# Path for private key file used for ssh/scp
#param knownhosts     ./known_hosts  			# Path for ssh known_hosts file for ssh/scp

#param localpkgs      false                     # to determine if PKG files are to be loaded locally
param hardlinkfs     true                       # When enabled, duplicate files will be hardlinked to save space
param sametimestmp   true                       # When enabled, the timestamps for all files and folders will be set to 00:00 of todays date in your timezone
#param stripelf       yes                       # Strip extraneous information from elf binaries (Maybe not Safe)

param haltonerror    false
param bootverbosity  3                      # Increased loggin when booting TS
                                			#   0 no logging messages
                                			#   1 boot
                                			#   2 network
                                			#   4 init
                                			#   8 kernel
                                			#  16 modules
                                			#  32 packages
                                			#  64 email bootlog file to SMTP server & user set in
                                			#     thinstation.conf file.  This will only work
                                			#     if networking is working.
                                			#
                                			#  Combinations can be used (e.g.12 does Kernel and Module Messages)

param fastboot       lotsofmem              # Mangles the filesystem a special way as to improve boot speed and reduce
                                            # memory utilization. Cool/Dangerous . Harder to dubug other packages. (Finishing Touch)
                                            # Set to 'true' for memory constrained systems or 'lotsofmem' for anything modern.
param squashopt      "-comp xz"             # Settings to be passed to mksquashfs for fastboot
param initrdcmd      "xz"                   # Can be any compression utility and settings, such as gzip, xz, bzip2 or lzo4

# Kernel command-line options
#param bootlogo       true                   # Enable or Disable the use of the Boot splash.
#param bootresolution 2560x1440              # Override the kernel detected video resolution with this value. Not usually needed.
#param acpisupport    disable                # Tells the kernel not to load acpi modules. (Breaks some Intel Chipsets if disabled)
param kernelcmdline "selinux=0"             # Just disable selinux, but show all the kernel and boot messages
#param kernelcmdline "selinux=0 vt.global_cursor_default=0 loglevel=3 udev.log_priority=3 systemd.show_status=false rd.systemd.show_status=false quiet" # A very quiet kernel command line

# Additional modules and settings
param allfirmware    true
param blacklist      "snd-pcsp.ko pcspkr.ko"
#param earlymicrocode	true

#!!Advanced
param downloads         /downloads         # Leave this alone unless you can t download (wget required on your Linux box)
#param bootprefix       "test/"             # Add a prefix directory to /boot for PXE based launch, used to allow multiple builds on same PXE server
                                            # Value will be inserted as follows: /${bootprefix}boot
                                            # Note: This is incompatible with non-network grub based boot images
