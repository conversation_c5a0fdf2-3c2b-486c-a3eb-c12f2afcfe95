# Originally generated from system-config-keyboard's model list.
# consolelayout		xlayout	xmodel		xvariant	xoptions
sg			ch	pc105		de_nodeadkeys	terminate:ctrl_alt_bksp
nl			nl	pc105		-		terminate:ctrl_alt_bksp
mk-utf			mk,us	pc105		-		terminate:ctrl_alt_bksp,grp:shifts_toggle,grp_led:scroll
trq			tr	pc105		-		terminate:ctrl_alt_bksp
uk			gb	pc105		-		terminate:ctrl_alt_bksp
is-latin1		is	pc105		-		terminate:ctrl_alt_bksp
de			de	pc105		-		terminate:ctrl_alt_bksp
la-latin1		latam	pc105		-		terminate:ctrl_alt_bksp
us			us	pc105+inet	-		terminate:ctrl_alt_bksp
ko			kr	pc105		-		terminate:ctrl_alt_bksp
ro-std			ro	pc105		std		terminate:ctrl_alt_bksp
de-latin1		de	pc105		-		terminate:ctrl_alt_bksp
slovene			si	pc105		-		terminate:ctrl_alt_bksp
hu			hu	pc105		-		terminate:ctrl_alt_bksp
jp106			jp	jp106		-		terminate:ctrl_alt_bksp
croat			hr	pc105		-		terminate:ctrl_alt_bksp
it2			it	pc105		-		terminate:ctrl_alt_bksp
hu101			hu	pc105		qwerty		terminate:ctrl_alt_bksp
sr-latin		rs	pc105		latin		terminate:ctrl_alt_bksp
fi			fi	pc105		-		terminate:ctrl_alt_bksp
fr_CH			ch	pc105		fr		terminate:ctrl_alt_bksp
dk-latin1		dk	pc105		-		terminate:ctrl_alt_bksp
fr			fr	pc105		-		terminate:ctrl_alt_bksp
it			it	pc105		-		terminate:ctrl_alt_bksp
ua-utf			ua,us	pc105		-		terminate:ctrl_alt_bksp,grp:shifts_toggle,grp_led:scroll
fr-latin1		fr	pc105		-		terminate:ctrl_alt_bksp
sg-latin1		ch	pc105		de_nodeadkeys	terminate:ctrl_alt_bksp
be-latin1		be	pc105		-		terminate:ctrl_alt_bksp
dk			dk	pc105		-		terminate:ctrl_alt_bksp
fr-pc			fr	pc105		-		terminate:ctrl_alt_bksp
bg_pho-utf8		bg,us	pc105		,phonetic	terminate:ctrl_alt_bksp,grp:shifts_toggle,grp_led:scroll
it-ibm			it	pc105		-		terminate:ctrl_alt_bksp
cz-us-qwertz		cz,us	pc105		-		terminate:ctrl_alt_bksp,grp:shifts_toggle,grp_led:scroll
cz-qwerty		cz,us	pc105		qwerty,		terminate:ctrl_alt_bksp,grp:shifts_toggle,grp_led:scroll
br-abnt2		br	abnt2		-		terminate:ctrl_alt_bksp
ro			ro	pc105		-		terminate:ctrl_alt_bksp
us-acentos		us	pc105		intl		terminate:ctrl_alt_bksp
pt-latin1		pt	pc105		-		terminate:ctrl_alt_bksp
ro-std-cedilla		ro	pc105		std_cedilla	terminate:ctrl_alt_bksp
tj_alt-UTF8		tj	pc105		-		terminate:ctrl_alt_bksp
de-latin1-nodeadkeys	de	pc105		nodeadkeys	terminate:ctrl_alt_bksp
no			no	pc105		-		terminate:ctrl_alt_bksp
bg_bds-utf8		bg,us	pc105		-		terminate:ctrl_alt_bksp,grp:shifts_toggle,grp_led:scroll
dvorak			us	pc105		dvorak		terminate:ctrl_alt_bksp
dvorak			us	pc105		dvorak-alt-intl	terminate:ctrl_alt_bksp
ru			ru,us	pc105		-		terminate:ctrl_alt_bksp,grp:shifts_toggle,grp_led:scroll
cz-lat2			cz	pc105		qwerty		terminate:ctrl_alt_bksp
pl2			pl	pc105		-		terminate:ctrl_alt_bksp
es			es	pc105		-		terminate:ctrl_alt_bksp
ro-cedilla		ro	pc105		cedilla		terminate:ctrl_alt_bksp
ie			ie	pc105		-		terminate:ctrl_alt_bksp
et			ee	pc105		-		terminate:ctrl_alt_bksp
sk-qwerty		sk	pc105		qwerty		terminate:ctrl_alt_bksp
sk-qwertz		sk	pc105		-		terminate:ctrl_alt_bksp
fr-latin9		fr	pc105		latin9		terminate:ctrl_alt_bksp
fr_CH-latin1		ch	pc105		fr		terminate:ctrl_alt_bksp
cf			ca	pc105		-		terminate:ctrl_alt_bksp
sv-latin1		se	pc105		-		terminate:ctrl_alt_bksp
sr-cy			rs	pc105		-		terminate:ctrl_alt_bksp
gr			gr,us	pc105		-		terminate:ctrl_alt_bksp,grp:shifts_toggle,grp_led:scroll
by			by,us	pc105		-		terminate:ctrl_alt_bksp,grp:shifts_toggle,grp_led:scroll
il			il	pc105		-		terminate:ctrl_alt_bksp
kazakh			kz,us	pc105		-		terminate:ctrl_alt_bksp,grp:shifts_toggle,grp_led:scroll
lt.baltic		lt	pc105		-		terminate:ctrl_alt_bksp
lt.l4			lt	pc105		-		terminate:ctrl_alt_bksp
lt			lt	pc105		-		terminate:ctrl_alt_bksp
khmer			kh,us	pc105		-		terminate:ctrl_alt_bksp
es-dvorak		es	microsoftpro	dvorak		terminate:ctrl_alt_bksp
lv			lv	pc105		apostrophe	terminate:ctrl_alt_bksp
lv-tilde		lv	pc105		tilde		terminate:ctrl_alt_bksp
ge			ge,us	pc105		-		terminate:ctrl_alt_bksp
