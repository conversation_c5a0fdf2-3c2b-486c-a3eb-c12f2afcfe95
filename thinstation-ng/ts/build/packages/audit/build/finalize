#audit 80
/usr/lib/systemd/systemd-update-helper install-system-units audit-rules.service || : 

install -m 0640 -o 0 -g 0 -p /usr/share/audit-rules/10-base-config.rules /etc/audit/rules.d/10-base-config.rules
install -m 0640 -o 0 -g 0 -p /usr/share/audit-rules/30-stig.rules /etc/audit/rules.d/30-stig.rules
install -m 0640 -o 0 -g 0 -p /usr/share/audit-rules/99-finalize.rules /etc/audit/rules.d/99-finalize.rules
if [ ! -d /etc/NetworkManager ]; then
	sed -i -e '/NetworkManager/d' /etc/audit/rules.d/30-stig.rules
fi
if [ ! -d /etc/sudoers.d ]; then
	sed -i -e '/sudoers/d' /etc/audit/rules.d/30-stig.rules
fi
if [ ! -d /etc/selinux ]; then
	sed -i -e '/selinux/d' /etc/audit/rules.d/30-stig.rules
fi

find /bin -type f -perm -04000 2>/dev/null | awk '{ printf "-a always,exit -F arch=b64 -F path=%s -F perm=x -F auid>=1000 -F auid!=unset -F key=privileged\n", $1 }' > /etc/audit/rules.d/31-privileged.rules
find /sbin -type f -perm -04000 2>/dev/null | awk '{ printf "-a always,exit -F arch=b64 -F path=%s -F perm=x -F auid>=1000 -F auid!=unset -F key=privileged\n", $1 }' >> /etc/audit/rules.d/31-privileged.rules
filecap /bin 2>/dev/null | sed '1d' | awk '{ printf "-a always,exit -F path=%s -F arch=b64 -F perm=x -F auid>=1000 -F auid!=unset -F key=privileged\n", $2 }' >> /etc/audit/rules.d/31-privileged.rules
filecap /sbin 2>/dev/null | sed '1d' | awk '{ printf "-a always,exit -F path=%s -F arch=b64 -F perm=x -F auid>=1000 -F auid!=unset -F key=privileged\n", $2 }' >> /etc/audit/rules.d/31-privileged.rules

find /bin -type f -perm -04000 2>/dev/null | awk '{ printf "-a always,exit -F arch=b32 -F path=%s -F perm=x -F auid>=1000 -F auid!=unset -F key=privileged\n", $1 }' >> /etc/audit/rules.d/31-privileged.rules
find /sbin -type f -perm -04000 2>/dev/null | awk '{ printf "-a always,exit -F arch=b32 -F path=%s -F perm=x -F auid>=1000 -F auid!=unset -F key=privileged\n", $1 }' >> /etc/audit/rules.d/31-privileged.rules
filecap /bin 2>/dev/null | sed '1d' | awk '{ printf "-a always,exit -F path=%s -F arch=b32 -F perm=x -F auid>=1000 -F auid!=unset -F key=privileged\n", $2 }' >> /etc/audit/rules.d/31-privileged.rules
filecap /sbin 2>/dev/null | sed '1d' | awk '{ printf "-a always,exit -F path=%s -F arch=b32 -F perm=x -F auid>=1000 -F auid!=unset -F key=privileged\n", $2 }' >> /etc/audit/rules.d/31-privileged.rules

# Copy default rules into place on new installation
files=`ls /etc/audit/rules.d/ 2>/dev/null | wc -w`
if [ "$files" -eq 0 ] ; then
    echo "No rules detected, adding default"
    # FESCO asked for audit to be off by default. #1117953
    if [ -e /usr/share/audit-rules/10-no-audit.rules ] ; then
        install -m 0640 -o 0 -g 0 -p /usr/share/audit-rules/10-no-audit.rules /etc/audit/rules.d/audit.rules
    else
        install -m 0640 -o 0 -g 0 /dev/null /etc/audit/rules.d/audit.rules
    fi
fi
/usr/lib/systemd/systemd-update-helper install-system-units auditd.service
sed -i -e 's|/sbin/auditctl|/bin/auditctl|g' /bin/augenrules
