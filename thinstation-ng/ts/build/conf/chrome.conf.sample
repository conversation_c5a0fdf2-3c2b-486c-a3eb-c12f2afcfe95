
# SESSION_#_TYPE=chrome									Sets the session type to chrome
# SESSION_#_CHROME_HOMEPAGE=http://thinstation.org		Sets the homepage the chrome session will open on to a given url
# CHROME_ZOOM=1.5										Sets the zoom for the pages in a chrome session. 
#														1 = 100% (normal zoom), 0.5 = 50%, 1.5 = 150%
#
# NETWORK_REQUIRED=true									Makes the session require connecting to a network. 
#														After a certain amount of time if no network is detected, 
#														a message will pop up saying so and the session will be 
#														killed (unless a no-session action is specified)
#
# GLOBAL FUNCTIONS:										
# These refer to the Chrome package's default behaviors. 
# They can be found at /build/packages/chrome/etc/cmd
# **To change them, you must edit those files directly.
#
# Chrome will start in fullscreen.
# Chrome will start in kiosk mode, with the window size being
# the size of the display
# The window will be named chrome by default.
