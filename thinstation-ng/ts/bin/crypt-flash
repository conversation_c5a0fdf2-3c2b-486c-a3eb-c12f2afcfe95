#!/bin/bash

USER_TOKEN="useruser"
ADMIN_TOKEN="adminadmin" # Comment out to only have a user token

if [ -z "$1" ]; then
	rm -f /thinstation.img
	truncate -s 12G /thinstation.img

	loop=`losetup -f`
	losetup -P $loop /thinstation.img

	# Define the disk to use
	DISK="$loop"
else
	DISK="$1"
fi

if echo $DISK |grep -q -E 'nvme|loop'; then p=p; else unset p; fi

# Unmount disk if it's currently mounted (be very cautious with this command)
umount ${DISK}* 2>/dev/null

# Create new partition table
parted $DISK --script mklabel gpt

# Create a 2GB FAT partition for boot
parted $DISK --script mkpart primary fat32 1MiB 2GiB
mkfs.vfat -F 32 ${DISK}${p}1 -n boot

# Create a LUKS partition that takes the rest of the disk from 2GiB onward
parted $DISK --script mkpart primary 2GiB 100%
parted $DISK --script name 2 SafeStore  # Set the PARTLABEL to 'SafeStore'

# Setup LUKS encryption on the new partition
echo -n "${USER_TOKEN}" | cryptsetup luksFormat -q --force-password ${DISK}${p}2 -
cryptsetup token import --token-id 0 /dev/loop0p2 --json-file /ts/etc/user_token.json

if [ -n "${ADMIN_TOKEN}" ]; then
	echo -en "${USER_TOKEN}\n${ADMIN_TOKEN}" |cryptsetup luksAddKey -q --force-password ${DISK}${p}2 --key-slot 1 -
	cryptsetup token import --token-id 1 /dev/loop0p2 --json-file /ts/etc/admin_token.json
fi

# Open the encrypted LUKS partition
echo -n "${USER_TOKEN}" | cryptsetup open ${DISK}${p}2 cryptdisk -

# Set up LVM on the encrypted partition
pvcreate /dev/mapper/cryptdisk
vgcreate vg_crypt /dev/mapper/cryptdisk

# Create logical volumes within the encrypted volume group
lvcreate -L 1G -n lv_prstnt vg_crypt  # 1GB for the first volume
lvcreate -L 2G -n lv_log vg_crypt  # 2GB for a log volume, adjust size as needed
lvcreate -L 1G -n lv_root vg_crypt
lvcreate -L 4G -n lv_home vg_crypt
lvcreate -l 100%FREE -n lv_tsdev vg_crypt

# Create filesystems on the logical volumes
mkfs.ext4 /dev/vg_crypt/lv_prstnt
mkfs.ext4 /dev/vg_crypt/lv_log
mkfs.ext4 /dev/vg_crypt/lv_root
mkfs.ext4 /dev/vg_crypt/lv_home
mkfs.ext4 /dev/vg_crypt/lv_tsdev

# Label the filesystems
e2label /dev/vg_crypt/lv_prstnt prstnt
e2label /dev/vg_crypt/lv_log log
e2label /dev/vg_crypt/lv_root root
e2label /dev/vg_crypt/lv_home home
e2label /dev/vg_crypt/lv_tsdev tsdev

# Inform LVM that we are about to close the volume group
lvchange -an /dev/vg_crypt/lv_prstnt
lvchange -an /dev/vg_crypt/lv_log
lvchange -an /dev/vg_crypt/lv_root
lvchange -an /dev/vg_crypt/lv_home
lvchange -an /dev/vg_crypt/lv_tsdev

# Close the LUKS partition
cryptsetup close cryptdisk

mount $DISK${p}1 /mnt
cp -a /build/boot-images/grub/efi-source/* /mnt
umount /mnt

losetup -d $loop

# Configure crypttab using PARTLABEL
#echo 'cryptdisk PARTLABEL=SafeStore none luks,discard' | tee /etc/crypttab

echo "Partitioning, LUKS, and LVM setup complete."
