#!/bin/bash

export NMAP_TIMEOUT=15
export CONFIG_FILE="/root/dnsmasq.conf"
export DNSMASQ_CONFIG_FILE=/etc/dnsmasq.conf


wan_interfaces=()
lan_interfaces=()

# Define functions

gen_text_widget()
{
	cat <<EOF
		<text use-markup="true" justify="2" space-expand="true" space-fill="true" wrap="true" width-chars="40">
			<label>"$1"</label>
		</text>
EOF
}

x_echo()
{
	title="$1"
	shift

	while [ -n "$1" ] ; do
		local text_widget="$text_widget `gen_text_widget \"$1\"`"
		shift
	done

	export MAIN_DIALOG='
	<window title="'$title'" decorated="true" resizable="false" image-name="/lib/pixmaps/ThinStation_32x32.xpm">
		<vbox>
			<hseparator></hseparator>
			<hbox>
				<pixmap icon_size="6">
					<input file stock="gtk-dialog-info"></input>
				</pixmap>
				<vbox>
					'$text_widget'
				</vbox>
			</hbox>
			<hseparator></hseparator>
			<hbox>
				<button has-focus="true">
					<label>"Exit"</label>
					<action>EXIT:ok</action>
				</button>
			</hbox>
		</vbox>
	</window>'

	result=`$GTKDIALOG`
}

nmap_progress()
{
	let chunksize="100 / $NMAP_TIMEOUT"
	progress=".    "
	while kill -0 $nmap_pid 2>/dev/null; do
		for i in $(seq 0 +$chunksize 99); do
			if kill -0 $nmap_pid 2>/dev/null; then
				echo $i
				echo "Scanning$progress"
				if      [ "$progress" == ".    " ]; then progress="..   "
				elif    [ "$progress" == "..   " ]; then progress="...  "
				elif    [ "$progress" == "...  " ]; then progress=".... "
				elif    [ "$progress" == ".... " ]; then progress="....."
				elif    [ "$progress" == "....." ]; then progress=".    "
				fi
				sleep 1
			fi
		done
	done
	echo 100 Done
}

look_for_dhcp_server()
{
	export -f nmap_progress
	export BAR_DIALOG='
<window title="DHCP Scan Progress" decorated="false" width-request="325" height-request="95" allow-grow="false" allow-shrink="false" skip_taskbar_hint="true">
	<vbox>
		<text>
			<label>Scanning for DHCP Servers on '$1'</label>
		</text>
		<vseparator height-request="5"></vseparator>
		<progressbar>
			<variable>PROGRESS_BAR</variable>
			<label>Scanning</label>
			<input>bash -c nmap_progress</input>
			<action function="refresh">ENTRY</action>
			<action function="closewindow">BAR_DIALOG</action>
			<action>echo ready</action>
		</progressbar>
	</vbox>
	<variable>BAR_DIALOG</variable>
</window>
'
	[ -z $GTKDIALOG ] && GTKDIALOG=gtkdialog
	local output_file="/tmp/nmap_dhcp_scan_$1.txt"
	nmap -e "$1" --script broadcast-dhcp-discover --script-timeout ${NMAP_TIMEOUT}s > "$output_file" 2>&1 &
	nmap_pid=$!
	export nmap_pid
	$GTKDIALOG -c --program=BAR_DIALOG

	if grep -q "Server Identifier" "$output_file"; then
		wan_interfaces+=("$1")
	else
		# Allow for the possibility of a statically configured IP on the WAN
		# This will put the adapter in both lists
		if  [ -n "$(ip addr show $1 |grep -e "inet " |awk '{print $2}')" ]; then
			wan_interfaces+=("$1")
		fi
		lan_interfaces+=("$1")
	fi

	# Cleanup
	rm "$output_file"
}

scan_dhcp_servers() {
	# This function scans the /sys/class/net directory for network interfaces
	# that have a carrier (link)

	for interface in /sys/class/net/*; do
		# Extract the interface name from the path
		iface=$(basename "$interface")

		# Avoid the loopback interface
		if [[ "$iface" == "lo" ]]; then
			continue
		elif [[ "$(cat "$interface"/carrier)" == "1" ]]; then
			look_for_dhcp_server "$iface"
		fi
	done
}

read_config() {
	if [[ -f "$CONFIG_FILE" ]]; then
		source "$CONFIG_FILE"
	else
		DHCP_START_1="192"
		DHCP_START_2="168"
		DHCP_START_3="1"
		DHCP_START_4="50"

		DHCP_END_1="192"
		DHCP_END_2="168"
		DHCP_END_3="1"
		DHCP_END_4="150"

		BIOS_FILE="boot/grub2/pxeboot.img"
		EFI_FILE="EFI/BOOT/grubx64.efi"

		LAN_IP_1="192"
		LAN_IP_2="168"
		LAN_IP_3="1"
		LAN_IP_4="1"

		LAN_NETMASK="24"

		LAN_INTERFACE=""
		WAN_INTERFACE=""
	fi
}

save_config() {
	echo "Saving configuration..."
	cat <<EOF > $CONFIG_FILE
DHCP_START_1="$DHCP_START_1"
DHCP_START_2="$DHCP_START_2"
DHCP_START_3="$DHCP_START_3"
DHCP_START_4="$DHCP_START_4"

DHCP_END_1="$DHCP_END_1"
DHCP_END_2="$DHCP_END_2"
DHCP_END_3="$DHCP_END_3"
DHCP_END_4="$DHCP_END_4"

BIOS_FILE="$BIOS_FILE"
EFI_FILE="$EFI_FILE"

LAN_IP_1="$LAN_IP_1"
LAN_IP_2="$LAN_IP_2"
LAN_IP_3="$LAN_IP_3"
LAN_IP_4="$LAN_IP_4"

LAN_NETMASK="$LAN_NETMASK"

LAN_INTERFACE="$LAN_INTERFACE"
WAN_INTERFACE="$WAN_INTERFACE"
EOF
	if [ "$?" == "0" ]; then
		echo "Saved succesfully"
	fi
}

enable_dnsmasq() {
	# Writing dnsmasq configuration
	echo "Writing dnsmasq configuration to $CONFIG_FILE..."
	cat << EOF > $DNSMASQ_CONFIG_FILE
interface=$LAN_INTERFACE
bind-interfaces
dhcp-range=$DHCP_START_1.$DHCP_START_2.$DHCP_START_3.$DHCP_START_4,$DHCP_END_1.$DHCP_END_2.$DHCP_END_3.$DHCP_END_4,12h
dhcp-option=option:router,${LAN_IP_1}.${LAN_IP_2}.${LAN_IP_3}.${LAN_IP_4}
dhcp-option=66,${LAN_IP_1}.${LAN_IP_2}.${LAN_IP_3}.${LAN_IP_4}
dhcp-match=set:bios-x86,option:client-arch,0
dhcp-match=set:bios-x86-http,option:client-arch,14
dhcp-match=set:efi-x86_64,option:client-arch,7
dhcp-match=set:efi-x86_64,option:client-arch,9
dhcp-match=set:efi-x86_64-http,option:client-arch,10
dhcp-option=tag:bios-x86,option:bootfile-name,$BIOS_FILE
dhcp-option=tag:bios-x86-http,option:bootfile-name,http://${LAN_IP_1}.${LAN_IP_2}.${LAN_IP_3}.${LAN_IP_4}/$BIOS_FILE
dhcp-option=tag:efi-x86_64,option:bootfile-name,$EFI_FILE
dhcp-option=tag:efi-x86_64-http,option:bootfile-name,http://${LAN_IP_1}.${LAN_IP_2}.${LAN_IP_3}.${LAN_IP_4}/$EFI_FILE
EOF

	# Configure the network interface via nmcli
	echo "Configuring the network interface..."
	UUID=`nmcli con show |grep -e $LAN_INTERFACE |cut -c 21-56`
	if [ -n "$UUID" ]; then
		for interface in $UUID; do
			nmcli con down $interface
			nmcli con del $interface
		done
	fi
	nmcli con add type ethernet con-name $LAN_INTERFACE \
		ifname $LAN_INTERFACE \
		ipv4.addresses ${LAN_IP_1}.${LAN_IP_2}.${LAN_IP_3}.${LAN_IP_4}/$LAN_NETMASK \
		ipv4.gateway ${LAN_IP_1}.${LAN_IP_2}.${LAN_IP_3}.${LAN_IP_4} \
		ipv4.method manual
        nmcli con up "$LAN_INTERFACE"

	WAN_UUID=`nmcli con show |grep -e $WAN_INTERFACE |cut -c 21-56`
	nmcli connection modify "$WAN_UUID" connection.id "$WAN_INTERFACE"
	nmcli connection modify $LAN_INTERFACE connection.zone internal
	nmcli connection modify $WAN_INTERFACE connection.zone external
	nmcli connection reload

        find /etc/NetworkManager/system-connections -type f -empty -exec rm {} \;

	firewall-cmd --permanent --zone=external --add-masquerade
	firewall-cmd --permanent --zone=internal --set-target=ACCEPT
	firewall-cmd --permanent --zone=internal --add-service=http --add-service=tftp --add-service=dns --add-service=dhcp --add-service=ipp --add-service=ipp-client
	firewall-cmd --reload

	# Start dnsmasq
	systemctl start dnsmasq
}

disable_dnsmasq()
{
	systemctl stop dnsmasq
	rm $DNSMASQ_CONFIG_FILE

	nmcli con down $LAN_INTERFACE
	nmcli connection modify $LAN_INTERFACE connection.zone public
        nmcli connection modify $WAN_INTERFACE connection.zone public
	nmcli connection reload

	find /etc/NetworkManager/system-connections -type f -empty -exec rm {} \;

        firewall-cmd --permanent --zone=external --remove-masquerade
        firewall-cmd --permanent --zone=internal --set-target=DROP
        firewall-cmd --reload
}

status_dnsmasq()
{
	echo "DNSMasq: $(systemctl status dnsmasq |grep -e Active|awk '{print $2}')"
}

set_main_dialog()
{
	# Create and display the GUI's
	echo "Creating GUI..."
	export dnsmasq_dialog='
<window title="DNSMasq Configuration" window-position="1" width_request="345" image-name="/lib/pixmaps/ThinStation_32x32.xpm">
	<vbox>
		 <frame DHCP Settings>
			<vbox>
				<hbox>
					<text><label>DHCP Start IP:</label></text>
					<entry space_expand="false" width_chars="3" text="'$DHCP_START_1'"><variable>DHCP_START_1</variable></entry>
					<entry space_expand="false" width_chars="3" text="'$DHCP_START_2'"><variable>DHCP_START_2</variable></entry>
					<entry space_expand="false" width_chars="3" text="'$DHCP_START_3'"><variable>DHCP_START_3</variable></entry>
					<entry space_expand="false" width_chars="3" text="'$DHCP_START_4'"><variable>DHCP_START_4</variable></entry>
				</hbox>
				<hbox>
					<text><label>DHCP End IP:</label></text>
					<entry space_expand="false" width_chars="3" text="'$DHCP_END_1'"><variable>DHCP_END_1</variable></entry>
					<entry space_expand="false" width_chars="3" text="'$DHCP_END_2'"><variable>DHCP_END_2</variable></entry>
					<entry space_expand="false" width_chars="3" text="'$DHCP_END_3'"><variable>DHCP_END_3</variable></entry>
					<entry space_expand="false" width_chars="3" text="'$DHCP_END_4'"><variable>DHCP_END_4</variable></entry>
				</hbox>
				<hbox>
					<text><label>BIOS boot file:</label></text>
                                        <entry space_expand="false" width_chars="25" text="'$BIOS_FILE'"><variable>BIOS_FILE</variable></entry>
				</hbox>
                                <hbox>
                                        <text><label>EFI boot file:</label></text>
                                        <entry space_expand="false" width_chars="25" text="'$EFI_FILE'"><variable>EFI_FILE</variable></entry>
                                </hbox>
			</vbox>
		</frame>
		<frame Network Adapters>
			<hbox>
				<text><label>WAN Interface:</label></text>
				<comboboxtext><variable>WAN_INTERFACE</variable>
					'$(for adapter in "${wan_interfaces[@]}"; do echo "<item>$adapter</item>"; done)'
					'$(for adapter in "${wan_interfaces[@]}"; do if [[ "$adapter" == "$WAN_INTERFACE" ]]; then echo "<default>$adapter</default>"; fi; done)'
				</comboboxtext>
			</hbox>
			<hbox>
				<text><label>LAN Interface:</label></text>
				<comboboxtext><variable>LAN_INTERFACE</variable>
					'$(for adapter in "${lan_interfaces[@]}"; do echo "<item>$adapter</item>"; done)'
					'$(for adapter in "${lan_interfaces[@]}"; do if [[ "$adapter" == "$LAN_INTERFACE" ]]; then echo "<default>$adapter</default>"; fi; done)'
				</comboboxtext>
			</hbox>
			<vbox>
				<hbox>
					<text><label>LAN IP:</label></text>
					<entry space_expand="false" width_chars="3" text="'$LAN_IP_1'"><variable>LAN_IP_1</variable></entry>
					<entry space_expand="false" width_chars="3" text="'$LAN_IP_2'"><variable>LAN_IP_2</variable></entry>
					<entry space_expand="false" width_chars="3" text="'$LAN_IP_3'"><variable>LAN_IP_3</variable></entry>
					<entry space_expand="false" width_chars="3" text="'$LAN_IP_4'"><variable>LAN_IP_4</variable></entry>
					<text><label>/</label></text>
					<comboboxtext space_expand="false" width_chars="2"><variable>LAN_NETMASK</variable>
						'$(for cidr in `seq 1 32`; do echo "<item>$cidr</item>"; done)'
						<default>'$LAN_NETMASK'</default>
					</comboboxtext>
				</hbox>
			</vbox>
		</frame>
		<hbox>
			<button>
				<label>Enable</label>
				<action>bash -c "save_config"</action>
				<action>bash -c "enable_dnsmasq"</action>
				<action function="Refresh">dnsmasq_status</action>
			</button>
			<button>
				<label>Disable</label>
				<action>bash -c "disable_dnsmasq"</action>
				<action function="Refresh">dnsmasq_status</action>
			</button>
		</hbox>
		<statusbar has-resize-grip="false">
			<label>DNSMasq Status</label>
			<default>'$status'</default>
			<variable>dnsmasq_status</variable>
			<input>bash -c status_dnsmasq</input>
			<sensitive>true</sensitive>
		</statusbar>
	</vbox>
</window>
'
}

export -f save_config enable_dnsmasq disable_dnsmasq status_dnsmasq

# Main execution
read_config
status=$(bash -c status_dnsmasq)
if [ "$status" != "DNSMasq: active" ] || [ -z "$LAN_INTERFACE" ] || [ -z "$WAN_INTERFACE" ]; then
	scan_dhcp_servers
else
	lan_interfaces[0]=$LAN_INTERFACE
	wan_interfaces[0]=$WAN_INTERFACE
fi
if [ -z "${wan_interfaces[@]}" ]; then
        x_echo "No WAN interface" "No suitable interface was found that could be used to route traffic. Maybe wait for connections to settle."
        exit 1
fi
if [ -z "${lan_interfaces[@]}" ]; then
	x_echo "No LAN interface" "No suitable interface was found that could safely host a DHCP server."
	exit 1
fi
if [ "${#wan_interfaces[@]}" -eq 1 ] && [ "${#lan_interfaces[@]}" -eq 1 ] && [ "${wan_interfaces[0]}" == "${lan_interfaces[0]}" ]; then
	x_echo "Single interface" "We only found one interface, so this utility will not work."
	exit 1
fi
set_main_dialog
gtkdialog -c --program=dnsmasq_dialog
