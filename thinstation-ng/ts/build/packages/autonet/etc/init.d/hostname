#!/bin/sh

#. `dirname $0`/common

#set -x
#exec </dev/null >>/var/log/hostname.log  2>&1

for adapter in `ls /run/log/net`; do
        . /run/log/net/$adapter
        echo "$CLIENT_IP $CLIENT_NAME" >> /etc/hosts
        unset CLIENT_IP CLIENT_NAME
        if [ -n "$SERVER_IP" ] && [ -n "$SERVER_NAME" ]; then
                echo "$SERVER_IP $SERVER_NAME" >> /etc/hosts
                unset SERVER_IP SERVER_NAME
        fi
done

. $TS_RUNTIME

if [ -n "$NET_DEVICE" ] ; then
	. /run/log/net/$NET_DEVICE
	hostnamectl set-hostname $CLIENT_NAME
fi
