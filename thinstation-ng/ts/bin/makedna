#!/bin/bash
# Description: Thinstation Utility to create a .dna file for a Thinstation Package
# URL: http://www.thinstation.org
# Maintainer: <PERSON> (don cupp jr at ya hoo dot com)

IFS=$'\t\n'
showhelp()
{
	echo -e "This utility is designed to create a .dna file for a Thinstation Package"
	echo -e "It should be invoked as   'makedna <tspackage>' \n"
	echo -e "Although it is in the executable path and can be called from anywhere,"
	echo -e "it will always search packages in the directory for Thinstation which"
	echo -e "is defined in the environment variable \$TSWRKNG and \$XTRWRKNG. These"
	echo -e "variables can be adjusted for a single session or permanently by"
	echo -e "editing /ts/TS_ENV. \n"
	echo -e "This script will create a .dna file in the root of the package dir for the "
	echo -e "package specified at the time of invokation."
}

makeblacklists()
{
	local IFS=$' \t\n'
	cleanup
	neverversioned=".xpm .conf .pcf .spd .pfa .afm .pfb .py .pyc .pyo .dll .def .fon .exe"
	neverupdate="prefoverride preferences gtkrc dependencies fonts.alias securetty profile services passwd nsswitch.conf fstab group protocols ld.so.conf inittab"
	nudir="TS.default init.d console build/install build/remove build/conf cmd menu"
	for extension in $neverversioned;
		do
			echo $extension >> neverversioned
		done
	for nu_file in $neverupdate;
		do
			echo $nu_file >> neverupdate
		done
	for nu_dir in $nudir;
		do
			echo $nu_dir >> nudir
		done
}

cleanup()
{
	for blacklist in neverversioned neverupdate nudir;
		do
			if [ -e $blacklist ]; then
				rm $blacklist
			fi
		done
}

getfootprints()
{
	if [ "$footprints" == "" ];
		then
			footprints="`find -L /{ts/ports} -name .footprint`"
	fi
}

getfiles()
{
	files=`find $dir/ -type f -printf "\n%f'%h" 2>/dev/null |grep -Ev '\.gitignore|finalize'`
}

getparts()
{
	ffile=$1
	if [ "$package" == "boot-images" ]; then
		loc=`echo $ffile |cut -d "'" -f2`
		loc=`echo $loc |cut -d / -f2-`
		file=`echo $ffile |cut -d "'" -f1`
	else
		loc=`echo $ffile |cut -d "'" -f2`
		loc=`echo $loc |cut -d / -f3-`
		file=`echo $ffile |cut -d "'" -f1`
	fi
}

getlinks()
{
	unset link[@]
	index=0
	if [ "$loc" == "" ];
		then
			rterm="$dir/$file"
			cterm="$file"
		else
			rterm="$dir/$loc/$file"
			cterm="$loc/$file"
	fi
	for i in `find -L $dir -samefile $rterm 2>/dev/null |grep -w -v "$cterm"`;
		do
			if [ `dirname $i` = `dirname $i | xargs realpath --relative-base=$PWD` ]; then
				link[$index]=`echo $i |cut -d / -f3-`
				if [ "${link[$index]}" == "$loc/$file" ];
				then
					unset link[$index]
				else
					index=$(( $index + 1 ))
				fi
			fi
		done
	if [ "${#link[@]}" -gt "9" ];
		then
			echo "Needs ${#link[@]} links."
			echo "There are to many links for $file. Not using any of them."
			unset link[@]
	fi
}

getpackage()
{
	unset packf[@]
	index=0
	for l in `dnf --disablerepo=* provides \*/$loc/$file 2>/dev/null| awk -F'-[0-9]' '{print $1}'|grep -v ':'|grep -v "^$"`;
		do
			packf[$index]=$l
			index=$(( $index + 1 ))
		done
	if [ "${#packf[@]}" -lt "1" ] && [ "$versioned" == "1" ];
		then
			echo "Fast Search didn't find anything, doing the slow one."
			version $file
			getfootprints
			for l in $footprints;
				do
					if [ "`cat $l |grep -e $prefix |grep -e $suffix |grep -v lrwx`" != "" ];
						then
							packf[$index]="`echo $l |cut -d / -f5`"
							index=$(( $index + 1 ))
					fi
				done
	fi
	if [ "${#packf[@]}" -gt "1" ] && [ "$override" == "" ];
		then
			index=$(( $index + 1 ))
			packf[$index]="other"
			index=$(( $index + 1 ))
			packf[$index]="none"
			echo -e "\nThe file $loc/$file was found in the following src packges.\nWhat is the number of the src pkg you would like to use?"
			select i in ${packf[@]};
				do
					echo -e "\n"
					srcpkg=$i
					break
				done
		else
			if [ "${#packf[@]}" == "1" ];
				then
					srcpkg=${packf[0]}
				else
					srcpkg=""
			fi
	fi
	if [ "$override" != "" ];
		then
			srcpkg=$override
	fi
	if [ "$srcpkg" == "other" ];
		then
			read -p "What is the name of the source package you would like to use? " srcpkg
	fi
	if [ "$srcpkg" == "none" ];
		then
			srcpkg=""
	fi
	if [ "$srcpkg" == "" ];
		then
			echo -e "There is no source package for $loc/$file."
		else
			echo -e "The source package for $loc/$file is $srcpkg"
	fi
}

isversioned()
{
	if [ `echo $file| grep -v -f neverversioned |grep -o -e [2-9] |wc -l` -gt "1" ];
		then while true;
			do
				echo -e "\n"
				read -p "Is $file versioned? " yn;
				case $yn in
					[Yy]*)
						getversion
						break;;
					[Nn]*)
						noversion
						break;;
					*)
						echo "Please answer yes or no."
						sleep 3;;
				esac
			done
		else
			noversion
	fi
}

noversion()
{
	head="0"
	tail="0"
	versioned="0"
}

getversion()
{
	echo -e "\n"
	echo "$file"
	echo "12345678911111111112222222222"
	echo "         01234567890123456789"
	read -p "What is the starting posistion of the version?  " head
	read -p "what is the ending posistion of the version? (default is last)  " tail
	local total=`echo $file |wc -m`
	total=$(( $total - 1 ))
	if [ "$tail" = "" ];
		then
			tail="0"
		else
			tail=$(( $total - $tail ))
	fi
	head=$(( $head - 1 ))
	versioned="1"
}

isdeleteable()
{
	if [ "`echo $loc| grep -c -e etc`" == "0" ] && [ "`echo $loc| grep -c -e bin`" == "0" ];
		then
			if [ "$srcpkg" == "" ];
				then
					delete="0"
					return 0
				else
					delete="1"
					return 0
			fi
	fi
	if [ "`file $dir/$loc/$file |grep -c -e ELF`" != "0" ];
		then
			if [ "$srcpkg" == "" ];
				then
					delete="0"
					return 0
				else
					delete="1"
					return 0
			fi
	fi
	if [ "`cat $dir/$loc/$file |grep -c -e TS_GLOBAL`" != "0" ];
		then
			echo -e "\nFile $loc/$file is not updatable. Removing source package."
			srcpkg=""
			delete=0
			return 0
	fi
	if [ "$srcpkg" == "" ];
		then
			delete=0
			return 0
	fi
	while true;
		do
			echo -e "\n"
			read -p "Can $loc/$file be removed during update? " yn
			case $yn in
				[Yy]*)
					delete=1
					break ;;
				[Nn]*)
					delete=0
					srcpkg=""
					echo -e "\nSource Package removed for $loc/$file. It will not be updated."
					break ;;
				*)
					echo "Please answer yes or no."
					;;
			esac
		done
}

version()
{
	local total=`echo $1 |wc -m`
	local hhead=$(( $head + 1 ))
	local ttail=""
	if [ "$head" == "0" -a "$tail" == "0" ];
		then
			prefix=$1
			version=""
			suffix=""
		else
			prefix=`echo $1|cut -c -$head`
			if [ "$tail" = "0" ];
				then
					version=`echo $1|cut -c $hhead-`
					suffix=""
				else
					ttail=$(( $total - $tail - 1 ))
					version=`echo $1|cut -c $hhead-$ttail`
					ttail=$(( $total - $tail ))
					suffix=`echo $1|cut -c $ttail-`
			fi
 	fi
}

init()
{
	package=$1
	override=$2
	work=$TSWRKNG
	if [ "$package" == "" -o "$package" == "--help" ];
		then
			showhelp
			exit
		elif [ "$package" == "tools" ];
			then
				dir=utils/tools
		elif [ "$package" == "boot-images" ];
			then
				dir=boot-images
		else
			for work in $TSWRKNG $XTRWRKNG; do
				if [ -d $work/packages/$package ]; then
					dir=packages/$package
					break
				fi
			done
			if [ -z "$dir" ]; then
				echo -e "\nThe package '$package' was not found.\n\n"
				showhelp
				exit
			fi
	fi
	cd $work
	if [ -f $dir/.dna ];
		then
			rm $dir/.dna
	fi
}

main()
{
	if [ "$package" == "tools" ] || [ "$package" == "boot-images" ];
		then
			touch neverversioned
			touch neverupdate
			touch nudir
		else
			makeblacklists
	fi
	getfiles
	for i in $files;
		do
			getparts $i
			if [ $(echo "$file" |grep -c -f neverupdate) != 0 -o $(echo "$loc" |grep -w -c -f nudir) != 0 ] || \
		   	[ $(cat "$dir/$loc/$file" |grep -c -e TS_GLOBAL) != 0 -o $(echo "$loc/$file" |grep -w -c -f nudir) != 0 ];
				then
					srcpkg=""
					head="0"
					tail="0"
					delete="0"
					unset link[@]
				elif [ ! -s $dir/$loc/$file ];
					then
						srcpkg="EMPTY"
						head="0"
						tail="0"
						delete="1"
						getlinks
				else
					echo -e "\nFound file $loc/$file. Attempting to configure dna! \n"
					getlinks
					isversioned
					getpackage
					isdeleteable
		fi
		echo "$srcpkg,$file,$head,$tail,$loc,$delete,${link[0]},${link[1]},${link[2]},${link[3]},${link[4]},${link[5]},${link[6]},${link[7]},${link[8]}" >> $dir/.dna
	done
	cleanup
}

init $1 $2
main
exit
