# Omnissa Horizon Global Options
# Connect to server without waiting for the user to click connect
#HORIZON_AUTOCONNECT=TRUE
# Set the certificate verification mode
#HORIZON_SSLVERIFYMODE=3
# Default server to connect to if none specified
#HORIZON_DEFAULTBROKER=***********
# Default SSL protocols to use
#HORIZON_SSLPROTOCOL=
# Default SSL ciphers to us
#HORIZON_SSLCIPHERS=
# Module to use for PKCS#11
#PKCS11_MODULE=

# Omnissa Horizon Session options
#SESSION_#_TITLE="VDI"
#SESSION_#_TYPE=horizon
#SESSION_#_AUTOSTART=On
#SESSION_#_HORIZON_SERVER=VDI    ## Should be the same as "TITLE"
#SESSION_#_HORIZON_SERVERURL=***********
#SESSION_#_HORIZON_FULLSCREEN=true
#SESSION_#_HORIZON_NONINTERACTIVE=true
#SESSION_#_HORIZON_KEEP-WM-BINDINGS=true
#SESSION_#_HORIZON_BACKGROUND=/etc/background.jpg
#SESSION_#_HORIZON_DOMAINNAME=donaldduck.com
#SESSION_#_HORIZON_DESKTOPNAME=mickey
#SESSION_#_HORIZON_USERNAME=minnie
#SESSION_#_HORIZON_PASSWORD=mouse
#SESSION_#_HORIZON_REDIRECT1="disk:usb=/mnt/usbdevice/sda1"
#SESSION_#_HORIZON_REDIRECT2="printer:usb='HP LaserJet 4'"
#SESSION_#_HORIZON_USB1="????"
#SESSION_#_HORIZON_USB2="????"
#SESSION_#_HORIZON_MMRPATH=/usr/local/mmr
#SESSION_#_HORIZON_RDESKTOPOPTIONS="-N"

