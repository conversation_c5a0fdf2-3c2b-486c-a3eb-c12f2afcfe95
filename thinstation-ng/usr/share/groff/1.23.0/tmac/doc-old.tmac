.\" Copyright (c) 1990 The Regents of the University of California.
.\" All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that the following conditions
.\" are met:
.\" 1. Redistributions of source code must retain the above copyright
.\"    notice, this list of conditions and the following disclaimer.
.\" 2. Redistributions in binary form must reproduce the above copyright
.\"    notice, this list of conditions and the following disclaimer in
.\"    the documentation and/or other materials provided with the
.\"    distribution.
.\" 3. [Deleted.  See
.\"     ftp://ftp.cs.berkeley.edu/pub/4bsd/README.Impt.License.Change]
.\" 4. Neither the name of the University nor the names of its
.\"    contributors may be used to endorse or promote products derived
.\"    from this software without specific prior written permission.
.\"
.\" THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS "AS IS"
.\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
.\" TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
.\" PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR
.\" CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
.\" SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
.\" LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
.\" USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
.\" ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
.\" OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
.\" OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
.\" SUCH DAMAGE.
.\"
.\"     @(#)doc-old.tmac	5.2 (Berkeley) 3/13/91
.\"     Slightly <NAME_EMAIL> to work with groff as well.
.\"
.\" Assume nroff on crt's only if cR==1
.if n .nr cR 1
.\"	STRING CONSTANTS
.\" 	DITROFF
.if t \{\
.\"	Address Style
.ds aD \fI
.\"	Argument Reference Style
.ds aR \f(CO
.\"	Interactive Command Modifier (flag)
.ds cM \f(CB
.\"	Emphasis (in the English sense - usually italics)
.ds eM \fI
.\"	Errno Style
.ds eR \fC
.\"	Environment Variable Style
.ds eV \fC
.\"	Command Line Flag Style
.ds fL \f(CB
.\"	Header String Style
.ds Hs \fR
.\"	Interactive Command Style
.ds iC \f(CB
.\"	Literal Style
.ds lI \fC
.\"	Left Parenthesis Style
.ds lP \fR\|(\|\fP
.\"	Right Parenthesis Style
.ds rP \fR\|)\|\fP
.\"	Options Open Bracket Style
.ds lB \fR\^[\^\fP
.\"	Options Open Bracket Style
.ds rB \fR\^]\fP
.\"	Name (subject of manpage) Style
.ds nM \f(CB
.\"	Pathname Style
.ds pA \fC
.\"	Accepted punctuation string for -mdoc syntax
.ds Pu \fR[.,:;(\^)[\^]\fR]
.\"	Section Header Style
.ds Sp \s12\fB
.\" .ds sT \s-2\fR
.\"	Symbolic Emphasis (boldface)
.ds sY \f(CB
.\"	Generic Variable Style
.ds vA \fI
.\"	Volume Title Style
.ds Vs \fR
.\"	Cross Reference STyle (man page only)
.ds xR \fC
.\"	Math *
.tr *\(**
.\}
.\"	NROFF
.if n \{\
.\"	Address Style
.ds aD \fI
.\"	Argument Reference Style
.ds aR \fI
.\"	Interactive Command Modifier (flag)
.ds cM \fB
.\"	Emphasis (in the English sense - usually italics)
.ds eM \fI
.\"	Errno Style
.ds eR \fR
.\"	Environment Variable Style
.ds eV \fR
.\"	Command Line Flag Style
.ds fL \fB
.\"	Header String Style
.ds Hs \fR
.\"	Interactive Command Style
.ds iC \fB
.\"	Literal Style
.ds lI \fR
.\"	Left Parenthesis Style
.ds lP \fR\|(\fP
.\"	Right Parenthesis Style
.ds rP \fR\|)\fP
.\"	Options Open Bracket Style
.ds lB \fR\|[\|\fP
.\"	Options Open Bracket Style
.ds rB \fR\|]\fP
.\"	Name (subject of manpage) Style
.ds nM \fB
.\"	Pathname Style
.ds pA \fI
.\"	Accepted punctuation string for -mdoc syntax
.ds Pu [.,;:()[]]
.\"	Section Header Style
.ds Sp \s12\fB
.\"	.ds sT \s-2\fR
.\" .ds sT \s-2\fR
.\"	Symbol, Mode or Mask Style
.ds sY \fB
.\"	Generic Variable Style
.ds vA \fI
.\"	Volume Title Style
.ds Vs \fR
.\"	Cross Reference Style (man page only)
.ds xR \fR
.\}
.\"	INDENTS - Subheaders(sI), Text(Ti) between Section Headers and Subsects
.if t \{\
.	nr sI \w'\fC,'u*5
.	nr Ti \n(sIu
.\}
.if n \{\
.	nr sI .5i
.	nr Ti .5i
.\}
.\"	Flags for macros names which are used only for .Ds
.nr dI 6n
.nr dC 1
.nr dL 1
.nr dR 1
.\"	INDENT WIDTHS (for Lists)
.\"	Width Needed for Address Tag (indented amount)
.nr Ad 12n
.\"	Angle Quote Width
.nr Aq 12n
.\"	Width Needed for Argument
.nr Ar 12n
.\"	Width Needed for Column offset
.nr Cl 15n
.\"	Width needed for Interactive Command Modifier
.nr Cm 10n
.\"	Width Needed for Complex Expressions
.nr Cx 20n
.\"	Indent Width Needed for Display (right and left margins)
.nr Ds 6n
.\"	Double Quote Width
.nr Dq 12n
.\"	tI is dependent on Ds and used by .Dp
.nr tI \n(Dsu
.\"	Width Needed for Display
.nr Em 10n
.\"	Width Needed for Errno Types
.nr Er 15n
.\"	Width Needed for Environment Variables
.nr Ev 15n
.\"	Width Needed for Example Indent
.nr Ex 10n
.\"	Width Needed for Flag
.nr Fl 10n
.\"	Width Needed for Function
.nr Fn 16n
.\"	Width needed for Interactive Command Name
.nr Ic 10n
.\"	Width Needed for Constant
.nr Li 16n
.\"	Width Needed for Math Symbol ? not sure if needed
.nr Ms 6n
.\"	Width Needed for Name
.nr Nm 10n
.\"	Width Needed for Option Begin
.nr Ob 14n
.\"	Width Needed for Option End
.nr Oe 14n
.\"	Width Needed for Option (one line)
.nr Op 14n
.\"	Width Needed for Pathname
.nr Pa 32n
.\"	Parenthesis Quote Width
.nr Pq 12n
.\"	Single Quote Width
.nr Sq 12n
.\"	Width Needed for Symbols, Modes or Masks
.nr Sy 6n
.\"	Width needed for default or unknown text width
.nr Tx 22n
.\"	Width Needed for Generic Variable
.nr Va 12n
.\"	Width Needed for Cross Reference, should the cross ref be annotated.
.nr Xr 10n
.\" PARAGRAPH SPACE
.if t \{\
.	nr Pp .5v
.\}
.if n \{\
.	nr Pp 1v
.\}
.\"	PAGE LAYOUT
.\" .Li Tagged Paragraph Style - zero if break on oversized tag
.\" one if add em space and continue filling line.
.nr tP 0
.\" Page Layout Macro
.de pL
.\"	DITROFF
.ie t \{\
.\" Header Margin
.	nr Hm .5i
.\" Footer Margin
.	nr Fm .5i
.\" Line length
.	nr ll 5.5i
.\" Line length
.	ll 5.5i
.\" Title length
.	nr lt 5.5i
.\" Title length
.	lt 5.5i
.\" Page offset
.	nr po 1.56i
.\" Page offset
.	po 1.56i
.\" Vertical space distance (from Section headers/Lists/Subsections)
.	nr vV .5v
.\" em space
.	ds tP \|\|\|\|\|\|
.\}
.el \{\
.\" Line length
.	nr ll 78n
.	ll 78n
.\" Title length
.	nr lt 78n
.\" Title length
.	lt 78n
.\" Page offset
.	nr po 0i
.\" Page offset
.	po 0i
.\" Vertical space distance (from Section headers/Lists/Subsections)
.	nr vV 1v
.\" em space
.	ds tP \0\0
.\" Test for crt
.	ie \\n(cR .nr Hm 0
.	el .nr Hm .5i
.\" Footer Margin
.	nr Fm .5i
.\}
..
.\" Adjustment mode
.if n \{\
.ad l
.na
..
.\}
.\" PREDEFINED STRINGS
.if t \{\
.	ds <= \(<=
.	ds >= \(>=
.	ds Lq \&``
.	ds Rq \&''
.	ds ua \(ua
.	ds aa \(aa
.	ds ga \(ga
.	ds sR \(aa
.	ds sL \(ga
.\}
.if n \{\
.	ds <= \&<\&=
.	ds >= \&>\&=
.       ds Rq ''
.       ds Lq ``
.	ds ua ^
.	ds aa '
.	ds ga `
.	ds sL `
.	ds sR '
.\}
.\" Note: The distances from the bottom or top of the page are set
.\" in headers (macro .hK): to -1.25 for troff, and -1.167 for nroff
.\" bottoms, and top is 0.
.\"
.\"	.Dt Document/manpage_title section/chapter volume
.\"		The \{ and \} is necessary as roff doesn't nest if-elses
.\"		properly, especially with .ds.
.\"	TODO: separate Dt into Dt, Ch and Vt for supp docs.
.de Dt
.ds dT UNTITLED
.ds vT Local
.ds cH Null
.\" 	Volume and Section Number or Chapter Number
.if !"\\$1"" .ds dT \\$1
.if !"\\$2"" \{\
.	ds cH \\$2
.	if "\\$3"" \{\
.		\" Volume Title if none given
.		if \\$2>=1 .if \\$2<=8 \{\
.			ds vT UNIX Reference Manual
.			if \\$2>1 .if \\$2<6 .ds vT UNIX Programmer's Manual
.			if "\\$2"8" .ds vT UNIX System Manager's Manual
.		\}
.		if "\\$2"unass"  .ds vT DRAFT
.		if "\\$2"draft"  .ds vT DRAFT
.		if "\\$2"paper"  .ds vT Null
.	\}
.\}
.if !"\\$3"" \{\
.	\" Volume Title if given
.	if "\\$3"USD"   .ds vT UNIX User's Supplementary Documents
.	if "\\$3"PS1"   .ds vT UNIX Programmers's Supplementary Documents
.	if "\\$3"AMD"   .ds vT UNIX Ancestral Manual Documents
.	if "\\$3"SMM"   .ds vT UNIX System Manager's Manual
.	if "\\$3"URM"   .ds vT UNIX Reference Manual
.	if "\\$3"PRM"   .ds vT UNIX Programmers's Manual
.	if "\\$3"IND"   .ds vT UNIX Manual Master Index
.	if "\\$3"CON"   .ds vT UNIX Contributed Software Manual
.	if "\\$3"IMP"	.ds vT UNIX Implementation Notes
.	if "\\$3"HOW"	.ds vT UNIX How Pocket Manual
.	if "\\$3"LOCAL" .ds vT UNIX Local Manual
.	if "\\*(vT"Local" .ds vT \\$3
.\}
..
.\"
.\"	.Os Operating System/Standard and Release or Version Number
.\"
.de Os
.ds oS Null
.if "\\$1"" \{\
.	ds oS \fIBSD Experimental\fP
.\" .	ds oS (\fIBag o' Bits\fP)
.\}
.if "\\$2"" \{\
.	ds o1 Non-Null
.\}
.if "\\$1"ATT"   \{\
.	ds oS AT&T
.	if "\\$2""    .as oS \0UNIX
.	if "\\$2"7th" .as oS \07th Edition
.	if "\\$2"7"   .as oS \07th Edition
.	if "\\$2"III" .as oS \0System III
.	if "\\$2"3"   .as oS \0System III
.	if "\\$2"V"   .as oS \0System V
.	if "\\$2"V.2" .as oS \0System V Release 2
.	if "\\$2"V.3" .as oS \0System V Release 3
.	if "\\$2"V.4" .as oS \0System V Release 4
.\}
.if "\\$1"BSD" \{\
.	if "\\$2"3"    .ds oS 3rd Berkeley Distribution
.	if "\\$2"4"    .ds oS 4th Berkeley Distribution
.	if "\\$2"4.1"  .ds oS 4.1 Berkeley Distribution
.	if "\\$2"4.2"  .ds oS 4.2 Berkeley Distribution
.	if "\\$2"4.3"  .ds oS 4.3 Berkeley Distribution
.	if "\\$2"4.3+" .ds oS 4.3+tahoe Berkeley Distribution
.\}
.if "\\*(oS"Null" .ds oS \\$1
.if "\\*(o1"Non-Null" .as oS \0\\$2
.rm o1
..
.\"
.\" Standards
.\"
.\" .de St
.\" .ds sT Null
.\" .if "\\$1"POSIX" \{\
.\" .	ds sT IEEE Standard POSIX
.\" .	if \\$2 .as sT \0\\$2
.\" .\}
.\" .if "\\$1"ANSI" \{\
.\" .	ds sT ANSI Standard
.\" .	if \\$2 .as sT \0\\$2
.\" .\}
.\" .if "\\$1"ISO" \{\
.\" .	ds sT ISO Standard
.\" .	if \\$2 .as sT \0\\$2
.\" .\}
.\" .if "\\*(sT"Null" .ds sR \\$3
.\" ..
.\"
.\" .de Gp
.\" .ie !"\\$1"" .ds gP \&\\$1 \\$2 \\$3 \\$4 \\$5
.\" .el .ds gP Null
.\" ..
.\"
.\"
.de Dd
.nr aa 0
.ie \\n(.$>0 \{\
.	ie \\n(.$<4 \{\
.		ds dD \\$1 \\$2 \\$3
.	\}
.	el .tm Usage: .Dd Month Day, Year (e.g July 4, 1977).
.\}
.el \{\
.	ds dD Epoch
.\}
..
.\"
.\"	House Keeping Macro - Make sense of dT, cH, vT, sT, gP and dS
.\"	TODO: Try to get else's for efficiency
.\"	TODO: GET RID OF .wh -1.167i (its in v7)
.\"
.\"
.de hK
.nr % 1
.ds hT \\*(dT
.if !"\\*(cH"Null" \{\
.	ie !"\\*(gP"Null" .as hT \|(\|\\*(cH\\*(gP\|)
.	el .as hT \\|(\\|\\*(cH\\|)
.\}
.if "\\*(cH"Null" .if !"\\*(gP"Null" .as hT \&\|(\|\\*(gP\|)
.if t \{\
.	wh 0 hM
.	wh -1.25i fM
.\}
.if n \{\
.	ie \\n(cR \{\
.		hM
.		wh -0v fM
.	\}
.	el \{\
.		wh 0 hM
.		wh -1.167i fM
.	\}
.\}
.if n \{\
.	if \\n(nl==0:\\n(nl==-1 'bp
.\}
.if t 'bp
.em lM
..
.\"	Header Macro
.\"
.de hM
.ev 1
.pL
.if !\\n(cR 'sp \\n(Hmu
.tl @\\*(Hs\\*(hT\fP@\\*(Vs\\*(vT\fP@\\*(Hs\\*(hT\fP@
'sp \\n(Hmu
.ev
..
.\"
.de fM
.ev 1
.pL
.if !\\n(cR \{\
'	sp \\n(Fmu
.	tl @\\*(Hs\\*(oS\fP@\\*(Vs\\*(dD\fP@%@
'	bp
.\}
.if \\n(cR \{\
.\" .	tl @\\*(Hs\\*(oS\fP@\\*(Vs\\*(dD\fP@%@
.\" '	bp
.\}
.ev
..
.de lM
.fl
.if \\n(cR \{\
.       fM
.       pl \\n(nlu
.\}
..
.de Pp
.sp \\n(Ppu
.ne 2
.ns
..
.de Lp
.Pp
..
.de LP
.tm Not a \-mdoc command: .LP
..
.de PP
.tm Not a \-mdoc command: .PP
..
.de pp
.tm Not a \-mdoc command: .pp
..
.de Co
.tm Not a \-mdoc command: .Co
..
.nr z. 1
.nr z, 1
.nr z: 1
.nr z; 1
.nr z) 1
.nr z( 1
.nr z[ 1
.nr z] 1
.\" This is disgusting, troff not parse if stmt properly
.nr z1 0
.nr z2 0
.nr z3 0
.nr z4 0
.nr z5 0
.nr z6 0
.nr z7 0
.nr z8 0
.nr z9 0
.nr z0 0
.nr z# 0
.\"
.de Ad
.ie \\n(.$==0 \{\
.       tm Usage: .Ad address [...] \\*(Pu
.\}
.el \{\
.       ds sV \\*(aD
.       nr cF \\n(.f
.       ie "\\*(iM"" .ds f1 \&\\*(sV
.       el .as f1 \&\\*(sV
.       nB \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.       ft \\n(cF
.\}
..
.\"
.\" Command Line Argument Macro
.\"
.de Ar
.ie \\n(.$==0 \{\
.       ie !"\\*(iM"" .as f1 \&[\|\\*(aRfile\ ...\fP\|]
.       el \&[\|\\*(aRfile\ ...\fP\|]
.\}
.el \{\
.       ds sV \\*(aR
.       nr cF \\n(.f
.       ie "\\*(iM"" .ds f1 \&\\*(sV
.       el .as f1 \&\\*(sV
.       nB \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.       ft \\n(cF
.\}
..
.\"
.de Em
.ie \\n(.$==0 \{\
.       tm Usage: .Em text ... \\*(Pu
.\}
.el \{\
.       ds sV \\*(eM
.       nr cF \\n(.f
.       ie "\\*(iM"" .ds f1 \&\\*(sV
.       el .as f1 \&\\*(sV
.       nB \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.       ft \\n(cF
.\}
..
.\"
.de Er
.ie \\n(.$==0 \{\
.       tm Usage: .Er ERRNOTYPE ... \\*(Pu
.       \}
.el \{\
.       ds sV \\*(eR
.       nr cF \\n(.f
.       ie "\\*(iM"" .ds f1 \&\\*(sV
.       el .as f1 \&\\*(sV
.       nB \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.       ft \\n(cF
.\}
..
.\"
.de Ev
.ie \\n(.$==0 \{\
.	tm Usage: .Ev ENVIRONMENT_VARIABLE(s) ... \\*(Pu
.	\}
.el \{\
.	ds sV \\*(eV
.	nr cF \\n(.f
.       ie "\\*(iM"" .ds f1 \&\\*(sV
.       el .as f1 \&\\*(sV
.       nB \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.	ft \\n(cF
.\}
..
.\"
.\" Flag Name Macro
.\"
.de Fl
.ie \\n(.$==0 \{\
.       ie !"\\*(iM"" .as f1 \&\\*(fL\-\fP
.       el \&\\*(fL\-\fP
.\}
.el \{\
.       nr rZ 0
.       sW \\$1
.       if (\\n(sW==1&\\n(.$==1) .rZ \\$1
.       ds sV \\*(fL
.       nr cF \\n(.f
.       ie \\n(rZ \{\
.               ie "\\*(iM"" .ds f1 \&\\*(sV\-\f\\n(cF\\$1
.               el \&\\*(sV\-\f\\n(cF\\$1
.       \}
.	el \{\
.	       ie "\\*(iM"" .ds f1 \&\\*(sV
.	       el .as f1 \&\\*(sV
.	       fB \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.	       ft \\n(cF
.	\}
.\}
..
.\"	Interactive Commands Macro
.\"
.de Ic
.ie \\n(.$==0 \{\
.	tm Usage: .Ic Interactive Commands(s) ... \\*(Pu
.\}
.el \{\
.       ds sV \\*(iC
.       nr cF \\n(.f
.       ie "\\*(iM"" .ds f1 \&\\*(sV
.       el .as f1 \&\\*(sV
.       nB \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.       ft \\n(cF
.\}
..
.\"
.\" Interactive Command Modifiers (flags)
.\"
.de Cm
.ie \\n(.$==0 \{\
.	tm Usage: .Cm Interactive Command Modifier(s) ... \\*(Pu
.\}
.el \{\
.       ds sV \\*(cM
.       nr cF \\n(.f
.       ie "\\*(iM"" .ds f1 \&\\*(sV
.       el .as f1 \&\\*(sV
.       nB \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.       ft \\n(cF
.\}
..
.\"
.de Li
.ie \\n(.$==0 \{\
.       tm Usage: .Li literal ... \\*(Pu
.       \}
.el \{\
.       ds sV \\*(lI
.       nr cF \\n(.f
.       ie "\\*(iM"" .ds f1 \&\\*(sV
.       el .as f1 \&\\*(sV
.       nB \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.       ft \\n(cF
.\}
..
.\" If in nroff or any other case where the default font
.\" is constant width, and literal means zilch, single quote instead.
.ie n \{\
.de Ql
.	ie \\n(.$==0 \{\
.	       tm Usage: .Ql literal ... \\*(Pu
.       \}
.	el \{\
.		Sq \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.	\}
..
.\}
.el \{\
.de Ql
.	ie \\n(.$==0 \{\
.	       tm Usage: .Ql literal ... \\*(Pu
.       \}
.	el \{\
.		Li \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.	\}
..
.\}
.\"
.de Nm
.ie \\n(.$==0 \{\
.	if "\\*(n1"" .tm Usage: .Nm Name(s) ... \\*(Pu
.	ie !"\\*(iM"" .as f1 \&\\*(nM\\*(n1\\$1\fP
.	el \&\\*(nM\\*(n1\\$1\fP
.\}
.el \{\
.	ds sV \\*(nM
.	nr cF \\n(.f
.	if \\n(nS \{\
.		rs
.		in -\\n(iSu
.		ie \\n(nS>1 .br
.		el \{\
.			sW \\$1
.			nr iS ((\\n(sW+1)*\\n(fW)u
.		\}
.		in +\\n(iSu
.		ti -\\n(iSu
.		nr nS \\n(nS+1
.	\}
.	if "\\*(n1"" .ds n1 \\$1
.	ie "\\*(iM"" .ds f1 \&\\*(sV
.	el .as f1 \&\\*(sV
.	nB \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.	ft \\n(cF
.\}
..
.\"
.de Pa
.ie \\n(.$==0 \{\
\&\\*(pA~\fP
.\}
.el \{\
.       ds sV \\*(pA
.       nr cF \\n(.f
.       ie "\\*(iM"" .ds f1 \&\\*(sV
.       el .as f1 \&\\*(sV
.       nB \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.       ft \\n(cF
.\}
..
.\"
.de Sy
.ie \\n(.$==0 \{\
.       tm Usage: .Sy Symbolic Text ... \\*(Pu
.       \}
.el \{\
.       ds sV \\*(sY
.       nr cF \\n(.f
.       ie "\\*(iM"" .ds f1 \&\\*(sV
.       el .as f1 \&\\*(sV
.       nB \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.       ft \\n(cF
.\}
..
.\"
.de Ms
.ie \\n(.$==0 \{\
.       tm Usage: .Ms Math Symbol ... \\*(Pu
.       \}
.el \{\
.       ds sV \\*(sY
.       nr cF \\n(.f
.       ie "\\*(iM"" .ds f1 \&\\*(sV
.       el .as f1 \&\\*(sV
.       nB \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.       ft \\n(cF
.\}
..
.\"
.de Va
.ie \\n(.$==0 \{\
.       tm Usage: .Va variable_name(s) ... \\*(Pu
.\}
.el \{\
.       ds sV \\*(vA
.       nr cF \\n(.f
.       ie "\\*(iM"" .ds f1 \&\\*(sV
.       el .as f1 \&\\*(sV
.       nB \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.       ft \\n(cF
.\}
..
.\"
.de nB
.hy 0
.if \\n(.$==0 .tm Usage error: called with empty arguments (empty quotes)?
.ie \\n(.$>1 \{\
.	rZ \\$1
.	ie \\n(rZ .as f1 \&\f\\n(cF\\$1\fP
.	el .as f1 \&\\$1
.	rZ \\$2
.	if !\\n(rZ \{\
.		ie !"\\*(iM""\{\
.\"			I surrender
.			if "\\*(iM"Tp" .as f1 \&\ \&
.			if "\\*(iM"Dp" .as f1 \&\ \&
.			if "\\*(iM"Op" .as f1 \&\ \&
.			if "\\*(iM"Cx" .as f1 \&\ \&
.			if "\\*(iM"Dq" .as f1 \& \&
.			if "\\*(iM"Sq" .as f1 \& \&
.			if "\\*(iM"Pq" .as f1 \& \&
.			if "\\*(iM"Aq" .as f1 \& \&
.		\}
.		el .as f1 \& \&
.	\}
.	nB \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.\}
.el \{\
.	rZ \\$1
.	ie \\n(rZ .as f1 \&\f\\n(cF\\$1
.	el .as f1 \&\\$1\f\\n(cF
.	if "\\*(iM"" \{\&\\*(f1
.		ds f1
.	\}
.	hy
.\}
..
.de fB
.hy 0
.if \\n(.$==0 .tm Usage error: called with empty arguments (empty quotes)?
.ie \\n(.$>1 \{\
.	rZ \\$1
.	ie \\n(rZ .as f1 \&\f\\n(cF\\$1\fP
.	el \{\
.		ie "\\$1"-" .as f1 \&\-\-
.		el .as f1 \&\-\\$1
.	\}
.	rZ \\$2
.	if !\\n(rZ \{\
.		ie !"\\*(iM""\{\
.\"			I surrender
.			if "\\*(iM"Tp" .as f1 \&\ \&
.			if "\\*(iM"Dp" .as f1 \&\ \&
.			if "\\*(iM"Op" .as f1 \&\ \&
.			if "\\*(iM"Cx" .as f1 \&\ \&
.			if "\\*(iM"Dq" .as f1 \& \&
.			if "\\*(iM"Sq" .as f1 \& \&
.			if "\\*(iM"Pq" .as f1 \& \&
.			if "\\*(iM"Aq" .as f1 \& \&
.		\}
.		el .as f1 \& \&
.	\}
.	fB \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.\}
.el \{\
.	rZ \\$1
.	ie \\n(rZ .as f1 \&\f\\n(cF\\$1
.	el \{\
.		ie "\\$1"-" .as f1 \&\-\-\f\\n(cF
.		el .as f1 \&\-\\$1\f\\n(cF
.	\}
.	if "\\*(iM"" \{\&\\*(f1
.		ds f1
.	\}
.	hy
.\}
..
.\"
.\" Single quoted Items
.\" eF, sB g[0-9] and f2
.de Sq
.nr eF 0
.ie \\n(.$==0 \{\
.       ie "\\*(iM"" \&\\*(sL\&\\*sR
.       el .as f1 \&\\*(sL\&\\*(sR
.\}
.el \{\
.       ie "\\*(iM"" \{\
.		ds f1 \&\\*(sL
.		ds iM Sq
.	\}
.       el .as f1 \&\\*(sL
.       sB \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.	ie \\n(eF>0 .\\*(g1 \\*(g2 \\*(g3 \\*(g4 \\*(g5 \\*(g6 \\*(g7 \\*(g8
.	el .as f1 \\*(g0
.	as f1 \\*(sR
.	if !"\\*(f2"" .as f1 \\*(f2
.	if "\\*(iM"Sq" \{\
\&\\*(f1
.		ds f1
.		ds iM
.	\}
.	ds f2
.	rm  g0 g1 g2 g3 g4 g5 g6 g7 g8 g9
.	nr eF 0
.\}
..
.\"
.\" Double quoted Items
.de Dq
.nr Ef 0
.ie \\n(.$==0 \{\
.       ie "\\*(iM"" \&\\*(Lq\&\\*(Rq
.       el .as f1 \&\\*(Lq\&\\*(Rq
.\}
.el \{\
.       ie "\\*(iM"" \{\
.               ds f1 \&\\*(Lq
.               ds iM Dq
.       \}
.       el .as f1 \&\\*(Lq
.       Sb \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.       ie \\n(Ef>0 .\\*(k1 \\*(k2 \\*(k3 \\*(k4 \\*(k5 \\*(k6 \\*(k7 \\*(k8
.       el .as f1 \\*(k0
.       as f1 \\*(Rq
.       if !"\\*(f4"" .as f1 \\*(f4
.       if "\\*(iM"Dq" \{\
\&\\*(f1
.               ds f1
.               ds iM
.       \}
.       ds f4
.       rm  k0 k1 k2 k3 k4 k5 k6 k7 k8 k9
.       nr Ef 0
.\}
..
.\"
.\" Parenthesis quoted Items
.de Pq
.nr pQ 0
.ie \\n(.$==0 \{\
.       ie "\\*(iM"" \&(\&)
.       el .as f1 \&(\&)
.\}
.el \{\
.       ie "\\*(iM"" \{\
.               ds f1 \&(
.               ds iM Pq
.       \}
.       el .as f1 \&(
.       pB \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.       ie \\n(pQ>0 .\\*(y1 \\*(y2 \\*(y3 \\*(y4 \\*(y5 \\*(y6 \\*(y7 \\*(y8
.       el .as f1 \\*(y0
.       as f1 \&)
.       if !"\\*(f3"" .as f1 \\*(f3
.       if "\\*(iM"Pq" \{\
\&\\*(f1
.               ds f1
.               ds iM
.       \}
.       ds f3
.       rm  y0 y1 y2 y3 y4 y5 y6 y7 y8 y9
.       nr pQ 0
.\}
..
.\" eF, sB g[0-9] and f2
.de sB
.hy 0
.ie \\n(.$==0 .tm Sick Logic: macro sB
.el \{\
.	ie \\n(eF>=1 .nr eF \\n(eF+1
.	el \{\
.		mN \\$1
.		if \\n(mN .nr eF \\n(eF+1
.	\}
.       rZ \\$1
.       ie \\n(rZ .as f2 \\$1
.       el \{\
.		ie \\n(eF<1 .as g\\n(eF \\$1
.		el .as g\\n(eF \\$1
.	\}
.       if \\n(.$>1 \{\
.		rZ \\$2
.	        if \\n(rZ==0 \{\
.			if \\n(eF<1 \{\
.				as g\\n(eF \& \&
.			\}
.		\}
.		sB \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.	\}
.\}
..
.de Sb
.hy 0
.ie \\n(.$==0 .tm Sick Logic: macro Sb
.el \{\
.       ie \\n(Ef>=1 .nr Ef \\n(Ef+1
.	el \{\
.		mN \\$1
.	        if \\n(mN .nr Ef \\n(Ef+1
.	\}
.       rZ \\$1
.       ie \\n(rZ .as f4 \\$1
.       el \{\
.               ie \\n(Ef<1 .as k\\n(Ef \\$1
.               el .as k\\n(Ef \\$1
.       \}
.       if \\n(.$>1 \{\
.               rZ \\$2
.               if \\n(rZ==0 \{\
.                       if \\n(Ef<1 \{\
.                               as k\\n(Ef \& \&
.                       \}
.               \}
.               Sb \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.       \}
.\}
..
.de pB
.hy 0
.ie \\n(.$==0 .tm Sick Logic: macro pB
.el \{\
.       ie \\n(pQ>=1 .nr pQ \\n(pQ+1
.       el \{\
.               mN \\$1
.               if \\n(mN .nr pQ \\n(pQ+1
.       \}
.       rZ \\$1
.       ie \\n(rZ .as f3 \\$1
.       el \{\
.               ie \\n(pQ<1 .as y\\n(pQ \\$1
.               el .as y\\n(pQ \\$1
.       \}
.       if \\n(.$>1 \{\
.               rZ \\$2
.               if \\n(rZ==0 \{\
.                       if \\n(pQ<1 \{\
.                               as y\\n(pQ \& \&
.                       \}
.               \}
.               pB \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.       \}
.\}
..
.de aQ
.hy 0
.ie \\n(.$==0 .tm Bad Syntax: .Aq
.el \{\
.       ie \\n(aQ>=1 .nr aQ \\n(aQ+1
.       el \{\
.               mN \\$1
.               if \\n(mN .nr aQ \\n(aQ+1
.       \}
.       rZ \\$1
.       ie \\n(rZ .as aZ \\$1
.       el \{\
.               ie \\n(aQ<1 .as a\\n(aQ \\$1
.               el .as a\\n(aQ \\$1
.       \}
.       if \\n(.$>1 \{\
.               rZ \\$2
.               if \\n(rZ==0 \{\
.                       if \\n(aQ<1 \{\
.                               as a\\n(aQ \& \&
.                       \}
.               \}
.               aQ \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.       \}
.\}
..
.\" Angle Bracket Quoted Items
.de Aq
.nr aQ 0
.ie \\n(.$==0 \{\
.       ie "\\*(iM"" \&<\&>
.       el .as f1 \&<\&>
.\}
.el \{\
.       ie "\\*(iM"" \{\
.               ds f1 \&<
.               ds iM Aq
.       \}
.       el .as f1 \&<
.       aQ \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8
.       ie \\n(aQ>0 .\\*(a1 \\*(a2 \\*(a3 \\*(a4 \\*(a5 \\*(a6 \\*(a7 \\*(a8
.       el .as f1 \\*(a0
.       as f1 \&>
.       if !"\\*(aZ"" .as f1 \\*(aZ
.       if "\\*(iM"Aq" \{\
\&\\*(f1
.               ds f1
.               ds iM
.       \}
.       ds aZ
.       rm  a0 a1 a2 a3 a4 a5 a6 a7 a8
.       nr aQ 0
.\}
..
.\" macro Name test, return macro register value if true
.if \n(.g .ig
.de mN
.nr mN 0
.sW \\$1
.if \\n(sW==2 \{\
.	if \\n(\\$1 .nr mN \\n(\\$1
.\}
..
.if !\n(.g .ig
.de mN
.nr mN 0
.if \A'\\$1' \{\
.	sW \\$1
.	if \\n(sW==2 \{\
.		if \\n(\\$1 .nr mN \\n(\\$1
.	\}
.\}
..
.\" Punctuation test (using z registers), return 1 if true
.if \n(.g .ig
.de rZ
.nr rZ 0
.sW \\$1
.if \\n(sW==1 \{\
.	if \\n(z\\$1==1 \{\
.		nr rZ 1
.	\}
.\}
..
.if !\n(.g .ig
.de rZ
.nr rZ 0
.if \A'\\$1' \{\
.	sW \\$1
.	if \\n(sW==1 \{\
.		if \\n(z\\$1==1 \{\
.			nr rZ 1
.		\}
.	\}
.\}
..
.\"
.\" sW returns number of characters in a string
.if t \{\
.nr fW \w'\fC,'
.de sW
.nr sW \w'\fC\\$1'
.\}
.if n \{\
.nr fW \w'0'
.de sW
.nr sW \w'\\$1'
.\}
.ie \\n(sW>=\\n(fW \{\
.	ie \\n(sW%\\n(fW .nr sW (\\n(sW/\\n(fW)+1
.	el .nr sW \\n(sW/\\n(fW
.\}
.el .nr sW 0
..
.\"	Option Expression -
.\"	TODO - add line overflow check (right!)
.nr eP 0
.ds e1
.nr oE 0
.nr hP 0
.ds hP
.nr Ep 0
.de Op
.hy 0
.if "\\*(iM"" \{\
.	ds iM Op
.       ds f1 \&
.\}
.as f1 \&\\*(lB
.\" .tm Op:  \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.dO \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.ie !"\\$1"Cx" .oE
.el .nr oE \\n(oE+1
..
.\"
.\" just for mike, with every bite of garlic in mind (oops, i mean burp).
.\" dO: go dOwn an argument vector and test each argument to see if
.\" a macro name or punctuation. stash in respective place along
.\" with its arguments.
.nr oO 0
.nr oP 0
.nr aO 0
.de dO
.mN \\$1
.ie \\n(mN \{\
.       if \\n(oP  \{\
.               if \\n(hP \{\
.                       nr oZ 1
.                       oZ
.                       Oz
.               \}
.               if \\n(e1==1 \{\
.\\*(e1 \\*(e2 \\*(e3 \\*(e4 \\*(e5 \\*(e6 \\*(e7 \\*(e8 \\*(e9
.               \}
.               uO
.		if !(\\n(oO:\\n(aO) .as f1 \& \&
.	\}
.       ie "\\$1"Op" \{\
.               as f1 \&\\*(lB
.               nr aO \\n(aO+1
.       \}
.	el \{\
.               nr eP \\n(eP+1
.               ds e\\n(eP \\$1
.               nr e\\n(eP 1
.       \}
.\}
.el \{\
.\" .tm dO: $1: \\$1: eP \\n(eP e[\\n(eP]: \\*(e\\n(ePEE
.	rZ \\$1
.	ie \\n(rZ \{\
.\" .tm dO:rZ: $1: \\$1: eP \\n(eP e[\\n(eP]: \\*(e\\n(eP
.		nr hP \\n(hP+1
.		ds h\\n(hP \\$1
.	\}
.	el \{\
.\" .tm dO:word $1: \\$1: eP \\n(eP e[\\n(eP]: \\*(e\\n(ePEE
.		if \\n(eP==0:\\n(e\\n(eP==1 .nr eP \\n(eP+1
.		if \\n(eZ .as e\\n(eP \& \&
.		as e\\n(eP " \&\\$1
.\" .		ds e\\n(eP \&\\$1
.		nr eZ \\n(eZ+1
.	\}
.\}
.nr oP 1
.ie \\n(.$>1 \{\
.	dO \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.\}
.el \{\
.     ie \\n(e1 \{\
.\\*(e1 \\*(e2 \\*(e3 \\*(e4 \\*(e5 \\*(e6 \\*(e7 \\*(e8 \\*(e9
.	\}
.     el \{\
.	as f1 \\*(e1
.	\}
.\}
..
.\" handle old style arguments such as the arg -Idir
.\" in adb, .Oo is a toggle.
.de Oo
.ie \\n(oO .nr oO 0
.el .nr oO 1
..
.\" stash punctuation
.de oZ
.if \\n(hP>=\\n(oZ \{\
.	nr eP \\n(eP+1
.	ds e\\n(eP \\*(h\\n(oZ
.	nr oZ \\n(oZ+1
.	oZ
.\}
..
.\" clean up punctuation vector
.de Oz
.if \\n(hP>0 \{\
.	rm h\\n(hP
.	nr hP \\n(hP-1
.	Oz
.\}
..
.\" uO: go back up created vector cleaning it up along the way
.de uO
.if \\n(eP>0 \{\
.	rm e\\n(eP
.	rr e\\n(eP
.	nr eP \\n(eP-1
.	nr oP 0
.	nr eZ 0
.	uO
.\}
..
.\" option end
.de oE
.uO
.ie \\n(hP \{\
.       as f1 \\*(rB\\*(h1\\*(h2\\*(h3
.	Oz
.	nr oZ 0
.\}
.el \{\
.	as f1 \\*(rB
.\}
.ie "\\*(iM"Op" \{\
.	if \\n(aO .aO
.if t \{\
.	if (\\n(.lu-\\n(.ku-\\n(.ou-(2*\\n(fWu))<\w'\fC\\*(f1'u .br
.\}
.if n \{\
.	nr aa \w'\\*(f1'u
.\" .	nr qq \\n(.lu-\\n(.ku-\\n(.ou
.\" \&aa == \\n(aa, f1==\\*(f1, qq==\\n(qq
.	if (\\n(.lu-\\n(.ku-\\n(.ou-\\n(aau)<=(8*\\n(fWu) .br
.\}
\&\\*(f1
.	ds iM
.	ds f1
.	hy
.\}
.el .nr oE \\n(oE-1
..
.de aO
.as f1 \\*(rB
.nr aO \\n(aO-1
.if \\n(aO >0 .aO
..
.\"
.de Xr
.if \\n(.$<=1 \{\
.	ie \\n(.$==1 \{\
.		if !"\\*(iM"" .as f1 \&\\*(xR\\$1\fP
.		if "\\*(iM"" \&\\*(xR\\$1\fP
.	\}
.	el .tm Xr Usage: .Xr manpage_name [section#] \\*(Pu
.\}
.if \\n(.$==2 \{\
.	rZ \\$2
.	ie "\\*(iM"" \{\
.		ie \\n(rZ \&\\*(xR\\$1\fP\\$2
.		el \&\\*(xR\\$1\fP(\\$2)
.	\}
.	el \{\
.		ie \\n(rZ .as f1 \&\\*(xR\\$1\fP\\$2
.		el .as f1 \&\\*(xR\\$1\fP(\\$2)
.	\}
.\}
.if \\n(.$>=3 \{\
.	rZ \\$2
.	ie \\n(rZ \{\
.		ie !"\\*(iM"" .as f1 \&\\*(xR\\$1\fP\\$2\\$3\\$4\\$5\\$6\\$7\\$8
.		el \&\\*(xR\\$1\fP\\$2\\$3\\$4\\$5\\$6\\$7\\$8
.	\}
.	el \{\
.		rZ \\$3
.		ie \\n(rZ \{\
.			if !"\\*(iM"" \{\
.			     as f1 \&\\*(xR\\$1\fP(\\$2)\\$3\\$4\\$5\\$6\\$7\\$8
.			\}
.			if "\\*(iM"" \{\
\&\\*(xR\\$1\fP(\\$2)\\$3\\$4\\$5\\$6\\$7\\$8
.			\}
.		\}
.		el \{\
.			tm rZ = \\n(rZ  the arg is \\$3
.			tm Xr-XX Usage: .Xr manpage_name [section#] \\*(Pu
.		\}
.	\}
.\}
..
.\"
.\"
.de Ex
.tm Ex defunct, Use .Dl: \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
..
.\" Display (one) Line of text.
.de Dl
.ie "\\*(iM"" \{\
'	ta .5i 1i 1.5i 2i 2.5i 3i 3.5i 4i 4.5i 5i 5.5i 6i 6.5i
.	in \\n(.iu+\\n(Dsu
.	mN \\$1
.	ie \\n(mN .\\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.	el \{\
.		nr cF \\n(.f
.\"	 Literal font is none specified
\&\\*(lI\\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.		ft \\n(cF
.	\}
.	in \\n(.iu-\\n(Dsu
.\}
.el \{\
.	mN \\$1
.	ie \\n(mN .\\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8
.	el \{\
.		nr cF \\n(.f
.		ds f1 \&\\*(lI\\&\\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8
.		as f1 \&\f\\n(cF
.	\}
.\}
..
.\"
.\"
.\" user set Tagged Paragraph Width (used in both Dp and Tp)
.de Tw
.ie \\n(.$==0 \{\
.	nr aa 0
.\}
.el \{\
.	mN \\$1
.	ie \\n(sW>2 \{\
.		nr tW (\\n(sW+3)*\\n(fWu)
.	\}
.	el \{\
.		ie \\n(mN .nr tW \\n(mN
.		el .nr tW \\$1
.	\}
.	nr tF 1
.\}
..
.\"
.de Dw
.Tw \\$1
..
.\"
.de Di
.ie \\n(.$==0 \{\
.	nr tI \\n(Dsu
.\}
.el \{\
.	sW \\$1
.	if \\n(sW>=2 \{\
.		nr tI \\$1u
.	\}
.	if \\n(sW<2 \{\
.		if "\\$1"L" \{\
.			nr tI 0
.		\}
.	\}
.\}
..
.\" tagged paragraph
.\" initialize baby stack variables
.nr np 0
.nr p1 0
.ds s\n(np
.\"
.de Tp
.ie "\\$1"" .pE p s np
.el \{\
.	ds iM Tp
.	mN \\$1
.	ie \\n(tF \{\
.		ds tC Tw
.		nr tC 1
.		nr tF 0
.	\}
.	el \{\
.		if !"Tw"\\*(s\\n(np" \{\
.			ie \\n(mN \{\
.				ds tC \\$1
.				nr tW \\n(mN
.			\}
.			el \{\
.				ds tC Tx
.				nr tW \\n(Tx
.			\}
.			if !"\\*(tC"\\*(s\\n(np" .nr tC 1
.		\}
.	\}
.	sp \\n(vVu
.	if !\\n(cR .ne 2
.	if \\n(tC \{\
.		nr np \\n(np+1
.		nr p\\n(np \\n(tW
.		ds s\\n(np \\*(tC
.		nr tC 0
.		ds tC
.		in \\n(.iu+\\n(p\\n(npu
.	\}
.	ie \\n(mN \{\
.		ds f1
.		\\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.		if !"\\$1"Cx" .pT st p np
.	\}
.	el \{\
.		br
.		ev 1
.		fi
.		di Td
\&\\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.		br
.		di
.		ev
.		na
.		ds tD \\*(Td\\
.		pT di p np
.	\}
.\}
..
.\"
.\"
.\" Complex Expression Macro
.\"
.\"	 TODO: add length across line boundary check (like Li)
.de Cx
.hy 0
.ie \\n(.$==0 \{\
.	if "\\*(iM"Cx" \{\
.		ds iM
.		if \\n(oE .oE
\&\\*(f1
.		ds f1
.	\}
.	if "\\*(iM"Tp" .pT st p np
.	if "\\*(iM"Dp" .pT st q mp
.\}
.el \{\
.	if "\\*(iM"" \{\
.		ds iM Cx
.		ds f1 \&
.	\}
.	mN \\$1
.\" Here are the args: '\\$1'  '\\$2'  '\\$3'  '\\$4'
.	ie \\n(mN .\\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.	el \{\
.		as f1 \&\\$1
.		if \\n(.$>1 .Cx \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.	\}
.\}
..
.\" Prefix string in default font to content specified string
.de Pf
.Cx \\$1
.\\$2 \\$3 \\$4 \\$5
.Cx
..
.\" Suffix string in default font to content specified string
.de Sf
.Cx \\$1 \\$2
.Cx \\$3
.Cx
..
.\" Simple Option Begin
.de Ob
.hy 0
.ie "\\*(iM"" \{\
.	ev 2
.	fi
.	di oB
.\}
.el \{\
.tm shouldn't be here
.	as f1 \&[
.	mN \\$1
.	ie \\n(mN .\\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.	el \{\
.		as f1 \&\\$1
.		if \\n(.$>1 .Oc \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.	\}
.\}
..
.de Oc
.as f1 \&\\$1
.if \\n(.$>1 .Oc \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
..
.de Oe
.hy 0
.ie "\\*(iM"" \{\
.	br
.	di
.	ev
.	ds bO \\*(oB\\
\&[\\*(bO\&]
.\}
.el \{\
.	as f1 \&]
.\}
..
.\" White space for Cx
.de Ws
.Cx \&\ \&
..
.\" tagged paragraph
.\" initialize baby stack variables
.nr mp 0
.nr q1 0
.ds r\n(np
.\"
.\" Complex Dp tag
.de Dc
.Dp Cx \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8
..
.\" Complex Tp tag
.de Tc
.Tp Cx \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8
..
.\" Tag with a flag and an argument with a space
.de Ta
.if "\\$2"" \{\
.	Tp Fl \\$1
.\}
.el \{\
.	Tp Fl \\$1
.	Cx \&\ \&
.	Ar \\$2 \\$3
.	Cx
.\}
..
.de Da
.Dp Cx Fl \\$1
.Ws
.Ar \\$2 \\$3
.Cx
..
.de To
.Tp Cx Fl \\$1
.Ar \\$2 \\$3
.Cx
..
.de Do
.Dp Cx Fl \\$1
.Ar \\$2 \\$3
.Cx
..
.\" Blended tag toggle
.de Bt
.ie \\n(tP==0 .nr tP 1
.el .nr tP 0
..
.\" Bullet paragraph
.de Bu
.Tp Sy \&\(bu
..
.\" Display tagged paragraph
.de Dp
.ie "\\$1"" \{\
.	pE q r mp
.	sp \\n(vVu
.\}
.el \{\
.       ds iM Dp
.       mN \\$1
.       ie \\n(tF \{\
.               ds tC Tw
.               nr tC 1
.               nr tF 0
.       \}
.       el \{\
.               if !"Tw"\\*(r\\n(mp" \{\
.                       ie \\n(mN \{\
.                               ds tC \\$1
.                               nr tW \\n(mN
.                       \}
.                       el \{\
.                               ds tC Tx
.                               nr tW \\n(Tx
.                       \}
.                       if !"\\*(tC"\\*(r\\n(mp" .nr tC 1
.               \}
.       \}
.       if !\\n(cR .ne 2
.       if \\n(tC \{\
.               nr mp \\n(mp+1
.               nr q\\n(mp \\n(tW
.               ds r\\n(mp \\*(tC
.               nr tC 0
.               ds tC
.		ie \\n(tIu==\\n(Dsu .nr i\\n(mp \\n(Dsu
.		el \{\
.			nr i\\n(mp \\n(tIu
.			nr tI \\n(Dsu
.		\}
.              	in \\n(.iu+\\n(i\\n(mpu
.		sp \\n(vVu
.		in \\n(.iu+\\n(\\q\\n(mpu
.       \}
.       ie \\n(mN \{\
.               \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.               if !"\\$1"Cx" .pT st q mp
.       \}
.       el \{\
.               br
.               ev 1
.               fi
.               di Td
\&\\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.               br
.               di
.               ev
.               na
.               ds tD \\*(Td\\
.               pT di q mp
.       \}
.\}
..
.\"
.\" .pE number_stack string_stack counter
.de pE
.ie "\\$3"mp" \{\
.	in \\n(.iu-(\\n(\\$1\\n(\\$3u)-(\\n(i\\n(mpu)
.	rr i\\n(mp
.\}
.el .in \\n(.iu-\\n(\\$1\\n(\\$3u
.\" .in \\n(.iu-\\n(\\$1\\n(\\$3u
.if \\n(\\$3<=0 .tm Extraneous call .Tp or .Dp
.rr \\$1\\n(\\$3
.rm \\$2\\n(\\$3
.nr \\$3 \\n(\\$3-1
.ds iM
..
.\"
.\" .pT [st or di] number_stack counter
.de pT
.ie "\\$1"st" \{\
.	nr bb \\n(\\$2\\n(\\$3u
.	ti -\\n(bbu
.	ie (\\n(\\$2\\n(\\$3u-2n)<=\w'\\*(f1'u \{\&\\*(f1\\*(tP
.		if \\n(tP==0 .br
.	\}
.	el \\*(f1\h'|\\n(\\$2\\n(\\$3u'\c
.\}
.el \{\
.       ti -\\n(\\$2\\n(\\$3u
.	ie (\\n(\\$2\\n(\\$3u-2n)<=\\n(dlu \{\&\\*(tD\\*(tP
.	       if !\\n(tP .br
.	\}
.	el \\*(tD\h'|\\n(\\$2\\n(\\$3u'\c
.	if t 'ad
.\}
.	ds iM
.	ds f1
'fi
..
.\"
.\" The new SH
.\"
.de Sh
.\" set Sh state off, check for list state before calling indent (.In)
.nr nS 0
.nr sE 0
.ie "\\$1"NAME" \{\
.\"	name state on, housekeep (headers & footers)
.	hK
'	in 0
.\}
.el \{\
.	if "\\$1"SYNOPSIS" .nr nS 1
.	in 0
.\}
.pL
'sp
.ns
.ta .5i 1i 1.5i 2i 2.5i 3i 3.5i 4i 4.5i 5i 5.5i 6i 6.5i
.if !\\n(cR .ne 3
'fi
\&\fB\\$1 \|\\$2 \|\\$3 \|\\$4 \|\\$5 \|\\$6 \|\\$7 \|\\$8 \|\\$9
\&\fP\&
.in \\n(.iu+\\n(Tiu
.if "\\$1"SEE" .nr sE 1
.ns
..
.\"
.\" Nd minus sign for an en dash used in .Sh Name
.de Nd
\&\-\& \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
..
.de Ss
.sp
.ti -.25i
\&\fB\\$1 \|\\$2 \|\\$3 \|\\$4 \|\\$5 \|\\$6 \|\\$7 \|\\$8 \|\\$9
\&\fP\&
.ta .5i 1i 1.5i 2i 2.5i 3i 3.5i 4i 4.5i 5i 5.5i 6i 6.5i
.if !\\n(cR .ne 2
.br
..
.\"	.if "\\$1"Ss" .in \\n(.iu+\\n(sIu
.\"..
.\"
.\"
.\" Column Macro
.\"
.hy 0
.de Cw
.ie \\n(.$==0 \{\
.	br
.	in \\n(.iu-\\n(eWu
.	ta .5i 1i 1.5i 2i 2.5i 3i 3.5i 4i 4.5i 5i 5.5i 6i 6.5i
.\}
.el \{\
.	Pp
.	if \\n(.$==1 \{\
.		ta \w'\\$1    'u
.		nr eW \w'\\$1    'u
'		in \\n(.iu+\\n(eWu
.	\}
.	if \\n(.$==2 \{\
.		ta \w'\\$1    'u +\w'\\$2    'u
.		nr eW \w'\\$1    'u+\w'\\$2    'u
'		in \\n(.iu+\\n(eWu
.	\}
.	if \\n(.$==3 \{\
.		ta \w'\\$1    'u +\w'\\$2    'u +\w'\\$3    'u
.		nr eW \w'\\$1    'u+\w'\\$2    'u+\w'\\$3    'u
'		in \\n(.iu+\\n(eWu
.	\}
.	if \\n(.$==4 \{\
.	ta \w'\\$1    'u +\w'\\$2    'u +\w'\\$3    'u +\w'\\$4    'u
.	nr eW \w'\\$1    'u+\w'\\$2    'u+\w'\\$3    'u +\w'\\$4    'u
'	in \\n(.iu+\\n(eWu
.	\}
.	if \\n(.$==5 \{\
.ta \w'\\$1    'u +\w'\\$2    'u +\w'\\$3    'u +\w'\\$4    'u +\w'\\$5    'u
.nr eW \w'\\$1    'u +\w'\\$2    'u +\w'\\$3    'u +\w'\\$4    'u +\w'\\$5    'u
'	in \\n(.iu+\\n(eWu
.	\}
.\}
..
.de Cl
.ti -\\n(eWu
.mN \\$1
.ie \\n(mN .\\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.el \\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
..
.nr dQ 0
.de Ds
.ie !"\\$1"" \{\
.	mN d\\$1
.	if \\n(mN \{\
.		nr dQ \\n(dQ+1
.		d\\$1
.	\}
.\}
.el .br
.nf
..
.de Df
.ie !"\\$1"" \{\
.       mN d\\$1
.       if \\n(mN \{\
.               nr dQ \\n(dQ+1
.               d\\$1
.       \}
.\}
.el .br
..
.de Dn
\\$1 \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.nf
..
.de dI
.nr d\\n(dQ \\n(dIu
.in \\n(.iu+\\n(dIu
..
.de dC
.nr d\\n(dQ (\\n(.l-\\n(.i)/4u
.in \\n(.iu+\\n(d\\n(dQu
..
.de dR
.nr d\\n(dQ (\\n(.l/3)u
.in \\n(.iu+\\n(d\\n(dQu
..
.de dL
.nr aa 0
..
.de De
.br
.if \\n(d\\n(dQ \{\
.	in \\n(.iu-\\n(d\\n(dQu
.	rr d\\n(dQ
.	nr dQ \\n(dQ-1
.\}
.fi
..
.\"
.de Fn
.ie \\n(.$==0 \{\
.	tm Usage: .Fn function_name function_arg(s) ... \\*(Pu
.\}
.el \{\
.	nr cF \\n(.f
.	ie \\n(.$==1 .ds f1 \&\\*(nM\\$1\fP\\*(lP\fP\\*(rP\fP
.	el \{\
.		ds f1 \\*(nM\\$1\fP\\*(lP
.		nr aa 0
.		rC \\$2 \\$3 \\$4 \\$5 \\$6 \\$7 \\$8 \\$9
.	\}
.	if "\\*(iM"" \{\\&\\*(f1
.		ds f1
.	\}
.\}
..
.\"
.de rC
.rZ \\$1
.ie \\n(rZ \{\
.	as f1 \f\\n(cF\\*(rP\f\\n(cF\\$1\\$2\\$3\\$4\\$5\\$6\\$7
.\}
.el \{\
.	ie \\n(aa .as f1 \fP, \\*(aR\\$1
.	el .as f1 \\*(aR\\$1
.	nr aa 1
.	ie \\n(.$>1 .rC \\$2 \\$3 \\$4 \\$5 \\$6 \\$7
.	el .as f1 \fP\\*(rP\fP
.\}
..
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set filetype=nroff textwidth=72:
