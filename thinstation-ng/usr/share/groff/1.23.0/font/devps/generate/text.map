#
# Predefined groff symbols for textual fonts.
#
a14 rh
a a
A A
aacute 'a
Aacute 'A
acircumflex ^a
Acircumflex ^A
acute aa
adieresis :a
Adieresis :A
ae ae
AE AE
agrave `a
Agrave `A
aleph Ah
alpha *a
Alpha *A
ampersand &
angle /_
angleleft la
angleright ra
approxequal ~~
approxequal ~=
aring oa
Aring oA
arrowboth <>
arrowdblboth hA
arrowdbldown dA
arrowdblleft lA
arrowdblright rA
arrowdblup uA
arrowdown da
arrowhorizex an
arrowleft <-
arrowright ->
arrowupdn va
arrowup ua
arrowvertex arrowvertex
asciicircum ha
asciitilde ti
asterisk *
asteriskmath **
at @
at at
atilde ~a
Atilde ~A
backslash \
backslash rs
bar |
bar ba
b b
B B
beta *b
Beta *B
braceex barex
braceex braceex
braceex braceleftex
braceex bracerightex
braceex bv
braceleft {
braceleftbt braceleftbt
braceleftbt lb
braceleft lC
braceleftmid braceleftmid
braceleftmid lk
bracelefttp bracelefttp
bracelefttp lt
braceright }
bracerightbt bracerightbt
bracerightbt rb
bracerightmid bracerightmid
bracerightmid rk
braceright rC
bracerighttp bracerighttp
bracerighttp rt
bracketleft [
bracketleftbt bracketleftbt
bracketleftbt lf
bracketleftex bracketleftex
bracketleft lB
bracketlefttp bracketlefttp
bracketlefttp lc
bracketright ]
bracketrightbt bracketrightbt
bracketrightbt rf
bracketrightex bracketrightex
bracketright rB
bracketrighttp bracketrighttp
bracketrighttp rc
breve ab
brokenbar bb
bullet bu
cacute 'c
Cacute 'C
caron ah
carriagereturn CR
c c
C C
ccedilla ,c
Ccedilla ,C
cedilla ac
cent ct
checkmark OK
chi *x
Chi *X
circle ci
circlemultiply c*
circleplus c+
circumflex ^
circumflex a^
club CL
colon :
comma ,
congruent =~
copyright co
currency Cs
daggerdbl dd
dagger dg
d d
D D
degree de
delta *d
Delta *D
diamond DI
dieresis ad
divide tdi
dollar $
dollar Do
dotaccent a.
dotlessi .i
dotlessj .j
dotmath md
eacute 'e
Eacute 'E
ecircumflex ^e
Ecircumflex ^E
edieresis :e
Edieresis :E
e e
E E
egrave `e
Egrave `E
eight 8
element mo
emdash em
emptyset es
endash en
epsilon *e
Epsilon *E
equal =
equivalence ==
eta *y
Eta *Y
Eth -D
eth Sd
Euro Eu
exclam !
exclamdown r!
existential te
f f
F F
ff ff
ffi Fi
ffl Fl
fi fi
five 5
fiveeighths 58
fl fl
florin Fn
four 4
fraction f/
gamma *g
Gamma *G
germandbls ss
g g
G G
gradient gr
grave ga
greater >
greaterequal >=
guillemotleft Fo
guillemotright Fc
guilsinglleft fo
guilsinglright fc
heart HE
h h
H H
hungarumlaut a"
hyphen -
hyphen hy
iacute 'i
Iacute 'I
icircumflex ^i
Icircumflex ^I
idieresis :i
Idieresis :I
Ifraktur Im
igrave `i
Igrave `I
i i
I I
ij ij
IJ IJ
infinity if
# the next line overrides the (old) PUA value of the AGL
integralex u23AE
integral integral
integral is
intersection ca
iota *i
Iota *I
j j
J J
kappa *k
Kappa *K
k k
K K
lambda *l
Lambda *L
less <
lessequal <=
l l
L L
logicaland AN
logicalnot tno
logicalor OR
lozenge lz
lslash /l
Lslash /L
macron a-
minus \-
minute fm
m m
M M
mu1 mc
multiply tmu
mu *m
Mu *M
# if there is "mu1" glyph in the font - comment out the following line
mu mc
nine 9
n n
N N
notelement nm
notequal !=
notsubset nb
ntilde ~n
Ntilde ~N
numbersign #
numbersign sh
nu *n
Nu *N
oacute 'o
Oacute 'O
ocircumflex ^o
Ocircumflex ^O
odieresis :o
Odieresis :O
oe oe
OE OE
ogonek ho
ograve `o
Ograve `O
omega1 +p
omega *w
Omega *W
omicron *o
Omicron *O
one 1
oneeighth 18
onehalf 12
onequarter 14
onesuperior S1
o o
O O
ordfeminine Of
ordmasculine Om
oslash /o
Oslash /O
otilde ~o
Otilde ~O
overline rn
paragraph ps
parenleft (
parenleftbt parenleftbt
parenleftex parenleftex
parenlefttp parenlefttp
parenright )
parenrightbt parenrightbt
parenrightex parenrightex
parenrighttp parenrighttp
partialdiff pd
percent %
period .
periodcentered pc
perpendicular pp
perthousand %0
phi1 +f
phi *f
Phi *F
pi *p
Pi *P
plus +
plusminus t+-
p p
P P
product product
propersubset sb
propersuperset sp
proportional pt
psi *q
Psi *Q
q q
Q Q
question ?
questiondown r?
quotedbl "
quotedblbase Bq
quotedbl dq
quotedblleft lq
quotedblright rq
quoteleft `
quoteleft oq
quoteright '
quoteright cq
quotesinglbase bq
quotesingle aq
radicalex radicalex
radical sqrt
radical sr
reflexsubset ib
reflexsuperset ip
registered rg
Rfraktur Re
rho *r
Rho *R
ring ao
r r
R R
scaron vs
Scaron vS
second sd
section sc
semicolon ;
seven 7
seveneighths 78
SF110000 br
sigma1 ts
sigma *s
Sigma *S
similar ap
six 6
slash /
slash sl
spade SP
s s
S S
sterling Po
suchthat st
summation sum
tau *t
Tau *T
therefore 3d
therefore tf
theta1 +h
theta *h
Theta *H
thorn Tp
Thorn TP
three 3
threeeighths 38
threequarters 34
threesuperior S3
tilde ~
tilde a~
trademark tm
t t
T T
two 2
twosuperior S2
uacute 'u
Uacute 'U
ucircumflex ^u
Ucircumflex ^U
udieresis :u
Udieresis :U
ugrave `u
Ugrave `U
underscore _
uni03F5 +e
uni210F -h
uni210F hbar
uni21D5 vA
uni2210 coproduct
uni2213 -+
uni2243 |=
uni2262 ne
uni226A >>
uni226B <<
uni2285 nc
uni25A1 sq
uni261C lh
union cu
universal fa
upsilon *u
Upsilon *U
u u
U U
v v
V V
weierstrass wp
w w
W W
xi *c
Xi *C
x x
X X
yacute 'y
Yacute 'Y
ydieresis :y
Ydieresis :Y
yen Ye
y y
Y Y
zcaron vz
Zcaron vZ
zero 0
zeta *z
Zeta *Z
z z
Z Z
