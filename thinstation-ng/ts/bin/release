#!/bin/bash
set -e
set -x

user=$2
ver=$1

mk_devstat()
{
	# Make DevStation
	wkhtmltoimage --format svg --width 1920 --height 1080 /build/packages/lightdm/build/banner.html /build/packages/lightdm/build/banner.svg
	cd /build
	cp conf/devstation/* .
	stb --allmodules

	rm -rf /tmp/{thindev,thindev-default.tar.xz,image.md5}
	mkdir /tmp/thindev
	cp /build/boot-images/initrd/{initrd,vmlinuz,lib.squash} /tmp/thindev/.
	cp /build/boot-images/initrd/image.md5 /tmp/image-$TSVER.md5
	cd /tmp/thindev
	mv lib.squash lib.update
	tar -c * |xz > ../thindev-default-$TSVER.tar.xz
	cd ..
#	scp -o StrictHostKeyChecking=no image-*.md5 ${user}@frs.sourceforge.net:/home/<USER>/project/thinstation/DevStation-Source/
#	scp -o StrictHostKeyChecking=no thindev-default-*.tar.xz ${user}@frs.sourceforge.net:/home/<USER>/project/thinstation/DevStation-Source/
       scp -o StrictHostKeyChecking=no image-*.md5 ${user}@ftp.thinstation.us:public_html/
       scp -o StrictHostKeyChecking=no thindev-default-*.tar.xz ${user}@ftp.thinstation.us:public_html/

}

mk_installer()
{
	# Make HW lister
	cd /build
	cp conf/hwlister/* .
#	sed -i -e 's/^usermode/#usermode/g' /build/packages/switchuser/dependencies
	stb --allmodules
#	sed -i -e 's/^#usermode/usermode/g' /build/packages/switchuser/dependencies

	mkdir -p /build/boot-images/hw/boot/hwlister
	mkdir -p /build/boot-images/hw/boot/grub2
	cp /build/boot-images/initrd/{vmlinuz,initrd} /build/boot-images/hw/boot/hwlister/.
	cp -a  /build/boot-images/templates/grub/devstation /build/boot-images/hw/boot/grub2/.
	# Make Installer
	name=TS-${ver}-Installer-`date +%m%d`.iso
	cp conf/installer/* .
	stb --allmodules
	cp /build/boot-images/grub/thinstation-efi.iso /tmp/$name
	scp -o StrictHostKeyChecking=no /tmp/$name ${user}@ftp.thinstation.us:public_html/
}

mk_livecd(){
        # Make LiveCD
	cd /build
        cp conf/LiveCD/* .
        stb --allmodules
	scp -o StrictHostKeyChecking=no /build/boot-images/grub/thinstation-efi.iso ${user}@frs.sourceforge.net:/home/<USER>/project/thinstation/LiveCD/TS-Multiclient-Desktop-LiveCD-Demo.iso
}

case $3 in
	-d) mk_devstat;;
	-i) mk_installer;;
	-l) mk_livecd;;
	*) mk_devstat; mk_installer;;
esac
