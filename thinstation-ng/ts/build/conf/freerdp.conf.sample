# Global FreeRDP commands affecting all session and icon connections
# These modify /etc/cmd/freerdp.global and /etc/cmd/freerdp.window
#  FREERDP_GETUSER=off		# OFF removes user/password prompt
#  FREERDP_SMARTCARD=on		# Equiv to adding /smartcard
#  FREERDP_SMARTCARD=00		# Equiv to adding /smartcard:<option>
#  FREERDP_SOUND=sys:pulse	# Equiv to adding /sound:<option>
#  FREERDP_CERTIGNORE=off	# ON is adding /cert:ignore
#  FREERDP_CERT=deny		# Equiv to adding /cert:<option>

#For the full list options, refer to the xfreerdp help
    
#SESSION_#_TITLE="Big Bad Server Donald"
#SESSION_#_TYPE=freerdp
#SESSION_#_FREERDP_SERVER=***********
#SESSION_#_FREERDP_OPTIONS="/u:user /p:password /bpp:16"
#SESSION_#_AUTOSTART=Off

# /u:'' gives a blank user name
#SESSION_#_TITLE="Big Bad Server Road Runner"
#SESSION_#_TYPE=freerdp
#SESSION_#_FREERDP_SERVER=***********
#SESSION_#_FREERDP_OPTIONS="/u:'' /bpp:8"
#SESSION_#_AUTOSTART=Off

#Example on USB and CD redirection for freerdp
#SESSION_#_TITLE="Remote Desktop"
#SESSION_#_TYPE=freerdp
#SESSION_#_FREERDP_SERVER=***********
#SESSION_#_FREERDP_OPTIONS="+drives /multimedia /rfx +fonts +aero"
#SESSION_#_AUTOSTART=Off

#FREERDP_SELECT_SERVER=true
#FREERDP_SERVER_0="Big_Bad_Server_Donald"
#FREERDP_SERVER_0_OPTIONS="/d:thinstation.local"
#FREERDP_KNOWN_HOST_0="Big_Bad_Server_Donald 7a:a4:af:8a:98:da:50:52:ba:33:8d:e6:15:4e:49:7b:b3"
#FREERDP_SERVER_1="Big_Bad_Server_Road_Runner"
#FREERDP_SERVER_1_OPTIONS="/cert:ignore /d:thinstation.local"

SESSION_#_TITLE="Big Bad Server Donald"
SESSION_#_TYPE=freerdp
SESSION_#_AUTOSTART=ON
SESSION_#_FREERDP_SERVER=***********
SESSION_#_FREERDP_USER=testuser		#Seed the user field
SESSION_#_FREERDP_PASS_ENABLE=true	#Force asking for a password
SESSION_#_FREERDP_OPTIONS="/gt:<gateway ip> +drives /multimedia /rfx +fonts +aero"
