#!/bin/bash

new_file() {
    if [ "$(stat -c '%X' "$1")" -ne "$epoch_time" ]; then
        echo "$1"
    fi
}
export -f new_file

stamp() {
    touch -a -c -h -t "$timestamp" "$1"
}
export -f stamp

# Initialize variables
procs=$(nproc)
epoch_time=1700000000
timestamp=$(date -d @"$epoch_time" +%Y%m%d%H%M.%S)
scan_dirs="usr lib lib64 bin sbin opt etc share var libexec"

export timestamp
export epoch_time

# Update timestamps
for dir in $scan_dirs; do
    if [ -d "/$dir" ]; then
        find "/$dir" ! -type d -print0 | xargs -0 -P "$procs" -I {} bash -c 'stamp "$@"' _ {}
    else
        echo "Warning: Directory /$dir does not exist, skipping."
    fi
done

# Run user-specified command
#"$@"
echo "This is a test." | lp

# Identify new files
for dir in $scan_dirs; do
    if [ -d "/$dir" ]; then
        find "/$dir" ! -type d -print0 | xargs -0 -P "$procs" -I {} bash -c 'new_file "$@"' _ {}
    fi
done
