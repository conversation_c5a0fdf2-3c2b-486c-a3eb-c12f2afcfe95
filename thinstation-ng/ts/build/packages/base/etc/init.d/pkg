#! /bin/sh

. `dirname $0`/common

PKGDIR=/tmp/pkg

case "$1" in
init)
    if ! pkg_initialized $PACKAGE; then
        pkg_set_init_flag $PACKAGE
	PKG_PACKAGES="$PKG_PACKAGES $PKG_PACKAGES1 $PKG_PACKAGES2 $PKG_PACKAGES3 $PKG_PACKAGES4 $PKG_PACKAGES5 $PKG_PACKAGES6 $PKG_PACKAGES7 $PKG_PACKAGES8"
	PKG_PACKAGES_BG="$PKG_PACKAGES_BG $PKG_PACKAGES_BG1 $PKG_PACKAGES_BG2 $PKG_PACKAGES_BG3 $PKG_PACKAGES_BG4 $PKG_PACKAGES_BG5 $PKG_PACKAGES_BG6 $PKG_PACKAGES_BG7 $PKG_PACKAGES_BG8"
	MOD_PACKAGES="$MOD_PACKAGES $MOD_PACKAGES1 $MOD_PACKAGES2 $MOD_PACKAGES3 $MOD_PACKAGES4 $MOD_PACKAGES5 $MOD_PACKAGES6 $MOD_PACKAGES7 $MOD_PACKAGES8"

	if [ -n "$DEBUG_NETWORK" ] ; then
		debug="-d"
	fi

#	mkdir $PKGDIR
	if [ "$PKG_PACKAGES_BG" != "        " ] ; then
		echo_log '\n\nLoading extra packages (background)... ' $debug
		# run the pkg load in background via setsid
		(setsid sh -c "for package in $PKG_PACKAGES_BG; do pkg load \$package pkg $PKG_PREFIX; done" 2>&1 >/dev/null) &
		echo_log "\n" $debug
	fi
	if [ "$PKG_PACKAGES" != "        " ] ; then
		echo_log '\n\nLoading extra packages... ' $debug
		for package in $PKG_PACKAGES; do
			pkg load $package pkg $PKG_PREFIX
		done
		if [ -e $PKGDIR/dependencies ] ; then
			$PKGDIR/dependencies
		fi
		echo_log "\n" $debug
		depmod 2>/dev/null
	fi
	if [ "$MOD_PACKAGES" != "        " ] ; then
		echo_log '\n\nLoading extra modules... \n' $debug
		for package in $MOD_PACKAGES; do
			pkg load $package mpkg $MOD_PREFIX
		done
		depmod 2>/dev/null
		echo_log '\nInstalling extra modules...' $debug
		if [ -e /etc/modules.mpkg ] ; then
		  (cat /etc/modules.mpkg; echo) | # make sure there is a LF at the end
		  while read module args
		  do
			case "$module" in
				\#*|"") continue ;;
			esac
			echo -n "$module - " >> $LOGFILE
			if ! modprobe "$module" $args >> $LOGFILE 2>&1; then
				echo "Device $module not Found, Removing" >> $LOGFILE
				modprobe -r "$module" >> $LOGFILE
			fi
		  done
	  	fi
		echo_log "\n" $debug

	fi
    fi
    ;;
help)
    echo "Usage: $0 init"
    ;;
  *)
    exit 0
    ;;
esac

exit 0
