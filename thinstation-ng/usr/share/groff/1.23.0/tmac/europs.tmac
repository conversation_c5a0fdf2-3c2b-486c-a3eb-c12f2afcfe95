.\" -*- nroff -*-
.\"
.\" europs.tmac
.
.do char \[eu] \f[EURO]\N'0'
.
.do if F AB   .do fschar AB   \[Eu] \f[EURO]\N'1'
.do if F ABI  .do fschar ABI  \[Eu] \f[EURO]\N'3'
.do if F AI   .do fschar AI   \[Eu] \f[EURO]\N'2'
.do if F AR   .do fschar AR   \[Eu] \f[EURO]\N'0'
.do if F BMB  .do fschar BMB  \[Eu] \f[EURO]\N'5'
.do if F BMBI .do fschar BMBI \[Eu] \f[EURO]\N'7'
.do if F BMI  .do fschar BMI  \[Eu] \f[EURO]\N'6'
.do if F BMR  .do fschar BMR  \[Eu] \f[EURO]\N'4'
.do if F CB   .do fschar CB   \[Eu] \f[EURO]\N'13'
.do if F CBI  .do fschar CBI  \[Eu] \f[EURO]\N'15'
.do if F CI   .do fschar CI   \[Eu] \f[EURO]\N'14'
.do if F CR   .do fschar CR   \[Eu] \f[EURO]\N'12'
.do if F HB   .do fschar HB   \[Eu] \f[EURO]\N'9'
.do if F HBI  .do fschar HBI  \[Eu] \f[EURO]\N'11'
.do if F HI   .do fschar HI   \[Eu] \f[EURO]\N'10'
.do if F HR   .do fschar HR   \[Eu] \f[EURO]\N'8'
.do if F HNB  .do fschar HNB  \[Eu] \f[EURO]\N'9'
.do if F HNBI .do fschar HNBI \[Eu] \f[EURO]\N'11'
.do if F HNI  .do fschar HNI  \[Eu] \f[EURO]\N'10'
.do if F HNR  .do fschar HNR  \[Eu] \f[EURO]\N'8'
.do if F NB   .do fschar NB   \[Eu] \f[EURO]\N'5'
.do if F NBI  .do fschar NBI  \[Eu] \f[EURO]\N'7'
.do if F NI   .do fschar NI   \[Eu] \f[EURO]\N'6'
.do if F NR   .do fschar NR   \[Eu] \f[EURO]\N'4'
.do if F PB   .do fschar PB   \[Eu] \f[EURO]\N'5'
.do if F PBI  .do fschar PBI  \[Eu] \f[EURO]\N'7'
.do if F PI   .do fschar PI   \[Eu] \f[EURO]\N'6'
.do if F PR   .do fschar PR   \[Eu] \f[EURO]\N'4'
.do if F TB   .do fschar TB   \[Eu] \f[EURO]\N'5'
.do if F TBI  .do fschar TBI  \[Eu] \f[EURO]\N'7'
.do if F TI   .do fschar TI   \[Eu] \f[EURO]\N'6'
.do if F TR   .do fschar TR   \[Eu] \f[EURO]\N'4'
.
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set filetype=groff textwidth=72:
