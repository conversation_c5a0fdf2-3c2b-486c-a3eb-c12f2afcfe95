.\" Define the PSPIC macro.
.\"
.\" When used with output devices other than ps, dvi, html, or xhtml,
.\" draw a box where the picture would go.
.\"
.\" Usage:
.\"
.\"   .PSPIC [-L|-R|-C|-I <indentation>] <file> [<width> [<height>]]
.
.do if d PSPIC .nx
.
.do nr *groff_pspic_tmac_C \n[.cp]
.cp 0
.
.\" This is called if the `psbb` request returned a bounding box of all
.\" zeroes.  It already issues a diagnostic; one can append `ab` to this
.\" macro if that should be a fatal error, or redefine it for some other
.\" purpose.
.de pspic*error-hook
..
.
.de PSPIC
.  nr ps-offset-mode 0
.  \" left-aligned?
.  ie '\\$1'-L' \{\
.    nr ps-offset-mode 1
.    shift
.    HTML-DO-IMAGE \\$1 l
.  \}
.  el \{\
.    \" right-aligned?
.    ie '\\$1'-R' \{\
.      nr ps-offset-mode 2
.      shift
.      HTML-DO-IMAGE \\$1 r
.    \}
.    el \{\
.      \" indented?
.      ie '\\$1'-I' \{\
.        nr ps-offset-mode 3
.        nr ps-offset (m;\\$2)
.        shift 2
.        HTML-DO-IMAGE \\$1 i
.      \}
.      el \{\
.        \" centered is the default
.        if '\\$1'-C' \
.          shift
.        HTML-DO-IMAGE \\$1 c
.      \}
.    \}
.  \}
.
.  br
.
.  \" get bounding box
.  psbb \\$1
.  ie (\\n[llx] : \\n[lly] : \\n[urx] : \\n[ury]) \{\
.    nr ps-wid (\\n[urx] - \\n[llx])
.    nr ps-ht (\\n[ury] - \\n[lly])
.    if (\\n[ps-wid] < 0) \
.      nr ps-wid (-\\n[ps-wid])
.    if (\\n[ps-ht] < 0) \
.      nr ps-ht (-\\n[ps-ht])
.
.    \" if we have a <width> parameter, use it as the final
.    \" image width; otherwise we use the image's natural width
.    \" or the current line length, whatever is smaller
.    ie (\\n[.$] >= 2) \
.      nr ps-deswid (i;\\$2)
.    el \
.      nr ps-deswid ((\\n[.l] - \\n[.i]) <? \\n[ps-wid]p)
.
.    \" compute the final image height (with proper rounding),
.    \" based on the image's aspect
.    nr ps-desht (\\n[ps-deswid] * 1000 + (\\n[ps-wid] / 2) \
                  / \\n[ps-wid] * \\n[ps-ht] \
                  + 500 / 1000)
.
.    \" if we have a <height> parameter, use it as the final
.    \" image height in case it is smaller than the height
.    \" value we have just computed
.    if ((\\n[.$] >= 3) & (\\n[ps-desht] > (i;0\\$3))) \{\
.      nr ps-desht (i;\\$3)
.      \" recompute the final image width since we always
.      \" keep the correct image aspect
.      nr ps-deswid (\\n[ps-desht] * 1000 + (\\n[ps-ht] / 2) \
                     / \\n[ps-ht] * \\n[ps-wid] \
                     + 500 / 1000)
.    \}
.
.    \" reserve vertical space for image
.    ne (\\n[ps-desht]u + 1v)
.
.    \" compute image offset w.r.t. the current left margin
.    if (\\n[ps-offset-mode] == 0) \
.      nr ps-offset (\\n[.l] - \\n[.i] - \\n[ps-deswid] / 2)
.    if (\\n[ps-offset-mode] == 1) \
.      nr ps-offset 0
.    if (\\n[ps-offset-mode] == 2) \
.      nr ps-offset (\\n[.l] - \\n[.i] - \\n[ps-deswid])
.
.    ie '\*[.T]'dvi' \{\
.      \" prepare values for \special{psfile=...} as needed by dvips
.      ie (\\n[ps-wid]p == \\n[ps-deswid]) \{\
.        ds ps-scale \" empty
.        ds ps-hoffset hoffset=-\\n[llx]
.        ds ps-voffset voffset=-\\n[lly]
.      \}
.      el \{\
.        nr ps-scale (\\n[ps-deswid] * 100 / \\n[ps-wid]p)
.        nr ps-hoffset (-\\n[llx] * \\n[ps-scale] / 100)
.        nr ps-voffset (-\\n[lly] * \\n[ps-scale] / 100)
.        ds ps-scale hscale=\\n[ps-scale] vscale=\\n[ps-scale]
.        ds ps-hoffset hoffset=\\n[ps-hoffset]
.        ds ps-voffset voffset=\\n[ps-voffset]
.      \}
.
\h'\\n[ps-offset]u'\
\v'\\n[ps-desht]u'\
\X'psfile=\\$1 \\*[ps-hoffset] \\*[ps-voffset] \\*[ps-scale]'
.    \}
.    el \{\
.      ie '\*[.T]'ps' \{\
.        \" prepare values for grops; the 'ps-invis' and 'ps-endinvis'
.        \" escapes are for groff's -X switch to provide a PS preview
.        \" with xditview: it uses -Tps for formatting but xditview
.        \" can't handle EPS files, thus alternative code is enclosed
.        \" between those two escapes
.        ds ps-invis \X'ps: invis'
.        ds ps-endinvis \X'ps: endinvis'
.        ds ps-import \X'ps: import \E$1 \En[llx] \En[lly] \En[urx] \En[ury] \
                                    \En[ps-deswid] \E*[ps-desht]'
.      \}
.      el \{\
.        ds ps-invis
.        ds ps-endinvis
.        ds ps-import
.      \}
.
.      ie (\\n[.$] >= 3) \
.        ds ps-desht \\n[ps-desht]
.      el \
.        ds ps-desht \" empty
.
\h'\\n[ps-offset]u'\
\\*[ps-invis]\
\# horizontally, the rectangle is slightly smaller than the image
\# to compensate the line thickness (especially needed for TTY devices)
\Z'\D'p 0 \\n[ps-desht]u \
        (\\n[ps-deswid]u - \\n[.H]u) 0 \
        0 -\\n[ps-desht]u''\
\# for convenience we also display the image file name (centered
\# vertically);
\Z'\v'((\\n[ps-desht]u / 2u) \
       + (\w'\\$1'u * 0) \
       + ((\\n[rst]u + \\n[rsb]u) / 2u))'\h'1m'\\$1'\
\\*[ps-endinvis]\
\v'\\n[ps-desht]u'\
\\*[ps-import]
.    \}
.
.    br
.    sp \\n[ps-desht]u
.  \}
.  el \
.    pspic*error-hook \\$@
.  HTML-IMAGE-END
..
.
.cp \n[*groff_pspic_tmac_C]
.do rr *groff_pspic_tmac_C
.
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set expandtab filetype=groff tabstop=2 textwidth=72:
