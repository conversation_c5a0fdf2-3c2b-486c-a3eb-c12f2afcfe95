# Shell Setup

if [ -e /etc/thinstation.user ]; then
	. /etc/thinstation.user
fi

get_gnome_proxy()
{
	if which gconftool-2 > /dev/null 2>&1; then
	        mode=`gconftool-2 -g /system/proxy/mode 2>/dev/null`
		if [ "$mode" == "manual" ]; then
		        use_http_proxy=`gconftool-2 -g /system/http_proxy/use_http_proxy 2>/dev/null`
			use_same_proxy=`gconftool-2 -g /system/http_proxy/use_same_proxy 2>/dev/null`
			if $use_http_proxy; then
			        use_authentication=`gconftool-2 -g /system/http_proxy/use_authentication 2>/dev/null`
				if $use_authentication; then
				        authentication_user=`gconftool-2 -g /system/http_proxy/authentication_user 2>/dev/null`
				        authentication_password=`gconftool-2 -g /system/http_proxy/authentication_password 2>/dev/null`
				fi
			        host=`gconftool-2 -g /system/http_proxy/host 2>/dev/null`
			        port=`gconftool-2 -g /system/http_proxy/port 2>/dev/null`
			        ignore_hosts=`gconftool-2 -g /system/http_proxy/ignore_hosts 2>/dev/null`
			fi
			if $use_same_proxy; then
				secure_host=$host
				secure_port=$port
				socks_host=$host
				socks_port=$port
				ftp_host=$host
				ftp_port=$port
			else
			        socks_host=`gconftool-2 -g /system/proxy/socks_host 2>/dev/null`
			        socks_port=`gconftool-2 -g /system/proxy/socks_port 2>/dev/null`
			        secure_host=`gconftool-2 -g /system/proxy/secure_host 2>/dev/null`
			        secure_port=`gconftool-2 -g /system/proxy/secure_port 2>/dev/null`
			        ftp_host=`gconftool-2 -g /system/proxy/ftp_host 2>/dev/null`
			        ftp_port=`gconftool-2 -g /system/proxy/ftp_port 2>/dev/null`
			fi
		fi
	fi
        if [ -z "$use_authentication" ]; then use_authentication=false ; fi
}

set_proxy()
{
	unset http_proxy https_proxy ftp_proxy no_proxy PROXY_AUTH
	if [ "$mode" == "manual" ]; then
		if $use_http_proxy && [ -n "$host" ] && [ -n "$port" ] ; then
                        no_proxy=`echo "$ignore_hosts" |sed -e 's/\[//g' |sed -e 's/\]//g'`
                        export no_proxy
			if $use_authentication; then
				if [ -n "$authentication_user" ] && [ -n "$authentication_password" ]; then
					PROXY_AUTH="$authentication_user:$authentication_password"
				elif [ -n "$authentication_user" ]; then
					PROXY_AUTH="$authentication_user"
				fi
				export http_proxy=http://${PROXY_AUTH}@$host:$port
	                        if [ -n "$ftp_host" ] && [ -n "$ftp_port" ]; then
					export ftp_proxy=http://${PROXY_AUTH}@$ftp_host:$ftp_port
				fi
			else
				if [ -n "$ftp_host" ] && [ -n "$ftp_port" ]; then
					export ftp_proxy=http://$ftp_host:$ftp_port
				fi
				export http_proxy=http://$host:$port
			fi
			if [ -n "$secure_host" ] && [ -n "$secure_port" ]; then
				export https_proxy=https://$secure_host:$secure_port
			fi
		fi
	fi
}

get_gnome_proxy
set_proxy

if ! readlink /bin/ls |grep -qe busybox; then
	export LS_COLORS='rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.cmd=01;32:*.exe=01;32:*.com=01;32:*.btm=01;32:*.bat=01;32:*.conf=01;33:*.tar=01;31:*.tgz=01;31:*.arj=01;31:*.taz=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.zip=01;31:*.z=01;31:*.Z=01;31:*.dz=01;31:*.gz=01;31:*.lz=01;31:*.xz=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.rar=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.jpg=01;35:*.jpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.axv=01;35:*.anx=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.axa=00;36:*.oga=00;36:*.spx=00;36:*.xspf=00;36:'
	alias ls='ls --color=auto'
fi

if [ "`id -u`" != "0" ]; then
        umask 027
else
    	umask 022
fi

cd ~

PS1='\h:\w\$ '

alias ll='ls -l'
alias l='ls -l'

if [ -e /bin/e3vi ]; then
	alias vi='e3vi'
fi

