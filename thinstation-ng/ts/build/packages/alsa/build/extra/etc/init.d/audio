#!/bin/sh

. `dirname $0`/common

#set -x
#exec </dev/null >/var/log/audio.log  2>&1

have_control()
{
	if amixer sget -c $card $1 > /dev/null 2>&1; then
		return 0
	else
		return 1
	fi
}

	for card in `grep -e "]:" /proc/asound/cards |cut -c2`; do
		if [ -n "$AUDIO_LEVEL" ]; then
			for control in Master PCM CD Video; do
				if have_control $control; then
					amixer sset -c $card $control $AUDIO_LEVEL% unmute
				fi
			done
			if which pactl; then
				pactl set-sink-volume @DEFAULT_SINK@ $AUDIO_LEVEL%
			fi
		fi
		if [ -n "$MIC_LEVEL" ] && [ "$MIC_LEVEL" -gt "0" ]; then
			if [ -z "$MIC_DEVICE" ]; then MIC_DEVICE=Mic; fi
			if have_control $MIC_DEVICE; then
				if have_control Capture; then
					amixer -c $card sset Capture cap
					amixer sset Capture $MIC_LEVEL% unmute
				fi
				amixer sset $MIC_DEVICE playback mute
				amixer sset $MIC_DEVICE $MIC_LEVEL% cap
			elif have_control Capture; then
				amixer sset Capture 0%
			fi
			if which pactl; then
				pactl set-source-volume @DEFAULT_SOURCE@ $MIC_LEVEL%
			fi
		fi
		for control in $UNMUTE; do
			if have_control $control; then
				amixer sset -c $card $control unmute
			fi
		done
		for control in $MUTE; do
			if have_control $control; then
				amixer sset -c $card $control mute
			fi
		done
	done
