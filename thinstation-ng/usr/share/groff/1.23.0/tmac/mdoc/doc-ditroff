.\" Copyright (c) 1991, 1993
.\"   The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that the following conditions
.\" are met:
.\" 1. Redistributions of source code must retain the above copyright
.\"    notice, this list of conditions and the following disclaimer.
.\" 2. Redistributions in binary form must reproduce the above copyright
.\"    notice, this list of conditions and the following disclaimer in
.\"    the documentation and/or other materials provided with the
.\"    distribution.
.\" 3. [Deleted.  See
.\"     ftp://ftp.cs.berkeley.edu/pub/4bsd/README.Impt.License.Change]
.\" 4. Neither the name of the University nor the names of its
.\"    contributors may be used to endorse or promote products derived
.\"    from this software without specific prior written permission.
.\"
.\" THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS "AS IS"
.\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
.\" TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
.\" PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR
.\" CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
.\" SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
.\" LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
.\" USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
.\" ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
.\" OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
.\" OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
.\" SUCH DAMAGE.
.\"
.\"     @(#)doc-ditroff	8.1 (Berkeley) 06/08/93
.
.
.\" Use -rS={11,12} to change the font size from 10pt to 11pt or 12pt.
.if !r S .nr S 10
.
.ie        (\n[S] == 11) \{\
.  ps 10.95z
.  vs 13.6p
.\}
.el \{ .ie (\n[S] == 12) \{\
.  ps 12z
.  vs 14.5p
.\}
.el \{\
.  ps 10z
.  vs 12p
.\}\}
.
.
.\" the 'doc-xx-font' strings must not be empty!
.
.ds doc-page-topic-font \f[I]
.ds doc-page-section-font \f[R]
.ds doc-Ad-font \f[I]
.ds doc-Ar-font \f[CI]
.ds doc-Cm-font \f[CR]
.ds doc-Em-font \f[I]
.ds doc-Er-font \f[CR]
.ds doc-Ev-font \f[CR]
.ds doc-Fa-font \f[CI]
.ds doc-Fd-font \f[CB]
.ds doc-Fl-font \f[CR]
.ds doc-Fn-font \f[CB]
.ds doc-Ft-font \f[CI]
.ds doc-Ic-font \f[CB]
.ds doc-Li-font \f[CR]
.ds doc-Lk-font \f[R]\"
.ds doc-Me-font \f[B]
.ds doc-Nm-font \f[CB]
.ds doc-No-font \f[R]
.ds doc-Pa-font \f[I]
.ds doc-Sh-font \f[\*[HF]]\"
.ds doc-Sy-font \f[B]
.ds doc-Tn-font \f[R]
.ds doc-Va-font \f[I]
.ds doc-Xr-font \f[I]
.
.ds doc-left-parenthesis \f[R](\f[]
.ds doc-right-parenthesis \f[R])\f[]
.ds lp \f[R](\f[]
.ds rp \f[R])\f[]
.ds doc-left-bracket \f[R][\f[]
.ds doc-right-bracket \f[R]]\f[]
.
.tr *\[**]
.
.\" miscellaneous
.nr doc-paragraph-space .4v
.
.nr doc-digit-width \w'\0'u
.nr doc-fixed-width \w'\f[CR]0'
.
.
.\" NS doc-display-vertical global register
.\" NS   vertical space between list elements etc.
.
.nr doc-display-vertical 0
.
.
.\" NS doc-setup-page-layout macro
.\" NS   set up page layout
.\" NS
.\" NS modifies:
.\" NS   doc-display-vertical
.\" NS   doc-line-length
.
.eo
.de doc-setup-page-layout
.  ie r LL \
.    ll \n[LL]u
.  el \
.    ll \n[.l]u
.
.  ie r LT \
.    lt \n[LT]u
.  el \
.    lt \n[.l]u
.
.  po 1i
.
.  nr doc-display-vertical .5v
.  nr doc-line-length \n[.l]
..
.ec
.
.
.ds doc-left-singlequote \[oq]
.ds doc-right-singlequote \[cq]
.
.\" the following strings are 'official'
.ds <= \[<=]
.ds >= \[>=]
.ds Lq \[lq]
.ds Rq \[rq]
.ds ua \[ua]
.ds aa \[aa]
.ds ga \[ga]
.ds q \[dq]
.ds Pi \[*p]
.ds Ne \[!=]
.ds Le \[<=]
.ds Ge \[>=]
.ds Lt <
.ds Gt >
.ds Pm \[+-]
.ds If \[if]
.ds Na \f[I]NaN\f[]
.ds Ba \f[R]|\f[]
.ds Am &
.
.
.\" NS doc-get-width macro
.\" NS   computes the width of a string as a multiple of
.\" NS   'doc-fixed-width': '.doc-get-width string'
.\" NS
.\" NS modifies:
.\" NS   doc-width
.
.eo
.de doc-get-width
.  nr doc-width \w'\f[CR]\$1'
.  ie (\n[doc-width] >= \n[doc-fixed-width]) \{\
.    ie (\n[doc-width] % \n[doc-fixed-width]) \
.      nr doc-width ((\n[doc-width] / \n[doc-fixed-width]) + 1)
.    el \
.      nr doc-width (\n[doc-width] / \n[doc-fixed-width])
.  \}
.  el \{\
.    ie \n[doc-width] \
.      nr doc-width 1
.    el \
.      nr doc-width 0
.  \}
..
.ec
.
.
.\" NS doc-get-arg-width macro
.\" NS   computes the width of an argument as a multiple of
.\" NS   'doc-fixed-width': '.doc-get-arg-width arg-index'
.\" NS
.\" NS modifies:
.\" NS   doc-width
.
.eo
.de doc-get-arg-width
.  nr doc-width \w'\f[CR]\*[doc-arg\$1]'
.  ie (\n[doc-width] >= \n[doc-fixed-width]) \{\
.    ie (\n[doc-width] % \n[doc-fixed-width]) \
.      nr doc-width ((\n[doc-width] / \n[doc-fixed-width]) + 1)
.    el \
.      nr doc-width (\n[doc-width] / \n[doc-fixed-width])
.  \}
.  el \{\
.    ie \n[doc-width] \
.      nr doc-width 1
.    el \
.      nr doc-width 0
.  \}
..
.ec
.
.
.\" NS Ql user macro
.\" NS   quoted literal define
.\" NS
.\" NS modifies:
.\" NS   doc-argXXX
.\" NS   doc-arg-count
.\" NS   doc-arg-ptr
.\" NS   doc-macro-name
.\" NS   doc-spaceXXX
.\" NS   doc-typeXXX
.\" NS   doc-quote-left
.\" NS   doc-quote-right
.\" NS
.\" NS local variables:
.\" NS   doc-reg-Ql
.\" NS   doc-reg-Ql1
.\" NS   doc-reg-Ql2
.\" NS
.\" NS width register 'Ql' set in doc-common
.
.eo
.de Ql
.  if !\n[doc-arg-count] \{\
.    ie \n[.$] \{\
.      ds doc-macro-name Ql
.      doc-parse-args \$@
.    \}
.    el \
.      tm Usage: .Ql argument ... (#\n[.c])
.  \}
.
.  nr doc-reg-Ql (\n[doc-arg-ptr] + 1)
.  doc-get-arg-width \n[doc-reg-Ql]
.
.  \" don't use quotes if we have more than two succeeding string
.  \" arguments
.  nr doc-reg-Ql +1
.  if (\n[doc-arg-count] >= \n[doc-reg-Ql]) \
.    if (\n[doc-type\n[doc-reg-Ql]] == 2) \
.      nr doc-width 3
.
.  \" make a difference in quotation style for strings longer
.  \" than two characters
.  ie (\n[doc-width] > 2) \
.    Li
.  el \{\
.    ie \n[doc-arg-ptr] \{\
.      \" we replace 'Ql' with 'Li'
.      ds doc-arg\n[doc-arg-ptr] Li
.      nr doc-arg-ptr -1
.    \}
.    el \{\
.      \" if .Ql has been called directly, we must shift all elements in
.      \" the argument vector to the right so that we can insert 'Li'
.      nr doc-reg-Ql \n[doc-arg-count]
.      nr doc-reg-Ql1 (\n[doc-arg-count] + 1)
.      while \n[doc-reg-Ql] \{\
.        rn doc-arg\n[doc-reg-Ql] doc-arg\n[doc-reg-Ql1]
.        rnn doc-type\n[doc-reg-Ql] doc-type\n[doc-reg-Ql1]
.        rn doc-space\n[doc-reg-Ql] doc-space\n[doc-reg-Ql1]
.        nr doc-reg-Ql -1
.        nr doc-reg-Ql1 -1
.      \}
.      ds doc-arg1 Li
.      nr doc-type1 1
.      ds doc-space1
.      nr doc-arg-count +1
.    \}
.
.    ds doc-quote-left "\*[doc-left-singlequote]
.    ds doc-quote-right "\*[doc-right-singlequote]
.    doc-enclose-string
.  \}
..
.ec
.
.
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set filetype=groff textwidth=72:
