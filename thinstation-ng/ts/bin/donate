#!/bin/bash
#set -x
message()
{
cat << 'EOF' >&1
       Hi!, Sorry to go all Wikipedia on you, but these are the times.

       Maybe  you're new and just starting with ThinStation, don't worry, it's
       still free software.

       There are many ways to support the ThinStation project.  Some  send  me
       code  PR's  that  benefit the whole community, Thank you. Many go on to
       use ThinStation for years and years, saving thousands, maybe even  mil-
       lions  of  dollars  in  development and licensing fees. The ThinStation
       project could certainly use any financial support that kind of  savings
       would produce.

       Maybe  you're  wondering  how much you should donate. Well, I do have a
       formula for you.

       o   $2 Per Seat, Per Year.



       How did I get there? It's a small number, but big enough that  even  if
       you  cut it in half, it could still make a difference. I know some com-
       panies take ThinStation, build products out of it, and never even  sub-
       mit  a PR for a typo. They might even go on to sell services to enhance
       TS without remuneration, here is your guilt trip. It's  free  software,
       that's  in  the  cards. It would be nice if they supported the project,
       but it might be part of corporate culture to take without thanks.  Non-
       profits  should _not_ send anything, you make the world a better place,
       Thank You.

       What will I do with the funds? Pay myself back for all the domains  and
       web hosting (that's  a  giant  hole). Buy new software and hardware for
       testing (even bigger hole). Please show your support. The donate button
       is  at http://www.thinstation.org and we also have a patreon account at
       https://www.patreon.com/thinstation. Do you want to sponsor a feature?

EOF
}

message
