###############################################
# -- Thinstation sample configuration file -- #
###############################################
#
# This file must be named "thinstation.conf<xxx>" where <xxx> can be:
# .buildtime   Defines the defaults build into the image (note the leading .)
# .network     Default global config file loaded from the TFTP server.
# .user        Config file on local storage.
# -<name>      Specific config file on the TFTP server for the terminal
#              "name" (e.g. thinstation.conf-paul). Requires thinstation.hosts. 
# .group-<id>  Config file for a group of terminals (e.g. with printer setup
#              for those terminal with local printers. Requires thinstation.hosts.
# -<IP>        Specific config file on the TFTP server for the terminal with
#              the IP number <IP> specified (e.g. thinstation.conf-********).
# -<MAC>       Specific config file on the TFTP server for the terminal with 
#              the MAC address specified (e.g thinstation.conf-112233445566).

##
# --- General Options
#
# AUDIO_LEVEL        Audio Level for sound, 0-100
# MIC_LEVEL          Recording level for input devices, 0-100
# LOCALE             If multiple locales are included in the image, you can use this to
#                     select one during boot with a .conf file.	
# TIME_ZONE          Used to set time zone on a TS client with a zone info file. If multiple
#                      zone-info files are included, this selection can be refined with additional
#                      .conf files.
# SYSLOG_SERVER      Log server ip address or hostname.
#                    If the work "local" is used, then syslog starts logging locally
#                    If not specified syslogd is not loaded.
# USB_STORAGE_SYNC   ON    Safer writing
#                    OFF   Faster writes to USB storage devices
#                    WARNING: This may lead to data corruption when the device is unplugged
#                             before buffers have been flushed
# USB_MOUNT_DIR      Directory to use for mounting usbdisks (default: /mnt/usbdevice/)
# USB_MOUNT_USELABEL Label to use for individual usb disks
#                    default is to use the device name (eg. sda1 - /mnt/usbdevice/sda1)
#                    NO       use default ONLY
#                    YES      will use label on usb volume if exists or fallback to default
#                    <label>  will use label on usb volume if exists or <label> otherwise
#                    adds suffix of n (1,2,3,etc.) if duplicate labels found
# USB_MOUNT_OPTIONS  additional mount options for usb devices (eg. utf8,shortname=win95)
# 		     see mount documentation for details (man mount)
# DISK_STORAGE_SYNC  ON    Safer writing
#                    OFF   Faster writes to USB storage devices
#                    WARNING: This may lead to data corruption when the device is unplugged
#                             before buffers have been flushed
# DISK_MOUNT_OPTIONS  additional mount options for disk devices (eg. utf8,shortname=win95)
# 		     see mount documentation for details (man mount)
# CDROM_MOUNT_OPTIONS  additional mount options for cdrom devices (eg. utf8,shortname=win95)
# 		     see mount documentation for details (man mount)
# BIND_MOUNT#        A disk label to bind mount point mapping (eg. boot:/boot or home:/root)
# DAILY_REBOOT       Will reboot server if up over a day and one of the session
#                    types is closed
# CUSTOM_CONFIG      Allows choosing custom boot config, On/Off 
# RECONNECT_PROMPT   This displays the reconnection/shutdown options for when a session is ended
#
#                    OFF    No reconnect prompt
#                    ON     Reconnect prompt will be displayed
#		     FORCE  Just reconnect
#                    AUTO   Automatic reconnect
#                    AUTOXX Automatic reconnect attempt after XX seconds - msg is displayed
#                    MENU   Shows a menu with a shutdown and reconnection option
#                    MENUXX As MENU option, but the XX is a time period in minutes.
#                           After XX minutes shutdown will occur unless the reconnect option
#                           is choosen
# NO_SESSION	     What to do in the event that all sessions have exited and not reconnected.
#		     POWEROFF  Shutdown the terminal.(default)
#		     REBOOT    Reboot the terminal.
# DEBUGPAUSE	     Will pause at prompt after load of all modules and packages but before starting
#		     Only works if debug package included
#		     true	pause at prompt to allow debug
#		     false	don't pause (default)
# CRON_JOBx	     Will add this crontab entry (1 - 9)
# FASTBOOT_URL       Normally fastboot will try to load lib.squash in the same way that vmlinuz and initrd
#		     were loaded. This will force fastboot to download lib.squash using a specific protocol
#		     and location

AUDIO_LEVEL=67
TIME_ZONE="America/Los_Angeles"
SYSLOG_SERVER=local
#USB_STORAGE_SYNC=On
#USB_MOUNT_DIR="/mnt/usbdevice"
#USB_MOUNT_USELABEL="Yes"
#USB_MOUNT_OPTIONS="utf8,shortname=win95"
BIND_MOUNT0="home:/root"
BIND_MOUNT1="boot:/boot"
BIND_MOUNT2="tsdev:/thinstation"
FASTBOOT_URL="http://www.thinstation.org"
DAILY_REBOOT=On
CUSTOM_CONFIG=Off
RECONNECT_PROMPT=On
#DEBUGPAUSE=true

##
# --- Session Defaults
# -- Default Settings for all sessions
#
# AUTOSTART		ON   Application will be executed immediately at startup
#			OFF  Application will appear in a menu to be started manually
# ICONMODE		MANUAL  Default sessions are not created in window manager/idesk or wbar
#			AUTO    Default sessions types are created in window manager/idesk or wbar
# ALWAYS_ENTER_SERVER	Whether or not the server entry dialog box is always displayed
# 			By default, autostarted sessions have this off and menu sessions have this on
#			OFF will disable the prompt for everything.
#			ON will enable the prompt for everything.
# ALLOW_EXIT		Whether or not a user will be allowed to cancel the server address entry.
# 			By default, autostarted sessions will not be allowed to exit and menu sessions will.
#			OFF will force entry for everything.
#			ON will allow exit from everything.

AUTOSTART=On
ICONMODE=AUTO
ALWAYS_ENTER_SERVER=Off
ALLOW_EXIT=Off

##
# --- Session Details
#
# Note:                     # is a number equal to or greater than 0
#
# SESSION_#_TITLE           Title description for SESSION. Needed for replimenu.
# SESSION_#_TYPE            Package type, choose beetwen:
#                           - vncviewer        Start vncviewer in X
#                           - rdesktop         Start rdesktop in X
#                           - x                Start x-terminal session (xdm)
#                           - xnest            Start x-terminal session (xdm) from within blackbox
#                           - ssh              Start ssh client in linux console
#                           - telnet           Start telnet client in linux console
#                           - ica              Start Citrix ICA client in X
#                           - ica_wfc          Start ICA Manager
#                           - openbox          Start openbox window manager session
#                           - icewm            Start icewm window manager session
#                           - dillo            Start Web Browser in X
#                           - tftpd            Start tftp daemon
#                           - tarantella       Start tarantella client
#                           - rxvt             Start light xterm client
#                           - xterm            Start xterm client
#                           - tn5250           Start AS400 client in linux console
#                           - nx               Start NX Client Session
# SESSION_#_AUTOSTART       ON      	Application will be executed immediately at startup
#                           WINDOW     	Application will be executed immediately at startup in window mode
#                           FULLSCREEN	Application will be executed immediately at startup in fullscreen mode
#                           CONSOLE     Application will be executed immediately at startup in console mode
#                           OFF     	Application will appear in a menu to be started manually
# SESSION_#_CUSTOM_CONFIG   ON      Allows choosing custom config for when session starts
#                           OFF     Session boots normally
# SESSION_#_ICON            ON      Places Icon on Desktop if package xtdesk is selected and
#                                   in Window Manager Main Menu
#                           SUBMENU Places Icon on Desktop if package xtdesk is selected and
#                                   in a Submenu within the window manager
# SESSION_#_type_SERVER     IP address/hostname of the server
# SESSION_#_type_OPTIONS    Command line options for the session type
# SESSION_#_type_USER       Specify the user for sessions types that require a user
# SESSION_#_type_PASS_ENABLE Can be set to false, to override a session type that wants password input.
#
# SESSION_#_type_specific   This allows you to specify specific config file details for certain session
#                           types.  Currently support is NX and ICA.  Any configuration file setting
#                           can be changed with this setting.  An example of this is
#                           SESSION_0_NX_GENERAL_DESKTOP="Gnome"
#                           SESSION_0_ICA_APPSRV_USEFULLSCREEN="Yes"
#                           See your application manual for documentation on the settings which
#                           can be used.
