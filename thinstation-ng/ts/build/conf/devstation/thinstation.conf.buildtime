AUDIO_LEVEL=90
MIC_LEVEL=0
HD_MOUNT=true
#X_DPI=100
#USE_XRANDR=TRUE
#XRANDR_OPTIONS="-s 1680x1050"
MEMORY_CONSTRAINED="TRUE"
LIGHTDM_BANNER=true
SESSION_0_TYPE=xfwm4
SESSION_1_TYPE=chrome
SESSION_0_AUTOSTART=on
SESSION_1_AUTOSTART=off
SAMBA_SECURITY=user
SAMBA_WORKGROUP=WORKGROUP
LOCALE=en_US
#SESSION_1_FIREFOX_HOMEPAGE=http://www.doncuppjr.net/projects
SESSION_1_CHROME_HOMEPAGE=https://www.google.com
#WEBUPDATEROOT="https://sourceforge.net/projects/thinstation/files/DevStation-Source"
WEBUPDATEROOT="https://www.thinstation.net"
GTK_THEME=Adwaita
XFWM4_THEME=Default
XTERM_CMD="xfce4-terminal --disable-server"
DESKTOP_ICON_SIZE=48
DESKTOP_FONT_SIZE=8
DESKTOP_SHOWTRASH=false
DEKSTOP_SHOWHOME=true
DESKTOP_SHOWREMOVEABLE=true
DESKTOP_SHOWFILESYSTEM=true
# PANEL_AUTOHIDE=false
PANEL_PAGER=false
PANEL_REV_CLOCKSYSTRAY=true
PANEL_USER=true
# XFWM4_START_POSITION="BOTTOM"
XFWM4_START_TITLE="Start"
XFWM4_START_POSITION=bottom
XFWM4_COMPOSITING="true"
XFWM4_RED=0
XFWM4_BLUE=0
XFWM4_GREEN=0
ICONS_THEME=gnome
#ICONS_CUT_SIZES="256x256 scalable"
XFWM4_START_ICON=ThinStation
#SERVER=**************
#MENU_SHOWRES=off
#MENU_SHOWAPPS=off
#MENU_SHOWABOUT=off
#MENU_SHOWLOGOUT=off
#MENU_SHOWSETTINGS=on
#HIDE_CONTROLS=on
SCREEN_BLANK_TIME=5
SCREEN_STANDBY_TIME=5
SCREEN_SUSPEND_TIME=5
SCREEN_OFF_TIME=5
#SCREEN_HORIZSYNC="30-70 | *"
#DONT_VT_SWITCH_STATE=TRUE
#DONT_ZAP_STATE=TRUE
#ALWAYS_ENTER_SERVER=TRUE
#FASTBOOT_URL=http://*************
NET_USE=BOTH
NET_USE_DHCP=on
NET_HOSTNAME=ts-*
NET_TELNETD_ENABLED=ON
#DEBUGPAUSE=TRUE
#EGALAX_DEVICE=/dev/ttyS1
#EGALAX_DEVICE=usbauto
#EGALAX_PARAMFILE=/boot/eeti.param
#RECONNECT_PROMPT=AUTO
TIME_ZONE=America/Los_Angeles
NET_TIME_SERVER=us.pool.ntp.org
NET_REMOTE_ACCESS_FROM="0.0.0.0"
#X_DRIVER_OPTION1="HWcursor False"
#X_DRIVER_OPTION2="PanelSize 1024x600"
#WIRELESS_ESSID="Example"
#WIRELESS_MODE="managed"
#WIRELESS_WPAKEY="3x@mpl39@559hr@53"
#WIRELESS_DRIVER="wext"
#TFTP_BLOCKSIZE=1024
TFTP_BLOCKSIZE=8940
#CRYPT_0="cryptdisk PARTLABEL=SafeStore none luks,discard"
MOUNT_0="LABEL=boot     /boot		auto	X-mount.mkdir,defaults	0	0"
MOUNT_1="LABEL=tsdev    /thinstation	auto	X-mount.mkdir,defaults	0	0"
MOUNT_2="LABEL=prstnt	/var/prstnt	auto	X-mount.mkdir,defaults	0	0"
MOUNT_3="LABEL=home	/home		auto	X-mount.mkdir,defaults	0	0"
MOUNT_4="LABEL=root	/root		auto	X-mount.mkdir,defaults	0	0"
MOUNT_5="LABEL=log	/var/log	auto	X-mount.mkdir,defaults	0	0"
MOUNT_6="LABEL=swap     swap		swap	defaults		0	0"
USB_STORAGE_SYNC=on
#DEBUGPAUSE=true
SSSD_USER_GROUP="Domain Admins"
