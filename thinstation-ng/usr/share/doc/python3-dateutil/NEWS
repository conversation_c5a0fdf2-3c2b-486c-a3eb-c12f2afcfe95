Version 2.8.2 (2021-07-08)
==========================

Data updates
------------

- Updated tzdata version to 2021a. (gh pr #1128)


Bugfixes
--------

- Fixed a bug in the parser where non-``ValueError`` exceptions would be raised
  during exception handling; this would happen, for example, if an
  ``IllegalMonthError`` was raised in ``dateutil`` code. Fixed by <PERSON>.
  (gh issue #981, pr #987).
- Fixed the custom ``repr`` for ``dateutil.parser.ParserError``, which was not
  defined due to an indentation error. (gh issue #991, gh pr #993)
- Fixed a bug that caused ``b'`` prefixes to appear in parse_isodate exception
  messages. Reported and fixed by <PERSON> (@pawl) (gh pr #1122)
- Make ``isoparse`` raise when trying to parse times with inconsistent use of
  `:` separator. Reported and fixed by @mariocj89 (gh pr #1125).
- Fixed ``tz.gettz()`` not returning local time when passed an empty string.
  Reported by @labrys (gh issues #925, #926). Fixed by @ffe4 (gh pr #1024)


Documentation changes
---------------------

- Rearranged parser documentation into "Functions", "Classes" and "Warnings and
  Exceptions" categories. (gh issue #992, pr #994).
- Updated ``parser.parse`` documentation to reflect the switch from
  ``ValueError`` to ``ParserError``. (gh issue #992, pr #994).
- Fixed methods in the ``rrule`` module not being displayed in the docs. (gh pr
  #1025)
- Changed some relative links in the exercise documentation to refer to the
  document locations in the input tree, rather than the generated HTML files in
  the HTML output tree (which presumably will not exist in non-HTML output
  formats). (gh pr #1078).


Misc
----

- Moved ``test_imports.py``, ``test_internals.py`` and ``test_utils.py`` to
  pytest.  Reported and fixed by @jpurviance (gh pr #978)
- Added project_urls for documentation and source. Patch by @andriyor (gh pr
  #975).
- Simplified handling of bytes and bytearray in ``_parser._timelex``. Reported
  and fixed by @frenzymadness (gh issue #1060).
- Changed the tests against the upstream tz database to always generate fat
  binaries, since until GH-590 and GH-1059 are resolved, "slim" zic binaries
  will cause problems in many zones, causing the tests to fail. This also
  updates ``zoneinfo.rebuild`` to always generate fat binaries. (gh pr #1076).
- Moved sdist and wheel generation to use `python-build`. Reported and fixed by
  @mariocj89 (gh pr #1133).


Version 2.8.1 (2019-11-03)
==========================

Data updates
------------

- Updated tzdata version to 2019c.


Bugfixes
--------

- Fixed a race condition in the ``tzoffset`` and ``tzstr`` "strong" caches on
  Python 2.7. Reported by @kainjow (gh issue #901).
- Parsing errors will now raise ``ParserError``, a subclass of ``ValueError``,
  which has a nicer string representation. Patch by @gfyoung (gh pr #881).
- ``parser.parse`` will now raise ``TypeError`` when ``tzinfos`` is passed a
  type that cannot be interpreted as a time zone. Prior to this change, it
  would raise an ``UnboundLocalError`` instead.  Patch by @jbrockmendel (gh pr
  #891).
- Changed error message raised when when passing a ``bytes`` object as the time
  zone name to gettz in Python 3.  Reported and fixed by @labrys () (gh issue
  #927, gh pr #935).
- Changed compatibility logic to support a potential Python 4.0 release. Patch
  by Hugo van Kemenade (gh pr #950).
- Updated many modules to use ``tz.UTC`` in favor of ``tz.tzutc()`` internally,
  to avoid an unnecessary function call. (gh pr #910).
- Fixed issue where ``dateutil.tz`` was using a backported version of
  ``contextlib.nullcontext`` even in Python 3.7 due to a malformed import
  statement. (gh pr #963).


Tests
-----

- Switched from using assertWarns to using pytest.warns in the test suite. (gh
  pr #969).
- Fix typo in setup.cfg causing PendingDeprecationWarning to not be explicitly
  specified as an error in the warnings filter. (gh pr #966)
- Fixed issue where ``test_tzlocal_offset_equal`` would fail in certain
  environments (such as FreeBSD) due to an invalid assumption about what time
  zone names are provided. Reported and fixed by Kubilay Kocak (gh issue #918,
  pr #928).
- Fixed a minor bug in ``test_isoparser`` related to ``bytes``/``str``
  handling. Fixed by @fhuang5 (gh issue #776, gh pr #879).
- Explicitly listed all markers used in the pytest configuration. (gh pr #915)
- Extensive improvements to the parser test suite, including the adoption of
  ``pytest``-style tests and the addition of parametrization of several test
  cases. Patches by @jbrockmendel (gh prs #735, #890, #892, #894).
- Added tests for tzinfos input types. Patch by @jbrockmendel (gh pr #891).
- Fixed failure of test suite when changing the TZ variable is forbidden.
  Patch by @shadchin (gh pr #893).
- Pinned all test dependencies on Python 3.3. (gh prs #934, #962)


Documentation changes
---------------------

- Fixed many misspellings, typos and styling errors in the comments and
  documentation. Patch by Hugo van Kemenade (gh pr #952).


Misc
----

- Added Python 3.8 to the trove classifiers. (gh pr #970)
- Moved as many keys from ``setup.py`` to ``setup.cfg`` as possible.  Fixed by
  @FakeNameSE, @aquinlan82, @jachen20, and @gurgenz221 (gh issue #871, gh pr
  #880).
- Reorganized ``parser`` methods by functionality. Patch by @jbrockmendel (gh
  pr #882).
- Switched ``release.py`` over to using ``pep517.build`` for creating releases,
  rather than direct invocations of ``setup.py``. Fixed by @smeng10 (gh issue
  #869, gh pr #875).
- Added a "build" environment into the tox configuration, to handle dependency
  management when making releases. Fixed by @smeng10 (gh issue #870,r
  gh pr #876).
- GH #916, GH #971


Version 2.8.0 (2019-02-04)
==========================

Data updates
------------

- Updated tzdata version to to 2018i.


Features
--------

- Added support for ``EXDATE`` parameters when parsing ``rrule`` strings.
  Reported by @mlorant (gh issue #410), fixed by @nicoe (gh pr #859).
- Added support for sub-minute time zone offsets in Python 3.6+.
  Fixed by @cssherry (gh issue #582, pr #763)
- Switched the ``tzoffset``, ``tzstr`` and ``gettz`` caches over to using weak
  references, so that the cache expires when no other references to the
  original ``tzinfo`` objects exist. This cache-expiry behavior is not
  guaranteed in the public interface and may change in the future. To improve
  performance in the case where transient references to the same time zones
  are repeatedly created but no strong reference is continuously held, a
  smaller "strong value" cache was also added. Weak value cache implemented by
  @cs-cordero (gh pr #672, #801), strong cache added by
  GÃ¶kÃ§en Nurlu (gh issue #691, gh pr #761)


Bugfixes
--------

- Add support for ISO 8601 times with comma as the decimal separator in the
  ``dateutil.parser.isoparse`` function. (gh pr #721)
- Changed handling of ``T24:00`` to be compliant with the standard. ``T24:00``
  now represents midnight on the *following* day.
  Fixed by @cheukting (gh issue #658, gh pr #751)
- Fixed an issue where ``isoparser.parse_isotime`` was unable to handle the
  ``24:00`` variant representation of midnight. (gh pr #773)
- Added support for more than 6 fractional digits in `isoparse`.
  Reported and fixed by @jayschwa (gh issue #786, gh pr #787).
- Added 'z' (lower case Z) as valid UTC time zone in isoparser.
  Reported by @cjgibson (gh issue #820). Fixed by @Cheukting (gh pr #822)
- Fixed a bug with base offset changes during DST in ``tzfile``, and refactored
  the way base offset changes are detected. Originally reported on
  Stack Overflow by @MartinThoma. (gh issue #812, gh pr #810)
- Fixed error condition in ``tz.gettz`` when a non-ASCII timezone is passed on
  Windows in Python 2.7. (gh issue #802, pr #861)
- Improved performance and inspection properties of ``tzname`` methods.
  (gh pr #811)
- Removed unnecessary binary_type compatibility shims.
  Added by @jdufresne (gh pr #817)
- Changed ``python setup.py test`` to print an error to ``stderr`` and exit
  with 1 instead of 0. Reported and fixed by @hroncok (gh pr #814)
- Added a ``pyproject.toml`` file with build requirements and an explicitly
  specified build backend. (gh issue #736, gh prs #746, #863)


Documentation changes
---------------------

- Added documentation for the ``rrule.rrulestr`` function.
  Fixed by @prdickson (gh issue #623, gh pr #762)
- Add documentation for the ``dateutil.tz.win`` module and mocked out certain
  Windows-specific modules so that autodoc can still be run on non-Windows
  systems. (gh issue #442, pr #715)
- Added changelog to documentation. (gh issue #692, gh pr #707)
- Improved documentation on the use of ``until`` and ``count`` parameters in
  ``rrule``. Fixed by @lucaferocino (gh pr #755).
- Added an example of how to use a custom ``parserinfo`` subclass to parse
  non-standard datetime formats in the examples documentation for ``parser``.
  Added by @prdickson (gh #753)
- Expanded the description and examples in the ``relativedelta`` class.
  Contributed by @andrewcbennett (gh pr #759)
- Improved the contributing documentation to clarify where to put new changelog
  files. Contributed by @andrewcbennett (gh pr #757)
- Fixed a broken doctest in the ``relativedelta`` module.
  Fixed by @nherriot (gh pr #758).
- Reorganized ``dateutil.tz`` documentation and fixed issue with the
  ``dateutil.tz`` docstring. (gh pr #714)


Misc
----

- GH #720, GH #723, GH #726, GH #727, GH #740, GH #750, GH #760, GH #767,
  GH #772, GH #773, GH #780, GH #784, GH #785, GH #791, GH #799, GH #813,
  GH #836, GH #839, GH #857


Version 2.7.5 (2018-10-27)
==========================

Data updates
------------

- Update tzdata to 2018g


Version 2.7.4 (2018-10-24)
==========================

Data updates
------------

- Updated tzdata version to 2018f.


Version 2.7.3 (2018-05-09)
==========================

Data updates
------------

- Update tzdata to 2018e. (gh pr #710)


Bugfixes
--------

- Fixed an issue where ``parser.parse`` would raise ``Decimal``-specific errors
  instead of a standard ``ValueError`` if certain malformed values were parsed
  (e.g. ``NaN`` or infinite values). Reported and fixed by
  @amureki (gh issue #662, gh pr #679).
- Fixed issue in ``parser`` where a ``tzinfos`` call explicitly returning
  ``None`` would throw a ``ValueError``.
  Fixed by @parsethis (gh issue #661, gh pr #681)
- Fixed incorrect parsing of certain dates earlier than 100 AD when represented
  in the form "%B.%Y.%d", e.g. "December.0031.30". (gh issue #687, pr #700)
- Added time zone inference when initializing an ``rrule`` with a specified
  ``UNTIL`` but without an explicitly specified ``DTSTART``; the time zone
  of the generated ``DTSTART`` will now be taken from the ``UNTIL`` rule.
  Reported by @href (gh issue #652). Fixed by @absreim (gh pr #693).

Documentation changes
---------------------

- Corrected link syntax and updated URL to https for ISO year week number
  notation in relativedelta examples. (gh issue #670, pr #711)
- Add doctest examples to tzfile documentation. Done by @weatherpattern and
  @pganssle (gh pr #671)
- Updated the documentation for relativedelta. Removed references to tuple
  arguments for weekday, explained effect of weekday(_, 1) and better explained
  the order of operations that relativedelta applies. Fixed by @kvn219
  @huangy22 and @ElliotJH (gh pr #673)
- Added changelog to documentation. (gh issue #692, gh pr #707)
- Changed order of keywords in rrule docstring. Reported and fixed by
  @rmahajan14 (gh issue #686, gh pr #695).
- Added documentation for ``dateutil.tz.gettz``. Reported by @pganssle (gh
  issue #647). Fixed by @weatherpattern (gh pr #704)
- Cleaned up malformed RST in the ``tz`` documentation. (gh issue #702, gh pr
  #706)
- Changed the default theme to ``sphinx_rtd_theme``, and changed the sphinx
  configuration accordingly. (gh pr #707)
- Reorganized ``dateutil.tz`` documentation and fixed issue with the
  ``dateutil.tz`` docstring. (gh pr #714)


Misc
----

- GH #674, GH #688, GH #699


Version 2.7.2 (2018-03-26)
==========================

Bugfixes
--------

- Fixed an issue with the setup script running in non-UTF-8 environment.
  Reported and fixed by @gergondet (gh pr #651)


Misc
----

- GH #655


Version 2.7.1 (2018-03-24)
===========================

Data updates
------------

- Updated tzdata version to 2018d.


Bugfixes
--------

- Fixed issue where parser.parse would occasionally raise
  decimal.Decimal-specific error types rather than ValueError. Reported by
  @amureki (gh issue #632). Fixed by @pganssle (gh pr #636).
- Improve error message when rrule's dtstart and until are not both naive or
  both aware. Reported and fixed by @ryanpetrello (gh issue #633, gh pr #634)


Misc
----

- GH #644, GH #648


Version 2.7.0
=============
- Dropped support for Python 2.6 (gh pr #362 by @jdufresne)
- Dropped support for Python 3.2 (gh pr #626)
- Updated zoneinfo file to 2018c (gh pr #616)
- Changed licensing scheme so all new contributions are dual licensed under
  Apache 2.0 and BSD. (gh pr #542, issue #496)
- Added __all__ variable to the root package. Reported by @tebriel
  (gh issue #406), fixed by @mariocj89 (gh pr #494)
- Added python_requires to setup.py so that pip will distribute the right
  version of dateutil. Fixed by @jakec-github (gh issue #537, pr #552)
- Added the utils submodule, for miscellaneous utilities.
- Added within_delta function to utils - added by @justanr (gh issue #432,
  gh pr #437)
- Added today function to utils (gh pr #474)
- Added default_tzinfo function to utils (gh pr #475), solving an issue
  reported by @nealmcb (gh issue #94)
- Added dedicated ISO 8601 parsing function isoparse (gh issue #424).
  Initial implementation by @pganssle in gh pr #489 and #622, with a
  pre-release fix by @kirit93 (gh issue #546, gh pr #573).
- Moved parser module into parser/_parser.py and officially deprecated the use
  of several private functions and classes from that module. (gh pr #501, #515)
- Tweaked parser error message to include rejected string format, added by
  @pbiering (gh pr #300)
- Add support for parsing bytesarray, reported by @uckelman (gh issue #417) and
  fixed by @uckelman and @pganssle (gh pr #514)
- Started raising a warning when the parser finds a timezone string that it
  cannot construct a tzinfo instance for (rather than succeeding with no
  indication of an error). Reported and fixed by @jbrockmendel (gh pr #540)
- Dropped the use of assert in the parser. Fixed by @jbrockmendel (gh pr #502)
- Fixed to assertion logic in parser to support dates like '2015-15-May',
  reported and fixed by @jbrockmendel (gh pr #409)
- Fixed IndexError in parser on dates with trailing colons, reported and fixed
  by @jbrockmendel (gh pr #420)
- Fixed bug where hours were not validated, leading to improper parse. Reported
  by @heappro (gh pr #353), fixed by @jbrockmendel (gh pr #482)
- Fixed problem parsing strings in %b-%Y-%d format. Reported and fixed by
  @jbrockmendel (gh pr #481)
- Fixed problem parsing strings in the %d%B%y format. Reported by @asishm
  (gh issue #360), fixed by @jbrockmendel (gh pr #483)
- Fixed problem parsing certain unambiguous strings when year <99 (gh pr #510).
  Reported by @alexwlchan (gh issue #293).
- Fixed issue with parsing an unambiguous string representation of an ambiguous
  datetime such that if possible the correct value for fold is set. Fixes
  issue reported by @JordonPhillips and @pganssle (gh issue #318, #320,
  gh pr #517)
- Fixed issue with improper rounding of fractional components. Reported by
  @dddmello (gh issue #427), fixed by @m-dz (gh pr #570)
- Performance improvement to parser from removing certain min() calls. Reported
  and fixed by @jbrockmendel (gh pr #589)
- Significantly refactored parser code by @jbrockmendel (gh prs #419, #436,
  #490, #498, #539) and @pganssle (gh prs #435, #468)
- Implemented of __hash__ for relativedelta and weekday, reported and fixed
  by @mrigor (gh pr #389)
- Implemented __abs__ for relativedelta. Reported by @binnisb and @pferreir
  (gh issue #350, pr #472)
- Fixed relativedelta.weeks property getter and setter to work for both
  negative and positive values. Reported and fixed by @souliane (gh issue #459,
  pr #460)
- Fixed issue where passing whole number floats to the months or years
  arguments of the relativedelta constructor would lead to errors during
  addition. Reported by @arouanet (gh pr #411), fixed by @lkollar (gh pr #553)
- Added a pre-built tz.UTC object representing UTC (gh pr #497)
- Added a cache to tz.gettz so that by default it will return the same object
  for identical inputs. This will change the semantics of certain operations
  between datetimes constructed with tzinfo=tz.gettz(...). (gh pr #628)
- Changed the behavior of tz.tzutc to return a singleton (gh pr #497, #504)
- Changed the behavior of tz.tzoffset to return the same object when passed the
  same inputs, with a corresponding performance improvement (gh pr #504)
- Changed the behavior of tz.tzstr to return the same object when passed the
  same inputs. (gh pr #628)
- Added .instance alternate constructors for tz.tzoffset and tz.tzstr, to
  allow the construction of a new instance if desired. (gh pr #628)
- Added the tz.gettz.nocache function to allow explicit retrieval of a new
  instance of the relevant tzinfo. (gh pr #628)
- Expand definition of tz.tzlocal equality so that the local zone is allow
  equality with tzoffset and tzutc. (gh pr #598)
- Deprecated the idiosyncratic tzstr format mentioned in several examples but
  evidently designed exclusively for dateutil, and very likely not used by
  any current users. (gh issue #595, gh pr #606)
- Added the tz.resolve_imaginary function, which generates a real date from
  an imaginary one, if necessary. Implemented by @Cheukting (gh issue #339,
  gh pr #607)
- Fixed issue where the tz.tzstr constructor would erroneously succeed if
  passed an invalid value for tzstr. Fixed by @pablogsal (gh issue #259,
  gh pr #581)
- Fixed issue with tz.gettz for TZ variables that start with a colon. Reported
  and fixed by @lapointexavier (gh pr #601)
- Added a lock to tz.tzical's cache. Reported and fixed by @Unrud (gh pr #430)
- Fixed an issue with fold support on certain Python 3 implementations that
  used the pre-3.6 pure Python implementation of datetime.replace, most
  notably pypy3 (gh pr #446).
- Added support for VALUE=DATE-TIME for DTSTART in rrulestr. Reported by @potuz
  (gh issue #401) and fixed by @Unrud (gh pr #429)
- Started enforcing that within VTIMEZONE, the VALUE parameter can only be
  omitted or DATE-TIME, per RFC 5545. Reported by @Unrud (gh pr #439)
- Added support for TZID parameter for DTSTART in rrulestr. Reported and
  fixed by @ryanpetrello (gh issue #614, gh pr #624)
- Added 'RRULE:' prefix to rrule strings generated by rrule.__str__, in
  compliance with the RFC. Reported by @AndrewPashkin (gh issue #86), fixed by
  @jarondl and @mlorant (gh pr #450)
- Switched to setuptools_scm for version management, automatically calculating
  a version number from the git metadata. Reported by @jreback (gh issue #511),
  implemented by @Sulley38 (gh pr #564)
- Switched setup.py to use find_packages, and started testing against pip
  installed versions of dateutil in CI. Fixed issue with parser import
  discovered by @jreback in pandas-dev/pandas#18141. (gh issue #507, pr #509)
- Switched test suite to using pytest (gh pr #495)
- Switched CI over to use tox. Fixed by @gaborbernat (gh pr #549)
- Added a test-only dependency on freezegun. (gh pr #474)
- Reduced number of CI builds on Appveyor. Fixed by @kirit93 (gh issue #529,
  gh pr #579)
- Made xfails strict by default, so that an xpass is a failure. (gh pr #567)
- Added a documentation generation stage to tox and CI. (gh pr #568)
- Added an explicit warning when running python setup.py explaining how to run
  the test suites with pytest. Fixed by @lkollar. (gh issue #544, gh pr #548)
- Added requirements-dev.txt for test dependency management (gh pr #499, #516)
- Fixed code coverage metrics to account for Windows builds (gh pr #526)
- Fixed code coverage metrics to NOT count xfails. Fixed by @gaborbernat
  (gh issue #519, gh pr #563)
- Style improvement to zoneinfo.tzfile that was confusing to static type
  checkers. Reported and fixed by @quodlibetor (gh pr #485)
- Several unused imports were removed by @jdufresne. (gh pr #486)
- Switched ``isinstance(*, collections.Callable)`` to callable, which is available
  on all supported Python versions. Implemented by @jdufresne (gh pr #612)
- Added CONTRIBUTING.md (gh pr #533)
- Added AUTHORS.md (gh pr #542)
- Corrected setup.py metadata to reflect author vs. maintainer, (gh issue #477,
  gh pr #538)
- Corrected README to reflect that tests are now run in pytest. Reported and
  fixed by @m-dz (gh issue #556, gh pr #557)
- Updated all references to RFC 2445 (iCalendar) to point to RFC 5545. Fixed
  by @mariocj89 (gh issue #543, gh pr #555)
- Corrected parse documentation to reflect proper integer offset units,
  reported and fixed by @abrugh (gh pr #458)
- Fixed dangling parenthesis in tzoffset documentation (gh pr #461)
- Started including the license file in wheels. Reported and fixed by
  @jdufresne (gh pr #476)
- Indentation fixes to parser docstring by @jbrockmendel (gh pr #492)
- Moved many examples from the "examples" documentation into their appropriate
  module documentation pages. Fixed by @Tomasz-Kluczkowski and @jakec-github
  (gh pr #558, #561)
- Fixed documentation so that the parser.isoparse documentation displays.
  Fixed by @alexchamberlain (gh issue #545, gh pr #560)
- Refactored build and release sections and added setup instructions to
  CONTRIBUTING. Reported and fixed by @kynan (gh pr #562)
- Cleaned up various dead links in the documentation. (gh pr #602, #608, #618)

Version 2.6.1
=============
- Updated zoneinfo file to 2017b. (gh pr #395)
- Added Python 3.6 to CI testing (gh pr #365)
- Removed duplicate test name that was preventing a test from being run.
  Reported and fixed by @jdufresne (gh pr #371)
- Fixed testing of folds and gaps, particularly on Windows (gh pr #392)
- Fixed deprecated escape characters in regular expressions. Reported by
  @nascheme and @thierryba (gh issue #361), fixed by @thierryba (gh pr #358)
- Many PEP8 style violations and other code smells were fixed by @jdufresne
  (gh prs #358, #363, #364, #366, #367, #368, #372, #374, #379, #380, #398)
- Improved performance of tzutc and tzoffset objects. (gh pr #391)
- Fixed issue with several time zone classes around DST transitions in any
  zones with +0 standard offset (e.g. Europe/London) (gh issue #321, pr #390)
- Fixed issue with fuzzy parsing where tokens similar to AM/PM that are in the
  end skipped were dropped in the fuzzy_with_tokens list. Reported and fixed
  by @jbrockmendel (gh pr #332).
- Fixed issue with parsing dates of the form X m YY. Reported by @jbrockmendel.
  (gh issue #333, pr #393)
- Added support for parser weekdays with less than 3 characters. Reported by
  @arcadefoam (gh issue #343), fixed by @jonemo (gh pr #382)
- Fixed issue with the addition and subtraction of certain relativedeltas.
  Reported and fixed by @kootenpv (gh issue #346, pr #347)
- Fixed issue where the COUNT parameter of rrules was ignored if 0. Fixed by
  @mshenfield (gh pr #330), reported by @vaultah (gh issue #329).
- Updated documentation to include the new tz methods. (gh pr #324)
- Update documentation to reflect that the parser can raise TypeError, reported
  and fixed by @tomchuk (gh issue #336, pr #337)
- Fixed an incorrect year in a parser doctest. Fixed by @xlotlu (gh pr #357)
- Moved version information into _version.py and set up the versions more
  granularly.

Version 2.6.0
=============
- Added PEP-495-compatible methods to address ambiguous and imaginary dates in
  time zones in a backwards-compatible way. Ambiguous dates and times can now
  be safely represented by all dateutil time zones. Many thanks to Alexander
  Belopolski (@abalkin) and Tim Peters @tim-one for their inputs on how to
  address this. Original issues reported by Yupeng and @zed (lP: 1390262,
  gh issues #57, #112, #249, #284, #286, prs #127, #225, #248, #264, #302).
- Added new methods for working with ambiguous and imaginary dates to the tz
  module. datetime_ambiguous() determines if a datetime is ambiguous for a given
  zone and datetime_exists() determines if a datetime exists in a given zone.
  This works for all fold-aware datetimes, not just those provided by dateutil.
  (gh issue #253, gh pr #302)
- Fixed an issue where dst() in Portugal in 1996 was returning the wrong value
  in tz.tzfile objects. Reported by @abalkin (gh issue #128, pr #225)
- Fixed an issue where zoneinfo.ZoneInfoFile errors were not being properly
  deep-copied. (gh issue #226, pr #225)
- Refactored tzwin and tzrange as a subclass of a common class, tzrangebase, as
  there was substantial overlapping functionality. As part of this change,
  tzrange and tzstr now expose a transitions() function, which returns the
  DST on and off transitions for a given year. (gh issue #260, pr #302)
- Deprecated zoneinfo.gettz() due to confusion with tz.gettz(), in favor of
  get() method of zoneinfo.ZoneInfoFile objects. (gh issue #11, pr #310)
- For non-character, non-stream arguments, parser.parse now raises TypeError
  instead of AttributeError. (gh issues #171, #269, pr #247)
- Fixed an issue where tzfile objects were not properly handling dst() and
  tzname() when attached to datetime.time objects. Reported by @ovacephaloid.
  (gh issue #292, pr #309)
- /usr/share/lib/zoneinfo was added to TZPATHS for compatibility with Solaris
  systems. Reported by @dhduvall (gh issue #276, pr #307)
- tzoffset and tzrange objects now accept either a number of seconds or a
  datetime.timedelta() object wherever previously only a number of seconds was
  allowed. (gh pr #264, #277)
- datetime.timedelta objects can now be added to relativedelta objects. Reported
  and added by Alec Nikolas Reiter (@justanr) (gh issue #282, pr #283
- Refactored relativedelta.weekday and rrule.weekday into a common base class
  to reduce code duplication. (gh issue #140, pr #311)
- An issue where the WKST parameter was improperly rendering in str(rrule) was
  reported and fixed by Daniel LePage (@dplepage). (gh issue #262, pr #263)
- A replace() method has been added to rrule objects by @jendas1, which creates
  new rrule with modified attributes, analogous to datetime.replace (gh pr #167)
- Made some significant performance improvements to rrule objects in Python 2.x
  (gh pr #245)
- All classes defining equality functions now return NotImplemented when
  compared to unsupported classes, rather than raising TypeError, to allow other
  classes to provide fallback support. (gh pr #236)
- Several classes have been marked as explicitly unhashable to maintain
  identical behavior between Python 2 and 3. Submitted by Roy Williams
  (@rowillia) (gh pr #296)
- Trailing whitespace in easter.py has been removed. Submitted by @OmgImAlexis
  (gh pr #299)
- Windows-only batch files in build scripts had line endings switched to CRLF.
  (gh pr #237)
- @adamchainz updated the documentation links to reflect that the canonical
  location for readthedocs links is now at .io, not .org. (gh pr #272)
- Made some changes to the CI and codecov to test against newer versions of
  Python and pypy, and to adjust the code coverage requirements. For the moment,
  full pypy3 compatibility is not supported until a new release is available,
  due to upstream bugs in the old version affecting PEP-495 support.
  (gh prs #265, #266, #304, #308)
- The full PGP signing key fingerprint was added to the README.md in favor of
  the previously used long-id. Reported by @valholl (gh issue #287, pr #304)
- Updated zoneinfo to 2016i. (gh issue #298, gh pr #306)


Version 2.5.3
=============
- Updated zoneinfo to 2016d
- Fixed parser bug where unambiguous datetimes fail to parse when dayfirst is
  set to true. (gh issue #233, pr #234)
- Bug in zoneinfo file on platforms such as Google App Engine which do not
  do not allow importing of subprocess.check_call was reported and fixed by
  @savraj (gh issue #239, gh pr #240)
- Fixed incorrect version in documentation (gh issue #235, pr #243)

Version 2.5.2
=============
- Updated zoneinfo to 2016c
- Fixed parser bug where yearfirst and dayfirst parameters were not being
  respected when no separator was present. (gh issue #81 and #217, pr #229)

Version 2.5.1
=============
- Updated zoneinfo to 2016b
- Changed MANIFEST.in to explicitly include test suite in source distributions,
  with help from @koobs (gh issue #193, pr #194, #201, #221)
- Explicitly set all line-endings to LF, except for the NEWS file, on a
  per-repository basis (gh pr #218)
- Fixed an issue with improper caching behavior in rruleset objects (gh issue
  #104, pr #207)
- Changed to an explicit error when rrulestr strings contain a missing BYDAY
  (gh issue #162, pr #211)
- tzfile now correctly handles files containing leapcnt (although the leapcnt
  information is not actually used). Contributed by @hjoukl (gh issue #146, pr
  #147)
- Fixed recursive import issue with tz module (gh pr #204)
- Added compatibility between tzwin objects and datetime.time objects (gh issue
  #216, gh pr #219)
- Refactored monolithic test suite by module (gh issue #61, pr #200 and #206)
- Improved test coverage in the relativedelta module (gh pr #215)
- Adjusted documentation to reflect possibly counter-intuitive properties of
  RFC-5545-compliant rrules, and other documentation improvements in the rrule
  module (gh issue #105, gh issue #149 - pointer to the solution by @phep,
  pr #213).


Version 2.5.0
=============
- Updated zoneinfo to 2016a
- zoneinfo_metadata file version increased to 2.0 - the updated updatezinfo.py
  script will work with older zoneinfo_metadata.json files, but new metadata
  files will not work with older updatezinfo.py versions. Additionally, we have
  started hosting our own mirror of the Olson databases on a GitHub pages
  site (https://dateutil.github.io/tzdata/) (gh pr #183)
- dateutil zoneinfo tarballs now contain the full zoneinfo_metadata file used
  to generate them. (gh issue #27, gh pr #85)
- relativedelta can now be safely subclassed without derived objects reverting
  to base relativedelta objects as a result of arithmetic operations.
  (lp:1010199, gh issue #44, pr #49)
- relativedelta 'weeks' parameter can now be set and retrieved as a property of
  relativedelta instances. (lp: 727525, gh issue #45, pr #49)
- relativedelta now explicitly supports fractional relative weeks, days, hours,
  minutes and seconds. Fractional values in absolute parameters (year, day, etc)
  are now deprecated. (gh issue #40, pr #190)
- relativedelta objects previously did not use microseconds to determine of two
  relativedelta objects were equal. This oversight has been corrected.
  Contributed by @elprans (gh pr #113)
- rrule now has an xafter() method for retrieving multiple recurrences after a
  specified date. (gh pr #38)
- str(rrule) now returns an RFC2445-compliant rrule string, contributed by
  @schinckel and @armicron (lp:1406305, gh issue #47, prs #50, #62 and #160)
- rrule performance under certain conditions has been significantly improved
  thanks to a patch contributed by @dekoza, based on an article by Brian Beck
  (@exogen) (gh pr #136)
- The use of both the 'until' and 'count' parameters is now deprecated as
  inconsistent with RFC2445 (gh pr #62, #185)
- Parsing an empty string will now raise a ValueError, rather than returning the
  datetime passed to the 'default' parameter. (gh issue #78, pr #187)
- tzwinlocal objects now have a meaningful repr() and str() implementation
  (gh issue #148, prs #184 and #186)
- Added equality logic for tzwin and tzwinlocal objects. (gh issue #151,
  pr #180, #184)
- Added some flexibility in subclassing timelex, and switched the default
  behavior over to using string methods rather than comparing against a fixed
  list. (gh pr #122, #139)
- An issue causing tzstr() to crash on Python 2.x was fixed. (lp: 1331576,
  gh issue #51, pr #55)
- An issue with string encoding causing exceptions under certain circumstances
  when tzname() is called was fixed. (gh issue #60, #74, pr #75)
- Parser issue where calling parse() on dates with no day specified when the
  day of the month in the default datetime (which is "today" if unspecified) is
  greater than the number of days in the parsed month was fixed (this issue
  tended to crop up between the 29th and 31st of the month, for obvious reasons)
  (canonical gh issue #25, pr #30, #191)
- Fixed parser issue causing fuzzy_with_tokens to raise an unexpected exception
  in certain circumstances. Contributed by @MichaelAquilina (gh pr #91)
- Fixed parser issue where years > 100 AD were incorrectly parsed. Contributed
  by @Bachmann1234 (gh pr #130)
- Fixed parser issue where commas were not a valid separator between seconds
  and microseconds, preventing parsing of ISO 8601 dates. Contributed by
  @ryanss (gh issue #28, pr #106)
- Fixed issue with tzwin encoding in locales with non-Latin alphabets
  (gh issue #92, pr #98)
- Fixed an issue where tzwin was not being properly imported on Windows.
  Contributed by @labrys. (gh pr #134)
- Fixed a problem causing issues importing zoneinfo in certain circumstances.
  Issue and solution contributed by @alexxv (gh issue #97, pr #99)
- Fixed an issue where dateutil timezones were not compatible with basic time
  objects. One of many, many timezone related issues contributed and tested by
  @labrys. (gh issue #132, pr #181)
- Fixed issue where tzwinlocal had an invalid utcoffset. (gh issue #135,
  pr #141, #142)
- Fixed issue with tzwin and tzwinlocal where DST transitions were incorrectly
  parsed from the registry. (gh issue #143, pr #178)
- updatezinfo.py no longer suppresses certain OSErrors. Contributed by @bjamesv
  (gh pr #164)
- An issue that arose when timezone locale changes during runtime has been
  fixed by @carlosxl and @mjschultz (gh issue #100, prs #107, #109)
- Python 3.5 was added to the supported platforms in the metadata (@tacaswell
  gh pr #159) and the test suites (@moreati gh pr #117).
- An issue with tox failing without unittest2 installed in Python 2.6 was fixed
  by @moreati (gh pr #115)
- Several deprecated functions were replaced in the tests by @moreati
  (gh pr #116)
- Improved the logic in Travis and Appveyor to alleviate issues where builds
  were failing due to connection issues when downloading the IANA timezone
  files. In addition to adding our own mirror for the files (gh pr #183), the
  download is now retried a number of times (with a delay) (gh pr #177)
- Many failing doctests were fixed by @moreati. (gh pr #120)
- Many fixes to the documentation (gh pr #103, gh pr #87 from @radarhere,
  gh pr #154 from @gpoesia, gh pr #156 from @awsum, gh pr #168 from @ja8zyjits)
- Added a code coverage tool to the CI to help improve the library. (gh pr #182)
- We now have a mailing list - <EMAIL>, graciously hosted by
  Python.org.


Version 2.4.2
=============
- Updated zoneinfo to 2015b.
- Fixed issue with parsing of tzstr on Python 2.7.x; tzstr will now be decoded
  if not a unicode type. gh #51 (lp:1331576), gh pr #55.
- Fix a parser issue where AM and PM tokens were showing up in fuzzy date
  stamps, triggering inappropriate errors. gh #56 (lp: 1428895), gh pr #63.
- Missing function "setcachesize" removed from zoneinfo __all__ list by @ryanss,
  fixing an issue with wildcard imports of dateutil.zoneinfo. (gh pr #66).
- (PyPI only) Fix an issue with source distributions not including the test
  suite.


Version 2.4.1
=============

- Added explicit check for valid hours if AM/PM is specified in parser.
  (gh pr #22, issue #21)
- Fix bug in rrule introduced in 2.4.0 where byweekday parameter was not
  handled properly. (gh pr #35, issue #34)
- Fix error where parser allowed some invalid dates, overwriting existing hours
  with the last 2-digit number in the string. (gh pr #32, issue #31)
- Fix and add test for Python 2.x compatibility with boolean checking of
  relativedelta objects. Implemented by @nimasmi (gh pr #43) and CÃ©dric Krier
  (lp: 1035038)
- Replaced parse() calls with explicit datetime objects in unit tests unrelated
  to parser. (gh pr #36)
- Changed private _byxxx from sets to sorted tuples and fixed one currently
  unreachable bug in _construct_byset. (gh pr #54)
- Additional documentation for parser (gh pr #29, #33, #41) and rrule.
- Formatting fixes to documentation of rrule and README.rst.
- Updated zoneinfo to 2015a.

Version 2.4.0
=============

- Fix an issue with relativedelta and freezegun (lp:1374022)
- Fix tzinfo in windows for timezones without dst (lp:1010050, gh #2)
- Ignore missing timezones in windows like in POSIX
- Fix minimal version requirement for six (gh #6)
- Many rrule changes and fixes by @pganssle (gh pull requests #13 #14 #17),
    including defusing some infinite loops (gh #4)

Version 2.3
===========

- Cleanup directory structure, moved test.py to dateutil/tests/test.py

- Changed many aspects of dealing with the zone info file. Instead of a cache,
  all the zones are loaded to memory, but symbolic links are loaded only once,
  so not much memory is used.

- The package is now zip-safe, and universal-wheelable, thanks to changes in
  the handling of the zoneinfo file.

- Fixed tzwin silently not imported on windows python2

- New maintainer, together with new hosting: GitHub, Travis, Read-The-Docs

Version 2.2
===========

- Updated zoneinfo to 2013h

- fuzzy_with_tokens parse addon from Christopher Corley

- Bug with LANG=C fixed by Mike Gilbert

Version 2.1
===========

- New maintainer

- Dateutil now works on Python 2.6, 2.7 and 3.2 from same codebase (with six)

- #704047: Ismael Carnales' patch for a new time format

- Small bug fixes, thanks for reporters!


Version 2.0
===========

- Ported to Python 3, by Brian Jones.  If you need dateutil for Python 2.X,
  please continue using the 1.X series.

- There's no such thing as a "PSF License".  This source code is now
  made available under the Simplified BSD license.  See LICENSE for
  details.

Version 1.5
===========

- As reported by Mathieu Bridon, rrules were matching the bysecond rules
  incorrectly against byminute in some circumstances when the SECONDLY
  frequency was in use, due to a copy & paste bug.  The problem has been
  unittested and corrected.

- Adam Ryan reported a problem in the relativedelta implementation which
  affected the yearday parameter in the month of January specifically.
  This has been unittested and fixed.

- Updated timezone information.


Version 1.4.1
=============

- Updated timezone information.


Version 1.4
===========

- Fixed another parser precision problem on conversion of decimal seconds
  to microseconds, as reported by Erik Brown.  Now these issues are gone
  for real since it's not using floating point arithmetic anymore.

- Fixed case where tzrange.utcoffset and tzrange.dst() might fail due
  to a date being used where a datetime was expected (reported and fixed
  by Lennart Regebro).

- Prevent tzstr from introducing daylight timings in strings that didn't
  specify them (reported by Lennart Regebro).

- Calls like gettz("GMT+3") and gettz("UTC-2") will now return the
  expected values, instead of the TZ variable behavior.

- Fixed DST signal handling in zoneinfo files.  Reported by
  Nicholas F. Fabry and John-Mark Gurney.


Version 1.3
===========

- Fixed precision problem on conversion of decimal seconds to
  microseconds, as reported by Skip Montanaro.

- Fixed bug in constructor of parser, and converted parser classes to
  new-style classes.  Original report and patch by Michael ElsdÃ¶rfer.

- Initialize tzid and comps in tz.py, to prevent the code from ever
  raising a NameError (even with broken files).  Johan Dahlin suggested
  the fix after a pyflakes run.

- Version is now published in dateutil.__version__, as requested
  by Darren Dale.

- All code is compatible with new-style division.


Version 1.2
===========

- Now tzfile will round timezones to full-minutes if necessary,
  since Python's datetime doesn't support sub-minute offsets.
  Thanks to Ilpo NyyssÃ¶nen for reporting the issue.

- Removed bare string exceptions, as reported and fixed by
  Wilfredo SÃ¡nchez Vega.

- Fix bug in leap count parsing (reported and fixed by Eugene Oden).


Version 1.1
===========

- Fixed rrule byyearday handling. Abramo Bagnara pointed out that
  RFC2445 allows negative numbers.

- Fixed --prefix handling in setup.py (by Sidnei da Silva).

- Now tz.gettz() returns a tzlocal instance when not given any
  arguments and no other timezone information is found.

- Updating timezone information to version 2005q.


Version 1.0
===========

- Fixed parsing of XXhXXm formatted time after day/month/year
  has been parsed.

- Added patch by Jeffrey Harris optimizing rrule.__contains__.


Version 0.9
===========

- Fixed pickling of timezone types, as reported by
  Andreas KÃ¶hler.

- Implemented internal timezone information with binary
  timezone files. datautil.tz.gettz() function will now
  try to use the system timezone files, and fallback to
  the internal versions. It's also possible to ask for
  the internal versions directly by using
  dateutil.zoneinfo.gettz().

- New tzwin timezone type, allowing access to Windows
  internal timezones (contributed by Jeffrey Harris).

- Fixed parsing of unicode date strings.

- Accept parserinfo instances as the parser constructor
  parameter, besides parserinfo (sub)classes.

- Changed weekday to spell the not-set n value as None
  instead of 0.

- Fixed other reported bugs.


Version 0.5
===========

- Removed ``FREQ_`` prefix from rrule frequency constants
  WARNING: this breaks compatibility with previous versions.

- Fixed rrule.between() for cases where "after" is achieved
  before even starting, as reported by Andreas KÃ¶hler.

- Fixed two digit zero-year parsing (such as 31-Dec-00), as
  reported by Jim Abramson, and included test case for this.

- Sort exdate and rdate before iterating over them, so that
  it's not necessary to sort them before adding to the rruleset,
  as reported by Nicholas Piper.
