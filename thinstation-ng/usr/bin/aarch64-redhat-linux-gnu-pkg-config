#!/usr/bin/sh

# Simple wrapper to tell pkgconf to behave as a platform-specific version of pkg-config
# Platform: aarch64-redhat-linux-gnu

if [ -z "${RPM_BUILD_ROOT}" ]; then
	export PKG_CONFIG_LIBDIR="${PKG_CONFIG_LIBDIR:-/usr/local/lib64/pkgconfig:/usr/local/share/pkgconfig:/usr/lib64/pkgconfig:/usr/share/pkgconfig}"
else
	export PKG_CONFIG_LIBDIR="${PKG_CONFIG_LIBDIR:-/usr/lib64/pkgconfig:/usr/share/pkgconfig}"
fi
export PKG_CONFIG_SYSTEM_LIBRARY_PATH="${PKG_CONFIG_SYSTEM_LIBRARY_PATH:-/usr/lib64}"
export PKG_CONFIG_SYSTEM_INCLUDE_PATH="${PKG_CONFIG_SYSTEM_INCLUDE_PATH:-/usr/include}"

exec pkgconf "$@"
