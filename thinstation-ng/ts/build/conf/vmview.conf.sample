# VMWare View Global Options
# Connect to server without waiting for the user to click connect
#VMVIEW_AUTOCONNECT=TRUE
# Set the certificate verification mode
#VMVIEW_SSLVERIFYMODE=3
# Default server to connect to if none specified
#VMVIEW_DEFAULTBROKER=***********
# Default SSL protocols to use
#VMVIEW_SSLPROTOCOL=
# Default SSL ciphers to us
#VMVIEW_SSLCIPHERS=
# Module to use for PKCS#11
#PKCS11_MODULE=

# VMWare View Session options
#SESSION_#_TITLE="VDI"
#SESSION_#_TYPE=vmview
#SESSION_#_AUTOSTART=On
#SESSION_#_VMVIEW_SERVERURL=***********
#SESSION_#_VMVIEW_FULLSCREEN=true
#SESSION_#_VMVIEW_NONINTERACTIVE=true
#SESSION_#_VMVIEW_KEEP-WM-BINDINGS=true
#SESSION_#_VMVIEW_BACKGROUND=/etc/background.jpg
#SESSION_#_VMVIEW_DOMAINNAME=donaldduck.com
#SESSION_#_VMVIEW_DESKTOPNAME=mickey
#SESSION_#_VMVIEW_USERNAME=minnie
#SESSION_#_VMVIEW_PASSWORD=mouse
#SESSION_#_VMVIEW_REDIRECT1="disk:usb=/mnt/usbdevice/sda1"
#SESSION_#_VMVIEW_REDIRECT2="printer:usb='HP LaserJet 4'"
#SESSION_#_VMVIEW_USB1="????"
#SESSION_#_VMVIEW_USB2="????"
#SESSION_#_VMVIEW_MMRPATH=/usr/local/mmr
#SESSION_#_VMVIEW_RDESKTOPOPTIONS="-N"

