#!/bin/bash
mkdir -p /arch
cd /arch
if [ ! -e packages ]; then
	git clone --depth 1 git://projects.archlinux.org/svntogit/packages.git
else
	cd packages
	git pull
	cd ..
fi
if [ ! -e community ]; then
	git clone --depth 1 git://projects.archlinux.org/svntogit/community.git
else
	cd community
	git pull
	cd ..
fi

update_port()
{
#	echo "hell yes"
	sed -i -e "s/$TSVER/$pkgver/g" $TPKGFILE
	prt-get update -fr -if -im -is $prt
	echo $prt >> /updated
}

if [ -n "$1" ]; then
	prts="`ls /ts/ports/$1/`"
else
	prts="`prt-get listinst`"
fi
for prt in $prts; do
	TPKGFILE=`prt-get path $prt`/Pkgfile
	. $TPKGFILE
	TSVER=`eval echo $version`
	if [ -z "$archname" ]; then
		archname=$prt
	fi
	ADIR=`find /arch/ -mindepth 2 -maxdepth 2 -type d -name $archname`
	if [ -n "$ADIR" ]; then
		APKGFILE=`find $ADIR -name PKGBUILD |grep -v testing |grep -E '\-any|x86_64'`
		. $APKGFILE
		if [ "$TSVER" != "$pkgver" ]; then
			echo $prt
			echo -e "Thin Version is $TSVER"
			echo -e "Arch Version is $pkgver\n"
			echo -e "Would you like to try and update?"
			read answer
			case $answer in
				y|Y)	update_port;;
				*)	unset TPKGFILE ADIR APKGFILE pkgver version archname
					continue;;
			esac
		fi
	else
		echo -e "Port $prt not found\n"
		echo $prt >> /notfound
	fi
	unset TPKGFILE ADIR APKGFILE pkgver version archname
done

