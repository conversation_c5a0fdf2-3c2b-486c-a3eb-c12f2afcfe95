AUDIO_LEVEL=90
MIC_LEVEL=0
SESSION_0_TYPE=xfwm4
SESSION_0_AUTOSTART=on
XFWM4_COMPOSITING=true
XFWM4_RED=0
XFWM4_GREEN=0
XFWM4_BLUE=0
XFWM4_START_POSITION="BOTTOM"
DESKTOP_ICON_SIZE=48
DESKTOP_FONT_SIZE=8
GTK_THEME=Adwaita
XFWM4_THEME=Default
PANEL_PAGER=false
PANEL_USER=false
PANEL_REV_CLOCKSYSTRAY=true
ICONS_THEME="Adwaita"
ICONS_CUT_SIZES="256x256 scalable"
XFWM4_START_ICON=ThinStation
XTERM_CMD="xfce4-terminal"
NET_USE=BOTH
NET_USE_DHCP=on
NET_HOSTNAME=ts-*
NET_TELNETD_ENABLED=ON
TIME_ZONE=America/Los_Angeles
NET_TIME_SERVER=us.pool.ntp.org
NET_REMOTE_ACCESS_FROM="0.0.0.0"
TFTP_BLOCKSIZE=1024
BIND_MOUNT0="storage:/mnt/storage"
MOUNT_0="LABEL=THINSTATION     /boot           auto    x-mount.mkdir,defaults  0       0"
MOUNT_1="LABEL=home     /root           auto    x-mount.mkdir,defaults  0       0"
MOUNT_2="LABEL=swap     swap            swap    defaults        0       0"
STORAGE_CONFIG1=/mnt/storage
USB_STORAGE_SYNC=on
LOCALE=en_US
ICA_DISABLE_START_CONFIG=true
MEMORY_CONSTRAINED=true
