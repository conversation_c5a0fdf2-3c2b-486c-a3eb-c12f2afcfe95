Program: Pinentry
Homepage: https://gnupg.org/software/pinentry/
Download: https://gnupg.org/ftp/gcrypt/pinentry/
Repository: git://git.gnupg.org/pinentry.git
Bug reports: https://bugs.gnupg.org
Security related bug reports: <<EMAIL>>
License: GPLv2

Pinentry is free software.  See the files COPYING for copying conditions.
License copyright years may be listed using range notation, e.g.,
2000-2016, indicating that every year in the range, inclusive, is a
copyrightable year that would otherwise be listed individually.

List of Copyright holders
=========================

Copyright (C) 1999 <PERSON> <<EMAIL>>
Copyright (C) 2001-2004, 2007-2008, 2010, 2015-2017, 2021 g10 Code GmbH
Copyright (C) 2002, 2008 Klarälvdalens Datakonsult AB (KDAB)
Copyright (C) 2004 by <PERSON><PERSON> <<EMAIL>>
Copyright (C) 2007 Ingo Klöcker
Copyright (C) 2014 Serge V<PERSON>oko<PERSON>
Copyright (C) 2015 Daiki <PERSON>
Copyright (C) 2015 <PERSON> <<EMAIL>>
Copyright (C) 2016 Intevation GmbH
Copyright (C) 2016 Anatoly madRat L. Berenblit


Authors
-------

<PERSON> Bihlmeyer <<EMAIL>>
Werner Koch, g10 Code GmbH <<EMAIL>>
Steffen Hansen, Klarälvdalens Datakonsult AB
               <<EMAIL>>
Marcus Brinkmann, g10 Code GmbH <<EMAIL>>
Timo Schulz, g10 Code GmbH
Neal Walfied, g10 Code GmbH <<EMAIL>>
Daniel Kahn Gillmor <<EMAIL>>


License
========

The optional TQt pinentry (found in the directory tqt/) is distributed
under the GNU General Public License, version 2 (GPLv2only).  All
other parts of Pinentry are distributed under the GNU General Public
License, version 2 or later (GPLv2+).  See the file COPYING for details.
