##
# --- LPR Printing Options
#
# CUPS_PRINTER_0_NAME        Workstation Printer Name, Can be Any Valid Name
#                            If you have turned ICA_PRINTER=ON then this is the 
#                            name of the printer driver
# CUPS_PRINTER_0_DEVICEURI   Workstation printer device (if not specified devices
#                            are not loaded).
#
#                            Requirement:
#                            FileDevice Yes in /etc/cups/cups-files.conf  (by default enabled in thinstation) 
#
#                            file:///dev/lp[0-2]   for parallel ports
#                            file:///dev/usb/lp[0-2] for USB printers
#                            socket://ip_address for network printers
#			
# CUPS_PRINTER_0_DEFAULT     Set as the default printer
#
# CUPS_PRINTER_0_OPTIONS_0   Set a printer option
#                      
#
# CUPS_PRINTER_1_*           See CUPS_PRINTER_0_*
# CUPS_PRINTER_2_*           See CUPS_PRINTER_0_*
# CUPS_PRINTER_3_*           See CUPS_PRINTER_0_*

 # If having trouble with autocreate, keep the name "CUPS_PRINTER_?_NAME" under 15 characters & don't use spaces 
#Example network printer
#CUPS_PRINTER_0_NAME="sample network printer 0"
#CUPS_PRINTER_0_DEVICEURI="socket://ip_address"
#CUPS_PRINTER_0_DRIVER="drv:///sample.drv/generpcl.ppd"
#CUPS_PRINTER_0_DEFAULT=true
#CUPS_PRINTER_0_OPTION_0="PageSize=A4"

#Example usb printer
#CUPS_PRINTER_1_NAME="sample usb printer 1"
#CUPS_PRINTER_1_DEVICEURI="file:///dev/usb/lp0"
#CUPS_PRINTER_1_DRIVER="drv:///sample.drv/generpcl.ppd"

#Example lpt printer
#CUPS_PRINTER_2_NAME="sample lpt printer 2"
#CUPS_PRINTER_2_DEVICEURI="file:///dev/lp0"
#CUPS_PRINTER_2_DRIVER="drv:///sample.drv/generpcl.ppd"
