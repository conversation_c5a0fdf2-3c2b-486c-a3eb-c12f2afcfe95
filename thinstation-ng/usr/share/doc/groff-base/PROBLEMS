    Copyright 1989-2023 Free Software Foundation, Inc.

    Copying and distribution of this file, with or without modification,
    are permitted in any medium without royalty provided the copyright
    notice and this notice are preserved.

This file describes various problems that have been encountered in
compiling, installing and running groff.  Suggestions for additions or
other improvements to this file are welcome.

----------------------------------------------------------------------

Problems are organized into categories underscored with equals signs.

* General Problems
* Printing and Display Problems
* Platform-Dependent Macro Problems
* Compilation Problems

Within each category, items are organized in reverse chronological order
(that is, with the most recent first).  groff version numbers
corresponding to their inclusion in this file are included as
guideposts.  Entries have been revised for clarity in the years since.

In the following discussions, several references to "/usr/local" are
made.  You should read this directory name as the destination directory
you set up with the "--prefix" option to groff's "configure" script.


General Problems
================


[groff 1.23.0]

* gdiffmk doesn't work / its automated test fails.

Some portability issues are known to affect groff's gdiffmk utility.

- A defect in GNU diffutils 3.9 (January 2023) causes gdiffmk to
  malfunction and its automated test to fail.  See
  <https://debbugs.gnu.org/db/61/61193.html>.

- gdiffmk does not work with BusyBox diff (which does not implement GNU
  diff's "-D" option).

- gdiffmk does not work on FreeBSD due to specifics of that platform's
  expr(1) implementation.

  gdiffmk uses the expr(1) command to parse its arguments.  FreeBSD has
  extended the syntax of its expr command in a non-backward compatible
  way that it claims better conforms with POSIX's utility syntax
  guidelines with respect to option processing: however, POSIX mandates
  no options for expr.  Other implementations of expr do not support
  traditional Unix-style options ('-a', '-b', ...), and perhaps as a
  consequence do not follow FreeBSD's interpretation of the guidelines.
  You way want to set $EXPR_COMPAT in your shell environment.  We hope
  to have a workaround for this behavior in a future release.

----------------------------------------------------------------------

* The `PDFPIC` macro doesn't work / its automated test fails.

  FAIL: tmac/tests/pdfpic_does-not-choke-on-bad-pdfinfo-output.sh

Due to a limitation (shared by AT&T troff) in the way the `sy` request
constructs a C string argument to the C library's system(3) function,
groff requires a GNU sed(1) extension that interprets "\n" as a newline
in the replacement text of the 's' command.  (We might enhance GNU
troff's `sy` request to avoid this dependency in the future.)  We have
observed this problem on Solaris 10 and 11 and Mac OS X 10.11.6, but not
macOS 12: the last's sed supports the extension in question.

Install GNU sed in the default $PATH as "gsed" and edit
tmac/pdfpic.tmac.  On line 172, change "sed" to "gsed".  Alternatively,
you can use the absolute path to GNU sed's location.  (`system()`
sanitizes $PATH to avoid privilege escalation.)  Then (re-)make the
"check" target or format your PDFPIC-employing document again.

----------------------------------------------------------------------

[groff 1.19.2]

* When viewing man pages, some characters on my UTF-8 terminal emulator
  look funny or copy-and-paste wrong.  Why?

Some Unicode Basic Latin ("ASCII") input characters are mapped to
non-Basic Latin code points in output for consistency with other output
devices, like PDF.  See groff_man_style(7) and groff_char(7) for correct
input conventions and background.  If you use the correct groff special
character escape sequences to input them, you will get correct output no
matter what device the input is formatted for.

However, many man pages are written in ignorance of the correct special
characters to obtain the desired glyphs.  You can conceal these errors
by adding the following to your site-local man(7) configuration.  The
file is called "man.local"; its installation directory depends on how
groff was configured when it was built.

--- start ---
.if '\*[.T]'utf8' \{\
.  char ' \[aq]
.  char - \-
.  char ^ \[ha]
.  char ` \[ga]
.  char ~ \[ti]
.\}
--- end ---

You may also wish to do the same for "mdoc.local".

In man pages (only), groff maps the minus sign special character '\-' to
the Basic Latin hyphen-minus (U+002D) because man pages require this
glyph and there is no historically established *roff input character,
ordinary or special, for obtaining it when a hyphen and minus sign are
both separately available.  To obtain a true minus sign, use the special
character escape sequences '\(mi' or '\[mi]'.

----------------------------------------------------------------------

* Displaying a man page on a terminal with/without my favorite pager
  shows garbage.

groff's terminal output driver, grotty, by default uses ISO 6429/ECMA-48
escape sequences understood by video terminals and their emulators,
rather than the overstriking sequences implemented for typewriter-like
terminals used in the 1960s and 1970s.  These escape sequences control
display attributes like bold and italic or oblique typefaces,
underlining, foreground and background color selection, and hyperlink
marking.  Terminal emulators that claim compatibility with the DEC
VT100, Linux console driver, or xterm should ignore well-formed escape
sequences that they are not able to support, but some implementations
are buggy.

Furthermore, the popular "less" pager by default assumes that its input
will use overstriking sequences.  (This is a surprising choice, as users
of paper terminals had no need for pager programs; to "scroll back", the
operator would simply physically pull up the spool of ejected paper to
read it.)  less(1) must instead be given the '-R' option to interpret
escape sequences used by video terminals.  Be aware that the
overstriking convention is inescapably ambiguous in some output
sequences.  See the grotty(1) man page.

Hyperlink support in terminal emulators is a relatively new initiative
(as of 2022) employing a sequence known as "OSC 8".
  https://gist.github.com/egmontkob/eb114294efbcd5adb1944c9f3cb5feda

Due to the feature's young age, the man and mdoc macro packages have a
configuration switch for hyperlink support, and it may be disabled in
your site's man.local and mdoc.local files.  Use a command like
  printf '\033]8;;man:grotty(1)\033\\grotty(1)\033]8;;\033\\\n' | more
to check your terminal and pager for OSC 8 support.  If you see
"grotty(1)" and no additional garbage characters, then you may wish to
edit those site files to remove any lines that disable this feature.

There are a couple of workarounds if you prefer or require overstriking
sequences rather than ISO 6429/ECMA-48 escape sequences.

  1. Set the GROFF_NO_SGR environment variable to any value.

  2. Pass option '-c' to grotty (that is, add '-P-c' to groff's
     command-line options).

The third and probably best option is to use terminal and pager programs
that handle standardized video terminal escape sequences well.

----------------------------------------------------------------------

[groff 1.16]

* My document says that the current year is 19100, not 2000.

In groff, as in traditional troff, the yr number register yields the
year minus 1900.  Unfortunately, the Bell Labs document "Troff User's
Manual" (Computing Science Technical Report #54) incorrectly claims that
yr is the last two digits of the year.  This claim has never been true
AT&T troff nor of groff.

If your text looks like this:

	.\" Wrong:
	This document was formatted in 19\n(yr.

you can correct it as follows:

	This document was formatted in \n[year].

or, if you want to be portable to older troff versions, as follows:

	.nr y4 1900+\n(yr
	This document was formatted in \n(y4.

----------------------------------------------------------------------

[groff 1.09]

* Where can I get grap?

Ted Faber <<EMAIL>> has written a freely available grap:

  http://www.lunabase.org/~faber/Vault/software/grap/

You need version 1.42 or newer.

----------------------------------------------------------------------

* The \n(st and \n(sb registers don't seem to work.  I thought \w set
  them to the height and depth of its argument, but the registers
  always seem to be 0.

\n(st and \n(sb don't give the height and depth of the argument to \w,
but the maximum vertical displacements of the text baseline above
(\n(st) and below (\n(sb) its original position.  Consider an example
where no text is formatted, but small vertical motions are used.

  \w"\v'-1u'\v'3u'"
  .tm st=\n(st, sb=\n(sb
    st=1, sb=-2

Observe that the sign convention of these registers is opposite that of
relative vertical motion.  (This is how Documenter's Workbench troff
and Heirloom Doctools troff work as well.)

The height and depth of formatted text in the \w argument are available
in the \n[rst] and \n[rsb] registers; these are groff extensions.

----------------------------------------------------------------------

[groff 1.08]

* I'm having problems formatting man pages produced by the perl
  wrapman script.

Some versions of wrapman have a superfluous blank line before the .TH
line.  This must be deleted.  Then either use groff -C, or apply the
following patch:

*** wrapman.~2~	Sun Jan 19 12:10:24 1992
--- wrapman	Tue Aug 10 02:06:41 1993
***************
*** 35,41 ****
      $line1 .= <IN> if $line1 =~ /eval/;
      $line1 .= <IN> if $line1 =~ /argv/;
      $line2 = <IN>;
!     next if $line2 eq "'di';\n";

      # Pull the old switcheroo.

--- 35,41 ----
      $line1 .= <IN> if $line1 =~ /eval/;
      $line1 .= <IN> if $line1 =~ /argv/;
      $line2 = <IN>;
!     next if $line2 eq "'di ';\n" || $line2 eq "'di';\n";

      # Pull the old switcheroo.

***************
*** 49,56 ****

      print OUT $line1;
      print OUT <<EOF;
! 'di';
! 'ig00';
  #
  # $header
  #
--- 49,58 ----

      print OUT $line1;
      print OUT <<EOF;
! 'di ';
! 'ds 00 \\"';
! 'eo ';
! 'ig 00 ';
  #
  # $header
  #
***************
*** 72,85 ****

      # These next few lines are legal in both Perl and nroff.

! $null.00;                       # finish .ig

  'di           \\" finish diversion--previous line must be blank
  .nr nl 0-1    \\" fake up transition to first page again
  .nr % 0         \\" start at page 1
! '; __END__ ##### From here on it's a standard manual page #####
  .TH $PROG 1 "$month $mday, 19$year"
- .AT 3
  .SH NAME
  $prog \\- whatever
  .SH SYNOPSIS
--- 74,87 ----

      # These next few lines are legal in both Perl and nroff.

! $null.00 ;                      # finish .ig
! 'ec \\';

  'di           \\" finish diversion--previous line must be blank
  .nr nl 0-1    \\" fake up transition to first page again
  .nr % 0         \\" start at page 1
! .\\"'; __END__ ##### From here on it's a standard manual page #####
  .TH $PROG 1 "$month $mday, 19$year"
  .SH NAME
  $prog \\- whatever
  .SH SYNOPSIS

----------------------------------------------------------------------

[groff 1.07]

* groff uses up an enormous amount of memory processing large files.
  I'm using 386BSD 0.1.

386BSD includes an old version of g++, 1.39, which has a bug that
causes a major memory leak in gtroff.  Apply the following fix to g++
and recompile groff:

*** cplus-decl.c.~1~	Mon Aug  6 05:28:59 1990
--- cplus-decl.c	Wed Jun  5 08:55:04 1991
***************
*** 7951,7961 ****

        /* At the end, call delete if that's what's requested.  */
        if (TREE_GETS_DELETE (current_class_type))
  	exprstmt = build_method_call (build1 (NOP_EXPR, TYPE_POINTER_TO (current_class_type), error_mark_node),
  				      get_identifier (OPERATOR_DELETE_FORMAT),
! 				      build_tree_list (NULL_TREE, integer_zero_node),
  				      NULL_TREE, LOOKUP_NORMAL);
        else if (TYPE_USES_VIRTUAL_BASECLASSES (current_class_type))
  	exprstmt = build_x_delete (ptr_type_node, current_class_decl, 0);
        else
  	exprstmt = 0;
--- 7951,7961 ----

        /* At the end, call delete if that's what's requested.  */
        if (TREE_GETS_DELETE (current_class_type))
  	exprstmt = build_method_call (build1 (NOP_EXPR, TYPE_POINTER_TO (current_class_type), error_mark_node),
  				      get_identifier (OPERATOR_DELETE_FORMAT),
! 				      build_tree_list (NULL_TREE, current_class_decl),
  				      NULL_TREE, LOOKUP_NORMAL);
        else if (TYPE_USES_VIRTUAL_BASECLASSES (current_class_type))
  	exprstmt = build_x_delete (ptr_type_node, current_class_decl, 0);
        else
  	exprstmt = 0;

----------------------------------------------------------------------

[groff 1.06]

* groff can't handle my troff document.  It works fine with AT&T
  troff.

Read the section on incompatibilities in groff_diff(7).  Try using the
-C option.  Alternatively there's the sed script `tmac/fixmacros.sed'
which attempts to edit a file of macros so that it can be used with
groff without the -C flag.

----------------------------------------------------------------------

* groff -Tdvi produces dvi files that use fonts at weird
  magnifications.

Yes, it does.  You may need to compile fonts with Metafont at these
magnifications.  The CompileFonts script in the devdvi/generate
directory may help you to do this.  (It takes a *long* time on slow
computers.)

----------------------------------------------------------------------

[groff 1.01]

* gpic output is not centered horizontally; pictures sometimes run off
  the bottom of the page.

The macro package you are using is not supplying appropriate
definitions of PS and PE.  Give groff a -mpic option.

----------------------------------------------------------------------

* Groff doesn't use the font names I'm used to.

Use the `ftr' request.  See groff_diff(7).

----------------------------------------------------------------------

* I get errors using the Unix -ms macros with groff -e -C.

Apply this change.  Be careful of copying and pasting it; literal BEL
characters (Control+G) appear in the source--this is an AT&T troff idiom
in output comparisons that is never necessary in GNU troff.

*** /usr/lib/ms/ms.eqn	Tue Apr 25 02:14:28 1989
--- ms.eqn	Sun Nov 11 10:33:59 1990
***************
*** 22,29 ****
  ..
  .	\" EN - end of a displayed equation
  .de EN
! .if !\\*(10 .br
  .di
  .rm EZ
  .nr ZN \\n(dn
  .if \\n(ZN>0 .if \\n(YE=0 .LP
--- 22,30 ----
  ..
  .	\" EN - end of a displayed equation
  .de EN
! .if \\n(.k>0 .br
  .di
+ .ds 10 \\*(EZ\\
  .rm EZ
  .nr ZN \\n(dn
  .if \\n(ZN>0 .if \\n(YE=0 .LP

----------------------------------------------------------------------

* gpic doesn't accept the syntax `chop N M' for chopping both ends of
  a line.

The correct syntax is `chop N chop M'.

----------------------------------------------------------------------

* With gpic -t, when I print `line ->; box' using a dvi to ps program,
  the arrow head sticks through into the inside of the box.

The dvi to ps program should be modified to set the line cap and line
join parameters to 1 while printing tpic specials.

----------------------------------------------------------------------

* gtroff doesn't understand lines like `.ce99' with no space between
  the name of the request or macro and the arguments.

gtroff requires a space between macro or request and its arguments
because it allows the use of long names for macros and requests.  You
can use the -C option or the `cp' request to put gtroff into a
compatibility mode in which it is not possible to use long names for
macros but in which no space is required between macros and their
arguments.  The use of compatibility mode is strongly discouraged.

----------------------------------------------------------------------

* gtroff gives warnings about lines like
  .ev	\" a comment
  (with a tab after the .ev).

A tab character cannot be used as a substitute for a space character
(except in one case: between a control character at the beginning of a
line and the name of a macro or request).  For example, in Unix troff

  .ps	\" restore the previous point size

(with a tab after the .ps) does NOT restore the previous point-size;
instead it is silently ignored.  Since this is very likely to be an
error, gtroff can give a warning about it.  If you want to align
comments, you can do it like this:

  .ev\"				\" a comment

----------------------------------------------------------------------

* I don't like the page headers and footers produced by groff -man.

There seem to be many different styles of page header and footer
produced by different versions of the -man macros.  You need to put
modified macros from tmac/an.tmac into man.local.  More information is
available in groff_man(7).

----------------------------------------------------------------------

* While formatting a manual page, groff complains about not being able
  to break lines.  A line like the following seems to cause this.
    .TP \w'label'+2

The groff_man(7) man page says that the default scaling unit for the
`TP` macro is 'n' (ens), and that is how the groff man macros are
implemented.  Consequently, the macro argument above is evaluated
equivalently to this expression.

  \w'label'n+2n

AT&T troff's man macros don't implement this correctly (probably because
it's hard to do in that troff); instead, they append 'n' to the entire
argument, so that it is evaluated as if it were written as follows.

  \w'label'u+2n

The solution is to fix the manual page.

  .TP \w'label'u+2

It might be better still to avoid such computations in macro arguments,
however; programs that are not *roff formatters that attempt to
interpret man pages can lack the ability to interpret numeric
expressions.  See section "Portability" of groff_man_style(7).



Printing and Display Problems
=============================



[groff 1.09]

* How can I use groff with an old LaserJet printer that doesn't work
  with groff -Tlj4?

You have at least 3 options:

- use groff -Tps with GNU Ghostscript;

- use groff -Tdvi with a TeX .dvi to LaserJet driver;

- use groff with the LaserJet driver in Chris Lewis' psroff package
  (available for ftp from:
  ftp.uunet.ca:/distrib/chris_lewis/psroff3.0pl17).

----------------------------------------------------------------------

* Groff seems to generate level 3 Postscript, but my printer is only a
  level 1 or 2 PostScript printer.

In fact groff generates only level 2 PostScript (or rather level 1
with some extensions; see grops(1) for more information how to disable
them).  The `%!PS-Adobe-3.0' comment at the beginning of PostScript
output generated by groff indicates that the file conforms to version
3.0 of the Adobe Document Structuring Conventions.  The output
generated by groff should be printable on any PostScript printer.
Problems with groff output's not printing are most often caused by the
spooling system.

----------------------------------------------------------------------

[groff 1.04]

* When I try to run gxditview, I get the error:
  Error: Widget viewport has zero width and/or height

This error means you haven't correctly installed the application
defaults file, GXditview.ad; `make install' does this for you
automatically, so either you didn't do `make install', or you haven't
passed a good `--appdefdir=<DIR>' argument to groff's configure
script.

See the X(7) man page for information how and where application
defaults files have to be located.  Look for the XAPPLRESDIR and
XUSERFILESEARCHPATH environment variables.

----------------------------------------------------------------------

[groff 1.01]

* I'm having problems including PostScript illustrations (EPS) using
  the PSPIC macro and/or \X'ps: import ...'.

A PostScript document must meet three requirements in order to be
included with the PSPIC macro: it must comply with the Adobe Document
Structuring Conventions; it must contain a BoundingBox line; it must
be `well-behaved'.  The BoundingBox line should be of the form:

  %%BoundingBox: llx lly urx ury

where llx, lly, urx, ury are the coordinates of the lower left x,
lower left y, upper right x, upper right y of the bounding box of
marks on the page expressed as integers in the default PostScript
coordinate system (72 units per inch, origin at bottom left corner).

The most convenient program to get the bounding box of a document is
the `ps2epsi' script coming with GhostScript.

If you can't use this program, another useful tactic is to print out
the illustration by itself (you may need to add a `showpage' at the
end), and physically measure the bounding box.  For more detail on
these requirements, read the specification of Encapsulated PostScript
format.  (This is available from the Adobe file server; send a message
with a body of `help' to <EMAIL>.)

If an EPS file to be included via \X'ps: import' does not start with
`%!PS-Adobe-...', gtroff still includes the file, but grops does not
add any fonts to the generated output file that are listed in the EPS
file, even though the files are listed in the `download' file and are
available in the devps directory.

----------------------------------------------------------------------

* I've configured groff for A4 paper, but gtroff still seems to think
  that the length of a page (as returned by `\n(.p') is 11 inches.

This is intentional.  The PAGE option during configuration is used
only by grops.  For compatibility with AT&T troff, the default page
length in GNU troff is always 11 inches.  The page length can be changed
with the `pl' request.

A convenient way to set paper dimensions is to use the -dpaper option
of groff, together with proper -P options for the postprocessor
(overriding the default).  For example, use the following command for
PostScript output on A4 paper in landscape orientation.

  groff -Tps -dpaper=a4l -P-pa4 -P-l -ms foo.ms > foo.ps

See groff(1) and groff_tmac(5) for more information.

----------------------------------------------------------------------

* When I print the output of groff -Tps, the output is always shifted
  up by about 0.7 inches; I'm using 8.5x11 inch paper.

Make sure that the paper format is "letter".  See groff_tmac(5).

----------------------------------------------------------------------

* When I try to print the output of groff -Tps, I get no output at all
  from the printer, and the log file shows the error
  %%[ error: undefined; offendingcommand: BP ]%%
  I'm using TranScript spooling software.

This is a bug in the page reversal filter in early versions of
TranScript.  Change the `broken' parameter in
/usr/local/lib/groff/font/devps/DESC to 7.

----------------------------------------------------------------------

* When I preview groff -Tps output using the Sun OpenWindows 2.0
  pageview program, all the pages are displayed on top of each other.

This is a defect in pageview.  Change the `broken' parameter in
/usr/local/lib/groff/font/devps/DESC to 2.

----------------------------------------------------------------------

* With groff -TX75, -TX100 or -X, I can only view the first page.

The left mouse button brings up a menu that allows you to view other
pages.  You can also press <space> and <backspace> to advance and
retreat the current page view, respectively.

----------------------------------------------------------------------

* When I print the output of groff -Tdvi, I just get a black dot in
  upper left corner.

Some dvi drivers (notably early versions of xtex) do not correctly
handle dvi files that use a resolution different from that used by dvi
files produced by TeX.  Try getting a more up to date driver.

----------------------------------------------------------------------

* When I preview documents using -TX75 or -TX100, the layout is not
  the same as when I print the document with -Tps: the line and page
  breaks come in different places.

Use `groff -X -Tps'.



Platform-Dependent Macro Problems
=================================



[groff 1.17]

* groff produces wrapper macros for `ms' and friends which call the
  system's original macros.  Then, to get groff's ms macro package I
  have to use `-mgs' instead `-ms'.  Can I avoid this?

Yes.  Configure and compile groff as usual, but install it with

  make install tmac_wrap=""

Then no wrapper files are produced, and `-ms' uses groff's `ms'
macros.

----------------------------------------------------------------------

[groff 1.09]

* On an SGI system, how can I make the man command use groff?

From David Hinds <<EMAIL>> (some of these steps
are unnecessary if you install with the `g' Makefile variable defined
as empty):

Create a script called `eqn':

 > #!/bin/sh
 > if [ ${1:-""} = /usr/pub/eqnchar ] ; then shift ; fi
 > geqn $*

and a script called `neqn':

 > #!/bin/sh
 > if [ ${1:-""} = /usr/pub/eqnchar ] ; then shift ; fi
 > geqn -Tascii $*

and do:

 > ln -s gnroff nroff

and edit the end of the gnroff script to be:

 > rest=`echo ${1+"$@"} | sed -e 's+/usr/lib/tmac+/usr/local/lib/groff/tmac+'`
 > exec groff -Wall -mtty-char $T $opts $rest

To get PostScript output from `man -t', you also need to create a
`psroff' script similar to `nroff'.  Here are the context diffs:

*** /usr/local/bin/nroff        Sat Feb 13 15:51:09 1993
--- /usr/local/bin/psroff       Sat Feb 13 17:45:46 1993
***************
*** 1,8 ****
  #! /bin/sh
! # Emulate nroff with groff.

  prog="$0"
! T=-Tascii
  opts=

  for i
--- 1,8 ----
  #! /bin/sh
! # Emulate psroff with groff.

  prog="$0"
! T=-Tps
  opts=

  for i
***************
*** 25,30 ****
--- 25,33 ----
        -Tascii|-Tlatin1)
                T=$1
                ;;
+       -t)
+               # ignore -- default is send to stdout
+               ;;
        -T*)
                # ignore other devices
                ;;
***************
*** 49,53 ****
  rest=`echo ${1+"$@"} | sed -e 's+/usr/lib/tmac+/usr/local/lib/groff/tmac+'`

  # This shell script is intended for use with man, so warnings are
  # probably not wanted.  Also load nroff-style character definitions.
! exec groff -Wall -mtty-char $T $opts $rest
--- 52,56 ----
  rest=`echo ${1+"$@"} | sed -e 's+/usr/lib/tmac+/usr/local/lib/groff/tmac+'`

  # This shell script is intended for use with man, so warnings are
! # probably not wanted.
! exec groff -Wall $T $opts $rest

----------------------------------------------------------------------

[groff 1.08]

* I'm having problems formatting HP-UX 9.0 man pages with groff -man.

Copy HP's tmac.an into /usr/local/share/groff/site-tmac/an.tmac, and
either put `.cp 1' at the beginning or filter it (and any files it
.so's) through tmac/fixmacros.sed.

----------------------------------------------------------------------

[groff 1.07]

* I'm having problems formatting Ultrix man pages with groff -man.

The Ultrix man pages use a number of non-standard extensions to the
Unix man macros.  [To be fair, SunOS did too, albeit not as many; see
groff_man(7).  groff embraced SunOS's extensions early on, but not
Ultrix's. --GBR in 2023]  One solution is to use the Ultrix -man macros
with groff.  Copy /usr/lib/tmac/tmac.an to
/usr/local/share/groff/site-tmac/an.tmac and apply the following patch
(from Frank Wortner):

*** /usr/local/lib/groff/tmac/tmac.an     Wed Sep  9 12:29:28 1992
--- /usr/lib/tmac/tmac.an       Fri Jul 24 19:58:19 1992
***************
*** 489,495 ****
  .     \" make special case of shift out of italic
  .de }S
  .ds ]F
! .if \\$12 .if !\\$5 .ds ]F \^
  .ie !\\$4 .}S \\$2 \\$1 "\\$3\f\\$1\\$4\\*(]F" "\\$5" "\\$6" "\\$7" "\\$8" "\\$9"
  .el \\$3
  .}f
--- 489,495 ----
  .     \" make special case of shift out of italic
  .de }S
  .ds ]F
! .if \\$12 .if !\\$5 .ds ]F\^
  .ie !\\$4 .}S \\$2 \\$1 "\\$3\f\\$1\\$4\\*(]F" "\\$5" "\\$6" "\\$7" "\\$8" "\\$9"
  .el \\$3
  .}f

Another possible solution is to install tmac/man.ultrix as
/usr/local/share/groff/site-tmac/man.local.

----------------------------------------------------------------------

[groff 1.01]

* I get lots of errors when I use groff with the AT&T -mm macros.

Use the groff -mm macros.



Compilation Problems
====================



[groff 1.23.0]

* The "initialization_is_quiet" test fails on my NetBSD box.

This is a known problem.  We haven't tracked down the cause yet, but
have improved the reporting of the test output in hopes that we can
isolate it in a future release.

----------------------------------------------------------------------

* I get a build failure on Cygwin / a system using newlib and GCC 11.

"newlib" defines a function called "utoa" which conflicts with a static
(file scope-local) function in src/libs/libxutil/XFontName.c.

We expect to fix this in the near future; in the meantime, you can patch
the file to rename the function (and update its call sites) or, if you
don't require the "gxditview" output previewer or "xtotroff" utility,
you can build groff without X11 support.

  $ make distclean
  $ ./configure --without-x

----------------------------------------------------------------------

* The "check-default-foundry" test fails when I run "make check".

Your Ghostscript installation may have its fonts embedded in the
executable; this can be discerned by searching for the pattern "%rom%"
in its search path.

  $ gs -h | grep '%rom%'

The consequence is that gropdf(1) will be unable to embed fonts into PDF
files it generates (apart from groff's "EURO" font) when the default
foundry is used.  This is the same outcome as if Ghostscript were not
installed at all.  If you install URW fonts (see "INSTALL.extra"), you
will be able to embed them all by using the "U" foundry with gropdf to
overcome this problem.

----------------------------------------------------------------------

* I get a lot of warnings about "sprintf" on macOS.

Apple has decided to treat the sprintf() standard C library function as
deprecated even though the C standard itself has not.

https://developer.apple.com/forums/thread/714675

----------------------------------------------------------------------

* I get a make(1) failure involving grep and the groff_man.7.man.in file
  on Solaris 11.

Solaris make(1) has a bug easily exhibited by the following Makefile.

all:
	! false

Use GNU make instead; it may be available in /opt/csw/bin/gmake.

----------------------------------------------------------------------

* Tests fail when I run "make check" on Solaris 10 or 11.

The test suite expects a POSIX-conforming shell and utilities.  Solaris
10 does not offer these in the default $PATH.  We try to use features
standardized no later than POSIX Issue 4 (1994).  Unfortunately even
that is too recent for some implementations.  Solaris 11 has a (mostly)
conforming shell.  It may help to ensure that "/usr/xpg6/bin" and
"/usr/xpg4/bin" precede "/usr/bin" in the $PATH when building groff.

For Solaris 10, it is necessary to modify the shell-based test scripts
in place to use a conforming shell.  Here is an example.

  $ gsed -i -e '1s@#!/bin/sh@#!/usr/xpg4/bin/sh@' \
      `find . -name '*.sh' | grep /tests/`
  $ PATH=/usr/xpg4/bin:$PATH gmake check

Some test failures remain expected on Solaris 10 and/or 11.

1.  FAIL: contrib/hdtbl/examples/test-hdtbl.sh

    /usr/bin/tr on Solaris 10 is non-conforming with the POSIX Issue 4
    standard.  It furthermore issues anonymous diagnostics, saying only
    "Bad string".

    Install tr from GNU coreutils in the $PATH.  Edit line 57 of each of
    contrib/hdtbl/examples/fonts_x.in and
    contrib/hdtbl/examples/fonts_n.in.  Change "tr" to "gtr".
    Alternatively, you can use the absolute path to GNU tr's location.
    Re-run "gmake check" as above.  (Some files will be rebuilt.)

    The tr commands in /usr/xpg4/bin and /usr/xpg6/bin also work, but
    the documents constructed from the above inputs use groff's `pso`
    request, which wraps the standard C library `popen()` function,
    which sanitizes $PATH to avoid privilege escalation, thus making it
    likely that the non-conforming tr in /usr/bin will be found first.

2.  FAIL: src/roff/groff/tests/initialization_is_quiet.sh
    FAIL: src/roff/groff/tests/msoquiet_works.sh
    FAIL: src/roff/groff/tests/soquiet_works.sh

    (The first of these might be SKIPped instead.)

    /usr/xpg4/bin/sh is non-conforming with the POSIX Issue 4 standard,
    despite its name.  Its "unset" builtin is buggy.  (The /usr/bin/sh
    in Solaris 11 does not have this problem.)

    These tests use the "unset" shell builtin command to prevent
    environment variables from confounding test results.

    POSIX says "[u]nsetting a variable ... that was not previously set
    is not considered an error and will not cause the shell to abort."

    Nevertheless this builtin returns an error exit status in this
    circumstance.

    $ /usr/xpg4/bin/sh -c 'unset _NON_EXISTENT_XYZ; echo $?'
    1

    You may disregard these failures, edit the test scripts to append
    "|| true" to the "unset" commands, or change the scripts to use GNU
    Bash or some other POSIX-conforming shell as illustrated above.

----------------------------------------------------------------------

* I get warnings from afmtodit about names already being mapped.

afmtodit: AGL name 'Delta' already mapped to groff name '*D'; ignoring AGL name 'uni0394'

You can ignore these if they're in the form shown above, where the
ignored AGL name is 'uniXXXX' and 'XXXX' is four hexadecimal digits.
The Adobe Glyph List (AGL) has its own names for glyphs; they are often
different from groff's special character names.  The afmtodit program is
constructing a mapping from groff special character names to AGL names;
this can be a one-to-one or many-to-one mapping, but one-to-many will
not work, so afmtodit discards the excessive mappings.  The example you
see above is telling you that the groff font description that afmtodit
is writing cannot map the groff special character '*D' to both 'Delta'
and 'uni0394'.

Which, if any, such warnings you see depends on the version of the URW
fonts you are building groff against.  See the '--with-urw-fonts-dir'
option to the "configure" script, and the afmtodit(1) and groff_char(7)
man pages for more background.

----------------------------------------------------------------------

* I am building from the Git repository, using 'autoreconf' from a GNU
  Autoconf release earlier than 2.69, and I get this.

  /usr/share/aclocal/gtkglextmm-1.2.m4:225:
    warning: underquoted definition of AC_GTKGLEXTMM_SUPPORTS_MULTIHEAD
  /usr/share/aclocal/gtkglextmm-1.2.m4:225:
    run info '(automake)Extending aclocal'
  /usr/share/aclocal/gtkglextmm-1.2.m4:225:
    or see http://www.gnu.org/software/automake/manual/automake.html#Extending-aclocal

Ignore this.  It doesn't occur in more recent versions of 'autoreconf'.

----------------------------------------------------------------------

* I get warnings about special characters in the groff_char(7) man page.

troff:man/groff_char.7:1033: warning: special character '.j' not defined

  (...and similar for 'vA', 'bs', '-+', 'coproduct', and '+e'.)

You can ignore these.  groff defines a handful of special characters for
which historical PostScript fonts usually did not possess glyphs.
Except for 'bs' (the Bell System logo), we hope to provide fallbacks or
a supplementary PostScript font in groff in the future (as was done for
the Euro glyph).

----------------------------------------------------------------------

* I get warnings about the "vasnprintf" function.

lib/vasnprintf.c: In function 'vasnprintf':
lib/vasnprintf.c:5268:27: warning: format not a string literal, argument types not checked [-Wformat-nonliteral]
(and similar)

The groff source tree includes gnulib, the GNU portability library
<https://www.gnu.org/software/gnulib/>.  These warnings are about its
source code and that project's responsibility to resolve.  We expect a
future release of gnulib to do so.

----------------------------------------------------------------------

[groff 1.22.4]

* When compiling on NetBSD, make issues warnings like

  Warning: line 28: Unable to locate font(s)
  URWGothicL-Demi,a010015l.pfb on the given path(s)

  and

  Warning: line 77: Failed to create groff font 'U-AB' by running
  afmtodit

In this case install the package "urw-fonts":

  pkgin install urw-fonts

and make the font path known to ghostscript, e.g. with (ksh):

  export GS_LIB=/usr/pkg/share/fonts/urw

----------------------------------------------------------------------

* Currently (December 2015) building groff fails on NetBSD and
  FreeBSD with the message:

  make[1]: don't know how to make contrib/chem/chem.1. Stop

The reason is a bug in the make(1) tool used on those systems related
to .SUFFIXES lines.

A temporary workaround is to change the Makefile line

  .SUFFIXES: .roff .in .ps .mom .pdf .me .ms .ps .html .txt .texi \
    .dvi .pdf .xhtml .man .c .cpp .log .o .obj .sed .sin \
    .test .test$(EXEEXT) .trs .ypp

into

  .SUFFIXES: .man .roff .in .ps .mom .pdf .me .ms .ps .html .txt \
    .texi .dvi .pdf .xhtml .c .cpp .log .o .obj .sed .sin \
    .test .test$(EXEEXT) .trs .ypp

(put .man at begin of the list).

The bug is reported to the maintainer of the make(1) tool of those
systems.

----------------------------------------------------------------------

[groff 1.22.3]

* Configuration on MacOS X 10.6 doesn't succeed.

Use

  ./configure CXX=g++-4.2

----------------------------------------------------------------------

[groff 1.21]

* In MacOS X, I want to completely replace the groff that came with
  the system.

Use

  ./configure --prefix=/usr --mandir=/usr/share/man

then

  make
  make install

Note that subsequent system updates may replace your groff.

----------------------------------------------------------------------

[groff 1.19.2]

* I get warnings from the Sun linker while using gcc 3.4.0:

  ld: warning: relocation error: R_SPARC_UA32:
      file groff/src/libs/libgroff/libgroff.a(getopt.o): symbol optarg:
      external symbolic relocation against non-allocatable
      section .debug_info; cannot be processed at runtime:
      relocation ignored

This seems to be a known problem (Sun bugs #4910101 and #4910810,
filed in September 2003; gcc bug #15599, filed May 2004) without a
public fix as of this writing.  A work-around is to use option
`-gstabs+' instead of `-g' (and a high probability that the output is
only debuggable with gdb but not with Sun's debuggers).

----------------------------------------------------------------------

[groff 1.19]

* When compiling on MacOS X 10.2, groff compiles but does not run
  well, especially `eqn', causing many `can't break line' messages.

Use

  ./configure CXX=g++2

then

  make

as usual.

----------------------------------------------------------------------

[groff 1.18.0]

* Compilation dies with

    y.tab.c: In function `int yyparse()':
    y.tab.c: `size_t' undeclared in namespace `std'

* bison reports conflicts (either on stderr or in the `pic.output'
  file) while processing `pic.y', and the produced pic binary doesn't
  work at all.

You need bison version 1.875b or greater.  Alternatively, use yacc or
byacc.

----------------------------------------------------------------------

[groff 1.17]

* On HP-UX, the compiler complains about missing symbol `alloca'.

Say

  export LDFLAGS=-lPW

before starting the configure script.

----------------------------------------------------------------------

[groff 1.16.1]

* On a host using Unix make (e.g. Solaris), if you are compiling for
  multiple architectures by building in a subdirectory, the make stops
  with a message like this:

    make: Fatal error: Don't know how to make target `assert.o'

  or like this:

    make: Fatal error: Can't find /u/src/groff/src/include/Makefile.sub': No such file or directory

This occurs because GNU make and Unix make handle VPATH differently,
and the groff build relies on GNU make's VPATH handling.

Use GNU make <http://www.gnu.org/software/make/> to work around this.
In Solaris 8 and 9, GNU make is on the Software Companion CD in
package SFWgmake and is installed as /opt/sfw/bin/gmake.  Prebuilt
versions of GNU make for Solaris are also available from
sunfreeware.com.

----------------------------------------------------------------------

* There are many empty `Makefile.dep' files.  Is this a bug?

No.  Real dependency files are created with a `make depend' call.

----------------------------------------------------------------------

[groff 1.16]

* The configure script fails on OS/390 (z/OS) Unix.

[This has been fixed in z/OS V1R3 (aka OS/390 R13).]

There is a bug in the Language Environment (LE) whereby the test
program for static destructors fails.  You see the message `configure:
error: a working C++ compiler is required'.

Applying PTF UQ42006 is supposed to fix this, but the test program is
still returning the wrong value (1).  To work around this problem, you
can comment out the following in the configure script (near line
2029).  This effectively bypasses the test (static constructors and
destructors do actually work properly):

#if { (eval echo "$as_me:2029: \"$ac_link\"") >&5
#  (eval $ac_link) 2>&5
#  ac_status=$?
#  echo "$as_me:2032: \$? = $ac_status" >&5
#  (exit $ac_status); } && { ac_try='./conftest$ac_exeext'
#  { (eval echo "$as_me:2034: \"$ac_try\"") >&5
#  (eval $ac_try) 2>&5
#  ac_status=$?
#  echo "$as_me:2037: \$? = $ac_status" >&5
#  (exit $ac_status); }; }; then
#  echo "$as_me:2039: result: yes" >&5
#echo "${ECHO_T}yes" >&6
#else
#  echo "$as_me: program exited with status $ac_status" >&5
#echo "$as_me: failed program was:" >&5
#cat conftest.$ac_ext >&5
#echo "$as_me:2045: result: no" >&5
#echo "${ECHO_T}no" >&6;{ { echo "$as_me:2046: error: a working C++ compiler is required" >&5
#echo "$as_me: error: a working C++ compiler is required" >&2;}
#   { (exit 1); exit 1; }; }
#fi

----------------------------------------------------------------------

[groff 1.15]

* I get errors when I try to compile groff with Forte Development 6
  or 6u1, or Sun C++ version 5.0 through 5.2.

This is a known problem; see Sun bug #4301919.  See Sun patches
109482, 109490, 109508, and 109509 for fixes.

----------------------------------------------------------------------

[groff 1.08]

* I'm having problems compiling groff on 386BSD 0.1.

If you're using ash as /bin/sh, you'll need the following patch.

*** gendef.sh.org	Sun Jun 30 13:30:36 1991
--- gendef.sh	Sun Feb 28 10:23:49 1993
***************
*** 3,9 ****
  file=$1
  shift

! defs="#define $1"
  shift
  for def
  do
--- 3,10 ----
  file=$1
  shift

! x=$1
! defs="#define $x"
  shift
  for def
  do

You'll also need to change dirnamemax.c so that it doesn't use
pathconf().

----------------------------------------------------------------------

* While compiling on Xenix, ranlib libgroff.a fails.

The system ranlib can't handle externals longer than 40 characters.
Use the ranlib included in demon.co.uk:/pub/xenix/g++-1.40.3a.v1
instead.

----------------------------------------------------------------------

[groff 1.07]

* I get errors when I try to compile groff with DEC C++.

Fix the declaration of write() in <unistd.h> so that the second
argument is a const char *.  Fix the declaration of open() in
<sys/file.h> so that the first argument is a const char *.

----------------------------------------------------------------------

* On Ultrix, the make program stops with the message

    *** Error code 1

    Stop.

  for no apparent reason.

Use GNU make.

----------------------------------------------------------------------

[groff 1.01]

* I get errors when I try to compile groff with Sun C++ version 3 or
  earlier.

Groff requires header files that are moderately compatible with AT&T
C++ and ANSI C.  With some versions of Sun C++, the supplied header
files need some of the following changes to meet this requirement:
<string.h> must declare the mem* functions, (just add `#include
<memory.h>' to <string.h>); the first argument to fopen and freopen
should be declared as `const char *'; the first argument to fread
should be declared as `void *'; the first argument to fwrite should be
declared as `const void *'; malloc should be declared to return
`void *'; in <alloca.h>, the declaration `extern "C" { void
*__builtin_alloca(int); }' should be added; in <sys/signal.h> the
return type and the second argument type of signal() should be changed
to be `void (*)(int)'.

You can either change them in place, or copy them to some other
directory and include that directory with a -I option.



##### Editor settings
Local Variables:
fill-column: 72
mode: text
End:
vim: set autoindent textwidth=72:
