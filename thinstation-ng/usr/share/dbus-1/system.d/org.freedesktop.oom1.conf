<?xml version="1.0"?> <!--*-nxml-*-->
<!DOCTYPE busconfig PUBLIC "-//freedesktop//DTD D-BUS Bus Configuration 1.0//EN"
        "https://www.freedesktop.org/standards/dbus/1.0/busconfig.dtd">

<!-- SPDX-License-Identifier: LGPL-2.1-or-later -->

<busconfig>

        <policy user="systemd-oom">
                <allow own="org.freedesktop.oom1"/>
                <allow send_destination="org.freedesktop.oom1"/>
                <allow receive_sender="org.freedesktop.oom1"/>
        </policy>

        <policy user="root">
                <allow send_destination="org.freedesktop.oom1"/>
        </policy>

        <policy context="default">
                <deny send_destination="org.freedesktop.oom1"/>

                <!-- Generic interfaces -->

                <allow send_destination="org.freedesktop.oom1"
                       send_interface="org.freedesktop.DBus.Introspectable"/>

                <allow send_destination="org.freedesktop.oom1"
                       send_interface="org.freedesktop.DBus.Peer"/>

                <allow send_destination="org.freedesktop.oom1"
                       send_interface="org.freedesktop.DBus.Properties"
                       send_member="Get"/>

                <allow send_destination="org.freedesktop.oom1"
                       send_interface="org.freedesktop.DBus.Properties"
                       send_member="GetAll"/>

                <!-- Manager interface -->

                <allow send_destination="org.freedesktop.oom1"
                       send_interface="org.freedesktop.oom1.Manager"
                       send_member="DumpByFileDescriptor"/>

                <allow receive_sender="org.freedesktop.oom1"/>
        </policy>

</busconfig>
