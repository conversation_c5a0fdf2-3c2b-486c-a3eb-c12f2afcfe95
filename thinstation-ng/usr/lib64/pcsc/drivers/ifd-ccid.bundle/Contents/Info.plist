<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>English</string>
	<key>CFBundleExecutable</key>
	<string>libccid.so</string>
	<key>CFBundleIdentifier</key>
	<string>fr.apdu.ccid.smartcardccid</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>CCIDCLASSDRIVER</string>
	<key>CFBundlePackageType</key>
	<string>BNDL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.6.1</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>0.0.1d1</string>
	<key>ifdCapabilities</key>
	<string>0x00000000</string>

	<!-- Possible values for ifdCapabilities bits
	1: IFD_GENERATE_HOTPLUG
	   plugging the reader calls pcscd \-\-hotplug

	Default value is 0x00000000
	-->

	<key>ifdProtocolSupport</key>
	<string>0x00000001</string>
	<key>ifdVersionNumber</key>
	<string>0x00000001</string>

	<key>ifdLogLevel</key>
	<string>0x0003</string>

	<!-- Possible values for ifdLogLevel
	1: CRITICAL  important error messages
	2: INFO      informative messages like what reader was detected
	4: COMM      a dump of all the bytes exchanged between the host and
	             the reader
	8: PERIODIC  periodic info when pcscd test if a card is present
	             (every 1/10 of a second)

	The final value is a OR of these values

	Default value: 3 (CRITICAL + INFO)
	-->

	<key>ifdDriverOptions</key>
	<string>0x0000</string>

	<!-- Possible values for ifdDriverOptions
	0x01: DRIVER_OPTION_CCID_EXCHANGE_AUTHORIZED
		the CCID Exchange command is allowed. You can use it through
		SCardControl(hCard, IOCTL_SMARTCARD_VENDOR_IFD_EXCHANGE, ...)

	0x02: DRIVER_OPTION_GEMPC_TWIN_KEY_APDU
		If set the GemPC Twin and GemPC Key readers with be configured
		so that the T=1 TPDU protocol is done by the firmware instead of
		the driver.
		This switches the reader in APDU mode and also in EMV mode so
		may not work with non EMV cards.

	0x04: DRIVER_OPTION_USE_BOGUS_FIRMWARE
		Some reader firmwares have bugs. By default the driver refuses
		to work with such firmware versions. If your reader is rejected
		because of the firmware (log message: "Firmware (x.y) is
		bogus!") you can:
		- upgrade your reader firmware (not all readers can do that)
		or
		- get another reader with a new/bugfree firmware
		or
		- activate this option but you will have problems depending on
		  the bug

	0x08: free

	bits 4 & 5: (values 0x00, 0x10, 0x20, 0x30)
	 0x00: power on the card at 5V, then 1.8V then 3V (default value)
	 0x10: power on the card at 3V, then 5V then 1.8V
	 0x20: power on the card at 1.8V, then 3V and then 5V
	 0x30: let the reader decide

	0x40: DRIVER_OPTION_DISABLE_PIN_RETRIES
		The Gemalto pinpad reader sends a VERIFY command with no PIN
		value in order to retrieve the remaining retries from the card.
		Some cards (like the OpenPGP card) do not support this.

	Default value: 0
	-->

	<key>ifdManufacturerString</key>
	<string>Ludovic Rousseau (<EMAIL>)</string>

	<key>ifdProductString</key>
	<string>Generic CCID driver</string>

	<key>ifdVendorID</key>
	<array>
		<string>0x072F</string>
		<string>0x09C3</string>
		<string>0x09C3</string>
		<string>0x058F</string>
		<string>0x0DC3</string>
		<string>0x0DC3</string>
		<string>0x1B0E</string>
		<string>0x0783</string>
		<string>0x046A</string>
		<string>0x046A</string>
		<string>0x046A</string>
		<string>0x046A</string>
		<string>0x046A</string>
		<string>0x0982</string>
		<string>0x413C</string>
		<string>0x413C</string>
		<string>0x073D</string>
		<string>0x073D</string>
		<string>0x073D</string>
		<string>0x073D</string>
		<string>0x073D</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x1059</string>
		<string>0x1059</string>
		<string>0x0B81</string>
		<string>0x0F14</string>
		<string>0x0F14</string>
		<string>0x0D46</string>
		<string>0x0D46</string>
		<string>0x0D46</string>
		<string>0x0D46</string>
		<string>0x17EF</string>
		<string>0x09BE</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x03F0</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0xA625</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x0F14</string>
		<string>0x0F14</string>
		<string>0x0F14</string>
		<string>0x0DB5</string>
		<string>0x0DB5</string>
		<string>0x0DB5</string>
		<string>0x0DB5</string>
		<string>0x0DB5</string>
		<string>0x0DB5</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x2021</string>
		<string>0x2021</string>
		<string>0x2021</string>
		<string>0x0A89</string>
		<string>0x0A89</string>
		<string>0x0A89</string>
		<string>0x0A89</string>
		<string>0x0A89</string>
		<string>0x0A89</string>
		<string>0x0A89</string>
		<string>0x0A89</string>
		<string>0x24DC</string>
		<string>0x24DC</string>
		<string>0x24DC</string>
		<string>0x24DC</string>
		<string>0x24DC</string>
		<string>0x24DC</string>
		<string>0x24DC</string>
		<string>0x24DC</string>
		<string>0x24DC</string>
		<string>0x2CE3</string>
		<string>0x2CE3</string>
		<string>0x2CE3</string>
		<string>0x058F</string>
		<string>0x2A0C</string>
		<string>0x0483</string>
		<string>0x2406</string>
		<string>0x2406</string>
		<string>0x2406</string>
		<string>0x33B6</string>
		<string>0x1FD3</string>
		<string>0x0DC3</string>
		<string>0x0DC3</string>
		<string>0x0DC3</string>
		<string>0x03EB</string>
		<string>0x03EB</string>
		<string>0x03EB</string>
		<string>0x03EB</string>
		<string>0x03EB</string>
		<string>0x03EB</string>
		<string>0x03EB</string>
		<string>0x3606</string>
		<string>0xC1A6</string>
		<string>0x04E6</string>
		<string>0x15CF</string>
		<string>0x15CF</string>
		<string>0x15CF</string>
		<string>0x04E6</string>
		<string>0x23A0</string>
		<string>0x23A0</string>
		<string>0x23A0</string>
		<string>0x23A0</string>
		<string>0x25DD</string>
		<string>0x25DD</string>
		<string>0x25DD</string>
		<string>0x25DD</string>
		<string>0x25DD</string>
		<string>0x25DD</string>
		<string>0x25DD</string>
		<string>0x25DD</string>
		<string>0x25DD</string>
		<string>0x25DD</string>
		<string>0x25DD</string>
		<string>0x25DD</string>
		<string>0x25DD</string>
		<string>0x25DD</string>
		<string>0x25DD</string>
		<string>0x2ABE</string>
		<string>0x1B0E</string>
		<string>0x1B0E</string>
		<string>0x0A5C</string>
		<string>0x0A5C</string>
		<string>0x0A5C</string>
		<string>0x0A5C</string>
		<string>0x0A5C</string>
		<string>0x0A5C</string>
		<string>0x0A5C</string>
		<string>0x0A5C</string>
		<string>0x0A5C</string>
		<string>0x0783</string>
		<string>0x0783</string>
		<string>0x0783</string>
		<string>0x0783</string>
		<string>0x20A0</string>
		<string>0x0CA6</string>
		<string>0x8829</string>
		<string>0x2DFF</string>
		<string>0x2DFF</string>
		<string>0x19E7</string>
		<string>0x046A</string>
		<string>0x046A</string>
		<string>0x046A</string>
		<string>0x046A</string>
		<string>0x046A</string>
		<string>0x046A</string>
		<string>0x046A</string>
		<string>0x046A</string>
		<string>0x046A</string>
		<string>0x046A</string>
		<string>0x046A</string>
		<string>0x04F2</string>
		<string>0x03F0</string>
		<string>0x03F0</string>
		<string>0x04F2</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x31AA</string>
		<string>0x1FC9</string>
		<string>0x0982</string>
		<string>0x0982</string>
		<string>0x23D8</string>
		<string>0x257B</string>
		<string>0x1AC2</string>
		<string>0x1AC2</string>
		<string>0x0483</string>
		<string>0x0483</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1DB2</string>
		<string>0x1DB2</string>
		<string>0x1DB2</string>
		<string>0x257B</string>
		<string>0x09D8</string>
		<string>0x09D8</string>
		<string>0x1FFA</string>
		<string>0x2CE4</string>
		<string>0x2CE4</string>
		<string>0x2CE4</string>
		<string>0x1EA8</string>
		<string>0x1209</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x2925</string>
		<string>0x234B</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x096E</string>
		<string>0x0BF8</string>
		<string>0x0BF8</string>
		<string>0x0BF8</string>
		<string>0x0BF8</string>
		<string>0x0BF8</string>
		<string>0x0BF8</string>
		<string>0x0BF8</string>
		<string>0x0BF8</string>
		<string>0x0898</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x0BDA</string>
		<string>0x048D</string>
		<string>0x058C</string>
		<string>0x076B</string>
		<string>0x05E3</string>
		<string>0x05E3</string>
		<string>0x05E3</string>
		<string>0x20A0</string>
		<string>0xAE68</string>
		<string>0x1059</string>
		<string>0x1059</string>
		<string>0x0F1A</string>
		<string>0x19C8</string>
		<string>0x32A3</string>
		<string>0x1677</string>
		<string>0x03F0</string>
		<string>0x03F0</string>
		<string>0x03F0</string>
		<string>0x09C3</string>
		<string>0x09C3</string>
		<string>0x09C3</string>
		<string>0x09C3</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x04A4</string>
		<string>0x04A4</string>
		<string>0x0B81</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x1FFA</string>
		<string>0x2406</string>
		<string>0x03EB</string>
		<string>0x03EB</string>
		<string>0x0C27</string>
		<string>0x2FD2</string>
		<string>0x2FB0</string>
		<string>0x2022</string>
		<string>0x1FC9</string>
		<string>0x1403</string>
		<string>0x1403</string>
		<string>0x1403</string>
		<string>0x2406</string>
		<string>0x2406</string>
		<string>0x2406</string>
		<string>0x2406</string>
		<string>0x2406</string>
		<string>0x2406</string>
		<string>0x2406</string>
		<string>0x2406</string>
		<string>0x076B</string>
		<string>0x04D8</string>
		<string>0x2A18</string>
		<string>0x2A18</string>
		<string>0x2A18</string>
		<string>0x2947</string>
		<string>0x2947</string>
		<string>0x2947</string>
		<string>0x2947</string>
		<string>0x2947</string>
		<string>0x2947</string>
		<string>0x2947</string>
		<string>0x2947</string>
		<string>0x2947</string>
		<string>0x28B9</string>
		<string>0x2F76</string>
		<string>0x0483</string>
		<string>0x0D46</string>
		<string>0x0D46</string>
		<string>0x0D46</string>
		<string>0x0D46</string>
		<string>0x0D46</string>
		<string>0x0D46</string>
		<string>0x0D46</string>
		<string>0x2D25</string>
		<string>0x2D25</string>
		<string>0x0925</string>
		<string>0x2C97</string>
		<string>0x2C97</string>
		<string>0x2C97</string>
		<string>0x2C97</string>
		<string>0x2C97</string>
		<string>0x2C97</string>
		<string>0x2C97</string>
		<string>0x17EF</string>
		<string>0x17EF</string>
		<string>0x17EF</string>
		<string>0x03F0</string>
		<string>0x03F0</string>
		<string>0x03F0</string>
		<string>0x08AE</string>
		<string>0x1403</string>
		<string>0x0424</string>
		<string>0x0424</string>
		<string>0x0416</string>
		<string>0x0416</string>
		<string>0x079B</string>
		<string>0x079B</string>
		<string>0x0BDA</string>
		<string>0x4D55</string>
		<string>0x1E0D</string>
		<string>0x1E0D</string>
		<string>0x1E0D</string>
		<string>0x1E0D</string>
		<string>0x20A0</string>
		<string>0x20A0</string>
		<string>0x20A0</string>
		<string>0x20A0</string>
		<string>0x20A0</string>
		<string>0x04E6</string>
		<string>0x1FC9</string>
		<string>0x1FC9</string>
		<string>0x1FC9</string>
		<string>0x1FC9</string>
		<string>0x1A74</string>
		<string>0x1A74</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x076B</string>
		<string>0x04DA</string>
		<string>0x0471</string>
		<string>0x04B9</string>
		<string>0x096E</string>
		<string>0x21AB</string>
		<string>0x316D</string>
		<string>0x14DD</string>
		<string>0x1C6A</string>
		<string>0x0C4B</string>
		<string>0x0C4B</string>
		<string>0x0C4B</string>
		<string>0x0C4B</string>
		<string>0x0C4B</string>
		<string>0x0C27</string>
		<string>0x14CD</string>
		<string>0x13FE</string>
		<string>0x0529</string>
		<string>0x0529</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x08E6</string>
		<string>0x24A2</string>
		<string>0x2EE1</string>
		<string>0x0973</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x04E6</string>
		<string>0x0B81</string>
		<string>0x0403</string>
		<string>0x356F</string>
		<string>0x356F</string>
		<string>0x1677</string>
		<string>0x0DF6</string>
		<string>0x04E8</string>
		<string>0x1209</string>
		<string>0x054C</string>
		<string>0x054C</string>
		<string>0x054C</string>
		<string>0x316E</string>
		<string>0x316E</string>
		<string>0x316E</string>
		<string>0x316E</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x1C34</string>
		<string>0x08DF</string>
		<string>0x08DF</string>
		<string>0x08DF</string>
		<string>0x08DF</string>
		<string>0x08DF</string>
		<string>0x05AF</string>
		<string>0x1370</string>
		<string>0x1206</string>
		<string>0x1206</string>
		<string>0x0E6A</string>
		<string>0x1D50</string>
		<string>0x1862</string>
		<string>0x08E6</string>
		<string>0x17B9</string>
		<string>0x1976</string>
		<string>0xA625</string>
		<string>0x0B0C</string>
		<string>0x0B0C</string>
		<string>0x349E</string>
		<string>0x349E</string>
		<string>0x23EB</string>
		<string>0x19A6</string>
		<string>0x2A17</string>
		<string>0x2DFF</string>
		<string>0x2DFF</string>
		<string>0x1CF0</string>
		<string>0x1A44</string>
		<string>0x1A44</string>
		<string>0x1A44</string>
		<string>0x1A44</string>
		<string>0x1A44</string>
		<string>0x1A44</string>
		<string>0x1A44</string>
		<string>0x1A44</string>
		<string>0x1A44</string>
		<string>0x1A44</string>
		<string>0x1A44</string>
		<string>0x1A44</string>
		<string>0x1A44</string>
		<string>0x0E0F</string>
		<string>0x163C</string>
		<string>0x163C</string>
		<string>0x163C</string>
		<string>0x163C</string>
		<string>0x163C</string>
		<string>0x34EB</string>
		<string>0x0416</string>
		<string>0x1050</string>
		<string>0x1050</string>
		<string>0x1050</string>
		<string>0x1050</string>
		<string>0x1050</string>
		<string>0x1050</string>
		<string>0x1050</string>
		<string>0x1050</string>
		<string>0x072F</string>
		<string>0x072F</string>
		<string>0x09C3</string>
		<string>0x058F</string>
		<string>0x0DC3</string>
		<string>0x03EB</string>
		<string>0x0A5C</string>
		<string>0x0A5C</string>
		<string>0x0A5C</string>
		<string>0x0A5C</string>
		<string>0x0A5C</string>
		<string>0x0A5C</string>
		<string>0x0A5C</string>
		<string>0x0783</string>
		<string>0x096E</string>
		<string>0x0BDA</string>
		<string>0x03F0</string>
		<string>0x03F0</string>
		<string>0x04CC</string>
		<string>0x0D46</string>
		<string>0x0D46</string>
		<string>0x0B97</string>
		<string>0x0B97</string>
		<string>0x08C3</string>
		<string>0x08C3</string>
		<string>0x15E1</string>
		<string>0x062D</string>
	</array>

	<key>ifdProductID</key>
	<array>
		<string>0x90CC</string>
		<string>0x0013</string>
		<string>0x0014</string>
		<string>0x9520</string>
		<string>0x1004</string>
		<string>0x1102</string>
		<string>0x1078</string>
		<string>0x0006</string>
		<string>0x0005</string>
		<string>0x0010</string>
		<string>0x002D</string>
		<string>0x003E</string>
		<string>0x01A2</string>
		<string>0x0007</string>
		<string>0x2100</string>
		<string>0x2101</string>
		<string>0x0007</string>
		<string>0x0008</string>
		<string>0x0B00</string>
		<string>0x0C00</string>
		<string>0x0C01</string>
		<string>0x1227</string>
		<string>0x3437</string>
		<string>0x3438</string>
		<string>0x3478</string>
		<string>0x3480</string>
		<string>0x34C0</string>
		<string>0x34C3</string>
		<string>0x4433</string>
		<string>0x5503</string>
		<string>0x5504</string>
		<string>0x8000</string>
		<string>0x8141</string>
		<string>0x0017</string>
		<string>0x0019</string>
		<string>0x0200</string>
		<string>0x003B</string>
		<string>0x003D</string>
		<string>0x3001</string>
		<string>0x3002</string>
		<string>0x3003</string>
		<string>0x3010</string>
		<string>0x1003</string>
		<string>0x0002</string>
		<string>0x3021</string>
		<string>0xA022</string>
		<string>0x0824</string>
		<string>0x5111</string>
		<string>0x5116</string>
		<string>0x511D</string>
		<string>0x5410</string>
		<string>0xE001</string>
		<string>0xE003</string>
		<string>0x0801</string>
		<string>0x1359</string>
		<string>0xACE0</string>
		<string>0x0011</string>
		<string>0x0037</string>
		<string>0x0038</string>
		<string>0x0138</string>
		<string>0x0160</string>
		<string>0x0164</string>
		<string>0x0170</string>
		<string>0x0180</string>
		<string>0x0184</string>
		<string>0x1204</string>
		<string>0x221A</string>
		<string>0x223B</string>
		<string>0x223E</string>
		<string>0x223F</string>
		<string>0x2259</string>
		<string>0x226B</string>
		<string>0x2301</string>
		<string>0x2303</string>
		<string>0x2308</string>
		<string>0x8201</string>
		<string>0x8202</string>
		<string>0x8300</string>
		<string>0x90DB</string>
		<string>0xB000</string>
		<string>0xB100</string>
		<string>0xB106</string>
		<string>0xB112</string>
		<string>0xB501</string>
		<string>0xB506</string>
		<string>0xAFC0</string>
		<string>0xAFC1</string>
		<string>0xAFC2</string>
		<string>0xAFC3</string>
		<string>0x0001</string>
		<string>0x0011</string>
		<string>0x0101</string>
		<string>0x0025</string>
		<string>0x0030</string>
		<string>0x0069</string>
		<string>0x006A</string>
		<string>0x0080</string>
		<string>0x0081</string>
		<string>0x0082</string>
		<string>0x0060</string>
		<string>0x0101</string>
		<string>0x0102</string>
		<string>0x0201</string>
		<string>0x0401</string>
		<string>0x0402</string>
		<string>0x0420</string>
		<string>0x0428</string>
		<string>0x0501</string>
		<string>0x100F</string>
		<string>0x9563</string>
		<string>0x9567</string>
		<string>0x9573</string>
		<string>0x9522</string>
		<string>0x0021</string>
		<string>0xACD1</string>
		<string>0x5003</string>
		<string>0x5004</string>
		<string>0x5006</string>
		<string>0x0001</string>
		<string>0xCC1D</string>
		<string>0x0900</string>
		<string>0x1007</string>
		<string>0x1008</string>
		<string>0x6004</string>
		<string>0x6009</string>
		<string>0x600B</string>
		<string>0x6010</string>
		<string>0x6011</string>
		<string>0x6012</string>
		<string>0x6014</string>
		<string>0x5A11</string>
		<string>0x0131</string>
		<string>0x5824</string>
		<string>0x0019</string>
		<string>0x001D</string>
		<string>0x0020</string>
		<string>0x511C</string>
		<string>0x0001</string>
		<string>0x0002</string>
		<string>0x0003</string>
		<string>0x0008</string>
		<string>0x1101</string>
		<string>0x1201</string>
		<string>0x2221</string>
		<string>0x2321</string>
		<string>0x2341</string>
		<string>0x2351</string>
		<string>0x2354</string>
		<string>0x2361</string>
		<string>0x2362</string>
		<string>0x2371</string>
		<string>0x3211</string>
		<string>0xB001</string>
		<string>0x3111</string>
		<string>0x3403</string>
		<string>0x3503</string>
		<string>0x1003</string>
		<string>0x1079</string>
		<string>0x5A02</string>
		<string>0x5802</string>
		<string>0x5804</string>
		<string>0x5832</string>
		<string>0x5833</string>
		<string>0x5834</string>
		<string>0x5842</string>
		<string>0x5843</string>
		<string>0x5864</string>
		<string>0x5865</string>
		<string>0x0007</string>
		<string>0x0009</string>
		<string>0x0010</string>
		<string>0x0036</string>
		<string>0x42D4</string>
		<string>0x00A0</string>
		<string>0xCCB2</string>
		<string>0xB601</string>
		<string>0xB602</string>
		<string>0x0002</string>
		<string>0x0090</string>
		<string>0x0092</string>
		<string>0x00A1</string>
		<string>0x00A2</string>
		<string>0x00A3</string>
		<string>0x00A4</string>
		<string>0x00A5</string>
		<string>0x00A7</string>
		<string>0x005B</string>
		<string>0x0070</string>
		<string>0x0072</string>
		<string>0x0967</string>
		<string>0x114A</string>
		<string>0x124A</string>
		<string>0x1469</string>
		<string>0x0224</string>
		<string>0x0324</string>
		<string>0x1000</string>
		<string>0x1100</string>
		<string>0x2000</string>
		<string>0x2001</string>
		<string>0x2100</string>
		<string>0x3000</string>
		<string>0x3001</string>
		<string>0x3002</string>
		<string>0x3003</string>
		<string>0x3100</string>
		<string>0x4000</string>
		<string>0x4001</string>
		<string>0x5000</string>
		<string>0x6000</string>
		<string>0x6001</string>
		<string>0x7001</string>
		<string>0x7003</string>
		<string>0x7004</string>
		<string>0x7005</string>
		<string>0x7006</string>
		<string>0x0905</string>
		<string>0x0008</string>
		<string>0x0040</string>
		<string>0x0603</string>
		<string>0xC305</string>
		<string>0x0300</string>
		<string>0x0302</string>
		<string>0xA389</string>
		<string>0xA40B</string>
		<string>0xAFD0</string>
		<string>0xAFD1</string>
		<string>0xAFD2</string>
		<string>0xAFD3</string>
		<string>0x0801</string>
		<string>0x080C</string>
		<string>0x088B</string>
		<string>0xD205</string>
		<string>0x0427</string>
		<string>0x0428</string>
		<string>0x0002</string>
		<string>0x5001</string>
		<string>0x5020</string>
		<string>0x7479</string>
		<string>0x8013</string>
		<string>0x2702</string>
		<string>0x0608</string>
		<string>0x060D</string>
		<string>0x0621</string>
		<string>0x0622</string>
		<string>0x0623</string>
		<string>0x0624</string>
		<string>0x062E</string>
		<string>0x0685</string>
		<string>0x0807</string>
		<string>0x080F</string>
		<string>0x0619</string>
		<string>0x061A</string>
		<string>0x061C</string>
		<string>0x0505</string>
		<string>0x700A</string>
		<string>0x0000</string>
		<string>0x080A</string>
		<string>0x0853</string>
		<string>0x0855</string>
		<string>0x0856</string>
		<string>0x0859</string>
		<string>0x0867</string>
		<string>0x0868</string>
		<string>0x0869</string>
		<string>0x086C</string>
		<string>0x1022</string>
		<string>0x1023</string>
		<string>0x1024</string>
		<string>0x1005</string>
		<string>0x1006</string>
		<string>0x1017</string>
		<string>0x101B</string>
		<string>0x1021</string>
		<string>0x0101</string>
		<string>0x2202</string>
		<string>0x34C1</string>
		<string>0x34C2</string>
		<string>0x34C5</string>
		<string>0x34EC</string>
		<string>0x4042</string>
		<string>0x5743</string>
		<string>0x8108</string>
		<string>0x3440</string>
		<string>0x3479</string>
		<string>0x0169</string>
		<string>0x1366</string>
		<string>0x9590</string>
		<string>0x3A21</string>
		<string>0x0757</string>
		<string>0x0758</string>
		<string>0x0771</string>
		<string>0x4107</string>
		<string>0x8001</string>
		<string>0x000C</string>
		<string>0x000D</string>
		<string>0x0002</string>
		<string>0x0012</string>
		<string>0x3201</string>
		<string>0x0025</string>
		<string>0x104A</string>
		<string>0x2924</string>
		<string>0x581D</string>
		<string>0x0028</string>
		<string>0x0029</string>
		<string>0x002B</string>
		<string>0x002D</string>
		<string>0x3031</string>
		<string>0x5022</string>
		<string>0x5023</string>
		<string>0x5400</string>
		<string>0x5412</string>
		<string>0x5422</string>
		<string>0x5432</string>
		<string>0x5A27</string>
		<string>0x6632</string>
		<string>0x502A</string>
		<string>0x5127</string>
		<string>0x5326</string>
		<string>0x5427</string>
		<string>0x00C7</string>
		<string>0x00D4</string>
		<string>0x0220</string>
		<string>0x5612</string>
		<string>0x5613</string>
		<string>0x5713</string>
		<string>0x5724</string>
		<string>0x5725</string>
		<string>0x5790</string>
		<string>0x5791</string>
		<string>0x5811</string>
		<string>0x5812</string>
		<string>0x5814</string>
		<string>0x5815</string>
		<string>0x5816</string>
		<string>0x5818</string>
		<string>0x5819</string>
		<string>0x581A</string>
		<string>0x581B</string>
		<string>0x581C</string>
		<string>0x581D</string>
		<string>0x5826</string>
		<string>0x5710</string>
		<string>0x5720</string>
		<string>0x5721</string>
		<string>0x5723</string>
		<string>0x5810</string>
		<string>0x5817</string>
		<string>0x000C</string>
		<string>0x6200</string>
		<string>0x9308</string>
		<string>0x9324</string>
		<string>0xCCDB</string>
		<string>0x0100</string>
		<string>0x003A</string>
		<string>0x016C</string>
		<string>0x0102</string>
		<string>0x7506</string>
		<string>0x750C</string>
		<string>0x750D</string>
		<string>0x6300</string>
		<string>0x6301</string>
		<string>0x6302</string>
		<string>0x6403</string>
		<string>0x6404</string>
		<string>0x6407</string>
		<string>0x6303</string>
		<string>0x6305</string>
		<string>0x3B01</string>
		<string>0xEB61</string>
		<string>0x5000</string>
		<string>0x5001</string>
		<string>0x5002</string>
		<string>0x0101</string>
		<string>0x0102</string>
		<string>0x0103</string>
		<string>0x0104</string>
		<string>0x0105</string>
		<string>0x0112</string>
		<string>0x0111</string>
		<string>0x0113</string>
		<string>0x0114</string>
		<string>0x0002</string>
		<string>0x0906</string>
		<string>0x0007</string>
		<string>0x3014</string>
		<string>0x301D</string>
		<string>0x4189</string>
		<string>0x41A9</string>
		<string>0x4289</string>
		<string>0x4389</string>
		<string>0x43A9</string>
		<string>0x0000</string>
		<string>0x0001</string>
		<string>0x900A</string>
		<string>0x0001</string>
		<string>0x0004</string>
		<string>0x0005</string>
		<string>0x1009</string>
		<string>0x4009</string>
		<string>0x5009</string>
		<string>0x6009</string>
		<string>0x6007</string>
		<string>0x6055</string>
		<string>0x6111</string>
		<string>0x164A</string>
		<string>0x174A</string>
		<string>0x184A</string>
		<string>0x0BDF</string>
		<string>0x7502</string>
		<string>0x1104</string>
		<string>0x1202</string>
		<string>0xC136</string>
		<string>0xC137</string>
		<string>0x0026</string>
		<string>0x0052</string>
		<string>0x0161</string>
		<string>0x0010</string>
		<string>0x0013</string>
		<string>0x0033</string>
		<string>0x1023</string>
		<string>0x8033</string>
		<string>0x4108</string>
		<string>0x4109</string>
		<string>0x4211</string>
		<string>0x4230</string>
		<string>0x42B2</string>
		<string>0x511A</string>
		<string>0x0107</string>
		<string>0x010B</string>
		<string>0x0117</string>
		<string>0x0157</string>
		<string>0xB111</string>
		<string>0x6354</string>
		<string>0x1021</string>
		<string>0x4321</string>
		<string>0x5321</string>
		<string>0x5421</string>
		<string>0x6321</string>
		<string>0x3022</string>
		<string>0x3621</string>
		<string>0x3821</string>
		<string>0x5121</string>
		<string>0x5125</string>
		<string>0x6622</string>
		<string>0x6623</string>
		<string>0xA021</string>
		<string>0x117A</string>
		<string>0x040F</string>
		<string>0x1400</string>
		<string>0x0603</string>
		<string>0x0010</string>
		<string>0x4C4B</string>
		<string>0x1006</string>
		<string>0x7050</string>
		<string>0x0504</string>
		<string>0x0520</string>
		<string>0x0551</string>
		<string>0x0580</string>
		<string>0x9102</string>
		<string>0xCCDA</string>
		<string>0x8166</string>
		<string>0xC831</string>
		<string>0x0602</string>
		<string>0x0620</string>
		<string>0x34CC</string>
		<string>0x34CE</string>
		<string>0x34CF</string>
		<string>0x34D2</string>
		<string>0x0102</string>
		<string>0x0001</string>
		<string>0x0003</string>
		<string>0x5113</string>
		<string>0x5115</string>
		<string>0x5117</string>
		<string>0x5119</string>
		<string>0x511F</string>
		<string>0x5120</string>
		<string>0x5121</string>
		<string>0x512B</string>
		<string>0x512C</string>
		<string>0x5291</string>
		<string>0x5293</string>
		<string>0x8007</string>
		<string>0xC587</string>
		<string>0x22FC</string>
		<string>0x23FE</string>
		<string>0x0341</string>
		<string>0x800A</string>
		<string>0x0007</string>
		<string>0xBEEE</string>
		<string>0x0D8F</string>
		<string>0x0DC8</string>
		<string>0x0DC9</string>
		<string>0x0001</string>
		<string>0x0002</string>
		<string>0x0003</string>
		<string>0x0010</string>
		<string>0x6012</string>
		<string>0x601A</string>
		<string>0x6112</string>
		<string>0x611A</string>
		<string>0x6122</string>
		<string>0x612A</string>
		<string>0x6132</string>
		<string>0x613A</string>
		<string>0x6212</string>
		<string>0x621A</string>
		<string>0x7113</string>
		<string>0x7121</string>
		<string>0x7123</string>
		<string>0x7124</string>
		<string>0x7136</string>
		<string>0x7138</string>
		<string>0x7141</string>
		<string>0x8141</string>
		<string>0x91B1</string>
		<string>0xA1A1</string>
		<string>0x0004</string>
		<string>0x3115</string>
		<string>0x3117</string>
		<string>0x3201</string>
		<string>0x3203</string>
		<string>0x605A</string>
		<string>0x0901</string>
		<string>0x2105</string>
		<string>0x2107</string>
		<string>0x5083</string>
		<string>0x6141</string>
		<string>0x0000</string>
		<string>0x34C7</string>
		<string>0x400B</string>
		<string>0x0001</string>
		<string>0x0810</string>
		<string>0x0050</string>
		<string>0x0052</string>
		<string>0x0300</string>
		<string>0x0430</string>
		<string>0x0003</string>
		<string>0x0009</string>
		<string>0x0001</string>
		<string>0x1540</string>
		<string>0x1543</string>
		<string>0x0001</string>
		<string>0x0001</string>
		<string>0x0101</string>
		<string>0x0112</string>
		<string>0x0115</string>
		<string>0x0117</string>
		<string>0x0119</string>
		<string>0x0120</string>
		<string>0x0122</string>
		<string>0x0855</string>
		<string>0x0865</string>
		<string>0x0870</string>
		<string>0x0875</string>
		<string>0x0920</string>
		<string>0x0004</string>
		<string>0x0406</string>
		<string>0x0407</string>
		<string>0x0417</string>
		<string>0x0418</string>
		<string>0x0A03</string>
		<string>0x1506</string>
		<string>0x3815</string>
		<string>0x0111</string>
		<string>0x0112</string>
		<string>0x0115</string>
		<string>0x0116</string>
		<string>0x0404</string>
		<string>0x0405</string>
		<string>0x0406</string>
		<string>0x0407</string>
		<string>0x2200</string>
		<string>0x2224</string>
		<string>0x0008</string>
		<string>0x9540</string>
		<string>0x100F</string>
		<string>0x6016</string>
		<string>0x5800</string>
		<string>0x5801</string>
		<string>0x5805</string>
		<string>0x5844</string>
		<string>0x5845</string>
		<string>0x5866</string>
		<string>0x5867</string>
		<string>0x0003</string>
		<string>0x0503</string>
		<string>0x0165</string>
		<string>0x0036</string>
		<string>0x1024</string>
		<string>0x5072</string>
		<string>0x4000</string>
		<string>0x4001</string>
		<string>0x7762</string>
		<string>0x7772</string>
		<string>0x0401</string>
		<string>0x0402</string>
		<string>0x2007</string>
		<string>0x0001</string>
	</array>

	<key>ifdFriendlyName</key>
	<array>
		<string>ACS ACR 38U-CCID</string>
		<string>ActivIdentity USB Reader V3</string>
		<string>ActivIdentity Activkey_Sim</string>
		<string>Alcor Micro AU9520</string>
		<string>Athena ASE IIIe</string>
		<string>Athena ASEDrive IIIe KB</string>
		<string>BLUTRONICS BLUDRIVE II CCID</string>
		<string>C3PO LTC31 v2</string>
		<string>Cherry GmbH SmartBoard XX33</string>
		<string>Cherry GmbH SmartBoard XX44</string>
		<string>Cherry GmbH SmartTerminal XX44</string>
		<string>Cherry GmbH SmartTerminal ST-2xxx</string>
		<string>Cherry GmbH CHERRY SECURE BOARD 1.0</string>
		<string>COVADIS ALYA</string>
		<string>Dell keyboard SK-3106</string>
		<string>Dell Dell Smart Card Reader Keyboard</string>
		<string>Eutron CryptoIdentity CCID</string>
		<string>Eutron CryptoIdentity CCID</string>
		<string>Eutron Digipass 860</string>
		<string>Eutron Card Reader</string>
		<string>Eutron Smart Pocket</string>
		<string>Gemalto PDT</string>
		<string>Gemalto PC Twin Reader</string>
		<string>Gemalto USB Shell Token V2</string>
		<string>Gemalto USB GemPCPinpad SmartCard Reader</string>
		<string>Gemalto GemCore SIM Pro Smart Card Reader</string>
		<string>Gemalto Ezio Shield</string>
		<string>Gemalto EZIO CB+</string>
		<string>Gemalto Gemplus USB SmartCard Reader 433-Swap</string>
		<string>Gemalto Prox Dual USB PC Link Reader</string>
		<string>Gemalto Prox SU USB PC LinkReader</string>
		<string>Gemalto Smart Enterprise Guardian Secure USB Device</string>
		<string>Gemalto IDBridge K3000</string>
		<string>Giesecke &amp; Devrient GmbH StarSign Crypto USB Token</string>
		<string>Giesecke &amp; Devrient GmbH StarSign CUT S</string>
		<string>id3 Semiconductors CL1356T</string>
		<string>INGENICO Leo</string>
		<string>Ingenico WITEO USB Smart Card Reader</string>
		<string>KOBIL KAAN Base</string>
		<string>KOBIL KAAN Advanced</string>
		<string>KOBIL KAAN SIM III</string>
		<string>KOBIL EMV CAP - SecOVID Reader III</string>
		<string>Lenovo Integrated Smart Card Reader</string>
		<string>MYSMART MySMART PAD V2.0</string>
		<string>OMNIKEY AG CardMan 3121</string>
		<string>Precise Biometrics Sense MC</string>
		<string>SCM Microsystems Inc. HP USB Smartcard Reader</string>
		<string>SCM Microsystems Inc. SCR 331-DI</string>
		<string>SCM Microsystems Inc. SCR 3310</string>
		<string>SCM Microsystems Inc. SCR 3311</string>
		<string>SCM Microsystems Inc. SCR 355</string>
		<string>SCM Microsystems Inc. SCR 331</string>
		<string>SCM Microsystems Inc. SPR 532</string>
		<string>TianYu CCID Key TianYu CCID SmartKey</string>
		<string>Verisign Secure Storage Token</string>
		<string>Verisign Secure Token</string>
		<string>XIRING XI-SIGN USB V2</string>
		<string>XIRING MyLeo</string>
		<string>XIRING Leo v2</string>
		<string>Access IS ePassport Reader</string>
		<string>Access IS NFC Smart Module</string>
		<string>Access IS NFC Smart Module</string>
		<string>Access IS ATR210</string>
		<string>Access IS ATR220</string>
		<string>Access IS ATR220</string>
		<string>ACS ACR101 ICC Reader</string>
		<string>ACS ACR1251 Dual Reader</string>
		<string>ACS ACR1252 Dual Reader</string>
		<string>ACS ACR1252 Reader</string>
		<string>ACS ACR1255U-J1</string>
		<string>ACS ACR1252IMP Reader</string>
		<string>ACS WalletMate 1S CL Reader</string>
		<string>ACS ACR1581 1S Dual Reader</string>
		<string>ACS ACR1552 1S CL Reader</string>
		<string>ACS ACR1552 CL Reader</string>
		<string>ACS APG8201 PINhandy 1</string>
		<string>ACS APG8201 USB Reader</string>
		<string>ACS ACR33 ICC Reader</string>
		<string>ACS CryptoMate64</string>
		<string>ACS ACR3901U ICC Reader</string>
		<string>ACS ACR39U ICC Reader</string>
		<string>ACS CryptoMate (T2)</string>
		<string>ACS CryptoMate EVO</string>
		<string>ACS ACR40T ICC Reader</string>
		<string>ACS ACR40U ICC Reader</string>
		<string>AF Care One</string>
		<string>AF Care One</string>
		<string>AF Care Two</string>
		<string>AF Care Two</string>
		<string>AK910 CKey</string>
		<string>AK910 CKey</string>
		<string>AK910 IDONE</string>
		<string>Aktiv Rutoken lite</string>
		<string>Aktiv Rutoken ECP</string>
		<string>Aktiv Rutoken SCR 3001 Reader</string>
		<string>Aktiv Rutoken SCR 3101 NFC Reader</string>
		<string>Aktiv PINPad Ex</string>
		<string>Aktiv PINPad In</string>
		<string>Aktiv Rutoken PINPad 2</string>
		<string>Aktiv Co., ProgramPark Rutoken Magistra</string>
		<string>Aladdin R.D. JaCarta</string>
		<string>Aladdin R.D. JaCarta LT</string>
		<string>Aladdin R.D. JCR-770</string>
		<string>Aladdin R.D. JC-WebPass (JC600)</string>
		<string>Aladdin R.D. JaCarta</string>
		<string>Aladdin R.D. JCR SecurBio</string>
		<string>Aladdin R.D. JaCartaReader</string>
		<string>Aladdin R.D. JaCarta U2F (JC602)</string>
		<string>Aladdin R.D. JaCarta Flash</string>
		<string>Alcor Link AK9563</string>
		<string>Alcor Link AK9567</string>
		<string>Alcor Link AK9572</string>
		<string>Alcor Micro AU9522</string>
		<string>Alpha-Project ANGARA Token</string>
		<string>ANCUD CCID USB Reader &amp; RNG</string>
		<string>appidkey GmbH ID50 -USB</string>
		<string>appidkey GmbH ID100L-USB-SC-Reader</string>
		<string>appidkey GmbH ID60-USB</string>
		<string>ArkSigner Connect2Sign</string>
		<string>ASK-RFID CPL108</string>
		<string>Athena IDProtect Key v2</string>
		<string>Athena ASEDrive IIIe KB Bio PIV</string>
		<string>Athena ASEDrive IIIe Combo Bio PIV</string>
		<string>ATMEL AT91SO CCID Smart Card Reader</string>
		<string>ATMEL AT98SC032CT-USB</string>
		<string>ATMEL AT91SC192192CT-USB ICCD reader</string>
		<string>ATMEL AT90SCR100</string>
		<string>ATMEL AT90SCR050</string>
		<string>ATMEL VaultIC420 Smart Object</string>
		<string>ATMEL VaultIC440</string>
		<string>authenton #1- CTAP2.1</string>
		<string>AvestUA AvestKey</string>
		<string>AvidCard CAC Smart Card Reader</string>
		<string>Avtor SecureToken</string>
		<string>Avtor SC Reader 371</string>
		<string>Avtor SecureToken</string>
		<string>Axalto Reflex USB v3</string>
		<string>BIFIT USB-Token iBank2key</string>
		<string>BIFIT iBank2Key</string>
		<string>BIFIT iToken</string>
		<string>BIFIT ANGARA</string>
		<string>Bit4id miniLector-s</string>
		<string>Bit4id cryptokey</string>
		<string>Bit4id iAM</string>
		<string>Bit4id CKey4</string>
		<string>Bit4id tokenME FIPS v3</string>
		<string>Bit4id Digital DNA Key</string>
		<string>Bit4id Digital-DNA Key</string>
		<string>Bit4id Digital-DNA Key BT</string>
		<string>Bit4id Digital-DNA Key</string>
		<string>Bit4id TokenME EVO v2</string>
		<string>Bit4id miniLector AIR EVO</string>
		<string>Bit4id miniLector Blue</string>
		<string>bit4id miniLector-EVO</string>
		<string>BIT4ID miniLector AIR NFC v3</string>
		<string>BIT4ID mLector AIR DI V3</string>
		<string>Bluink Ltd. Bluink CCID</string>
		<string>BLUTRONICS BLUDRIVE II CCID</string>
		<string>BLUTRONICS TAURUS NFC</string>
		<string>Broadcom Corp 5880</string>
		<string>Broadcom Corp 5880</string>
		<string>Broadcom Corp 5880</string>
		<string>Broadcom Corp 5880</string>
		<string>Broadcom Corp 5880</string>
		<string>Broadcom Corp 58200</string>
		<string>Broadcom Corp 58200</string>
		<string>Broadcom Corp 58200</string>
		<string>Broadcom Corp 58200</string>
		<string>C3PO TLTC2USB</string>
		<string>C3PO KBR36</string>
		<string>C3PO LTC32</string>
		<string>C3PO LTC36</string>
		<string>Canokeys Canokey</string>
		<string>CASTLES EZCCID Smart Card Reader</string>
		<string>CCB eSafeLD</string>
		<string>Certgate GmbH AirID 2 USB</string>
		<string>Certgate GmbH ONEKEY ID 2 USB</string>
		<string>charismathics plug'n'crypt CCID token</string>
		<string>Cherry Smart Card Reader USB</string>
		<string>Cherry TC 1300</string>
		<string>Cherry KC 1000 SC</string>
		<string>Cherry KC 1000 SC/DI</string>
		<string>Cherry Smartcard Keyboard G87-1xx44</string>
		<string>Cherry KC 1000 SC Z</string>
		<string>Cherry KC 1000 SC/DI Z</string>
		<string>Cherry SmartTerminal XX44</string>
		<string>Cherry GmbH SmartBoard XX1X</string>
		<string>Cherry GmbH SmartTerminal XX1X</string>
		<string>Cherry GmbH SmartTerminal ST-1275</string>
		<string>Chicony USB Smart Card Keyboard</string>
		<string>Chicony HP USB Smartcard CCID Keyboard KR</string>
		<string>Chicony HP USB Smartcard CCID Keyboard JP</string>
		<string>Chicony HP Skylab USB Smartcard Keyboard</string>
		<string>Circle Idaxis SecurePIV</string>
		<string>Circle CIR315</string>
		<string>Circle CIR115 ICC</string>
		<string>Circle CIR125 ICC</string>
		<string>Circle CIR215 PICC</string>
		<string>Circle CIR215 CL</string>
		<string>Circle CIR215 CL</string>
		<string>Circle CIR315 Dual &amp; 1S</string>
		<string>Circle CIR315</string>
		<string>Circle CIR315 CL</string>
		<string>Circle CIR315 DI</string>
		<string>Circle CIR315</string>
		<string>Circle CIR415 CL &amp; 1S</string>
		<string>Circle CIR415 CL</string>
		<string>Circle CIR515 ICC</string>
		<string>Circle CIR615 CL &amp; 1S</string>
		<string>Circle CIR615 CL</string>
		<string>Circle CCR7115 ICC</string>
		<string>Circle CCR7315</string>
		<string>Circle CIR315</string>
		<string>Circle CCR7125 ICC</string>
		<string>Circle CIR125-DOT ICC</string>
		<string>CIRIGHT ONE PASS U2F</string>
		<string>COVADIS VEGA-ALPHA</string>
		<string>COVADIS Auriga</string>
		<string>CREATOR CRT-603(CZ1) CCR</string>
		<string>DC.Ltd DC4 5CCID READER</string>
		<string>DESKO GmbH IDenty chrom</string>
		<string>DESKO GmbH PENTA Scanner</string>
		<string>Dexon Tecnologias Digitais LTDA DXToken</string>
		<string>Dexon Tecnologias Digitais LTDA eSmartDX</string>
		<string>Doctolib SR</string>
		<string>Doctolib SR</string>
		<string>Doctolib SR</string>
		<string>Doctolib SR</string>
		<string>DUALi DE-620 Combi</string>
		<string>DUALi DE-ABCM6 RFRW</string>
		<string>DUALi DRAGON NFC READER</string>
		<string>eID_R6 001 X8</string>
		<string>Elatec TWN4 SmartCard NFC</string>
		<string>Elatec TWN4/B1.06/CPF3.05/S1SC1.32/P (Beta 3)</string>
		<string>ELYCTIS CL reader</string>
		<string>ESMART Reader ER433x ICC</string>
		<string>ESMART Reader ER773x Dual &amp; 1S</string>
		<string>ESMART Token GOST</string>
		<string>Excelsecu Card reader</string>
		<string>F-Secure Foundry USB Armory Mk II</string>
		<string>Feitian 502-CL</string>
		<string>Feitian R502</string>
		<string>Feitian BLE CCID Dongle</string>
		<string>Feitian VR504 VHBR Contactless &amp; Contact Card Reader</string>
		<string>Feitian bR500</string>
		<string>Feitian bR301</string>
		<string>Feitian R701</string>
		<string>Feitian R805</string>
		<string>Feitian ePass2003</string>
		<string>Feitian eJAVA Token</string>
		<string>FEITIAN iR301</string>
		<string>FEITIAN bR301</string>
		<string>FEITIAN iR301</string>
		<string>Feitian Technologies FT SCR310</string>
		<string>Flight system consulting Incredist</string>
		<string>Free Software Initiative of Japan Gnuk</string>
		<string>FT ePass2003Auto</string>
		<string>FT U2F CCID KB</string>
		<string>FT CCID KB</string>
		<string>FT U2F CCID</string>
		<string>FT CCID</string>
		<string>FT Biopass FIDO2</string>
		<string>FT Biopass KB FIDO CCID</string>
		<string>FT Biopass KB CCID</string>
		<string>FT Biopass CCID</string>
		<string>Fujitsu Keyboard KB100 SCR</string>
		<string>Fujitsu Keyboard KB100 SCR eSIG</string>
		<string>Fujitsu Smartcard Reader D323</string>
		<string>Fujitsu Siemens Computers SmartCard Keyboard USB 2A</string>
		<string>Fujitsu Siemens Computers SmartCard USB 2A</string>
		<string>FujitsuTechnologySolutions GmbH SmartCase KB SCR eSIG</string>
		<string>FujitsuTechnologySolutions GmbH Dual Smartcard Reader D321</string>
		<string>FujitsuTechnologySolutions GmbH Keyboard KB SCR2</string>
		<string>Gemalto RF CR5400</string>
		<string>Gemalto Gem e-Seal Pro USB Token</string>
		<string>Gemalto Ezio Shield Secure Channel</string>
		<string>Gemalto Ezio Shield</string>
		<string>Gemalto Ezio Shield Branch Reader</string>
		<string>Gemalto GemPC Express</string>
		<string>Gemalto SA .NET Dual</string>
		<string>Gemalto Hybrid Smartcard Reader</string>
		<string>Gemalto Smart Enterprise Guardian Secure USB Device</string>
		<string>GEMALTO CT1100</string>
		<string>Gemplus GemCore POS Pro Smart Card Reader</string>
		<string>Generic USB2.0-CRW</string>
		<string>Generic MultiCard Device</string>
		<string>Generic EMV Smartcard Reader</string>
		<string>Generic USB Smart Card Reader</string>
		<string>Genesys Logic CCID Card Reader</string>
		<string>Genesys Logic Combo Card Reader</string>
		<string>Genesys Logic CCID Card Reader</string>
		<string>German Privacy Foundation Crypto Stick v1.2</string>
		<string>GHI NC001</string>
		<string>Giesecke &amp; Devrient GmbH Star Sign Card Token 350 (ICCD)</string>
		<string>Giesecke &amp; Devrient GmbH Star Sign Card Token 550 (ICCD)</string>
		<string>GIS Ltd SmartMouse USB</string>
		<string>GoldKey Security PIV Token</string>
		<string>GoTrust Idem Key</string>
		<string>HDZB uKeyCI800-K18</string>
		<string>Hewlett Packard HP USB Smartcard CCID Keyboard</string>
		<string>Hewlett Packard MFP Smart Card Reader</string>
		<string>Hewlett-Packard HP lt4112 Gobi 4G Module</string>
		<string>HID Global Crescendo Key</string>
		<string>HID Global Crescendo Key</string>
		<string>HID Global Crescendo Key</string>
		<string>HID Global Crescendo Key</string>
		<string>HID Global OMNIKEY 3x21 Smart Card Reader</string>
		<string>HID Global OMNIKEY 5022 Smart Card Reader</string>
		<string>HID Global OMNIKEY 5023 Smart Card Reader</string>
		<string>HID Global veriCLASS Reader</string>
		<string>HID Global OMNIKEY 5122 Smartcard Reader</string>
		<string>HID Global OMNIKEY 5422 Smartcard Reader</string>
		<string>HID Global OMNIKEY 5122 Dual</string>
		<string>HID Global OMNIKEY 5027CK CCID CONFIG IF</string>
		<string>HID Global OMNIKEY 6121 Smart Card Reader</string>
		<string>HID OMNIKEY 5025-CL</string>
		<string>HID OMNIKEY 5127 CK</string>
		<string>HID OMNIKEY 5326 DFR</string>
		<string>HID OMNIKEY 5427 CK</string>
		<string>Hitachi, Ltd. Hitachi Biometric Reader</string>
		<string>Hitachi, Ltd. Hitachi Portable Biometric Reader</string>
		<string>id3 Semiconductors CL1356A_HID</string>
		<string>Identiv uTrust 3720 Contactless Reader</string>
		<string>Identiv uTrust 3721 Contactless Reader</string>
		<string>Identiv CLOUD 2980 F Smart Card Reader</string>
		<string>Identiv Identiv uTrust 4701 F Dual Interface Reader</string>
		<string>Identiv Identiv uTrust 4711 F CL + SAM Reader</string>
		<string>Identiv uTrust 3700 F CL Reader</string>
		<string>Identiv uTrust 3701 F CL Reader</string>
		<string>Identiv uTrust 2900 R Smart Card Reader</string>
		<string>Identiv uTrust 2910 R Smart Card Reader</string>
		<string>Identiv SCR3500 A Contact Reader</string>
		<string>Identiv SCR3500 B Contact Reader</string>
		<string>Identiv uTrust 3512 SAM slot Token</string>
		<string>Identiv @MAXX Light2 token</string>
		<string>Identiv @MAXX ID-1 Smart Card Reader</string>
		<string>Identiv uTrust 3522 embd SE RFID Token</string>
		<string>Identiv uTrust 2910 R Taglio SC Reader</string>
		<string>Identiv SCR35xx USB Smart Card Reader</string>
		<string>Identiv SCR3500 C Contact Reader</string>
		<string>Identiv uTrust Token Flex</string>
		<string>Identive CLOUD 2700 F Smart Card Reader</string>
		<string>Identive Identive CLOUD 4500 F Dual Interface Reader</string>
		<string>Identive Identive CLOUD 4510 F Contactless + SAM Reader</string>
		<string>Identive Identive CLOUD 4000 F DTC</string>
		<string>Identive CLOUD 2700 R Smart Card Reader</string>
		<string>Identive SCT3522CC token</string>
		<string>Identive Technologies Multi-ISO HF Reader - USB</string>
		<string>IID AT90S064 CCID READER</string>
		<string>IIT E.Key Crystal-1</string>
		<string>IIT E.Key Almaz-1C</string>
		<string>Imprivata USB CCID</string>
		<string>InfoCert WirelessKey</string>
		<string>Infocrypt Token++ lite</string>
		<string>Infocrypt HWDSSL DEVICE</string>
		<string>InfoThink IT-102MU Reader</string>
		<string>InfoThink IT-500U Reader</string>
		<string>INMAX DWR18 HC</string>
		<string>INMAX DWR18 HPC</string>
		<string>Inside Secure VaultIC 420 Smart Object</string>
		<string>Inside Secure VaultIC 440 Smart Object</string>
		<string>Inside Secure VaultIC 460 Smart Object</string>
		<string>Inside Secure AT90SCR100</string>
		<string>Inside Secure AT90SCR050</string>
		<string>Inside Secure AT90SCR200</string>
		<string>INSIDE Secure VaultIC 405 Smart Object</string>
		<string>INSIDE Secure VaultIC 441 Smart Object</string>
		<string>IonIDe Smartcard Reader</string>
		<string>jSolutions s.r.o. Multi SIM card reader 4/8</string>
		<string>KACST HSID Reader</string>
		<string>KACST HSID Reader Single Storage</string>
		<string>KACST HSID Reader Dual Storage</string>
		<string>KAPELSE KAP-LINK</string>
		<string>KAPELSE KAP-Care</string>
		<string>KAPELSE KAP-GO</string>
		<string>KAPELSE eS-KAP-Ad</string>
		<string>KAPELSE KAP-LINK2</string>
		<string>KAPELSE KAP-eCV</string>
		<string>Kapelse Ti-Kap</string>
		<string>Kapelse inSide</string>
		<string>Kapelse KAP-Move</string>
		<string>Kapsch TrafficCom USB SAM reader</string>
		<string>KeyXentic Inc. KX906 Smart Card Reader</string>
		<string>Kingtrust Multi-Reader</string>
		<string>KOBIL Systems Smart Token</string>
		<string>KOBIL Systems IDToken</string>
		<string>KOBIL Systems mIDentity 4smart</string>
		<string>KOBIL Systems mIDentity 4smart AES</string>
		<string>KOBIL Systems mIDentity visual</string>
		<string>KOBIL Systems mIDentity fullsize</string>
		<string>KOBIL Systems mIDentity fullsize AES</string>
		<string>KRONEGGER NFC blue Reader Platform</string>
		<string>KRONEGGER Micro Core Platform</string>
		<string>LDU LANDI</string>
		<string>Ledger Nano S</string>
		<string>Ledger Nano X</string>
		<string>Ledger Nano SP</string>
		<string>Ledger Nano S</string>
		<string>Ledger Nano X</string>
		<string>Ledger Nano S Plus</string>
		<string>Ledger Stax</string>
		<string>Lenovo Lenovo USB Smartcard Keyboard</string>
		<string>Lenovo Lenovo USB Smartcard Keyboard</string>
		<string>Lenovo Lenovo Smartcard Wired Keyboard II</string>
		<string>Liteon HP SC Keyboard - Apollo (Liteon)</string>
		<string>Liteon HP SC Keyboard - Apollo KR (Liteon)</string>
		<string>Liteon HP SC Keyboard - Apollo JP (Liteon)</string>
		<string>Macally NFC CCID eNetPad</string>
		<string>mCore SCard-Reader</string>
		<string>Microchip SEC1110</string>
		<string>Microchip SEC1210</string>
		<string>MK Technology KeyPass S1</string>
		<string>MK Technology KeyPass D1</string>
		<string>Morpho MSO350/MSO351 Fingerprint Sensor &amp; SmartCard Reader</string>
		<string>Morpho MSO1350 Fingerprint Sensor &amp; SmartCard Reader</string>
		<string>MSI StarReader SMART</string>
		<string>Mulann PVT</string>
		<string>Neowave Weneo</string>
		<string>Neowave Weneo</string>
		<string>Neowave Weneo</string>
		<string>Neowave Weneo</string>
		<string>Nitrokey Nitrokey Pro</string>
		<string>Nitrokey Nitrokey Storage</string>
		<string>Nitrokey Nitrokey Start</string>
		<string>Nitrokey Nitrokey HSM</string>
		<string>Nitrokey Nitrokey 3</string>
		<string>NTT Communications Corp. SCR3310-NTTCom USB SmartCard Reader</string>
		<string>NXP Pegoda 2 N</string>
		<string>NXP PR533</string>
		<string>NXP PN7462AU CCID</string>
		<string>NXP Pegoda 3</string>
		<string>OBERTHUR TECHNOLOGIES ID-ONE TOKEN SLIM v2</string>
		<string>OCS ID-One Cosmo Card USB Smart Chip Device</string>
		<string>OMNIKEY CardMan 1021</string>
		<string>OMNIKEY CardMan 4321</string>
		<string>OMNIKEY CardMan 5321</string>
		<string>OMNIKEY 5421</string>
		<string>OMNIKEY 6321 CLi USB</string>
		<string>OMNIKEY AG 3121 USB</string>
		<string>OMNIKEY AG CardMan 3621</string>
		<string>OMNIKEY AG CardMan 3821</string>
		<string>OMNIKEY AG CardMan 5121</string>
		<string>OMNIKEY AG CardMan 5125</string>
		<string>OMNIKEY AG CardMan 6121</string>
		<string>OMNIKEY AG 6121 USB mobile</string>
		<string>OMNIKEY AG Smart Card Reader</string>
		<string>Panasonic Panasonic USB Smart Card Reader 7A-Smart</string>
		<string>Philips Semiconductors JCOP41V221</string>
		<string>Philips Semiconductors SmartMX Sample</string>
		<string>PIVKey T800</string>
		<string>Planeta RC700-NFC CCID</string>
		<string>Purism, SPC Librem Key</string>
		<string>Raritan D2CIM-DVUSB VM/CCID</string>
		<string>Regula RFID Reader</string>
		<string>REINER SCT cyberJack go</string>
		<string>REINER SCT tanJack Bluetooth</string>
		<string>REINER SCT tanJack USB</string>
		<string>REINER SCT cyberJack one</string>
		<string>REINER SCT cyberJack RFID basis</string>
		<string>rf IDEAS USB CCID</string>
		<string>Rocketek RT-SCR1</string>
		<string>Route1 MobiKEY Fusion3</string>
		<string>SafeNet eToken 7300</string>
		<string>SafeNet eToken 5100</string>
		<string>SafeNet eToken 5300</string>
		<string>SafeNet eToken 5300 C</string>
		<string>SafeNet eToken 5110+ FIPS</string>
		<string>SafeNet eToken Fusion</string>
		<string>SafeTech SafeTouch</string>
		<string>SAFETRUST SABRE SCR</string>
		<string>SchlumbergerSema SchlumbergerSema Cyberflex Access</string>
		<string>SCM Microsystems Inc. SCR33x USB Smart Card Reader</string>
		<string>SCM Microsystems Inc. SCR 335</string>
		<string>SCM Microsystems Inc. SCR3320 - Smart Card Reader</string>
		<string>SCM Microsystems Inc. SCR3340 - ExpressCard54 Smart Card Reader</string>
		<string>SCM Microsystems Inc. SCR3310 USB Smart Card Reader</string>
		<string>SCM Microsystems Inc. SCR331-DI USB Smart Card Reader</string>
		<string>SCM Microsystems Inc. SDI010 Smart Card Reader</string>
		<string>SCM Microsystems Inc. SDI011 Contactless Reader</string>
		<string>SCM Microsystems Inc. SDI011 Contactless Reader</string>
		<string>SCM Microsystems Inc. SCL010 Contactless Reader</string>
		<string>SCM Microsystems Inc. SCL01x Contactless Reader</string>
		<string>Secure Device Solutions DOMINO-Key TWIN</string>
		<string>SecuTech SecuTech Token</string>
		<string>Sensyl SSC-NFC Reader</string>
		<string>Sensyl SSC-HV Reader</string>
		<string>SIMHUB pcsc reader</string>
		<string>Sitecom Sitecom USB simcard reader MD-010</string>
		<string>Softforum Co., Ltd XecureHSM</string>
		<string>SoloKeys Solo 2</string>
		<string>SONY FeliCa RC-S660/U</string>
		<string>SONY FeliCa RC-S300/S</string>
		<string>SONY FeliCa RC-S300/P</string>
		<string>SPECINFOSYSTEMS DIAMOND token</string>
		<string>SPECINFOSYSTEMS DIAMOND PRO token</string>
		<string>SPECINFOSYSTEMS DIAMOND PLUS token</string>
		<string>SPECINFOSYSTEMS DIAMOND HSM</string>
		<string>SpringCard SpringCore</string>
		<string>SpringCard SpringCore</string>
		<string>SpringCard E518</string>
		<string>SpringCard E518</string>
		<string>SpringCard H518</string>
		<string>SpringCard H518</string>
		<string>SpringCard Puck</string>
		<string>SpringCard Puck</string>
		<string>SpringCard M519</string>
		<string>SpringCard M519</string>
		<string>SpringCard CrazyWriter</string>
		<string>SpringCard CSB6 Basic</string>
		<string>SpringCard CSB6 Secure</string>
		<string>SpringCard CSB6 Ultimate</string>
		<string>SpringCard EasyFinger Standard</string>
		<string>SpringCard EasyFinger Ultimate</string>
		<string>SpringCard Prox'N'Roll</string>
		<string>SpringCard NFC'Roll</string>
		<string>SpringCard H663 Series</string>
		<string>SpringCard H512 Series</string>
		<string>Spyrus Inc Rosetta USB</string>
		<string>Spyrus Inc WorkSafe Pro</string>
		<string>Spyrus Inc WorkSafe Pro</string>
		<string>Spyrus Inc PocketVault P-3X</string>
		<string>Spyrus Inc PocketVault P-3X</string>
		<string>Sunrex HP USB Business Slim Smartcard CCID Keyboard</string>
		<string>Swissbit Secure USB PU-50n SE/PE</string>
		<string>SYNNIX STD200</string>
		<string>SYNNIX CL-2100R</string>
		<string>Sysking MII136C</string>
		<string>sysmocom - s.f.m.c. GmbH sysmoOCTSIM</string>
		<string>Teridian Semiconductors TSC12xxFV.09</string>
		<string>Thales Shield M4 Reader</string>
		<string>Thales RF Reader</string>
		<string>THURSBY SOFTWARE TSS-PK1</string>
		<string>Tianyu Smart Card Reader</string>
		<string>Todos Argos Mini II</string>
		<string>Todos CX00</string>
		<string>TOKEN2 Molto2</string>
		<string>TOKEN2 MFA NFC Reader</string>
		<string>TOPPAN FORMS CO.,LTD TC63CUT021</string>
		<string>ubisys 13.56MHz RFID (CCID)</string>
		<string>udea MILKO V1.</string>
		<string>Unicept GmbH AirID USB</string>
		<string>Unicept GmbH AirID USB Dongle</string>
		<string>Validy TokenA sl vt</string>
		<string>VASCO DP905v1.1</string>
		<string>VASCO DIGIPASS KEY 101</string>
		<string>VASCO DIGIPASS KEY 860</string>
		<string>VASCO DIGIPASS KEY 200</string>
		<string>VASCO DIGIPASS KEY 860</string>
		<string>VASCO DIGIPASS KEY 200</string>
		<string>VASCO DIGIPASS KEY 202</string>
		<string>VASCO DIGIPASS KEY 202</string>
		<string>VASCO DP855</string>
		<string>VASCO DP865</string>
		<string>VASCO DIGIPASS 870</string>
		<string>VASCO DIGIPASS 875</string>
		<string>VASCO DIGIPASS 920</string>
		<string>VMware Virtual USB CCID</string>
		<string>WatchCNPC USB CCID Key</string>
		<string>Watchdata USB Key</string>
		<string>Watchdata USB Key</string>
		<string>Watchdata USB Key</string>
		<string>Watchdata W5181 </string>
		<string>WCMi SD5931</string>
		<string>Winbond CCID SmartCard Controller</string>
		<string>Yubico Yubikey NEO OTP+CCID</string>
		<string>Yubico Yubikey NEO CCID</string>
		<string>Yubico Yubikey NEO U2F+CCID</string>
		<string>Yubico Yubikey NEO OTP+U2F+CCID</string>
		<string>Yubico YubiKey CCID</string>
		<string>Yubico YubiKey OTP+CCID</string>
		<string>Yubico YubiKey FIDO+CCID</string>
		<string>Yubico YubiKey OTP+FIDO+CCID</string>
		<string>ACS ACR122U PICC Interface</string>
		<string>ACS ACR1281U</string>
		<string>ActivCard ActivCard USB Reader V2</string>
		<string>Alcor Micro AU9540</string>
		<string>Athena IDProtect Flash</string>
		<string>ATMEL VaultIC460</string>
		<string>Broadcom Corp 5880</string>
		<string>Broadcom Corp 5880</string>
		<string>Broadcom Corp 5880</string>
		<string>Broadcom Corp 58200</string>
		<string>Broadcom Corp 58200</string>
		<string>Broadcom Corp 58200</string>
		<string>Broadcom Corp 58200</string>
		<string>C3PO LTC3x USB</string>
		<string>Feitian SCR301</string>
		<string>Generic Smart Card Reader Interface</string>
		<string>Hewlett-Packard Company HP USB CCID Smartcard Keyboard</string>
		<string>Hewlett-Packard Company HP USB Smart Card Keyboard</string>
		<string>KEBTechnology KONA USB SmartCard</string>
		<string>KOBIL Systems mIDentity M</string>
		<string>KOBIL Systems mIDentity XL</string>
		<string>O2 Micro Oz776</string>
		<string>O2 Micro Oz776</string>
		<string>Precise Biometrics Precise 250 MC</string>
		<string>Precise Biometrics Precise 200 MC</string>
		<string>RSA RSA SecurID (R) Authenticator</string>
		<string>THRC Smart Card Reader</string>
	</array>

	<key>Copyright</key>
	<string>This driver is protected by terms of the GNU Lesser General Public License version 2.1, or (at your option) any later version.</string>

</dict>
</plist>
