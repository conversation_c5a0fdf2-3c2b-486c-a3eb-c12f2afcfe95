#! /bin/sh

. `dirname $0`/common

no_package()
{
	. /etc/splash.functions
	force_splash_exit
	echo_log -n -d "ERROR: Package $type doesn't exist, aborting bootup. Check your "
	echo_log -n -d "thinstation.conf file and build.conf file to make sure you have the "
	echo_log -n -d "package loaded."
	while true; do
	sleep 10000
	done
}

if ! pkg_initialized $PACKAGE; then
	pkg_set_init_flag $PACKAGE
fi

#Figure out if we have a window manager.
(cat $WKDIR/session) |
while read type title screen position workspace autostart custom icon server options ; do
	if [ -e /etc/cmd/$type.wm ] && [ -z "$WMNAME" ] ; then
	echo "WMSCREEN=$screen" >> $TS_RUNTIME
	echo "WMNAME=$type" >> $TS_RUNTIME
	break
	fi
done

. $TS_RUNTIME

#Now read the sessions for real and create our session script.
(cat $WKDIR/session) |
while read type title screen position workspace autostart custom icon server options user pass_enable gate gate_user; do
	autostart=`make_caps $autostart`
	case $autostart in
	TRUE|ON|ENABLED|ENABLE|YES|POSITIVE|Y)
	if [ -e /etc/init.d/$type ]; then
		# Set screen variables
		export DISPLAY_NUMBER=$screen
		export WMWORKSPACE=$workspace
		export POSITION=$position
		if [ "$server" = "." ] ; then unset server ; fi
		if [ "$options" = "." ] ; then unset options ; fi
		if [ "$user" = "." ] ; then unset user ; fi
		if [ "$pass_enable" = "." ] ; then unset pass_enable ; fi
		if [ "$gate" = "." ] ; then unset gate ; fi
		if [ "$gate_user" = "." ] ; then unset gate_user ; fi

		if [ ! -e /etc/console/$type ]; then
			if [ -n "$WMNAME" ] && [ "$type" != "$WMNAME" ]; then
				echo "pkg auto $type \"$server\" \"$options\" \"$WMWORKSPACE\" \"$user\" \"$pass_enable\" \"$gate\" \"$gate_user\" &" >> $WKDIR/$WMNAME.autostart
	 			echo "sleep .2" >> $WKDIR/$WMNAME.autostart
			elif [ -n "$WMNAME" ] && [ "$type" == "$WMNAME" ]; then
				echo "export DISPLAY_NUMBER=$DISPLAY_NUMBER" >> $WKDIR/windowapps
				echo "exec pkg console $type \"$server\" \"$options\" \"$WMWORKSPACE\" \"$user\" \"$pass_enable\" \"$gate\" \"$gate_user\"" >> $WKDIR/windowapps
			else
				echo "export DISPLAY_NUMBER=$DISPLAY_NUMBER" >> $WKDIR/windowapps
				echo "pkg console $type \"$server\" \"$options\" \"$WMWORKSPACE\" \"$user\" \"$pass_enable\" \"$gate\" \"$gate_user\" &" >> $WKDIR/windowapps
			fi
		else
			if ! grep -e "#!/bin/sh" -q $WKDIR/consoleapps; then
				echo "#!/bin/sh" >> $WKDIR/consoleapps
			fi
			echo "pkg console $type \"$server\" \"$options\"" >> $WKDIR/consoleapps
		fi
	else
		no_package
	fi
	;;
	*)
	touch /tmp/BOOTMENU
	;;
	esac
done
if [ -e $WKDIR/windowapps ]; then
	echo "wait" >> $WKDIR/windowapps
	chmod +x $WKDIR/windowapps
fi
if [ -e $WKDIR/consoleapps ]; then
	chmod +x $WKDIR/consoleapps
fi
