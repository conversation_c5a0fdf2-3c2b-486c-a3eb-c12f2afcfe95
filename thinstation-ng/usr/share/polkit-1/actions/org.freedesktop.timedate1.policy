<?xml version="1.0" encoding="UTF-8"?> <!--*-nxml-*-->
<!DOCTYPE policyconfig PUBLIC "-//freedesktop//DTD PolicyKit Policy Configuration 1.0//EN"
        "https://www.freedesktop.org/standards/PolicyKit/1/policyconfig.dtd">

<!--
  SPDX-License-Identifier: LGPL-2.1-or-later

  This file is part of systemd.

  systemd is free software; you can redistribute it and/or modify it
  under the terms of the GNU Lesser General Public License as published by
  the Free Software Foundation; either version 2.1 of the License, or
  (at your option) any later version.
-->

<policyconfig>

        <vendor>The systemd Project</vendor>
        <vendor_url>https://systemd.io</vendor_url>

        <action id="org.freedesktop.timedate1.set-time">
                <description gettext-domain="systemd">Set system time</description>
                <message gettext-domain="systemd">Authentication is required to set the system time.</message>
                <defaults>
                        <allow_any>auth_admin_keep</allow_any>
                        <allow_inactive>auth_admin_keep</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.imply">org.freedesktop.timedate1.set-timezone org.freedesktop.timedate1.set-ntp</annotate>
        </action>

        <action id="org.freedesktop.timedate1.set-timezone">
                <description gettext-domain="systemd">Set system timezone</description>
                <message gettext-domain="systemd">Authentication is required to set the system timezone.</message>
                <defaults>
                        <allow_any>auth_admin_keep</allow_any>
                        <allow_inactive>auth_admin_keep</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
        </action>

        <action id="org.freedesktop.timedate1.set-local-rtc">
                <description gettext-domain="systemd">Set RTC to local timezone or UTC</description>
                <message gettext-domain="systemd">Authentication is required to control whether the RTC stores the local or UTC time.</message>
                <defaults>
                        <allow_any>auth_admin_keep</allow_any>
                        <allow_inactive>auth_admin_keep</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
        </action>

        <action id="org.freedesktop.timedate1.set-ntp">
                <description gettext-domain="systemd">Turn network time synchronization on or off</description>
                <message gettext-domain="systemd">Authentication is required to control whether network time synchronization shall be enabled.</message>
                <defaults>
                        <allow_any>auth_admin_keep</allow_any>
                        <allow_inactive>auth_admin_keep</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
        </action>

</policyconfig>
