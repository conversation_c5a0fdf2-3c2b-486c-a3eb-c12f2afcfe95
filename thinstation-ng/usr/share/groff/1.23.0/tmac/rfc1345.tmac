.ig
rfc1345.tmac

Adds glyph names formed from
- the mnemonics specified in
  RFC 1345, https://www.rfc-editor.org/rfc/rfc1345.txt,
  <PERSON><PERSON>, June 1992; and
- the digraphs ,. Eu =R =P from <PERSON><PERSON>'s digraph table.
..
.char \[!I] \[u00A1]    \" INVERTED EXCLAMATION MARK
.char \[Ct] \[u00A2]    \" CENT SIGN
.char \[Pd] \[u00A3]    \" POUND SIGN
.char \[Cu] \[u00A4]    \" CURRENCY SIGN
.char \[Ye] \[u00A5]    \" YEN SIGN
.char \[BB] \[u00A6]    \" BROKEN BAR
.char \[SE] \[u00A7]    \" SECTION SIGN
.char \[':] \[u00A8]    \" DIAERESIS
.char \[Co] \[u00A9]    \" COPYRIGHT SIGN
.char \[-a] \[u00AA]    \" FEMININE ORDINAL INDICATOR
.char \[<<] \[u00AB]    \" LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
.char \[NO] \[u00AC]    \" NOT SIGN
.char \[--] \[u00AD]    \" SOFT HYPHEN
.char \[Rg] \[u00AE]    \" REGISTERED SIGN
.char \['m] \[u00AF]    \" MACRON
.char \[DG] \[u00B0]    \" DEGREE SIGN
.char \[+-] \[u00B1]    \" PLUS-MINUS SIGN
.char \[2S] \[u00B2]    \" SUPERSCRIPT TWO
.char \[3S] \[u00B3]    \" SUPERSCRIPT THREE
.char \[''] \[u00B4]    \" ACUTE ACCENT
.char \[My] \[u00B5]    \" MICRO SIGN
.char \[PI] \[u00B6]    \" PILCROW SIGN
.char \[.M] \[u00B7]    \" MIDDLE DOT
.char \[',] \[u00B8]    \" CEDILLA
.char \[1S] \[u00B9]    \" SUPERSCRIPT ONE
.char \[-o] \[u00BA]    \" MASCULINE ORDINAL INDICATOR
.char \[>>] \[u00BB]    \" RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
.char \[14] \[u00BC]    \" VULGAR FRACTION ONE QUARTER
.char \[12] \[u00BD]    \" VULGAR FRACTION ONE HALF
.char \[34] \[u00BE]    \" VULGAR FRACTION THREE QUARTERS
.char \[?I] \[u00BF]    \" INVERTED QUESTION MARK
.char \[A!] \[u00C0]    \" LATIN CAPITAL LETTER A WITH GRAVE
.char \[A'] \[u00C1]    \" LATIN CAPITAL LETTER A WITH ACUTE
.char \[A>] \[u00C2]    \" LATIN CAPITAL LETTER A WITH CIRCUMFLEX
.char \[A?] \[u00C3]    \" LATIN CAPITAL LETTER A WITH TILDE
.char \[A:] \[u00C4]    \" LATIN CAPITAL LETTER A WITH DIAERESIS
.char \[AA] \[u00C5]    \" LATIN CAPITAL LETTER A WITH RING ABOVE
.char \[AE] \[u00C6]    \" LATIN CAPITAL LETTER AE
.char \[C,] \[u00C7]    \" LATIN CAPITAL LETTER C WITH CEDILLA
.char \[E!] \[u00C8]    \" LATIN CAPITAL LETTER E WITH GRAVE
.char \[E'] \[u00C9]    \" LATIN CAPITAL LETTER E WITH ACUTE
.char \[E>] \[u00CA]    \" LATIN CAPITAL LETTER E WITH CIRCUMFLEX
.char \[E:] \[u00CB]    \" LATIN CAPITAL LETTER E WITH DIAERESIS
.char \[I!] \[u00CC]    \" LATIN CAPITAL LETTER I WITH GRAVE
.char \[I'] \[u00CD]    \" LATIN CAPITAL LETTER I WITH ACUTE
.char \[I>] \[u00CE]    \" LATIN CAPITAL LETTER I WITH CIRCUMFLEX
.char \[I:] \[u00CF]    \" LATIN CAPITAL LETTER I WITH DIAERESIS
.char \[D-] \[u00D0]    \" LATIN CAPITAL LETTER ETH (Icelandic)
.char \[N?] \[u00D1]    \" LATIN CAPITAL LETTER N WITH TILDE
.char \[O!] \[u00D2]    \" LATIN CAPITAL LETTER O WITH GRAVE
.char \[O'] \[u00D3]    \" LATIN CAPITAL LETTER O WITH ACUTE
.char \[O>] \[u00D4]    \" LATIN CAPITAL LETTER O WITH CIRCUMFLEX
.char \[O?] \[u00D5]    \" LATIN CAPITAL LETTER O WITH TILDE
.char \[O:] \[u00D6]    \" LATIN CAPITAL LETTER O WITH DIAERESIS
.char \[*X] \[u00D7]    \" MULTIPLICATION SIGN
.char \[O/] \[u00D8]    \" LATIN CAPITAL LETTER O WITH STROKE
.char \[U!] \[u00D9]    \" LATIN CAPITAL LETTER U WITH GRAVE
.char \[U'] \[u00DA]    \" LATIN CAPITAL LETTER U WITH ACUTE
.char \[U>] \[u00DB]    \" LATIN CAPITAL LETTER U WITH CIRCUMFLEX
.char \[U:] \[u00DC]    \" LATIN CAPITAL LETTER U WITH DIAERESIS
.char \[Y'] \[u00DD]    \" LATIN CAPITAL LETTER Y WITH ACUTE
.char \[TH] \[u00DE]    \" LATIN CAPITAL LETTER THORN (Icelandic)
.char \[ss] \[u00DF]    \" LATIN SMALL LETTER SHARP S (German)
.char \[a!] \[u00E0]    \" LATIN SMALL LETTER A WITH GRAVE
.char \[a'] \[u00E1]    \" LATIN SMALL LETTER A WITH ACUTE
.char \[a>] \[u00E2]    \" LATIN SMALL LETTER A WITH CIRCUMFLEX
.char \[a?] \[u00E3]    \" LATIN SMALL LETTER A WITH TILDE
.char \[a:] \[u00E4]    \" LATIN SMALL LETTER A WITH DIAERESIS
.char \[aa] \[u00E5]    \" LATIN SMALL LETTER A WITH RING ABOVE
.char \[ae] \[u00E6]    \" LATIN SMALL LETTER AE
.char \[c,] \[u00E7]    \" LATIN SMALL LETTER C WITH CEDILLA
.char \[e!] \[u00E8]    \" LATIN SMALL LETTER E WITH GRAVE
.char \[e'] \[u00E9]    \" LATIN SMALL LETTER E WITH ACUTE
.char \[e>] \[u00EA]    \" LATIN SMALL LETTER E WITH CIRCUMFLEX
.char \[e:] \[u00EB]    \" LATIN SMALL LETTER E WITH DIAERESIS
.char \[i!] \[u00EC]    \" LATIN SMALL LETTER I WITH GRAVE
.char \[i'] \[u00ED]    \" LATIN SMALL LETTER I WITH ACUTE
.char \[i>] \[u00EE]    \" LATIN SMALL LETTER I WITH CIRCUMFLEX
.char \[i:] \[u00EF]    \" LATIN SMALL LETTER I WITH DIAERESIS
.char \[d-] \[u00F0]    \" LATIN SMALL LETTER ETH (Icelandic)
.char \[n?] \[u00F1]    \" LATIN SMALL LETTER N WITH TILDE
.char \[o!] \[u00F2]    \" LATIN SMALL LETTER O WITH GRAVE
.char \[o'] \[u00F3]    \" LATIN SMALL LETTER O WITH ACUTE
.char \[o>] \[u00F4]    \" LATIN SMALL LETTER O WITH CIRCUMFLEX
.char \[o?] \[u00F5]    \" LATIN SMALL LETTER O WITH TILDE
.char \[o:] \[u00F6]    \" LATIN SMALL LETTER O WITH DIAERESIS
.char \[-:] \[u00F7]    \" DIVISION SIGN
.char \[o/] \[u00F8]    \" LATIN SMALL LETTER O WITH STROKE
.char \[u!] \[u00F9]    \" LATIN SMALL LETTER U WITH GRAVE
.char \[u'] \[u00FA]    \" LATIN SMALL LETTER U WITH ACUTE
.char \[u>] \[u00FB]    \" LATIN SMALL LETTER U WITH CIRCUMFLEX
.char \[u:] \[u00FC]    \" LATIN SMALL LETTER U WITH DIAERESIS
.char \[y'] \[u00FD]    \" LATIN SMALL LETTER Y WITH ACUTE
.char \[th] \[u00FE]    \" LATIN SMALL LETTER THORN (Icelandic)
.char \[y:] \[u00FF]    \" LATIN SMALL LETTER Y WITH DIAERESIS
.char \[A-] \[u0100]    \" LATIN CAPITAL LETTER A WITH MACRON
.char \[a-] \[u0101]    \" LATIN SMALL LETTER A WITH MACRON
.char \[A(] \[u0102]    \" LATIN CAPITAL LETTER A WITH BREVE
.char \[a(] \[u0103]    \" LATIN SMALL LETTER A WITH BREVE
.char \[A;] \[u0104]    \" LATIN CAPITAL LETTER A WITH OGONEK
.char \[a;] \[u0105]    \" LATIN SMALL LETTER A WITH OGONEK
.char \[C'] \[u0106]    \" LATIN CAPITAL LETTER C WITH ACUTE
.char \[c'] \[u0107]    \" LATIN SMALL LETTER C WITH ACUTE
.char \[C>] \[u0108]    \" LATIN CAPITAL LETTER C WITH CIRCUMFLEX
.char \[c>] \[u0109]    \" LATIN SMALL LETTER C WITH CIRCUMFLEX
.char \[C.] \[u010A]    \" LATIN CAPITAL LETTER C WITH DOT ABOVE
.char \[c.] \[u010B]    \" LATIN SMALL LETTER C WITH DOT ABOVE
.char \[C<] \[u010C]    \" LATIN CAPITAL LETTER C WITH CARON
.char \[c<] \[u010D]    \" LATIN SMALL LETTER C WITH CARON
.char \[D<] \[u010E]    \" LATIN CAPITAL LETTER D WITH CARON
.char \[d<] \[u010F]    \" LATIN SMALL LETTER D WITH CARON
.char \[D/] \[u0110]    \" LATIN CAPITAL LETTER D WITH STROKE
.char \[d/] \[u0111]    \" LATIN SMALL LETTER D WITH STROKE
.char \[E-] \[u0112]    \" LATIN CAPITAL LETTER E WITH MACRON
.char \[e-] \[u0113]    \" LATIN SMALL LETTER E WITH MACRON
.char \[E(] \[u0114]    \" LATIN CAPITAL LETTER E WITH BREVE
.char \[e(] \[u0115]    \" LATIN SMALL LETTER E WITH BREVE
.char \[E.] \[u0116]    \" LATIN CAPITAL LETTER E WITH DOT ABOVE
.char \[e.] \[u0117]    \" LATIN SMALL LETTER E WITH DOT ABOVE
.char \[E;] \[u0118]    \" LATIN CAPITAL LETTER E WITH OGONEK
.char \[e;] \[u0119]    \" LATIN SMALL LETTER E WITH OGONEK
.char \[E<] \[u011A]    \" LATIN CAPITAL LETTER E WITH CARON
.char \[e<] \[u011B]    \" LATIN SMALL LETTER E WITH CARON
.char \[G>] \[u011C]    \" LATIN CAPITAL LETTER G WITH CIRCUMFLEX
.char \[g>] \[u011D]    \" LATIN SMALL LETTER G WITH CIRCUMFLEX
.char \[G(] \[u011E]    \" LATIN CAPITAL LETTER G WITH BREVE
.char \[g(] \[u011F]    \" LATIN SMALL LETTER G WITH BREVE
.char \[G.] \[u0120]    \" LATIN CAPITAL LETTER G WITH DOT ABOVE
.char \[g.] \[u0121]    \" LATIN SMALL LETTER G WITH DOT ABOVE
.char \[G,] \[u0122]    \" LATIN CAPITAL LETTER G WITH CEDILLA
.char \[g,] \[u0123]    \" LATIN SMALL LETTER G WITH CEDILLA
.char \[H>] \[u0124]    \" LATIN CAPITAL LETTER H WITH CIRCUMFLEX
.char \[h>] \[u0125]    \" LATIN SMALL LETTER H WITH CIRCUMFLEX
.char \[H/] \[u0126]    \" LATIN CAPITAL LETTER H WITH STROKE
.char \[h/] \[u0127]    \" LATIN SMALL LETTER H WITH STROKE
.char \[I?] \[u0128]    \" LATIN CAPITAL LETTER I WITH TILDE
.char \[i?] \[u0129]    \" LATIN SMALL LETTER I WITH TILDE
.char \[I-] \[u012A]    \" LATIN CAPITAL LETTER I WITH MACRON
.char \[i-] \[u012B]    \" LATIN SMALL LETTER I WITH MACRON
.char \[I(] \[u012C]    \" LATIN CAPITAL LETTER I WITH BREVE
.char \[i(] \[u012D]    \" LATIN SMALL LETTER I WITH BREVE
.char \[I;] \[u012E]    \" LATIN CAPITAL LETTER I WITH OGONEK
.char \[i;] \[u012F]    \" LATIN SMALL LETTER I WITH OGONEK
.char \[I.] \[u0130]    \" LATIN CAPITAL LETTER I WITH DOT ABOVE
.char \[i.] \[u0131]    \" LATIN SMALL LETTER I DOTLESS
.char \[IJ] \[u0132]    \" LATIN CAPITAL LIGATURE IJ
.char \[ij] \[u0133]    \" LATIN SMALL LIGATURE IJ
.char \[J>] \[u0134]    \" LATIN CAPITAL LETTER J WITH CIRCUMFLEX
.char \[j>] \[u0135]    \" LATIN SMALL LETTER J WITH CIRCUMFLEX
.char \[K,] \[u0136]    \" LATIN CAPITAL LETTER K WITH CEDILLA
.char \[k,] \[u0137]    \" LATIN SMALL LETTER K WITH CEDILLA
.char \[kk] \[u0138]    \" LATIN SMALL LETTER KRA (Greenlandic)
.char \[L'] \[u0139]    \" LATIN CAPITAL LETTER L WITH ACUTE
.char \[l'] \[u013A]    \" LATIN SMALL LETTER L WITH ACUTE
.char \[L,] \[u013B]    \" LATIN CAPITAL LETTER L WITH CEDILLA
.char \[l,] \[u013C]    \" LATIN SMALL LETTER L WITH CEDILLA
.char \[L<] \[u013D]    \" LATIN CAPITAL LETTER L WITH CARON
.char \[l<] \[u013E]    \" LATIN SMALL LETTER L WITH CARON
.char \[L.] \[u013F]    \" LATIN CAPITAL LETTER L WITH MIDDLE DOT
.char \[l.] \[u0140]    \" LATIN SMALL LETTER L WITH MIDDLE DOT
.char \[L/] \[u0141]    \" LATIN CAPITAL LETTER L WITH STROKE
.char \[l/] \[u0142]    \" LATIN SMALL LETTER L WITH STROKE
.char \[N'] \[u0143]    \" LATIN CAPITAL LETTER N WITH ACUTE
.char \[n'] \[u0144]    \" LATIN SMALL LETTER N WITH ACUTE
.char \[N,] \[u0145]    \" LATIN CAPITAL LETTER N WITH CEDILLA
.char \[n,] \[u0146]    \" LATIN SMALL LETTER N WITH CEDILLA
.char \[N<] \[u0147]    \" LATIN CAPITAL LETTER N WITH CARON
.char \[n<] \[u0148]    \" LATIN SMALL LETTER N WITH CARON
.char \['n] \[u0149]    \" LATIN SMALL LETTER N PRECEDED BY APOSTROPHE
.char \[NG] \[u014A]    \" LATIN CAPITAL LETTER ENG (Lappish)
.char \[ng] \[u014B]    \" LATIN SMALL LETTER ENG (Lappish)
.char \[O-] \[u014C]    \" LATIN CAPITAL LETTER O WITH MACRON
.char \[o-] \[u014D]    \" LATIN SMALL LETTER O WITH MACRON
.char \[O(] \[u014E]    \" LATIN CAPITAL LETTER O WITH BREVE
.char \[o(] \[u014F]    \" LATIN SMALL LETTER O WITH BREVE
.char \[O"] \[u0150]    \" LATIN CAPITAL LETTER O WITH DOUBLE ACUTE
.char \[o"] \[u0151]    \" LATIN SMALL LETTER O WITH DOUBLE ACUTE
.char \[OE] \[u0152]    \" LATIN CAPITAL LIGATURE OE
.char \[oe] \[u0153]    \" LATIN SMALL LIGATURE OE
.char \[R'] \[u0154]    \" LATIN CAPITAL LETTER R WITH ACUTE
.char \[r'] \[u0155]    \" LATIN SMALL LETTER R WITH ACUTE
.char \[R,] \[u0156]    \" LATIN CAPITAL LETTER R WITH CEDILLA
.char \[r,] \[u0157]    \" LATIN SMALL LETTER R WITH CEDILLA
.char \[R<] \[u0158]    \" LATIN CAPITAL LETTER R WITH CARON
.char \[r<] \[u0159]    \" LATIN SMALL LETTER R WITH CARON
.char \[S'] \[u015A]    \" LATIN CAPITAL LETTER S WITH ACUTE
.char \[s'] \[u015B]    \" LATIN SMALL LETTER S WITH ACUTE
.char \[S>] \[u015C]    \" LATIN CAPITAL LETTER S WITH CIRCUMFLEX
.char \[s>] \[u015D]    \" LATIN SMALL LETTER S WITH CIRCUMFLEX
.char \[S,] \[u015E]    \" LATIN CAPITAL LETTER S WITH CEDILLA
.char \[s,] \[u015F]    \" LATIN SMALL LETTER S WITH CEDILLA
.char \[S<] \[u0160]    \" LATIN CAPITAL LETTER S WITH CARON
.char \[s<] \[u0161]    \" LATIN SMALL LETTER S WITH CARON
.char \[T,] \[u0162]    \" LATIN CAPITAL LETTER T WITH CEDILLA
.char \[t,] \[u0163]    \" LATIN SMALL LETTER T WITH CEDILLA
.char \[T<] \[u0164]    \" LATIN CAPITAL LETTER T WITH CARON
.char \[t<] \[u0165]    \" LATIN SMALL LETTER T WITH CARON
.char \[T/] \[u0166]    \" LATIN CAPITAL LETTER T WITH STROKE
.char \[t/] \[u0167]    \" LATIN SMALL LETTER T WITH STROKE
.char \[U?] \[u0168]    \" LATIN CAPITAL LETTER U WITH TILDE
.char \[u?] \[u0169]    \" LATIN SMALL LETTER U WITH TILDE
.char \[U-] \[u016A]    \" LATIN CAPITAL LETTER U WITH MACRON
.char \[u-] \[u016B]    \" LATIN SMALL LETTER U WITH MACRON
.char \[U(] \[u016C]    \" LATIN CAPITAL LETTER U WITH BREVE
.char \[u(] \[u016D]    \" LATIN SMALL LETTER U WITH BREVE
.char \[U0] \[u016E]    \" LATIN CAPITAL LETTER U WITH RING ABOVE
.char \[u0] \[u016F]    \" LATIN SMALL LETTER U WITH RING ABOVE
.char \[U"] \[u0170]    \" LATIN CAPITAL LETTER U WITH DOUBLE ACUTE
.char \[u"] \[u0171]    \" LATIN SMALL LETTER U WITH DOUBLE ACUTE
.char \[U;] \[u0172]    \" LATIN CAPITAL LETTER U WITH OGONEK
.char \[u;] \[u0173]    \" LATIN SMALL LETTER U WITH OGONEK
.char \[W>] \[u0174]    \" LATIN CAPITAL LETTER W WITH CIRCUMFLEX
.char \[w>] \[u0175]    \" LATIN SMALL LETTER W WITH CIRCUMFLEX
.char \[Y>] \[u0176]    \" LATIN CAPITAL LETTER Y WITH CIRCUMFLEX
.char \[y>] \[u0177]    \" LATIN SMALL LETTER Y WITH CIRCUMFLEX
.char \[Y:] \[u0178]    \" LATIN CAPITAL LETTER Y WITH DIAERESIS
.char \[Z'] \[u0179]    \" LATIN CAPITAL LETTER Z WITH ACUTE
.char \[z'] \[u017A]    \" LATIN SMALL LETTER Z WITH ACUTE
.char \[Z.] \[u017B]    \" LATIN CAPITAL LETTER Z WITH DOT ABOVE
.char \[z.] \[u017C]    \" LATIN SMALL LETTER Z WITH DOT ABOVE
.char \[Z<] \[u017D]    \" LATIN CAPITAL LETTER Z WITH CARON
.char \[z<] \[u017E]    \" LATIN SMALL LETTER Z WITH CARON
.char \[O9] \[u01A0]    \" LATIN CAPITAL LETTER O WITH HORN
.char \[o9] \[u01A1]    \" LATIN SMALL LETTER O WITH HORN
.char \[OI] \[u01A2]    \" LATIN CAPITAL LETTER OI
.char \[oi] \[u01A3]    \" LATIN SMALL LETTER OI
.char \[yr] \[u01A6]    \" LATIN LETTER YR
.char \[U9] \[u01AF]    \" LATIN CAPITAL LETTER U WITH HORN
.char \[u9] \[u01B0]    \" LATIN SMALL LETTER U WITH HORN
.char \[Z/] \[u01B5]    \" LATIN CAPITAL LETTER Z WITH STROKE
.char \[z/] \[u01B6]    \" LATIN SMALL LETTER Z WITH STROKE
.char \[ED] \[u01B7]    \" LATIN CAPITAL LETTER EZH
.char \[A<] \[u01CD]    \" LATIN CAPITAL LETTER A WITH CARON
.char \[a<] \[u01CE]    \" LATIN SMALL LETTER A WITH CARON
.char \[I<] \[u01CF]    \" LATIN CAPITAL LETTER I WITH CARON
.char \[i<] \[u01D0]    \" LATIN SMALL LETTER I WITH CARON
.char \[O<] \[u01D1]    \" LATIN CAPITAL LETTER O WITH CARON
.char \[o<] \[u01D2]    \" LATIN SMALL LETTER O WITH CARON
.char \[U<] \[u01D3]    \" LATIN CAPITAL LETTER U WITH CARON
.char \[u<] \[u01D4]    \" LATIN SMALL LETTER U WITH CARON
.char \[U:-] \[u01D5]    \" LATIN CAPITAL LETTER U WITH DIAERESIS AND MACRON
.char \[u:-] \[u01D6]    \" LATIN SMALL LETTER U WITH DIAERESIS AND MACRON
.char \[U:'] \[u01D7]    \" LATIN CAPITAL LETTER U WITH DIAERESIS AND ACUTE
.char \[u:'] \[u01D8]    \" LATIN SMALL LETTER U WITH DIAERESIS AND ACUTE
.char \[U:<] \[u01D9]    \" LATIN CAPITAL LETTER U WITH DIAERESIS AND CARON
.char \[u:<] \[u01DA]    \" LATIN SMALL LETTER U WITH DIAERESIS AND CARON
.char \[U:!] \[u01DB]    \" LATIN CAPITAL LETTER U WITH DIAERESIS AND GRAVE
.char \[u:!] \[u01DC]    \" LATIN SMALL LETTER U WITH DIAERESIS AND GRAVE
.char \[A1] \[u01DE]    \" LATIN CAPITAL LETTER A WITH DIAERESIS AND MACRON
.char \[a1] \[u01DF]    \" LATIN SMALL LETTER A WITH DIAERESIS AND MACRON
.char \[A7] \[u01E0]    \" LATIN CAPITAL LETTER A WITH DOT ABOVE AND MACRON
.char \[a7] \[u01E1]    \" LATIN SMALL LETTER A WITH DOT ABOVE AND MACRON
.char \[A3] \[u01E2]    \" LATIN CAPITAL LETTER AE WITH MACRON
.char \[a3] \[u01E3]    \" LATIN SMALL LETTER AE WITH MACRON
.char \[G/] \[u01E4]    \" LATIN CAPITAL LETTER G WITH STROKE
.char \[g/] \[u01E5]    \" LATIN SMALL LETTER G WITH STROKE
.char \[G<] \[u01E6]    \" LATIN CAPITAL LETTER G WITH CARON
.char \[g<] \[u01E7]    \" LATIN SMALL LETTER G WITH CARON
.char \[K<] \[u01E8]    \" LATIN CAPITAL LETTER K WITH CARON
.char \[k<] \[u01E9]    \" LATIN SMALL LETTER K WITH CARON
.char \[O;] \[u01EA]    \" LATIN CAPITAL LETTER O WITH OGONEK
.char \[o;] \[u01EB]    \" LATIN SMALL LETTER O WITH OGONEK
.char \[O1] \[u01EC]    \" LATIN CAPITAL LETTER O WITH OGONEK AND MACRON
.char \[o1] \[u01ED]    \" LATIN SMALL LETTER O WITH OGONEK AND MACRON
.char \[EZ] \[u01EE]    \" LATIN CAPITAL LETTER EZH WITH CARON
.char \[ez] \[u01EF]    \" LATIN SMALL LETTER EZH WITH CARON
.char \[j<] \[u01F0]    \" LATIN SMALL LETTER J WITH CARON
.char \[G'] \[u01F4]    \" LATIN CAPITAL LETTER G WITH ACUTE
.char \[g'] \[u01F5]    \" LATIN SMALL LETTER G WITH ACUTE
.char \[AA'] \[u01FA]    \" LATIN CAPITAL LETTER A WITH RING ABOVE AND ACUTE
.char \[aa'] \[u01FB]    \" LATIN SMALL LETTER A WITH RING ABOVE AND ACUTE
.char \[AE'] \[u01FC]    \" LATIN CAPITAL LETTER AE WITH ACUTE
.char \[ae'] \[u01FD]    \" LATIN SMALL LETTER AE WITH ACUTE
.char \[O/'] \[u01FE]    \" LATIN CAPITAL LETTER O WITH STROKE AND ACUTE
.char \[o/'] \[u01FF]    \" LATIN SMALL LETTER O WITH STROKE AND ACUTE
.char \[;S] \[u02BF]    \" MODIFIER LETTER LEFT HALF RING
.char \['<] \[u02C7]    \" CARON
.char \['(] \[u02D8]    \" BREVE
.char \['.] \[u02D9]    \" DOT ABOVE
.char \['0] \[u02DA]    \" RING ABOVE
.char \[';] \[u02DB]    \" OGONEK
.char \['"] \[u02DD]    \" DOUBLE ACUTE ACCENT
.char \[A%] \[u0386]    \" GREEK CAPITAL LETTER ALPHA WITH ACUTE
.char \[E%] \[u0388]    \" GREEK CAPITAL LETTER EPSILON WITH ACUTE
.char \[Y%] \[u0389]    \" GREEK CAPITAL LETTER ETA WITH ACUTE
.char \[I%] \[u038A]    \" GREEK CAPITAL LETTER IOTA WITH ACUTE
.char \[O%] \[u038C]    \" GREEK CAPITAL LETTER OMICRON WITH ACUTE
.char \[U%] \[u038E]    \" GREEK CAPITAL LETTER UPSILON WITH ACUTE
.char \[W%] \[u038F]    \" GREEK CAPITAL LETTER OMEGA WITH ACUTE
.char \[i3] \[u0390]    \" GREEK SMALL LETTER IOTA WITH ACUTE AND DIAERESIS
.char \[A*] \[u0391]    \" GREEK CAPITAL LETTER ALPHA
.char \[B*] \[u0392]    \" GREEK CAPITAL LETTER BETA
.char \[G*] \[u0393]    \" GREEK CAPITAL LETTER GAMMA
.char \[D*] \[u0394]    \" GREEK CAPITAL LETTER DELTA
.char \[E*] \[u0395]    \" GREEK CAPITAL LETTER EPSILON
.char \[Z*] \[u0396]    \" GREEK CAPITAL LETTER ZETA
.char \[Y*] \[u0397]    \" GREEK CAPITAL LETTER ETA
.char \[H*] \[u0398]    \" GREEK CAPITAL LETTER THETA
.char \[I*] \[u0399]    \" GREEK CAPITAL LETTER IOTA
.char \[K*] \[u039A]    \" GREEK CAPITAL LETTER KAPPA
.char \[L*] \[u039B]    \" GREEK CAPITAL LETTER LAMDA
.char \[M*] \[u039C]    \" GREEK CAPITAL LETTER MU
.char \[N*] \[u039D]    \" GREEK CAPITAL LETTER NU
.char \[C*] \[u039E]    \" GREEK CAPITAL LETTER XI
.char \[O*] \[u039F]    \" GREEK CAPITAL LETTER OMICRON
.char \[P*] \[u03A0]    \" GREEK CAPITAL LETTER PI
.char \[R*] \[u03A1]    \" GREEK CAPITAL LETTER RHO
.char \[S*] \[u03A3]    \" GREEK CAPITAL LETTER SIGMA
.char \[T*] \[u03A4]    \" GREEK CAPITAL LETTER TAU
.char \[U*] \[u03A5]    \" GREEK CAPITAL LETTER UPSILON
.char \[F*] \[u03A6]    \" GREEK CAPITAL LETTER PHI
.char \[X*] \[u03A7]    \" GREEK CAPITAL LETTER CHI
.char \[Q*] \[u03A8]    \" GREEK CAPITAL LETTER PSI
.char \[W*] \[u03A9]    \" GREEK CAPITAL LETTER OMEGA
.char \[J*] \[u03AA]    \" GREEK CAPITAL LETTER IOTA WITH DIAERESIS
.char \[V*] \[u03AB]    \" GREEK CAPITAL LETTER UPSILON WITH DIAERESIS
.char \[a%] \[u03AC]    \" GREEK SMALL LETTER ALPHA WITH ACUTE
.char \[e%] \[u03AD]    \" GREEK SMALL LETTER EPSILON WITH ACUTE
.char \[y%] \[u03AE]    \" GREEK SMALL LETTER ETA WITH ACUTE
.char \[i%] \[u03AF]    \" GREEK SMALL LETTER IOTA WITH ACUTE
.char \[u3] \[u03B0]    \" GREEK SMALL LETTER UPSILON WITH ACUTE AND DIAERESIS
.char \[a*] \[u03B1]    \" GREEK SMALL LETTER ALPHA
.char \[b*] \[u03B2]    \" GREEK SMALL LETTER BETA
.char \[g*] \[u03B3]    \" GREEK SMALL LETTER GAMMA
.char \[d*] \[u03B4]    \" GREEK SMALL LETTER DELTA
.char \[e*] \[u03B5]    \" GREEK SMALL LETTER EPSILON
.char \[z*] \[u03B6]    \" GREEK SMALL LETTER ZETA
.char \[y*] \[u03B7]    \" GREEK SMALL LETTER ETA
.char \[h*] \[u03B8]    \" GREEK SMALL LETTER THETA
.char \[i*] \[u03B9]    \" GREEK SMALL LETTER IOTA
.char \[k*] \[u03BA]    \" GREEK SMALL LETTER KAPPA
.char \[l*] \[u03BB]    \" GREEK SMALL LETTER LAMDA
.char \[m*] \[u03BC]    \" GREEK SMALL LETTER MU
.char \[n*] \[u03BD]    \" GREEK SMALL LETTER NU
.char \[c*] \[u03BE]    \" GREEK SMALL LETTER XI
.char \[o*] \[u03BF]    \" GREEK SMALL LETTER OMICRON
.char \[p*] \[u03C0]    \" GREEK SMALL LETTER PI
.char \[r*] \[u03C1]    \" GREEK SMALL LETTER RHO
.char \[*s] \[u03C2]    \" GREEK SMALL LETTER FINAL SIGMA
.char \[s*] \[u03C3]    \" GREEK SMALL LETTER SIGMA
.char \[t*] \[u03C4]    \" GREEK SMALL LETTER TAU
.char \[u*] \[u03C5]    \" GREEK SMALL LETTER UPSILON
.char \[f*] \[u03C6]    \" GREEK SMALL LETTER PHI
.char \[x*] \[u03C7]    \" GREEK SMALL LETTER CHI
.char \[q*] \[u03C8]    \" GREEK SMALL LETTER PSI
.char \[w*] \[u03C9]    \" GREEK SMALL LETTER OMEGA
.char \[j*] \[u03CA]    \" GREEK SMALL LETTER IOTA WITH DIAERESIS
.char \[v*] \[u03CB]    \" GREEK SMALL LETTER UPSILON WITH DIAERESIS
.char \[o%] \[u03CC]    \" GREEK SMALL LETTER OMICRON WITH ACUTE
.char \[u%] \[u03CD]    \" GREEK SMALL LETTER UPSILON WITH ACUTE
.char \[w%] \[u03CE]    \" GREEK SMALL LETTER OMEGA WITH ACUTE
.char \['G] \[u03D8]    \" GREEK NUMERAL SIGN
.char \[,G] \[u03D9]    \" GREEK LOWER NUMERAL SIGN
.char \[T3] \[u03DA]    \" GREEK CAPITAL LETTER STIGMA
.char \[t3] \[u03DB]    \" GREEK SMALL LETTER STIGMA
.char \[M3] \[u03DC]    \" GREEK CAPITAL LETTER DIGAMMA
.char \[m3] \[u03DD]    \" GREEK SMALL LETTER DIGAMMA
.char \[K3] \[u03DE]    \" GREEK CAPITAL LETTER KOPPA
.char \[k3] \[u03DF]    \" GREEK SMALL LETTER KOPPA
.char \[P3] \[u03E0]    \" GREEK CAPITAL LETTER SAMPI
.char \[p3] \[u03E1]    \" GREEK SMALL LETTER SAMPI
.char \['%] \[u03F4]    \" ACUTE ACCENT AND DIAERESIS (Tonos and Dialytika)
.char \[j3] \[u03F5]    \" GREEK IOTA BELOW
.char \[IO] \[u0401]    \" CYRILLIC CAPITAL LETTER IO
.char \[D%] \[u0402]    \" CYRILLIC CAPITAL LETTER DJE (Serbocroatian)
.char \[G%] \[u0403]    \" CYRILLIC CAPITAL LETTER GJE (Macedonian)
.char \[IE] \[u0404]    \" CYRILLIC CAPITAL LETTER UKRAINIAN IE
.char \[DS] \[u0405]    \" CYRILLIC CAPITAL LETTER DZE (Macedonian)
.char \[II] \[u0406]    \" CYRILLIC CAPITAL LETTER BYELORUSSIAN-UKRAINIAN I
.char \[YI] \[u0407]    \" CYRILLIC CAPITAL LETTER YI (Ukrainian)
.char \[J%] \[u0408]    \" CYRILLIC CAPITAL LETTER JE
.char \[LJ] \[u0409]    \" CYRILLIC CAPITAL LETTER LJE
.char \[NJ] \[u040A]    \" CYRILLIC CAPITAL LETTER NJE
.char \[Ts] \[u040B]    \" CYRILLIC CAPITAL LETTER TSHE (Serbocroatian)
.char \[KJ] \[u040C]    \" CYRILLIC CAPITAL LETTER KJE (Macedonian)
.char \[V%] \[u040E]    \" CYRILLIC CAPITAL LETTER SHORT U (Byelorussian)
.char \[DZ] \[u040F]    \" CYRILLIC CAPITAL LETTER DZHE
.char \[A=] \[u0410]    \" CYRILLIC CAPITAL LETTER A
.char \[B=] \[u0411]    \" CYRILLIC CAPITAL LETTER BE
.char \[V=] \[u0412]    \" CYRILLIC CAPITAL LETTER VE
.char \[G=] \[u0413]    \" CYRILLIC CAPITAL LETTER GHE
.char \[D=] \[u0414]    \" CYRILLIC CAPITAL LETTER DE
.char \[E=] \[u0415]    \" CYRILLIC CAPITAL LETTER IE
.char \[Z%] \[u0416]    \" CYRILLIC CAPITAL LETTER ZHE
.char \[Z=] \[u0417]    \" CYRILLIC CAPITAL LETTER ZE
.char \[I=] \[u0418]    \" CYRILLIC CAPITAL LETTER I
.char \[J=] \[u0419]    \" CYRILLIC CAPITAL LETTER SHORT I
.char \[K=] \[u041A]    \" CYRILLIC CAPITAL LETTER KA
.char \[L=] \[u041B]    \" CYRILLIC CAPITAL LETTER EL
.char \[M=] \[u041C]    \" CYRILLIC CAPITAL LETTER EM
.char \[N=] \[u041D]    \" CYRILLIC CAPITAL LETTER EN
.char \[O=] \[u041E]    \" CYRILLIC CAPITAL LETTER O
.char \[P=] \[u041F]    \" CYRILLIC CAPITAL LETTER PE
.char \[R=] \[u0420]    \" CYRILLIC CAPITAL LETTER ER
.char \[S=] \[u0421]    \" CYRILLIC CAPITAL LETTER ES
.char \[T=] \[u0422]    \" CYRILLIC CAPITAL LETTER TE
.char \[U=] \[u0423]    \" CYRILLIC CAPITAL LETTER U
.char \[F=] \[u0424]    \" CYRILLIC CAPITAL LETTER EF
.char \[H=] \[u0425]    \" CYRILLIC CAPITAL LETTER HA
.char \[C=] \[u0426]    \" CYRILLIC CAPITAL LETTER TSE
.char \[C%] \[u0427]    \" CYRILLIC CAPITAL LETTER CHE
.char \[S%] \[u0428]    \" CYRILLIC CAPITAL LETTER SHA
.char \[Sc] \[u0429]    \" CYRILLIC CAPITAL LETTER SHCHA
.char \[="] \[u042A]    \" CYRILLIC CAPITAL LETTER HARD SIGN
.char \[Y=] \[u042B]    \" CYRILLIC CAPITAL LETTER YERU
.char \[%"] \[u042C]    \" CYRILLIC CAPITAL LETTER SOFT SIGN
.char \[JE] \[u042D]    \" CYRILLIC CAPITAL LETTER E
.char \[JU] \[u042E]    \" CYRILLIC CAPITAL LETTER YU
.char \[JA] \[u042F]    \" CYRILLIC CAPITAL LETTER YA
.char \[a=] \[u0430]    \" CYRILLIC SMALL LETTER A
.char \[b=] \[u0431]    \" CYRILLIC SMALL LETTER BE
.char \[v=] \[u0432]    \" CYRILLIC SMALL LETTER VE
.char \[g=] \[u0433]    \" CYRILLIC SMALL LETTER GHE
.char \[d=] \[u0434]    \" CYRILLIC SMALL LETTER DE
.char \[e=] \[u0435]    \" CYRILLIC SMALL LETTER IE
.char \[z%] \[u0436]    \" CYRILLIC SMALL LETTER ZHE
.char \[z=] \[u0437]    \" CYRILLIC SMALL LETTER ZE
.char \[i=] \[u0438]    \" CYRILLIC SMALL LETTER I
.char \[j=] \[u0439]    \" CYRILLIC SMALL LETTER SHORT I
.char \[k=] \[u043A]    \" CYRILLIC SMALL LETTER KA
.char \[l=] \[u043B]    \" CYRILLIC SMALL LETTER EL
.char \[m=] \[u043C]    \" CYRILLIC SMALL LETTER EM
.char \[n=] \[u043D]    \" CYRILLIC SMALL LETTER EN
.char \[o=] \[u043E]    \" CYRILLIC SMALL LETTER O
.char \[p=] \[u043F]    \" CYRILLIC SMALL LETTER PE
.char \[r=] \[u0440]    \" CYRILLIC SMALL LETTER ER
.char \[s=] \[u0441]    \" CYRILLIC SMALL LETTER ES
.char \[t=] \[u0442]    \" CYRILLIC SMALL LETTER TE
.char \[u=] \[u0443]    \" CYRILLIC SMALL LETTER U
.char \[f=] \[u0444]    \" CYRILLIC SMALL LETTER EF
.char \[h=] \[u0445]    \" CYRILLIC SMALL LETTER HA
.char \[c=] \[u0446]    \" CYRILLIC SMALL LETTER TSE
.char \[c%] \[u0447]    \" CYRILLIC SMALL LETTER CHE
.char \[s%] \[u0448]    \" CYRILLIC SMALL LETTER SHA
.char \[sc] \[u0449]    \" CYRILLIC SMALL LETTER SHCHA
.char \[='] \[u044A]    \" CYRILLIC SMALL LETTER HARD SIGN
.char \[y=] \[u044B]    \" CYRILLIC SMALL LETTER YERU
.char \[%'] \[u044C]    \" CYRILLIC SMALL LETTER SOFT SIGN
.char \[je] \[u044D]    \" CYRILLIC SMALL LETTER E
.char \[ju] \[u044E]    \" CYRILLIC SMALL LETTER YU
.char \[ja] \[u044F]    \" CYRILLIC SMALL LETTER YA
.char \[io] \[u0451]    \" CYRILLIC SMALL LETTER IO
.char \[d%] \[u0452]    \" CYRILLIC SMALL LETTER DJE (Serbocroatian)
.char \[g%] \[u0453]    \" CYRILLIC SMALL LETTER GJE (Macedonian)
.char \[ie] \[u0454]    \" CYRILLIC SMALL LETTER UKRAINIAN IE
.char \[ds] \[u0455]    \" CYRILLIC SMALL LETTER DZE (Macedonian)
.char \[ii] \[u0456]    \" CYRILLIC SMALL LETTER BYELORUSSIAN-UKRAINIAN I
.char \[yi] \[u0457]    \" CYRILLIC SMALL LETTER YI (Ukrainian)
.char \[j%] \[u0458]    \" CYRILLIC SMALL LETTER JE
.char \[lj] \[u0459]    \" CYRILLIC SMALL LETTER LJE
.char \[nj] \[u045A]    \" CYRILLIC SMALL LETTER NJE
.char \[ts] \[u045B]    \" CYRILLIC SMALL LETTER TSHE (Serbocroatian)
.char \[kj] \[u045C]    \" CYRILLIC SMALL LETTER KJE (Macedonian)
.char \[v%] \[u045E]    \" CYRILLIC SMALL LETTER SHORT U (Byelorussian)
.char \[dz] \[u045F]    \" CYRILLIC SMALL LETTER DZHE
.char \[Y3] \[u0462]    \" CYRILLIC CAPITAL LETTER YAT
.char \[y3] \[u0463]    \" CYRILLIC SMALL LETTER YAT
.char \[O3] \[u046A]    \" CYRILLIC CAPITAL LETTER BIG YUS
.char \[o3] \[u046B]    \" CYRILLIC SMALL LETTER BIG YUS
.char \[F3] \[u0472]    \" CYRILLIC CAPITAL LETTER FITA
.char \[f3] \[u0473]    \" CYRILLIC SMALL LETTER FITA
.char \[V3] \[u0474]    \" CYRILLIC CAPITAL LETTER IZHITSA
.char \[v3] \[u0475]    \" CYRILLIC SMALL LETTER IZHITSA
.char \[C3] \[u0480]    \" CYRILLIC CAPITAL LETTER KOPPA
.char \[c3] \[u0481]    \" CYRILLIC SMALL LETTER KOPPA
.char \[G3] \[u0490]    \" CYRILLIC CAPITAL LETTER GHE WITH UPTURN
.char \[g3] \[u0491]    \" CYRILLIC SMALL LETTER GHE WITH UPTURN
.char \[A+] \[u05D0]    \" HEBREW LETTER ALEF
.char \[B+] \[u05D1]    \" HEBREW LETTER BET
.char \[G+] \[u05D2]    \" HEBREW LETTER GIMEL
.char \[D+] \[u05D3]    \" HEBREW LETTER DALET
.char \[H+] \[u05D4]    \" HEBREW LETTER HE
.char \[W+] \[u05D5]    \" HEBREW LETTER VAV
.char \[Z+] \[u05D6]    \" HEBREW LETTER ZAYIN
.char \[X+] \[u05D7]    \" HEBREW LETTER HET
.char \[Tj] \[u05D8]    \" HEBREW LETTER TET
.char \[J+] \[u05D9]    \" HEBREW LETTER YOD
.char \[K%] \[u05DA]    \" HEBREW LETTER FINAL KAF
.char \[K+] \[u05DB]    \" HEBREW LETTER KAF
.char \[L+] \[u05DC]    \" HEBREW LETTER LAMED
.char \[M%] \[u05DD]    \" HEBREW LETTER FINAL MEM
.char \[M+] \[u05DE]    \" HEBREW LETTER MEM
.char \[N%] \[u05DF]    \" HEBREW LETTER FINAL NUN
.char \[N+] \[u05E0]    \" HEBREW LETTER NUN
.char \[S+] \[u05E1]    \" HEBREW LETTER SAMEKH
.char \[E+] \[u05E2]    \" HEBREW LETTER AYIN
.char \[P%] \[u05E3]    \" HEBREW LETTER FINAL PE
.char \[P+] \[u05E4]    \" HEBREW LETTER PE
.char \[Zj] \[u05E5]    \" HEBREW LETTER FINAL TSADI
.char \[ZJ] \[u05E6]    \" HEBREW LETTER TSADI
.char \[Q+] \[u05E7]    \" HEBREW LETTER QOF
.char \[R+] \[u05E8]    \" HEBREW LETTER RESH
.char \[Sh] \[u05E9]    \" HEBREW LETTER SHIN
.char \[T+] \[u05EA]    \" HEBREW LETTER TAV
.char \[,+] \[u060C]    \" ARABIC COMMA
.char \[;+] \[u061B]    \" ARABIC SEMICOLON
.char \[?+] \[u061F]    \" ARABIC QUESTION MARK
.char \[H'] \[u0621]    \" ARABIC LETTER HAMZA
.char \[aM] \[u0622]    \" ARABIC LETTER ALEF WITH MADDA ABOVE
.char \[aH] \[u0623]    \" ARABIC LETTER ALEF WITH HAMZA ABOVE
.char \[wH] \[u0624]    \" ARABIC LETTER WAW WITH HAMZA ABOVE
.char \[ah] \[u0625]    \" ARABIC LETTER ALEF WITH HAMZA BELOW
.char \[yH] \[u0626]    \" ARABIC LETTER YEH WITH HAMZA ABOVE
.char \[a+] \[u0627]    \" ARABIC LETTER ALEF
.char \[b+] \[u0628]    \" ARABIC LETTER BEH
.char \[tm] \[u0629]    \" ARABIC LETTER TEH MARBUTA
.char \[t+] \[u062A]    \" ARABIC LETTER TEH
.char \[tk] \[u062B]    \" ARABIC LETTER THEH
.char \[g+] \[u062C]    \" ARABIC LETTER JEEM
.char \[hk] \[u062D]    \" ARABIC LETTER HAH
.char \[x+] \[u062E]    \" ARABIC LETTER KHAH
.char \[d+] \[u062F]    \" ARABIC LETTER DAL
.char \[dk] \[u0630]    \" ARABIC LETTER THAL
.char \[r+] \[u0631]    \" ARABIC LETTER REH
.char \[z+] \[u0632]    \" ARABIC LETTER ZAIN
.char \[s+] \[u0633]    \" ARABIC LETTER SEEN
.char \[sn] \[u0634]    \" ARABIC LETTER SHEEN
.char \[c+] \[u0635]    \" ARABIC LETTER SAD
.char \[dd] \[u0636]    \" ARABIC LETTER DAD
.char \[tj] \[u0637]    \" ARABIC LETTER TAH
.char \[zH] \[u0638]    \" ARABIC LETTER ZAH
.char \[e+] \[u0639]    \" ARABIC LETTER AIN
.char \[i+] \[u063A]    \" ARABIC LETTER GHAIN
.char \[++] \[u0640]    \" ARABIC TATWEEL
.char \[f+] \[u0641]    \" ARABIC LETTER FEH
.char \[q+] \[u0642]    \" ARABIC LETTER QAF
.char \[k+] \[u0643]    \" ARABIC LETTER KAF
.char \[l+] \[u0644]    \" ARABIC LETTER LAM
.char \[m+] \[u0645]    \" ARABIC LETTER MEEM
.char \[n+] \[u0646]    \" ARABIC LETTER NOON
.char \[h+] \[u0647]    \" ARABIC LETTER HEH
.char \[w+] \[u0648]    \" ARABIC LETTER WAW
.char \[j+] \[u0649]    \" ARABIC LETTER ALEF MAKSURA
.char \[y+] \[u064A]    \" ARABIC LETTER YEH
.char \[:+] \[u064B]    \" ARABIC FATHATAN
.char \["+] \[u064C]    \" ARABIC DAMMATAN
.char \[=+] \[u064D]    \" ARABIC KASRATAN
.char \[/+] \[u064E]    \" ARABIC FATHA
.char \['+] \[u064F]    \" ARABIC DAMMA
.char \[1+] \[u0650]    \" ARABIC KASRA
.char \[3+] \[u0651]    \" ARABIC SHADDA
.char \[0+] \[u0652]    \" ARABIC SUKUN
.char \[aS] \[u0670]    \" SUPERSCRIPT ARABIC LETTER ALEF
.char \[p+] \[u067E]    \" ARABIC LETTER PEH
.char \[v+] \[u06A4]    \" ARABIC LETTER VEH
.char \[gf] \[u06AF]    \" ARABIC LETTER GAF
.char \[0a] \[u06F0]    \" EASTERN ARABIC-INDIC DIGIT ZERO
.char \[1a] \[u06F1]    \" EASTERN ARABIC-INDIC DIGIT ONE
.char \[2a] \[u06F2]    \" EASTERN ARABIC-INDIC DIGIT TWO
.char \[3a] \[u06F3]    \" EASTERN ARABIC-INDIC DIGIT THREE
.char \[4a] \[u06F4]    \" EASTERN ARABIC-INDIC DIGIT FOUR
.char \[5a] \[u06F5]    \" EASTERN ARABIC-INDIC DIGIT FIVE
.char \[6a] \[u06F6]    \" EASTERN ARABIC-INDIC DIGIT SIX
.char \[7a] \[u06F7]    \" EASTERN ARABIC-INDIC DIGIT SEVEN
.char \[8a] \[u06F8]    \" EASTERN ARABIC-INDIC DIGIT EIGHT
.char \[9a] \[u06F9]    \" EASTERN ARABIC-INDIC DIGIT NINE
.char \[A-0] \[u1E00]    \" LATIN CAPITAL LETTER A WITH RING BELOW
.char \[a-0] \[u1E01]    \" LATIN SMALL LETTER A WITH RING BELOW
.char \[B.] \[u1E02]    \" LATIN CAPITAL LETTER B WITH DOT ABOVE
.char \[b.] \[u1E03]    \" LATIN SMALL LETTER B WITH DOT ABOVE
.char \[B-.] \[u1E04]    \" LATIN CAPITAL LETTER B WITH DOT BELOW
.char \[b-.] \[u1E05]    \" LATIN SMALL LETTER B WITH DOT BELOW
.char \[B_] \[u1E06]    \" LATIN CAPITAL LETTER B WITH LINE BELOW
.char \[b_] \[u1E07]    \" LATIN SMALL LETTER B WITH LINE BELOW
.char \[C,'] \[u1E08]    \" LATIN CAPITAL LETTER C WITH CEDILLA AND ACUTE
.char \[c,'] \[u1E09]    \" LATIN SMALL LETTER C WITH CEDILLA AND ACUTE
.char \[D.] \[u1E0A]    \" LATIN CAPITAL LETTER D WITH DOT ABOVE
.char \[d.] \[u1E0B]    \" LATIN SMALL LETTER D WITH DOT ABOVE
.char \[D-.] \[u1E0C]    \" LATIN CAPITAL LETTER D WITH DOT BELOW
.char \[d-.] \[u1E0D]    \" LATIN SMALL LETTER D WITH DOT BELOW
.char \[D_] \[u1E0E]    \" LATIN CAPITAL LETTER D WITH LINE BELOW
.char \[d_] \[u1E0F]    \" LATIN SMALL LETTER D WITH LINE BELOW
.char \[D,] \[u1E10]    \" LATIN CAPITAL LETTER D WITH CEDILLA
.char \[d,] \[u1E11]    \" LATIN SMALL LETTER D WITH CEDILLA
.char \[D->] \[u1E12]    \" LATIN CAPITAL LETTER D WITH CIRCUMFLEX BELOW
.char \[d->] \[u1E13]    \" LATIN SMALL LETTER D WITH CIRCUMFLEX BELOW
.char \[E-!] \[u1E14]    \" LATIN CAPITAL LETTER E WITH MACRON AND GRAVE
.char \[e-!] \[u1E15]    \" LATIN SMALL LETTER E WITH MACRON AND GRAVE
.char \[E-'] \[u1E16]    \" LATIN CAPITAL LETTER E WITH MACRON AND ACUTE
.char \[e-'] \[u1E17]    \" LATIN SMALL LETTER E WITH MACRON AND ACUTE
.char \[E->] \[u1E18]    \" LATIN CAPITAL LETTER E WITH CIRCUMFLEX BELOW
.char \[e->] \[u1E19]    \" LATIN SMALL LETTER E WITH CIRCUMFLEX BELOW
.char \[E-?] \[u1E1A]    \" LATIN CAPITAL LETTER E WITH TILDE BELOW
.char \[e-?] \[u1E1B]    \" LATIN SMALL LETTER E WITH TILDE BELOW
.char \[E,(] \[u1E1C]    \" LATIN CAPITAL LETTER E WITH CEDILLA AND BREVE
.char \[e,(] \[u1E1D]    \" LATIN SMALL LETTER E WITH CEDILLA AND BREVE
.char \[F.] \[u1E1E]    \" LATIN CAPITAL LETTER F WITH DOT ABOVE
.char \[f.] \[u1E1F]    \" LATIN SMALL LETTER F WITH DOT ABOVE
.char \[G-] \[u1E20]    \" LATIN CAPITAL LETTER G WITH MACRON
.char \[g-] \[u1E21]    \" LATIN SMALL LETTER G WITH MACRON
.char \[H.] \[u1E22]    \" LATIN CAPITAL LETTER H WITH DOT ABOVE
.char \[h.] \[u1E23]    \" LATIN SMALL LETTER H WITH DOT ABOVE
.char \[H-.] \[u1E24]    \" LATIN CAPITAL LETTER H WITH DOT BELOW
.char \[h-.] \[u1E25]    \" LATIN SMALL LETTER H WITH DOT BELOW
.char \[H:] \[u1E26]    \" LATIN CAPITAL LETTER H WITH DIAERESIS
.char \[h:] \[u1E27]    \" LATIN SMALL LETTER H WITH DIAERESIS
.char \[H,] \[u1E28]    \" LATIN CAPITAL LETTER H WITH CEDILLA
.char \[h,] \[u1E29]    \" LATIN SMALL LETTER H WITH CEDILLA
.char \[H-(] \[u1E2A]    \" LATIN CAPITAL LETTER H WITH BREVE BELOW
.char \[h-(] \[u1E2B]    \" LATIN SMALL LETTER H WITH BREVE BELOW
.char \[I-?] \[u1E2C]    \" LATIN CAPITAL LETTER I WITH TILDE BELOW
.char \[i-?] \[u1E2D]    \" LATIN SMALL LETTER I WITH TILDE BELOW
.char \[I:'] \[u1E2E]    \" LATIN CAPITAL LETTER I WITH DIAERESIS AND ACUTE
.char \[i:'] \[u1E2F]    \" LATIN SMALL LETTER I WITH DIAERESIS AND ACUTE
.char \[K'] \[u1E30]    \" LATIN CAPITAL LETTER K WITH ACUTE
.char \[k'] \[u1E31]    \" LATIN SMALL LETTER K WITH ACUTE
.char \[K-.] \[u1E32]    \" LATIN CAPITAL LETTER K WITH DOT BELOW
.char \[k-.] \[u1E33]    \" LATIN SMALL LETTER K WITH DOT BELOW
.char \[K_] \[u1E34]    \" LATIN CAPITAL LETTER K WITH LINE BELOW
.char \[k_] \[u1E35]    \" LATIN SMALL LETTER K WITH LINE BELOW
.char \[L-.] \[u1E36]    \" LATIN CAPITAL LETTER L WITH DOT BELOW
.char \[l-.] \[u1E37]    \" LATIN SMALL LETTER L WITH DOT BELOW
.char \[L--.] \[u1E38]    \" LATIN CAPITAL LETTER L WITH DOT BELOW AND MACRON
.char \[l--.] \[u1E39]    \" LATIN SMALL LETTER L WITH DOT BELOW AND MACRON
.char \[L_] \[u1E3A]    \" LATIN CAPITAL LETTER L WITH LINE BELOW
.char \[l_] \[u1E3B]    \" LATIN SMALL LETTER L WITH LINE BELOW
.char \[L->] \[u1E3C]    \" LATIN CAPITAL LETTER L WITH CIRCUMFLEX BELOW
.char \[l->] \[u1E3D]    \" LATIN SMALL LETTER L WITH CIRCUMFLEX BELOW
.char \[M'] \[u1E3E]    \" LATIN CAPITAL LETTER M WITH ACUTE
.char \[m'] \[u1E3F]    \" LATIN SMALL LETTER M WITH ACUTE
.char \[M.] \[u1E40]    \" LATIN CAPITAL LETTER M WITH DOT ABOVE
.char \[m.] \[u1E41]    \" LATIN SMALL LETTER M WITH DOT ABOVE
.char \[M-.] \[u1E42]    \" LATIN CAPITAL LETTER M WITH DOT BELOW
.char \[m-.] \[u1E43]    \" LATIN SMALL LETTER M WITH DOT BELOW
.char \[N.] \[u1E44]    \" LATIN CAPITAL LETTER N WITH DOT ABOVE
.char \[n.] \[u1E45]    \" LATIN SMALL LETTER N WITH DOT ABOVE
.char \[N-.] \[u1E46]    \" LATIN CAPITAL LETTER N WITH DOT BELOW
.char \[n-.] \[u1E47]    \" LATIN SMALL LETTER N WITH DOT BELOW
.char \[N_] \[u1E48]    \" LATIN CAPITAL LETTER N WITH LINE BELOW
.char \[n_] \[u1E49]    \" LATIN SMALL LETTER N WITH LINE BELOW
.char \[N->] \[u1E4A]    \" LATIN CAPITAL LETTER N WITH CIRCUMFLEX BELOW
.char \[N->] \[u1E4B]    \" LATIN SMALL LETTER N WITH CIRCUMFLEX BELOW
.char \[O?'] \[u1E4C]    \" LATIN CAPITAL LETTER O WITH TILDE AND ACUTE
.char \[o?'] \[u1E4D]    \" LATIN SMALL LETTER O WITH TILDE AND ACUTE
.char \[O?:] \[u1E4E]    \" LATIN CAPITAL LETTER O WITH TILDE AND DIAERESIS
.char \[o?:] \[u1E4F]    \" LATIN SMALL LETTER O WITH TILDE AND DIAERESIS
.char \[O-!] \[u1E50]    \" LATIN CAPITAL LETTER O WITH MACRON AND GRAVE
.char \[o-!] \[u1E51]    \" LATIN SMALL LETTER O WITH MACRON AND GRAVE
.char \[O-'] \[u1E52]    \" LATIN CAPITAL LETTER O WITH MACRON AND ACUTE
.char \[o-'] \[u1E53]    \" LATIN SMALL LETTER O WITH MACRON AND ACUTE
.char \[P'] \[u1E54]    \" LATIN CAPITAL LETTER P WITH ACUTE
.char \[p'] \[u1E55]    \" LATIN SMALL LETTER P WITH ACUTE
.char \[P.] \[u1E56]    \" LATIN CAPITAL LETTER P WITH DOT ABOVE
.char \[p.] \[u1E57]    \" LATIN SMALL LETTER P WITH DOT ABOVE
.char \[R.] \[u1E58]    \" LATIN CAPITAL LETTER R WITH DOT ABOVE
.char \[r.] \[u1E59]    \" LATIN SMALL LETTER R WITH DOT ABOVE
.char \[R-.] \[u1E5A]    \" LATIN CAPITAL LETTER R WITH DOT BELOW
.char \[r-.] \[u1E5B]    \" LATIN SMALL LETTER R WITH DOT BELOW
.char \[R--.] \[u1E5C]    \" LATIN CAPITAL LETTER R WITH DOT BELOW AND MACRON
.char \[r--.] \[u1E5D]    \" LATIN SMALL LETTER R WITH DOT BELOW AND MACRON
.char \[R_] \[u1E5E]    \" LATIN CAPITAL LETTER R WITH LINE BELOW
.char \[r_] \[u1E5F]    \" LATIN SMALL LETTER R WITH LINE BELOW
.char \[S.] \[u1E60]    \" LATIN CAPITAL LETTER S WITH DOT ABOVE
.char \[s.] \[u1E61]    \" LATIN SMALL LETTER S WITH DOT ABOVE
.char \[S-.] \[u1E62]    \" LATIN CAPITAL LETTER S WITH DOT BELOW
.char \[s-.] \[u1E63]    \" LATIN SMALL LETTER S WITH DOT BELOW
.char \[S'.] \[u1E64]    \" LATIN CAPITAL LETTER S WITH ACUTE AND DOT ABOVE
.char \[s'.] \[u1E65]    \" LATIN SMALL LETTER S WITH ACUTE AND DOT ABOVE
.char \[S<.] \[u1E66]    \" LATIN CAPITAL LETTER S WITH CARON AND DOT ABOVE
.char \[s<.] \[u1E67]    \" LATIN SMALL LETTER S WITH CARON AND DOT ABOVE
.char \[S.-.] \[u1E68]    \" LATIN CAPITAL LETTER S WITH DOT BELOW AND DOT ABOVE
.char \[S.-.] \[u1E69]    \" LATIN SMALL LETTER S WITH DOT BELOW AND DOT ABOVE
.char \[T.] \[u1E6A]    \" LATIN CAPITAL LETTER T WITH DOT ABOVE
.char \[t.] \[u1E6B]    \" LATIN SMALL LETTER T WITH DOT ABOVE
.char \[T-.] \[u1E6C]    \" LATIN CAPITAL LETTER T WITH DOT BELOW
.char \[t-.] \[u1E6D]    \" LATIN SMALL LETTER T WITH DOT BELOW
.char \[T_] \[u1E6E]    \" LATIN CAPITAL LETTER T WITH LINE BELOW
.char \[t_] \[u1E6F]    \" LATIN SMALL LETTER T WITH LINE BELOW
.char \[T->] \[u1E70]    \" LATIN CAPITAL LETTER T WITH CIRCUMFLEX BELOW
.char \[t->] \[u1E71]    \" LATIN SMALL LETTER T WITH CIRCUMFLEX BELOW
.char \[U--:] \[u1E72]    \" LATIN CAPITAL LETTER U WITH DIAERESIS BELOW
.char \[u--:] \[u1E73]    \" LATIN SMALL LETTER U WITH DIAERESIS BELOW
.char \[U-?] \[u1E74]    \" LATIN CAPITAL LETTER U WITH TILDE BELOW
.char \[u-?] \[u1E75]    \" LATIN SMALL LETTER U WITH TILDE BELOW
.char \[U->] \[u1E76]    \" LATIN CAPITAL LETTER U WITH CIRCUMFLEX BELOW
.char \[u->] \[u1E77]    \" LATIN SMALL LETTER U WITH CIRCUMFLEX BELOW
.char \[U?'] \[u1E78]    \" LATIN CAPITAL LETTER U WITH TILDE AND ACUTE
.char \[u?'] \[u1E79]    \" LATIN SMALL LETTER U WITH TILDE AND ACUTE
.char \[U-:] \[u1E7A]    \" LATIN CAPITAL LETTER U WITH MACRON AND DIAERESIS
.char \[u-:] \[u1E7B]    \" LATIN SMALL LETTER U WITH MACRON AND DIAERESIS
.char \[V?] \[u1E7C]    \" LATIN CAPITAL LETTER V WITH TILDE
.char \[v?] \[u1E7D]    \" LATIN SMALL LETTER V WITH TILDE
.char \[V-.] \[u1E7E]    \" LATIN CAPITAL LETTER V WITH DOT BELOW
.char \[v-.] \[u1E7F]    \" LATIN SMALL LETTER V WITH DOT BELOW
.char \[W!] \[u1E80]    \" LATIN CAPITAL LETTER W WITH GRAVE
.char \[w!] \[u1E81]    \" LATIN SMALL LETTER W WITH GRAVE
.char \[W'] \[u1E82]    \" LATIN CAPITAL LETTER W WITH ACUTE
.char \[w'] \[u1E83]    \" LATIN SMALL LETTER W WITH ACUTE
.char \[W:] \[u1E84]    \" LATIN CAPITAL LETTER W WITH DIAERESIS
.char \[w:] \[u1E85]    \" LATIN SMALL LETTER W WITH DIAERESIS
.char \[W.] \[u1E86]    \" LATIN CAPITAL LETTER W WITH DOT ABOVE
.char \[w.] \[u1E87]    \" LATIN SMALL LETTER W WITH DOT ABOVE
.char \[W-.] \[u1E88]    \" LATIN CAPITAL LETTER W WITH DOT BELOW
.char \[w-.] \[u1E89]    \" LATIN SMALL LETTER W WITH DOT BELOW
.char \[X.] \[u1E8A]    \" LATIN CAPITAL LETTER X WITH DOT ABOVE
.char \[x.] \[u1E8B]    \" LATIN SMALL LETTER X WITH DOT ABOVE
.char \[X:] \[u1E8C]    \" LATIN CAPITAL LETTER X WITH DIAERESIS
.char \[x:] \[u1E8D]    \" LATIN SMALL LETTER X WITH DIAERESIS
.char \[Y.] \[u1E8E]    \" LATIN CAPITAL LETTER Y WITH DOT ABOVE
.char \[y.] \[u1E8F]    \" LATIN SMALL LETTER Y WITH DOT ABOVE
.char \[Z>] \[u1E90]    \" LATIN CAPITAL LETTER Z WITH CIRCUMFLEX
.char \[z>] \[u1E91]    \" LATIN SMALL LETTER Z WITH CIRCUMFLEX
.char \[Z-.] \[u1E92]    \" LATIN CAPITAL LETTER Z WITH DOT BELOW
.char \[z-.] \[u1E93]    \" LATIN SMALL LETTER Z WITH DOT BELOW
.char \[Z_] \[u1E94]    \" LATIN CAPITAL LETTER Z WITH LINE BELOW
.char \[z_] \[u1E95]    \" LATIN SMALL LETTER Z WITH LINE BELOW
.char \[h_] \[u1E96]    \" LATIN SMALL LETTER H WITH LINE BELOW
.char \[t:] \[u1E97]    \" LATIN SMALL LETTER T WITH DIAERESIS
.char \[w0] \[u1E98]    \" LATIN SMALL LETTER W WITH RING ABOVE
.char \[y0] \[u1E99]    \" LATIN SMALL LETTER Y WITH RING ABOVE
.char \[A-.] \[u1EA0]    \" LATIN CAPITAL LETTER A WITH DOT BELOW
.char \[a-.] \[u1EA1]    \" LATIN SMALL LETTER A WITH DOT BELOW
.char \[A2] \[u1EA2]    \" LATIN CAPITAL LETTER A WITH HOOK ABOVE
.char \[a2] \[u1EA3]    \" LATIN SMALL LETTER A WITH HOOK ABOVE
.char \[A>'] \[u1EA4]    \" LATIN CAPITAL LETTER A WITH CIRCUMFLEX AND ACUTE
.char \[a>'] \[u1EA5]    \" LATIN SMALL LETTER A WITH CIRCUMFLEX AND ACUTE
.char \[A>!] \[u1EA6]    \" LATIN CAPITAL LETTER A WITH CIRCUMFLEX AND GRAVE
.char \[a>!] \[u1EA7]    \" LATIN SMALL LETTER A WITH CIRCUMFLEX AND GRAVE
.char \[A>2] \[u1EA8]    \" LATIN CAPITAL LETTER A WITH CIRCUMFLEX AND HOOK ABOVE
.char \[a>2] \[u1EA9]    \" LATIN SMALL LETTER A WITH CIRCUMFLEX AND HOOK ABOVE
.char \[A>?] \[u1EAA]    \" LATIN CAPITAL LETTER A WITH CIRCUMFLEX AND TILDE
.char \[a>?] \[u1EAB]    \" LATIN SMALL LETTER A WITH CIRCUMFLEX AND TILDE
.char \[A>-.] \[u1EAC]    \" LATIN CAPITAL LETTER A WITH CIRCUMFLEX AND DOT BELOW
.char \[a>-.] \[u1EAD]    \" LATIN SMALL LETTER A WITH CIRCUMFLEX AND DOT BELOW
.char \[A('] \[u1EAE]    \" LATIN CAPITAL LETTER A WITH BREVE AND ACUTE
.char \[a('] \[u1EAF]    \" LATIN SMALL LETTER A WITH BREVE AND ACUTE
.char \[A(!] \[u1EB0]    \" LATIN CAPITAL LETTER A WITH BREVE AND GRAVE
.char \[a(!] \[u1EB1]    \" LATIN SMALL LETTER A WITH BREVE AND GRAVE
.char \[A(2] \[u1EB2]    \" LATIN CAPITAL LETTER A WITH BREVE AND HOOK ABOVE
.char \[a(2] \[u1EB3]    \" LATIN SMALL LETTER A WITH BREVE AND HOOK ABOVE
.char \[A(?] \[u1EB4]    \" LATIN CAPITAL LETTER A WITH BREVE AND TILDE
.char \[a(?] \[u1EB5]    \" LATIN SMALL LETTER A WITH BREVE AND TILDE
.char \[A(-.] \[u1EB6]    \" LATIN CAPITAL LETTER A WITH BREVE AND DOT BELOW
.char \[a(-.] \[u1EB7]    \" LATIN SMALL LETTER A WITH BREVE AND DOT BELOW
.char \[E-.] \[u1EB8]    \" LATIN CAPITAL LETTER E WITH DOT BELOW
.char \[e-.] \[u1EB9]    \" LATIN SMALL LETTER E WITH DOT BELOW
.char \[E2] \[u1EBA]    \" LATIN CAPITAL LETTER E WITH HOOK ABOVE
.char \[e2] \[u1EBB]    \" LATIN SMALL LETTER E WITH HOOK ABOVE
.char \[E?] \[u1EBC]    \" LATIN CAPITAL LETTER E WITH TILDE
.char \[e?] \[u1EBD]    \" LATIN SMALL LETTER E WITH TILDE
.char \[E>'] \[u1EBE]    \" LATIN CAPITAL LETTER E WITH CIRCUMFLEX AND ACUTE
.char \[e>'] \[u1EBF]    \" LATIN SMALL LETTER E WITH CIRCUMFLEX AND ACUTE
.char \[E>!] \[u1EC0]    \" LATIN CAPITAL LETTER E WITH CIRCUMFLEX AND GRAVE
.char \[e>!] \[u1EC1]    \" LATIN SMALL LETTER E WITH CIRCUMFLEX AND GRAVE
.char \[E>2] \[u1EC2]    \" LATIN CAPITAL LETTER E WITH CIRCUMFLEX AND HOOK ABOVE
.char \[e>2] \[u1EC3]    \" LATIN SMALL LETTER E WITH CIRCUMFLEX AND HOOK ABOVE
.char \[E>?] \[u1EC4]    \" LATIN CAPITAL LETTER E WITH CIRCUMFLEX AND TILDE
.char \[e>?] \[u1EC5]    \" LATIN SMALL LETTER E WITH CIRCUMFLEX AND TILDE
.char \[E>-.] \[u1EC6]    \" LATIN CAPITAL LETTER E WITH CIRCUMFLEX AND DOT BELOW
.char \[e>-.] \[u1EC7]    \" LATIN SMALL LETTER E WITH CIRCUMFLEX AND DOT BELOW
.char \[I2] \[u1EC8]    \" LATIN CAPITAL LETTER I WITH HOOK ABOVE
.char \[i2] \[u1EC9]    \" LATIN SMALL LETTER I WITH HOOK ABOVE
.char \[I-.] \[u1ECA]    \" LATIN CAPITAL LETTER I WITH DOT BELOW
.char \[i-.] \[u1ECB]    \" LATIN SMALL LETTER I WITH DOT BELOW
.char \[O-.] \[u1ECC]    \" LATIN CAPITAL LETTER O WITH DOT BELOW
.char \[o-.] \[u1ECD]    \" LATIN SMALL LETTER O WITH DOT BELOW
.char \[O2] \[u1ECE]    \" LATIN CAPITAL LETTER O WITH HOOK ABOVE
.char \[o2] \[u1ECF]    \" LATIN SMALL LETTER O WITH HOOK ABOVE
.char \[O>'] \[u1ED0]    \" LATIN CAPITAL LETTER O WITH CIRCUMFLEX AND ACUTE
.char \[o>'] \[u1ED1]    \" LATIN SMALL LETTER O WITH CIRCUMFLEX AND ACUTE
.char \[O>!] \[u1ED2]    \" LATIN CAPITAL LETTER O WITH CIRCUMFLEX AND GRAVE
.char \[o>!] \[u1ED3]    \" LATIN SMALL LETTER O WITH CIRCUMFLEX AND GRAVE
.char \[O>2] \[u1ED4]    \" LATIN CAPITAL LETTER O WITH CIRCUMFLEX AND HOOK ABOVE
.char \[o>2] \[u1ED5]    \" LATIN SMALL LETTER O WITH CIRCUMFLEX AND HOOK ABOVE
.char \[O>?] \[u1ED6]    \" LATIN CAPITAL LETTER O WITH CIRCUMFLEX AND TILDE
.char \[o>?] \[u1ED7]    \" LATIN SMALL LETTER O WITH CIRCUMFLEX AND TILDE
.char \[O>-.] \[u1ED8]    \" LATIN CAPITAL LETTER O WITH CIRCUMFLEX AND DOT BELOW
.char \[o>-.] \[u1ED9]    \" LATIN SMALL LETTER O WITH CIRCUMFLEX AND DOT BELOW
.char \[O9'] \[u1EDA]    \" LATIN CAPITAL LETTER O WITH HORN AND ACUTE
.char \[o9'] \[u1EDB]    \" LATIN SMALL LETTER O WITH HORN AND ACUTE
.char \[O9!] \[u1EDC]    \" LATIN CAPITAL LETTER O WITH HORN AND GRAVE
.char \[o9!] \[u1EDD]    \" LATIN SMALL LETTER O WITH HORN AND GRAVE
.char \[O92] \[u1EDE]    \" LATIN CAPITAL LETTER O WITH HORN AND HOOK ABOVE
.char \[o92] \[u1EDF]    \" LATIN SMALL LETTER O WITH HORN AND HOOK ABOVE
.char \[O9?] \[u1EE0]    \" LATIN CAPITAL LETTER O WITH HORN AND TILDE
.char \[o9?] \[u1EE1]    \" LATIN SMALL LETTER O WITH HORN AND TILDE
.char \[O9-.] \[u1EE2]    \" LATIN CAPITAL LETTER O WITH HORN AND DOT BELOW
.char \[o9-.] \[u1EE3]    \" LATIN SMALL LETTER O WITH HORN AND DOT BELOW
.char \[U-.] \[u1EE4]    \" LATIN CAPITAL LETTER U WITH DOT BELOW
.char \[u-.] \[u1EE5]    \" LATIN SMALL LETTER U WITH DOT BELOW
.char \[U2] \[u1EE6]    \" LATIN CAPITAL LETTER U WITH HOOK ABOVE
.char \[u2] \[u1EE7]    \" LATIN SMALL LETTER U WITH HOOK ABOVE
.char \[U9'] \[u1EE8]    \" LATIN CAPITAL LETTER U WITH HORN AND ACUTE
.char \[u9'] \[u1EE9]    \" LATIN SMALL LETTER U WITH HORN AND ACUTE
.char \[U9!] \[u1EEA]    \" LATIN CAPITAL LETTER U WITH HORN AND GRAVE
.char \[u9!] \[u1EEB]    \" LATIN SMALL LETTER U WITH HORN AND GRAVE
.char \[U92] \[u1EEC]    \" LATIN CAPITAL LETTER U WITH HORN AND HOOK ABOVE
.char \[u92] \[u1EED]    \" LATIN SMALL LETTER U WITH HORN AND HOOK ABOVE
.char \[U9?] \[u1EEE]    \" LATIN CAPITAL LETTER U WITH HORN AND TILDE
.char \[u9?] \[u1EEF]    \" LATIN SMALL LETTER U WITH HORN AND TILDE
.char \[U9-.] \[u1EF0]    \" LATIN CAPITAL LETTER U WITH HORN AND DOT BELOW
.char \[u9-.] \[u1EF1]    \" LATIN SMALL LETTER U WITH HORN AND DOT BELOW
.char \[Y!] \[u1EF2]    \" LATIN CAPITAL LETTER Y WITH GRAVE
.char \[y!] \[u1EF3]    \" LATIN SMALL LETTER Y WITH GRAVE
.char \[Y-.] \[u1EF4]    \" LATIN CAPITAL LETTER Y WITH DOT BELOW
.char \[y-.] \[u1EF5]    \" LATIN SMALL LETTER Y WITH DOT BELOW
.char \[Y2] \[u1EF6]    \" LATIN CAPITAL LETTER Y WITH HOOK ABOVE
.char \[y2] \[u1EF7]    \" LATIN SMALL LETTER Y WITH HOOK ABOVE
.char \[Y?] \[u1EF8]    \" LATIN CAPITAL LETTER Y WITH TILDE
.char \[y?] \[u1EF9]    \" LATIN SMALL LETTER Y WITH TILDE
.char \[;'] \[u1F00]    \" GREEK DASIA AND ACUTE ACCENT
.char \[,'] \[u1F01]    \" GREEK PSILI AND ACUTE ACCENT
.char \[;!] \[u1F02]    \" GREEK DASIA AND VARIA
.char \[,!] \[u1F03]    \" GREEK PSILI AND VARIA
.char \[?;] \[u1F04]    \" GREEK DASIA AND PERISPOMENI
.char \[?,] \[u1F05]    \" GREEK PSILI AND PERISPOMENI
.char \[!:] \[u1F06]    \" GREEK DIAERESIS AND VARIA
.char \[?:] \[u1F07]    \" GREEK DIAERESIS AND PERISPOMENI
.char \[1N] \[u2002]    \" EN SPACE
.char \[1M] \[u2003]    \" EM SPACE
.char \[3M] \[u2004]    \" THREE-PER-EM SPACE
.char \[4M] \[u2005]    \" FOUR-PER-EM SPACE
.char \[6M] \[u2006]    \" SIX-PER-EM SPACE
.char \[1T] \[u2009]    \" THIN SPACE
.char \[1H] \[u200A]    \" HAIR SPACE
.char \[-1] \[u2010]    \" HYPHEN
.char \[-N] \[u2013]    \" EN DASH
.char \[-M] \[u2014]    \" EM DASH
.char \[-3] \[u2015]    \" HORIZONTAL BAR
.char \[!2] \[u2016]    \" DOUBLE VERTICAL LINE
.char \[=2] \[u2017]    \" DOUBLE LOW LINE
.char \['6] \[u2018]    \" LEFT SINGLE QUOTATION MARK
.char \['9] \[u2019]    \" RIGHT SINGLE QUOTATION MARK
.char \[.9] \[u201A]    \" SINGLE LOW-9 QUOTATION MARK
.char \[9'] \[u201B]    \" SINGLE HIGH-REVERSED-9 QUOTATION MARK
.char \["6] \[u201C]    \" LEFT DOUBLE QUOTATION MARK
.char \["9] \[u201D]    \" RIGHT DOUBLE QUOTATION MARK
.char \[:9] \[u201E]    \" DOUBLE LOW-9 QUOTATION MARK
.char \[9"] \[u201F]    \" DOUBLE HIGH-REVERSED-9 QUOTATION MARK
.char \[/-] \[u2020]    \" DAGGER
.char \[/=] \[u2021]    \" DOUBLE DAGGER
.char \[..] \[u2025]    \" TWO DOT LEADER
.char \[,.] \[u2026]    \" HORIZONTAL ELLIPSIS (Vim)
.char \[%0] \[u2030]    \" PER MILLE SIGN
.char \[1'] \[u2032]    \" PRIME
.char \[2'] \[u2033]    \" DOUBLE PRIME
.char \[3'] \[u2034]    \" TRIPLE PRIME
.char \[1"] \[u2035]    \" REVERSED PRIME
.char \[2"] \[u2036]    \" REVERSED DOUBLE PRIME
.char \[3"] \[u2037]    \" REVERSED TRIPLE PRIME
.char \[Ca] \[u2038]    \" CARET
.char \[<1] \[u2039]    \" SINGLE LEFT-POINTING ANGLE QUOTATION MARK
.char \[>1] \[u203A]    \" SINGLE RIGHT-POINTING ANGLE QUOTATION MARK
.char \[:X] \[u203B]    \" REFERENCE MARK
.char \[!*2] \[u203C]    \" DOUBLE EXCLAMATION MARK
.char \['-] \[u203E]    \" OVERLINE
.char \[/f] \[u2044]    \" FRACTION SLASH
.char \[0S] \[u2070]    \" SUPERSCRIPT DIGIT ZERO
.char \[4S] \[u2074]    \" SUPERSCRIPT DIGIT FOUR
.char \[5S] \[u2075]    \" SUPERSCRIPT DIGIT FIVE
.char \[6S] \[u2076]    \" SUPERSCRIPT DIGIT SIX
.char \[7S] \[u2077]    \" SUPERSCRIPT DIGIT SEVEN
.char \[8S] \[u2078]    \" SUPERSCRIPT DIGIT EIGHT
.char \[9S] \[u2079]    \" SUPERSCRIPT DIGIT NINE
.char \[+S] \[u207A]    \" SUPERSCRIPT PLUS SIGN
.char \[-S] \[u207B]    \" SUPERSCRIPT MINUS
.char \[=S] \[u207C]    \" SUPERSCRIPT EQUALS SIGN
.char \[(S] \[u207D]    \" SUPERSCRIPT LEFT PARENTHESIS
.char \[)S] \[u207E]    \" SUPERSCRIPT RIGHT PARENTHESIS
.char \[nS] \[u207F]    \" SUPERSCRIPT LATIN SMALL LETTER N
.char \[0s] \[u2080]    \" SUBSCRIPT DIGIT ZERO
.char \[1s] \[u2081]    \" SUBSCRIPT DIGIT ONE
.char \[2s] \[u2082]    \" SUBSCRIPT DIGIT TWO
.char \[3s] \[u2083]    \" SUBSCRIPT DIGIT THREE
.char \[4s] \[u2084]    \" SUBSCRIPT DIGIT FOUR
.char \[5s] \[u2085]    \" SUBSCRIPT DIGIT FIVE
.char \[6s] \[u2086]    \" SUBSCRIPT DIGIT SIX
.char \[7s] \[u2087]    \" SUBSCRIPT DIGIT SEVEN
.char \[8s] \[u2088]    \" SUBSCRIPT DIGIT EIGHT
.char \[9s] \[u2089]    \" SUBSCRIPT DIGIT NINE
.char \[+s] \[u208A]    \" SUBSCRIPT PLUS SIGN
.char \[-s] \[u208B]    \" SUBSCRIPT MINUS
.char \[=s] \[u208C]    \" SUBSCRIPT EQUALS SIGN
.char \[(s] \[u208D]    \" SUBSCRIPT LEFT PARENTHESIS
.char \[)s] \[u208E]    \" SUBSCRIPT RIGHT PARENTHESIS
.char \[Li] \[u20A4]    \" LIRA SIGN
.char \[Pt] \[u20A7]    \" PESETA SIGN
.char \[W=] \[u20A9]    \" WON SIGN
.char \[Eu] \[u20AC]    \" EURO SIGN (Vim)
.char \[=R] \[u20BD]    \" ROUBLE SIGN (Vim)
.char \[=P] \[u20BD]    \" ROUBLE SIGN (Vim)
.char \[oC] \[u2103]    \" DEGREE CENTIGRADE
.char \[co] \[u2105]    \" CARE OF
.char \[oF] \[u2109]    \" DEGREE FAHRENHEIT
.char \[N0] \[u2116]    \" NUMERO SIGN
.char \[PO] \[u2117]    \" SOUND RECORDING COPYRIGHT
.char \[Rx] \[u211E]    \" PRESCRIPTION TAKE
.char \[SM] \[u2120]    \" SERVICE MARK
.char \[TM] \[u2122]    \" TRADE MARK SIGN
.char \[Om] \[u2126]    \" OHM SIGN
.char \[AO] \[u212B]    \" ANGSTROEM SIGN
.char \[13] \[u2153]    \" VULGAR FRACTION ONE THIRD
.char \[23] \[u2154]    \" VULGAR FRACTION TWO THIRDS
.char \[15] \[u2155]    \" VULGAR FRACTION ONE FIFTH
.char \[25] \[u2156]    \" VULGAR FRACTION TWO FIFTHS
.char \[35] \[u2157]    \" VULGAR FRACTION THREE FIFTHS
.char \[45] \[u2158]    \" VULGAR FRACTION FOUR FIFTHS
.char \[16] \[u2159]    \" VULGAR FRACTION ONE SIXTH
.char \[56] \[u215A]    \" VULGAR FRACTION FIVE SIXTHS
.char \[18] \[u215B]    \" VULGAR FRACTION ONE EIGHTH
.char \[38] \[u215C]    \" VULGAR FRACTION THREE EIGHTHS
.char \[58] \[u215D]    \" VULGAR FRACTION FIVE EIGHTHS
.char \[78] \[u215E]    \" VULGAR FRACTION SEVEN EIGHTHS
.char \[1R] \[u2160]    \" ROMAN NUMERAL ONE
.char \[2R] \[u2161]    \" ROMAN NUMERAL TWO
.char \[3R] \[u2162]    \" ROMAN NUMERAL THREE
.char \[4R] \[u2163]    \" ROMAN NUMERAL FOUR
.char \[5R] \[u2164]    \" ROMAN NUMERAL FIVE
.char \[6R] \[u2165]    \" ROMAN NUMERAL SIX
.char \[7R] \[u2166]    \" ROMAN NUMERAL SEVEN
.char \[8R] \[u2167]    \" ROMAN NUMERAL EIGHT
.char \[9R] \[u2168]    \" ROMAN NUMERAL NINE
.char \[aR] \[u2169]    \" ROMAN NUMERAL TEN
.char \[bR] \[u216A]    \" ROMAN NUMERAL ELEVEN
.char \[cR] \[u216B]    \" ROMAN NUMERAL TWELVE
.char \[50R] \[u216C]    \" ROMAN NUMERAL FIFTY
.char \[100R] \[u216D]    \" ROMAN NUMERAL ONE HUNDRED
.char \[500R] \[u216E]    \" ROMAN NUMERAL FIVE HUNDRED
.char \[1000R] \[u216F]    \" ROMAN NUMERAL ONE THOUSAND
.char \[1r] \[u2170]    \" SMALL ROMAN NUMERAL ONE
.char \[2r] \[u2171]    \" SMALL ROMAN NUMERAL TWO
.char \[3r] \[u2172]    \" SMALL ROMAN NUMERAL THREE
.char \[4r] \[u2173]    \" SMALL ROMAN NUMERAL FOUR
.char \[5r] \[u2174]    \" SMALL ROMAN NUMERAL FIVE
.char \[6r] \[u2175]    \" SMALL ROMAN NUMERAL SIX
.char \[7r] \[u2176]    \" SMALL ROMAN NUMERAL SEVEN
.char \[8r] \[u2177]    \" SMALL ROMAN NUMERAL EIGHT
.char \[9r] \[u2178]    \" SMALL ROMAN NUMERAL NINE
.char \[ar] \[u2179]    \" SMALL ROMAN NUMERAL TEN
.char \[br] \[u217A]    \" SMALL ROMAN NUMERAL ELEVEN
.char \[cr] \[u217B]    \" SMALL ROMAN NUMERAL TWELVE
.char \[50r] \[u217C]    \" SMALL ROMAN NUMERAL FIFTY
.char \[100r] \[u217D]    \" SMALL ROMAN NUMERAL ONE HUNDRED
.char \[500r] \[u217E]    \" SMALL ROMAN NUMERAL FIVE HUNDRED
.char \[1000r] \[u217F]    \" SMALL ROMAN NUMERAL ONE THOUSAND
.char \[1000RCD] \[u2180]    \" ROMAN NUMERAL ONE THOUSAND C D
.char \[5000R] \[u2181]    \" ROMAN NUMERAL FIVE THOUSAND
.char \[10000R] \[u2182]    \" ROMAN NUMERAL TEN THOUSAND
.char \[<-] \[u2190]    \" LEFTWARDS ARROW
.char \[-!] \[u2191]    \" UPWARDS ARROW
.char \[->] \[u2192]    \" RIGHTWARDS ARROW
.char \[-v] \[u2193]    \" DOWNWARDS ARROW
.char \[<>] \[u2194]    \" LEFT RIGHT ARROW
.char \[UD] \[u2195]    \" UP DOWN ARROW
.char \[<!!] \[u2196]    \" NORTH WEST ARROW
.char \[//>] \[u2197]    \" NORTH EAST ARROW
.char \[!!>] \[u2198]    \" SOUTH EAST ARROW
.char \[<//] \[u2199]    \" SOUTH WEST ARROW
.char \[<=] \[u21D0]    \" LEFTWARDS DOUBLE ARROW
.char \[=>] \[u21D2]    \" RIGHTWARDS DOUBLE ARROW
.char \[==] \[u21D4]    \" LEFT RIGHT DOUBLE ARROW
.char \[FA] \[u2200]    \" FOR ALL
.char \[dP] \[u2202]    \" PARTIAL DIFFERENTIAL
.char \[TE] \[u2203]    \" THERE EXISTS
.char \[/0] \[u2205]    \" EMPTY SET
.char \[DE] \[u2206]    \" INCREMENT
.char \[NB] \[u2207]    \" NABLA
.char \[(-] \[u2208]    \" ELEMENT OF
.char \[-)] \[u220B]    \" CONTAINS AS MEMBER
.char \[*P] \[u220F]    \" N-ARY PRODUCT
.char \[+Z] \[u2211]    \" N-ARY SUMMATION
.char \[-2] \[u2212]    \" MINUS SIGN
.char \[-+] \[u2213]    \" MINUS-OR-PLUS SIGN
.char \[*-] \[u2217]    \" ASTERISK OPERATOR
.char \[Ob] \[u2218]    \" RING OPERATOR
.char \[Sb] \[u2219]    \" BULLET OPERATOR
.char \[RT] \[u221A]    \" SQUARE ROOT
.char \[0(] \[u221D]    \" PROPORTIONAL TO
.char \[00] \[u221E]    \" INFINITY
.char \[-L] \[u221F]    \" RIGHT ANGLE
.char \[-V] \[u2220]    \" ANGLE
.char \[PP] \[u2225]    \" PARALLEL TO
.char \[AN] \[u2227]    \" LOGICAL AND
.char \[OR] \[u2228]    \" LOGICAL OR
.char \[(U] \[u2229]    \" INTERSECTION
.char \[)U] \[u222A]    \" UNION
.char \[In] \[u222B]    \" INTEGRAL
.char \[DI] \[u222C]    \" DOUBLE INTEGRAL
.char \[Io] \[u222E]    \" CONTOUR INTEGRAL
.char \[.:] \[u2234]    \" THEREFORE
.char \[:.] \[u2235]    \" BECAUSE
.char \[:R] \[u2236]    \" RATIO
.char \[::] \[u2237]    \" PROPORTION
.char \[?1] \[u223C]    \" TILDE OPERATOR
.char \[CG] \[u223E]    \" INVERTED LAZY S
.char \[?-] \[u2243]    \" ASYMPTOTICALLY EQUAL TO
.char \[?=] \[u2245]    \" APPROXIMATELY EQUAL TO
.char \[?2] \[u2248]    \" ALMOST EQUAL TO
.char \[=?] \[u224C]    \" ALL EQUAL TO
.char \[HI] \[u2253]    \" IMAGE OF OR APPROXIMATELY EQUAL TO
.char \[!=] \[u2260]    \" NOT EQUAL TO
.char \[=3] \[u2261]    \" IDENTICAL TO
.char \[=<] \[u2264]    \" LESS-THAN OR EQUAL TO
.char \[>=] \[u2265]    \" GREATER-THAN OR EQUAL TO
.char \[<*] \[u226A]    \" MUCH LESS-THAN
.char \[*>] \[u226B]    \" MUCH GREATER-THAN
.char \[!<] \[u226E]    \" NOT LESS-THAN
.char \[!>] \[u226F]    \" NOT GREATER-THAN
.char \[(C] \[u2282]    \" SUBSET OF
.char \[)C] \[u2283]    \" SUPERSET OF
.char \[(_] \[u2286]    \" SUBSET OF OR EQUAL TO
.char \[)_] \[u2287]    \" SUPERSET OF OR EQUAL TO
.char \[0.] \[u2299]    \" CIRCLED DOT OPERATOR
.char \[02] \[u229A]    \" CIRCLED RING OPERATOR
.char \[-T] \[u22A5]    \" UP TACK
.char \[.P] \[u22C5]    \" DOT OPERATOR
.char \[:3] \[u22EE]    \" VERTICAL ELLIPSIS
.char \[.3] \[u22EF]    \" MIDLINE HORIZONTAL ELLIPSIS
.char \[Eh] \[u2302]    \" HOUSE
.char \[<7] \[u2308]    \" LEFT CEILING
.char \[>7] \[u2309]    \" RIGHT CEILING
.char \[7<] \[u230A]    \" LEFT FLOOR
.char \[7>] \[u230B]    \" RIGHT FLOOR
.char \[NI] \[u2310]    \" REVERSED NOT SIGN
.char \[(A] \[u2312]    \" ARC
.char \[TR] \[u2315]    \" TELEPHONE RECORDER
.char \[Iu] \[u2320]    \" TOP HALF INTEGRAL
.char \[Il] \[u2321]    \" BOTTOM HALF INTEGRAL
.char \[</] \[u2329]    \" LEFT-POINTING ANGLE BRACKET
.char \[/>] \[u232A]    \" RIGHT-POINTING ANGLE BRACKET
.char \[Vs] \[u2423]    \" OPEN BOX
.char \[1h] \[u2440]    \" OCR HOOK
.char \[3h] \[u2441]    \" OCR CHAIR
.char \[2h] \[u2442]    \" OCR FORK
.char \[4h] \[u2443]    \" OCR INVERTED FORK
.char \[1j] \[u2446]    \" OCR BRANCH BANK IDENTIFICATION
.char \[2j] \[u2447]    \" OCR AMOUNT OF CHECK
.char \[3j] \[u2448]    \" OCR DASH
.char \[4j] \[u2449]    \" OCR CUSTOMER ACCOUNT NUMBER
.char \[1-o] \[u2460]    \" CIRCLED DIGIT ONE
.char \[2-o] \[u2461]    \" CIRCLED DIGIT TWO
.char \[3-o] \[u2462]    \" CIRCLED DIGIT THREE
.char \[4-o] \[u2463]    \" CIRCLED DIGIT FOUR
.char \[5-o] \[u2464]    \" CIRCLED DIGIT FIVE
.char \[6-o] \[u2465]    \" CIRCLED DIGIT SIX
.char \[7-o] \[u2466]    \" CIRCLED DIGIT SEVEN
.char \[8-o] \[u2467]    \" CIRCLED DIGIT EIGHT
.char \[9-o] \[u2468]    \" CIRCLED DIGIT NINE
.char \[10-o] \[u2469]    \" CIRCLED NUMBER TEN
.char \[11-o] \[u246A]    \" CIRCLED NUMBER ELEVEN
.char \[12-o] \[u246B]    \" CIRCLED NUMBER TWELVE
.char \[13-o] \[u246C]    \" CIRCLED NUMBER THIRTEEN
.char \[14-o] \[u246D]    \" CIRCLED NUMBER FOURTEEN
.char \[15-o] \[u246E]    \" CIRCLED NUMBER FIFTEEN
.char \[16-o] \[u246F]    \" CIRCLED NUMBER SIXTEEN
.char \[17-o] \[u2470]    \" CIRCLED NUMBER SEVENTEEN
.char \[18-o] \[u2471]    \" CIRCLED NUMBER EIGHTEEN
.char \[19-o] \[u2472]    \" CIRCLED NUMBER NINETEEN
.char \[20-o] \[u2473]    \" CIRCLED NUMBER TWENTY
.char \[(1)] \[u2474]    \" PARENTHESIZED DIGIT ONE
.char \[(2)] \[u2475]    \" PARENTHESIZED DIGIT TWO
.char \[(3)] \[u2476]    \" PARENTHESIZED DIGIT THREE
.char \[(4)] \[u2477]    \" PARENTHESIZED DIGIT FOUR
.char \[(5)] \[u2478]    \" PARENTHESIZED DIGIT FIVE
.char \[(6)] \[u2479]    \" PARENTHESIZED DIGIT SIX
.char \[(7)] \[u247A]    \" PARENTHESIZED DIGIT SEVEN
.char \[(8)] \[u247B]    \" PARENTHESIZED DIGIT EIGHT
.char \[(9)] \[u247C]    \" PARENTHESIZED DIGIT NINE
.char \[(10)] \[u247D]    \" PARENTHESIZED NUMBER TEN
.char \[(11)] \[u247E]    \" PARENTHESIZED NUMBER ELEVEN
.char \[(12)] \[u247F]    \" PARENTHESIZED NUMBER TWELVE
.char \[(13)] \[u2480]    \" PARENTHESIZED NUMBER THIRTEEN
.char \[(14)] \[u2481]    \" PARENTHESIZED NUMBER FOURTEEN
.char \[(15)] \[u2482]    \" PARENTHESIZED NUMBER FIFTEEN
.char \[(16)] \[u2483]    \" PARENTHESIZED NUMBER SIXTEEN
.char \[(17)] \[u2484]    \" PARENTHESIZED NUMBER SEVENTEEN
.char \[(18)] \[u2485]    \" PARENTHESIZED NUMBER EIGHTEEN
.char \[(19)] \[u2486]    \" PARENTHESIZED NUMBER NINETEEN
.char \[(20)] \[u2487]    \" PARENTHESIZED NUMBER TWENTY
.char \[1.] \[u2488]    \" DIGIT ONE FULL STOP
.char \[2.] \[u2489]    \" DIGIT TWO FULL STOP
.char \[3.] \[u248A]    \" DIGIT THREE FULL STOP
.char \[4.] \[u248B]    \" DIGIT FOUR FULL STOP
.char \[5.] \[u248C]    \" DIGIT FIVE FULL STOP
.char \[6.] \[u248D]    \" DIGIT SIX FULL STOP
.char \[7.] \[u248E]    \" DIGIT SEVEN FULL STOP
.char \[8.] \[u248F]    \" DIGIT EIGHT FULL STOP
.char \[9.] \[u2490]    \" DIGIT NINE FULL STOP
.char \[10.] \[u2491]    \" NUMBER TEN FULL STOP
.char \[11.] \[u2492]    \" NUMBER ELEVEN FULL STOP
.char \[12.] \[u2493]    \" NUMBER TWELVE FULL STOP
.char \[13.] \[u2494]    \" NUMBER THIRTEEN FULL STOP
.char \[14.] \[u2495]    \" NUMBER FOURTEEN FULL STOP
.char \[15.] \[u2496]    \" NUMBER FIFTEEN FULL STOP
.char \[16.] \[u2497]    \" NUMBER SIXTEEN FULL STOP
.char \[17.] \[u2498]    \" NUMBER SEVENTEEN FULL STOP
.char \[18.] \[u2499]    \" NUMBER EIGHTEEN FULL STOP
.char \[19.] \[u249A]    \" NUMBER NINETEEN FULL STOP
.char \[20.] \[u249B]    \" NUMBER TWENTY FULL STOP
.char \[(a)] \[u249C]    \" PARENTHESIZED LATIN SMALL LETTER A
.char \[(b)] \[u249D]    \" PARENTHESIZED LATIN SMALL LETTER B
.char \[(c)] \[u249E]    \" PARENTHESIZED LATIN SMALL LETTER C
.char \[(d)] \[u249F]    \" PARENTHESIZED LATIN SMALL LETTER D
.char \[(e)] \[u24A0]    \" PARENTHESIZED LATIN SMALL LETTER E
.char \[(f)] \[u24A1]    \" PARENTHESIZED LATIN SMALL LETTER F
.char \[(g)] \[u24A2]    \" PARENTHESIZED LATIN SMALL LETTER G
.char \[(h)] \[u24A3]    \" PARENTHESIZED LATIN SMALL LETTER H
.char \[(i)] \[u24A4]    \" PARENTHESIZED LATIN SMALL LETTER I
.char \[(j)] \[u24A5]    \" PARENTHESIZED LATIN SMALL LETTER J
.char \[(k)] \[u24A6]    \" PARENTHESIZED LATIN SMALL LETTER K
.char \[(l)] \[u24A7]    \" PARENTHESIZED LATIN SMALL LETTER L
.char \[(m)] \[u24A8]    \" PARENTHESIZED LATIN SMALL LETTER M
.char \[(n)] \[u24A9]    \" PARENTHESIZED LATIN SMALL LETTER N
.char \[(o)] \[u24AA]    \" PARENTHESIZED LATIN SMALL LETTER O
.char \[(p)] \[u24AB]    \" PARENTHESIZED LATIN SMALL LETTER P
.char \[(q)] \[u24AC]    \" PARENTHESIZED LATIN SMALL LETTER Q
.char \[(r)] \[u24AD]    \" PARENTHESIZED LATIN SMALL LETTER R
.char \[(s)] \[u24AE]    \" PARENTHESIZED LATIN SMALL LETTER S
.char \[(t)] \[u24AF]    \" PARENTHESIZED LATIN SMALL LETTER T
.char \[(u)] \[u24B0]    \" PARENTHESIZED LATIN SMALL LETTER U
.char \[(v)] \[u24B1]    \" PARENTHESIZED LATIN SMALL LETTER V
.char \[(w)] \[u24B2]    \" PARENTHESIZED LATIN SMALL LETTER W
.char \[(x)] \[u24B3]    \" PARENTHESIZED LATIN SMALL LETTER X
.char \[(y)] \[u24B4]    \" PARENTHESIZED LATIN SMALL LETTER Y
.char \[(z)] \[u24B5]    \" PARENTHESIZED LATIN SMALL LETTER Z
.char \[A-o] \[u24B6]    \" CIRCLED LATIN CAPITAL LETTER A
.char \[B-o] \[u24B7]    \" CIRCLED LATIN CAPITAL LETTER B
.char \[C-o] \[u24B8]    \" CIRCLED LATIN CAPITAL LETTER C
.char \[D-o] \[u24B9]    \" CIRCLED LATIN CAPITAL LETTER D
.char \[E-o] \[u24BA]    \" CIRCLED LATIN CAPITAL LETTER E
.char \[F-o] \[u24BB]    \" CIRCLED LATIN CAPITAL LETTER F
.char \[G-o] \[u24BC]    \" CIRCLED LATIN CAPITAL LETTER G
.char \[H-o] \[u24BD]    \" CIRCLED LATIN CAPITAL LETTER H
.char \[I-o] \[u24BE]    \" CIRCLED LATIN CAPITAL LETTER I
.char \[J-o] \[u24BF]    \" CIRCLED LATIN CAPITAL LETTER J
.char \[K-o] \[u24C0]    \" CIRCLED LATIN CAPITAL LETTER K
.char \[L-o] \[u24C1]    \" CIRCLED LATIN CAPITAL LETTER L
.char \[M-o] \[u24C2]    \" CIRCLED LATIN CAPITAL LETTER M
.char \[N-o] \[u24C3]    \" CIRCLED LATIN CAPITAL LETTER N
.char \[O-o] \[u24C4]    \" CIRCLED LATIN CAPITAL LETTER O
.char \[P-o] \[u24C5]    \" CIRCLED LATIN CAPITAL LETTER P
.char \[Q-o] \[u24C6]    \" CIRCLED LATIN CAPITAL LETTER Q
.char \[R-o] \[u24C7]    \" CIRCLED LATIN CAPITAL LETTER R
.char \[S-o] \[u24C8]    \" CIRCLED LATIN CAPITAL LETTER S
.char \[T-o] \[u24C9]    \" CIRCLED LATIN CAPITAL LETTER T
.char \[U-o] \[u24CA]    \" CIRCLED LATIN CAPITAL LETTER U
.char \[V-o] \[u24CB]    \" CIRCLED LATIN CAPITAL LETTER V
.char \[W-o] \[u24CC]    \" CIRCLED LATIN CAPITAL LETTER W
.char \[X-o] \[u24CD]    \" CIRCLED LATIN CAPITAL LETTER X
.char \[Y-o] \[u24CE]    \" CIRCLED LATIN CAPITAL LETTER Y
.char \[Z-o] \[u24CF]    \" CIRCLED LATIN CAPITAL LETTER Z
.char \[a-o] \[u24D0]    \" CIRCLED LATIN SMALL LETTER A
.char \[b-o] \[u24D1]    \" CIRCLED LATIN SMALL LETTER B
.char \[c-o] \[u24D2]    \" CIRCLED LATIN SMALL LETTER C
.char \[d-o] \[u24D3]    \" CIRCLED LATIN SMALL LETTER D
.char \[e-o] \[u24D4]    \" CIRCLED LATIN SMALL LETTER E
.char \[f-o] \[u24D5]    \" CIRCLED LATIN SMALL LETTER F
.char \[g-o] \[u24D6]    \" CIRCLED LATIN SMALL LETTER G
.char \[h-o] \[u24D7]    \" CIRCLED LATIN SMALL LETTER H
.char \[i-o] \[u24D8]    \" CIRCLED LATIN SMALL LETTER I
.char \[j-o] \[u24D9]    \" CIRCLED LATIN SMALL LETTER J
.char \[k-o] \[u24DA]    \" CIRCLED LATIN SMALL LETTER K
.char \[l-o] \[u24DB]    \" CIRCLED LATIN SMALL LETTER L
.char \[m-o] \[u24DC]    \" CIRCLED LATIN SMALL LETTER M
.char \[n-o] \[u24DD]    \" CIRCLED LATIN SMALL LETTER N
.char \[o-o] \[u24DE]    \" CIRCLED LATIN SMALL LETTER O
.char \[p-o] \[u24DF]    \" CIRCLED LATIN SMALL LETTER P
.char \[q-o] \[u24E0]    \" CIRCLED LATIN SMALL LETTER Q
.char \[r-o] \[u24E1]    \" CIRCLED LATIN SMALL LETTER R
.char \[s-o] \[u24E2]    \" CIRCLED LATIN SMALL LETTER S
.char \[t-o] \[u24E3]    \" CIRCLED LATIN SMALL LETTER T
.char \[u-o] \[u24E4]    \" CIRCLED LATIN SMALL LETTER U
.char \[v-o] \[u24E5]    \" CIRCLED LATIN SMALL LETTER V
.char \[w-o] \[u24E6]    \" CIRCLED LATIN SMALL LETTER W
.char \[x-o] \[u24E7]    \" CIRCLED LATIN SMALL LETTER X
.char \[y-o] \[u24E8]    \" CIRCLED LATIN SMALL LETTER Y
.char \[z-o] \[u24E9]    \" CIRCLED LATIN SMALL LETTER Z
.char \[0-o] \[u24EA]    \" CIRCLED DIGIT ZERO
.char \[hh] \[u2500]    \" BOX DRAWINGS LIGHT HORIZONTAL
.char \[HH] \[u2501]    \" BOX DRAWINGS HEAVY HORIZONTAL
.char \[vv] \[u2502]    \" BOX DRAWINGS LIGHT VERTICAL
.char \[VV] \[u2503]    \" BOX DRAWINGS HEAVY VERTICAL
.char \[3-] \[u2504]    \" BOX DRAWINGS LIGHT TRIPLE DASH HORIZONTAL
.char \[3_] \[u2505]    \" BOX DRAWINGS HEAVY TRIPLE DASH HORIZONTAL
.char \[3!] \[u2506]    \" BOX DRAWINGS LIGHT TRIPLE DASH VERTICAL
.char \[3/] \[u2507]    \" BOX DRAWINGS HEAVY TRIPLE DASH VERTICAL
.char \[4-] \[u2508]    \" BOX DRAWINGS LIGHT QUADRUPLE DASH HORIZONTAL
.char \[4_] \[u2509]    \" BOX DRAWINGS HEAVY QUADRUPLE DASH HORIZONTAL
.char \[4!] \[u250A]    \" BOX DRAWINGS LIGHT QUADRUPLE DASH VERTICAL
.char \[4/] \[u250B]    \" BOX DRAWINGS HEAVY QUADRUPLE DASH VERTICAL
.char \[dr] \[u250C]    \" BOX DRAWINGS LIGHT DOWN AND RIGHT
.char \[dR] \[u250D]    \" BOX DRAWINGS DOWN LIGHT AND RIGHT HEAVY
.char \[Dr] \[u250E]    \" BOX DRAWINGS DOWN HEAVY AND RIGHT LIGHT
.char \[DR] \[u250F]    \" BOX DRAWINGS HEAVY DOWN AND RIGHT
.char \[dl] \[u2510]    \" BOX DRAWINGS LIGHT DOWN AND LEFT
.char \[dL] \[u2511]    \" BOX DRAWINGS DOWN LIGHT AND LEFT HEAVY
.char \[Dl] \[u2512]    \" BOX DRAWINGS DOWN HEAVY AND LEFT LIGHT
.char \[LD] \[u2513]    \" BOX DRAWINGS HEAVY DOWN AND LEFT
.char \[ur] \[u2514]    \" BOX DRAWINGS LIGHT UP AND RIGHT
.char \[uR] \[u2515]    \" BOX DRAWINGS UP LIGHT AND RIGHT HEAVY
.char \[Ur] \[u2516]    \" BOX DRAWINGS UP HEAVY AND RIGHT LIGHT
.char \[UR] \[u2517]    \" BOX DRAWINGS HEAVY UP AND RIGHT
.char \[ul] \[u2518]    \" BOX DRAWINGS LIGHT UP AND LEFT
.char \[uL] \[u2519]    \" BOX DRAWINGS UP LIGHT AND LEFT HEAVY
.char \[Ul] \[u251A]    \" BOX DRAWINGS UP HEAVY AND LEFT LIGHT
.char \[UL] \[u251B]    \" BOX DRAWINGS HEAVY UP AND LEFT
.char \[vr] \[u251C]    \" BOX DRAWINGS LIGHT VERTICAL AND RIGHT
.char \[vR] \[u251D]    \" BOX DRAWINGS VERTICAL LIGHT AND RIGHT HEAVY
.char \[Udr] \[u251E]    \" BOX DRAWINGS UP HEAVY AND RIGHT DOWN LIGHT
.char \[uDr] \[u251F]    \" BOX DRAWINGS DOWN HEAVY AND RIGHT UP LIGHT
.char \[Vr] \[u2520]    \" BOX DRAWINGS VERTICAL HEAVY AND RIGHT LIGHT
.char \[UdR] \[u2521]    \" BOX DRAWINGS DOWN LIGHT AND RIGHT UP HEAVY
.char \[uDR] \[u2522]    \" BOX DRAWINGS UP LIGHT AND RIGHT DOWN HEAVY
.char \[VR] \[u2523]    \" BOX DRAWINGS HEAVY VERTICAL AND RIGHT
.char \[vl] \[u2524]    \" BOX DRAWINGS LIGHT VERTICAL AND LEFT
.char \[vL] \[u2525]    \" BOX DRAWINGS VERTICAL LIGHT AND LEFT HEAVY
.char \[Udl] \[u2526]    \" BOX DRAWINGS UP HEAVY AND LEFT DOWN LIGHT
.char \[uDl] \[u2527]    \" BOX DRAWINGS DOWN HEAVY AND LEFT UP LIGHT
.char \[Vl] \[u2528]    \" BOX DRAWINGS VERTICAL HEAVY AND LEFT LIGHT
.char \[UdL] \[u2529]    \" BOX DRAWINGS DOWN LIGHT AND LEFT UP HEAVY
.char \[uDL] \[u252A]    \" BOX DRAWINGS UP LIGHT AND LEFT DOWN HEAVY
.char \[VL] \[u252B]    \" BOX DRAWINGS HEAVY VERTICAL AND LEFT
.char \[dh] \[u252C]    \" BOX DRAWINGS LIGHT DOWN AND HORIZONTAL
.char \[dLr] \[u252D]    \" BOX DRAWINGS LEFT HEAVY AND RIGHT DOWN LIGHT
.char \[dlR] \[u252E]    \" BOX DRAWINGS RIGHT HEAVY AND LEFT DOWN LIGHT
.char \[dH] \[u252F]    \" BOX DRAWINGS DOWN LIGHT AND HORIZONTAL HEAVY
.char \[Dh] \[u2530]    \" BOX DRAWINGS DOWN HEAVY AND HORIZONTAL LIGHT
.char \[DLr] \[u2531]    \" BOX DRAWINGS RIGHT LIGHT AND LEFT DOWN HEAVY
.char \[DlR] \[u2532]    \" BOX DRAWINGS LEFT LIGHT AND RIGHT DOWN HEAVY
.char \[DH] \[u2533]    \" BOX DRAWINGS HEAVY DOWN AND HORIZONTAL
.char \[uh] \[u2534]    \" BOX DRAWINGS LIGHT UP AND HORIZONTAL
.char \[uLr] \[u2535]    \" BOX DRAWINGS LEFT HEAVY AND RIGHT UP LIGHT
.char \[ulR] \[u2536]    \" BOX DRAWINGS RIGHT HEAVY AND LEFT UP LIGHT
.char \[uH] \[u2537]    \" BOX DRAWINGS UP LIGHT AND HORIZONTAL HEAVY
.char \[Uh] \[u2538]    \" BOX DRAWINGS UP HEAVY AND HORIZONTAL LIGHT
.char \[ULr] \[u2539]    \" BOX DRAWINGS RIGHT LIGHT AND LEFT UP HEAVY
.char \[UlR] \[u253A]    \" BOX DRAWINGS LEFT LIGHT AND RIGHT UP HEAVY
.char \[UH] \[u253B]    \" BOX DRAWINGS HEAVY UP AND HORIZONTAL
.char \[vh] \[u253C]    \" BOX DRAWINGS LIGHT VERTICAL AND HORIZONTAL
.char \[vLr] \[u253D]    \" BOX DRAWINGS LEFT HEAVY AND RIGHT VERTICAL LIGHT
.char \[vlR] \[u253E]    \" BOX DRAWINGS RIGHT HEAVY AND LEFT VERTICAL LIGHT
.char \[vH] \[u253F]    \" BOX DRAWINGS VERTICAL LIGHT AND HORIZONTAL HEAVY
.char \[Udh] \[u2540]    \" BOX DRAWINGS UP HEAVY AND DOWN HORIZONTAL LIGHT
.char \[uDh] \[u2541]    \" BOX DRAWINGS DOWN HEAVY AND UP HORIZONTAL LIGHT
.char \[Vh] \[u2542]    \" BOX DRAWINGS VERTICAL HEAVY AND HORIZONTAL LIGHT
.char \[UdLr] \[u2543]    \" BOX DRAWINGS LEFT UP HEAVY AND RIGHT DOWN LIGHT
.char \[UdlR] \[u2544]    \" BOX DRAWINGS RIGHT UP HEAVY AND LEFT DOWN LIGHT
.char \[uDLr] \[u2545]    \" BOX DRAWINGS LEFT DOWN HEAVY AND RIGHT UP LIGHT
.char \[uDlR] \[u2546]    \" BOX DRAWINGS RIGHT DOWN HEAVY AND LEFT UP LIGHT
.char \[UdH] \[u2547]    \" BOX DRAWINGS DOWN LIGHT AND UP HORIZONTAL HEAVY
.char \[uDH] \[u2548]    \" BOX DRAWINGS UP LIGHT AND DOWN HORIZONTAL HEAVY
.char \[VLr] \[u2549]    \" BOX DRAWINGS RIGHT LIGHT AND LEFT VERTICAL HEAVY
.char \[VlR] \[u254A]    \" BOX DRAWINGS LEFT LIGHT AND RIGHT VERTICAL HEAVY
.char \[VH] \[u254B]    \" BOX DRAWINGS HEAVY VERTICAL AND HORIZONTAL
.char \[FD] \[u2571]    \" BOX DRAWINGS LIGHT DIAGONAL UPPER RIGHT TO LOWER LEFT
.char \[BD] \[u2572]    \" BOX DRAWINGS LIGHT DIAGONAL UPPER LEFT TO LOWER RIGHT
.char \[TB] \[u2580]    \" UPPER HALF BLOCK
.char \[LB] \[u2584]    \" LOWER HALF BLOCK
.char \[FB] \[u2588]    \" FULL BLOCK
.char \[lB] \[u258C]    \" LEFT HALF BLOCK
.char \[RB] \[u2590]    \" RIGHT HALF BLOCK
.char \[.S] \[u2591]    \" LIGHT SHADE
.char \[:S] \[u2592]    \" MEDIUM SHADE
.char \[?S] \[u2593]    \" DARK SHADE
.char \[fS] \[u25A0]    \" BLACK SQUARE
.char \[OS] \[u25A1]    \" WHITE SQUARE
.char \[RO] \[u25A2]    \" WHITE SQUARE WITH ROUNDED CORNERS
.char \[Rr] \[u25A3]    \" WHITE SQUARE CONTAINING BLACK SMALL SQUARE
.char \[RF] \[u25A4]    \" SQUARE WITH HORIZONTAL FILL
.char \[RY] \[u25A5]    \" SQUARE WITH VERTICAL FILL
.char \[RH] \[u25A6]    \" SQUARE WITH ORTHOGONAL CROSSHATCH FILL
.char \[RZ] \[u25A7]    \" SQUARE WITH UPPER LEFT TO LOWER RIGHT FILL
.char \[RK] \[u25A8]    \" SQUARE WITH UPPER RIGHT TO LOWER LEFT FILL
.char \[RX] \[u25A9]    \" SQUARE WITH DIAGONAL CROSSHATCH FILL
.char \[sB] \[u25AA]    \" BLACK SMALL SQUARE
.char \[SR] \[u25AC]    \" BLACK RECTANGLE
.char \[Or] \[u25AD]    \" WHITE RECTANGLE
.char \[UT] \[u25B2]    \" BLACK UP-POINTING TRIANGLE
.char \[uT] \[u25B3]    \" WHITE UP-POINTING TRIANGLE
.char \[PR] \[u25B6]    \" BLACK RIGHT-POINTING TRIANGLE
.char \[Tr] \[u25B7]    \" WHITE RIGHT-POINTING TRIANGLE
.char \[Dt] \[u25BC]    \" BLACK DOWN-POINTING TRIANGLE
.char \[dT] \[u25BD]    \" WHITE DOWN-POINTING TRIANGLE
.char \[PL] \[u25C0]    \" BLACK LEFT-POINTING TRIANGLE
.char \[Tl] \[u25C1]    \" WHITE LEFT-POINTING TRIANGLE
.char \[Db] \[u25C6]    \" BLACK DIAMOND
.char \[Dw] \[u25C7]    \" WHITE DIAMOND
.char \[LZ] \[u25CA]    \" LOZENGE
.char \[0m] \[u25CB]    \" WHITE CIRCLE
.char \[0o] \[u25CE]    \" BULLSEYE
.char \[0M] \[u25CF]    \" BLACK CIRCLE
.char \[0L] \[u25D0]    \" CIRCLE WITH LEFT HALF BLACK
.char \[0R] \[u25D1]    \" CIRCLE WITH RIGHT HALF BLACK
.char \[Sn] \[u25D8]    \" INVERSE BULLET
.char \[Ic] \[u25D9]    \" INVERSE WHITE CIRCLE
.char \[Fd] \[u25E2]    \" BLACK LOWER RIGHT TRIANGLE
.char \[Bd] \[u25E3]    \" BLACK LOWER LEFT TRIANGLE
.char \[*2] \[u2605]    \" BLACK STAR
.char \[*1] \[u2606]    \" WHITE STAR
.char \[TEL] \[u260E]    \" BLACK TELEPHONE
.char \[tel] \[u260F]    \" WHITE TELEPHONE
.char \[<H] \[u261C]    \" WHITE LEFT POINTING INDEX
.char \[>H] \[u261E]    \" WHITE RIGHT POINTING INDEX
.char \[0u] \[u263A]    \" WHITE SMILING FACE
.char \[0U] \[u263B]    \" BLACK SMILING FACE
.char \[SU] \[u263C]    \" WHITE SUN WITH RAYS
.char \[Fm] \[u2640]    \" FEMALE SIGN
.char \[Ml] \[u2642]    \" MALE SIGN
.char \[cS] \[u2660]    \" BLACK SPADE SUIT
.char \[cH] \[u2661]    \" WHITE HEART SUIT
.char \[cD] \[u2662]    \" WHITE DIAMOND SUIT
.char \[cC] \[u2663]    \" BLACK CLUB SUIT
.char \[cS-] \[u2664]    \" WHITE SPADE SUIT
.char \[cH-] \[u2665]    \" BLACK HEART SUIT
.char \[cD-] \[u2666]    \" BLACK DIAMOND SUIT
.char \[cC-] \[u2667]    \" WHITE CLUB SUIT
.char \[Md] \[u2669]    \" QUARTER NOTE
.char \[M8] \[u266A]    \" EIGHTH NOTE
.char \[M2] \[u266B]    \" BARRED EIGHTH NOTES
.char \[M16] \[u266C]    \" BARRED SIXTEENTH NOTES
.char \[Mb] \[u266D]    \" MUSIC FLAT SIGN
.char \[Mx] \[u266E]    \" MUSIC NATURAL SIGN
.char \[MX] \[u266F]    \" MUSIC SHARP SIGN
.char \[OK] \[u2713]    \" CHECK MARK
.char \[XX] \[u2717]    \" BALLOT X
.char \[-X] \[u2720]    \" MALTESE CROSS
.char \[IS] \[u3000]    \" IDEOGRAPHIC SPACE
.char \[,_] \[u3001]    \" IDEOGRAPHIC COMMA
.char \[._] \[u3002]    \" IDEOGRAPHIC PERIOD
.char \[+"] \[u3003]    \" DITTO MARK
.char \[+_] \[u3004]    \" IDEOGRAPHIC DITTO MARK
.char \[*_] \[u3005]    \" IDEOGRAPHIC ITERATION MARK
.char \[;_] \[u3006]    \" IDEOGRAPHIC CLOSING MARK
.char \[0_] \[u3007]    \" IDEOGRAPHIC NUMBER ZERO
.char \[<+] \[u300A]    \" LEFT DOUBLE ANGLE BRACKET
.char \[>+] \[u300B]    \" RIGHT DOUBLE ANGLE BRACKET
.char \[<'] \[u300C]    \" LEFT CORNER BRACKET
.char \[>'] \[u300D]    \" RIGHT CORNER BRACKET
.char \[<"] \[u300E]    \" LEFT WHITE CORNER BRACKET
.char \[>"] \[u300F]    \" RIGHT WHITE CORNER BRACKET
.char \[("] \[u3010]    \" LEFT BLACK LENTICULAR BRACKET
.char \[)"] \[u3011]    \" RIGHT BLACK LENTICULAR BRACKET
.char \[=T] \[u3012]    \" POSTAL MARK
.char \[=_] \[u3013]    \" GETA MARK
.char \[('] \[u3014]    \" LEFT TORTOISE SHELL BRACKET
.char \[)'] \[u3015]    \" RIGHT TORTOISE SHELL BRACKET
.char \[(I] \[u3016]    \" LEFT WHITE LENTICULAR BRACKET
.char \[)I] \[u3017]    \" RIGHT WHITE LENTICULAR BRACKET
.char \[-?] \[u301C]    \" WAVE DASH
.char \[=T:)] \[u3020]    \" POSTAL MARK FACE
.char \[A5] \[u3041]    \" HIRAGANA LETTER SMALL A
.char \[a5] \[u3042]    \" HIRAGANA LETTER A
.char \[I5] \[u3043]    \" HIRAGANA LETTER SMALL I
.char \[i5] \[u3044]    \" HIRAGANA LETTER I
.char \[U5] \[u3045]    \" HIRAGANA LETTER SMALL U
.char \[u5] \[u3046]    \" HIRAGANA LETTER U
.char \[E5] \[u3047]    \" HIRAGANA LETTER SMALL E
.char \[e5] \[u3048]    \" HIRAGANA LETTER E
.char \[O5] \[u3049]    \" HIRAGANA LETTER SMALL O
.char \[o5] \[u304A]    \" HIRAGANA LETTER O
.char \[ka] \[u304B]    \" HIRAGANA LETTER KA
.char \[ga] \[u304C]    \" HIRAGANA LETTER GA
.char \[ki] \[u304D]    \" HIRAGANA LETTER KI
.char \[gi] \[u304E]    \" HIRAGANA LETTER GI
.char \[ku] \[u304F]    \" HIRAGANA LETTER KU
.char \[gu] \[u3050]    \" HIRAGANA LETTER GU
.char \[ke] \[u3051]    \" HIRAGANA LETTER KE
.char \[ge] \[u3052]    \" HIRAGANA LETTER GE
.char \[ko] \[u3053]    \" HIRAGANA LETTER KO
.char \[go] \[u3054]    \" HIRAGANA LETTER GO
.char \[sa] \[u3055]    \" HIRAGANA LETTER SA
.char \[za] \[u3056]    \" HIRAGANA LETTER ZA
.char \[si] \[u3057]    \" HIRAGANA LETTER SI
.char \[zi] \[u3058]    \" HIRAGANA LETTER ZI
.char \[su] \[u3059]    \" HIRAGANA LETTER SU
.char \[zu] \[u305A]    \" HIRAGANA LETTER ZU
.char \[se] \[u305B]    \" HIRAGANA LETTER SE
.char \[ze] \[u305C]    \" HIRAGANA LETTER ZE
.char \[so] \[u305D]    \" HIRAGANA LETTER SO
.char \[zo] \[u305E]    \" HIRAGANA LETTER ZO
.char \[ta] \[u305F]    \" HIRAGANA LETTER TA
.char \[da] \[u3060]    \" HIRAGANA LETTER DA
.char \[ti] \[u3061]    \" HIRAGANA LETTER TI
.char \[di] \[u3062]    \" HIRAGANA LETTER DI
.char \[tU] \[u3063]    \" HIRAGANA LETTER SMALL TU
.char \[tu] \[u3064]    \" HIRAGANA LETTER TU
.char \[du] \[u3065]    \" HIRAGANA LETTER DU
.char \[te] \[u3066]    \" HIRAGANA LETTER TE
.char \[de] \[u3067]    \" HIRAGANA LETTER DE
.char \[to] \[u3068]    \" HIRAGANA LETTER TO
.char \[do] \[u3069]    \" HIRAGANA LETTER DO
.char \[na] \[u306A]    \" HIRAGANA LETTER NA
.char \[ni] \[u306B]    \" HIRAGANA LETTER NI
.char \[nu] \[u306C]    \" HIRAGANA LETTER NU
.char \[ne] \[u306D]    \" HIRAGANA LETTER NE
.char \[no] \[u306E]    \" HIRAGANA LETTER NO
.char \[ha] \[u306F]    \" HIRAGANA LETTER HA
.char \[ba] \[u3070]    \" HIRAGANA LETTER BA
.char \[pa] \[u3071]    \" HIRAGANA LETTER PA
.char \[hi] \[u3072]    \" HIRAGANA LETTER HI
.char \[bi] \[u3073]    \" HIRAGANA LETTER BI
.char \[pi] \[u3074]    \" HIRAGANA LETTER PI
.char \[hu] \[u3075]    \" HIRAGANA LETTER HU
.char \[bu] \[u3076]    \" HIRAGANA LETTER BU
.char \[pu] \[u3077]    \" HIRAGANA LETTER PU
.char \[he] \[u3078]    \" HIRAGANA LETTER HE
.char \[be] \[u3079]    \" HIRAGANA LETTER BE
.char \[pe] \[u307A]    \" HIRAGANA LETTER PE
.char \[ho] \[u307B]    \" HIRAGANA LETTER HO
.char \[bo] \[u307C]    \" HIRAGANA LETTER BO
.char \[po] \[u307D]    \" HIRAGANA LETTER PO
.char \[ma] \[u307E]    \" HIRAGANA LETTER MA
.char \[mi] \[u307F]    \" HIRAGANA LETTER MI
.char \[mu] \[u3080]    \" HIRAGANA LETTER MU
.char \[me] \[u3081]    \" HIRAGANA LETTER ME
.char \[mo] \[u3082]    \" HIRAGANA LETTER MO
.char \[yA] \[u3083]    \" HIRAGANA LETTER SMALL YA
.char \[ya] \[u3084]    \" HIRAGANA LETTER YA
.char \[yU] \[u3085]    \" HIRAGANA LETTER SMALL YU
.char \[yu] \[u3086]    \" HIRAGANA LETTER YU
.char \[yO] \[u3087]    \" HIRAGANA LETTER SMALL YO
.char \[yo] \[u3088]    \" HIRAGANA LETTER YO
.char \[ra] \[u3089]    \" HIRAGANA LETTER RA
.char \[ri] \[u308A]    \" HIRAGANA LETTER RI
.char \[ru] \[u308B]    \" HIRAGANA LETTER RU
.char \[re] \[u308C]    \" HIRAGANA LETTER RE
.char \[ro] \[u308D]    \" HIRAGANA LETTER RO
.char \[wA] \[u308E]    \" HIRAGANA LETTER SMALL WA
.char \[wa] \[u308F]    \" HIRAGANA LETTER WA
.char \[wi] \[u3090]    \" HIRAGANA LETTER WI
.char \[we] \[u3091]    \" HIRAGANA LETTER WE
.char \[wo] \[u3092]    \" HIRAGANA LETTER WO
.char \[n5] \[u3093]    \" HIRAGANA LETTER N
.char \[vu] \[u3094]    \" HIRAGANA LETTER VU
.char \["5] \[u309B]    \" KATAKANA-HIRAGANA VOICED SOUND MARK
.char \[05] \[u309C]    \" KATAKANA-HIRAGANA SEMI-VOICED SOUND MARK
.char \[*5] \[u309D]    \" HIRAGANA ITERATION MARK
.char \[+5] \[u309E]    \" HIRAGANA VOICED ITERATION MARK
.char \[a6] \[u30A1]    \" KATAKANA LETTER SMALL A
.char \[A6] \[u30A2]    \" KATAKANA LETTER A
.char \[i6] \[u30A3]    \" KATAKANA LETTER SMALL I
.char \[I6] \[u30A4]    \" KATAKANA LETTER I
.char \[u6] \[u30A5]    \" KATAKANA LETTER SMALL U
.char \[U6] \[u30A6]    \" KATAKANA LETTER U
.char \[e6] \[u30A7]    \" KATAKANA LETTER SMALL E
.char \[E6] \[u30A8]    \" KATAKANA LETTER E
.char \[o6] \[u30A9]    \" KATAKANA LETTER SMALL O
.char \[O6] \[u30AA]    \" KATAKANA LETTER O
.char \[Ka] \[u30AB]    \" KATAKANA LETTER KA
.char \[Ga] \[u30AC]    \" KATAKANA LETTER GA
.char \[Ki] \[u30AD]    \" KATAKANA LETTER KI
.char \[Gi] \[u30AE]    \" KATAKANA LETTER GI
.char \[Ku] \[u30AF]    \" KATAKANA LETTER KU
.char \[Gu] \[u30B0]    \" KATAKANA LETTER GU
.char \[Ke] \[u30B1]    \" KATAKANA LETTER KE
.char \[Ge] \[u30B2]    \" KATAKANA LETTER GE
.char \[Ko] \[u30B3]    \" KATAKANA LETTER KO
.char \[Go] \[u30B4]    \" KATAKANA LETTER GO
.char \[Sa] \[u30B5]    \" KATAKANA LETTER SA
.char \[Za] \[u30B6]    \" KATAKANA LETTER ZA
.char \[Si] \[u30B7]    \" KATAKANA LETTER SI
.char \[Zi] \[u30B8]    \" KATAKANA LETTER ZI
.char \[Su] \[u30B9]    \" KATAKANA LETTER SU
.char \[Zu] \[u30BA]    \" KATAKANA LETTER ZU
.char \[Se] \[u30BB]    \" KATAKANA LETTER SE
.char \[Ze] \[u30BC]    \" KATAKANA LETTER ZE
.char \[So] \[u30BD]    \" KATAKANA LETTER SO
.char \[Zo] \[u30BE]    \" KATAKANA LETTER ZO
.char \[Ta] \[u30BF]    \" KATAKANA LETTER TA
.char \[Da] \[u30C0]    \" KATAKANA LETTER DA
.char \[Ti] \[u30C1]    \" KATAKANA LETTER TI
.char \[Di] \[u30C2]    \" KATAKANA LETTER DI
.char \[TU] \[u30C3]    \" KATAKANA LETTER SMALL TU
.char \[Tu] \[u30C4]    \" KATAKANA LETTER TU
.char \[Du] \[u30C5]    \" KATAKANA LETTER DU
.char \[Te] \[u30C6]    \" KATAKANA LETTER TE
.char \[De] \[u30C7]    \" KATAKANA LETTER DE
.char \[To] \[u30C8]    \" KATAKANA LETTER TO
.char \[Do] \[u30C9]    \" KATAKANA LETTER DO
.char \[Na] \[u30CA]    \" KATAKANA LETTER NA
.char \[Ni] \[u30CB]    \" KATAKANA LETTER NI
.char \[Nu] \[u30CC]    \" KATAKANA LETTER NU
.char \[Ne] \[u30CD]    \" KATAKANA LETTER NE
.char \[No] \[u30CE]    \" KATAKANA LETTER NO
.char \[Ha] \[u30CF]    \" KATAKANA LETTER HA
.char \[Ba] \[u30D0]    \" KATAKANA LETTER BA
.char \[Pa] \[u30D1]    \" KATAKANA LETTER PA
.char \[Hi] \[u30D2]    \" KATAKANA LETTER HI
.char \[Bi] \[u30D3]    \" KATAKANA LETTER BI
.char \[Pi] \[u30D4]    \" KATAKANA LETTER PI
.char \[Hu] \[u30D5]    \" KATAKANA LETTER HU
.char \[Bu] \[u30D6]    \" KATAKANA LETTER BU
.char \[Pu] \[u30D7]    \" KATAKANA LETTER PU
.char \[He] \[u30D8]    \" KATAKANA LETTER HE
.char \[Be] \[u30D9]    \" KATAKANA LETTER BE
.char \[Pe] \[u30DA]    \" KATAKANA LETTER PE
.char \[Ho] \[u30DB]    \" KATAKANA LETTER HO
.char \[Bo] \[u30DC]    \" KATAKANA LETTER BO
.char \[Po] \[u30DD]    \" KATAKANA LETTER PO
.char \[Ma] \[u30DE]    \" KATAKANA LETTER MA
.char \[Mi] \[u30DF]    \" KATAKANA LETTER MI
.char \[Mu] \[u30E0]    \" KATAKANA LETTER MU
.char \[Me] \[u30E1]    \" KATAKANA LETTER ME
.char \[Mo] \[u30E2]    \" KATAKANA LETTER MO
.char \[YA] \[u30E3]    \" KATAKANA LETTER SMALL YA
.char \[Ya] \[u30E4]    \" KATAKANA LETTER YA
.char \[YU] \[u30E5]    \" KATAKANA LETTER SMALL YU
.char \[Yu] \[u30E6]    \" KATAKANA LETTER YU
.char \[YO] \[u30E7]    \" KATAKANA LETTER SMALL YO
.char \[Yo] \[u30E8]    \" KATAKANA LETTER YO
.char \[Ra] \[u30E9]    \" KATAKANA LETTER RA
.char \[Ri] \[u30EA]    \" KATAKANA LETTER RI
.char \[Ru] \[u30EB]    \" KATAKANA LETTER RU
.char \[Re] \[u30EC]    \" KATAKANA LETTER RE
.char \[Ro] \[u30ED]    \" KATAKANA LETTER RO
.char \[WA] \[u30EE]    \" KATAKANA LETTER SMALL WA
.char \[Wa] \[u30EF]    \" KATAKANA LETTER WA
.char \[Wi] \[u30F0]    \" KATAKANA LETTER WI
.char \[We] \[u30F1]    \" KATAKANA LETTER WE
.char \[Wo] \[u30F2]    \" KATAKANA LETTER WO
.char \[N6] \[u30F3]    \" KATAKANA LETTER N
.char \[Vu] \[u30F4]    \" KATAKANA LETTER VU
.char \[KA] \[u30F5]    \" KATAKANA LETTER SMALL KA
.char \[KE] \[u30F6]    \" KATAKANA LETTER SMALL KE
.char \[Va] \[u30F7]    \" KATAKANA LETTER VA
.char \[Vi] \[u30F8]    \" KATAKANA LETTER VI
.char \[Ve] \[u30F9]    \" KATAKANA LETTER VE
.char \[Vo] \[u30FA]    \" KATAKANA LETTER VO
.char \[.6] \[u30FB]    \" KATAKANA MIDDLE DOT
.char \[-6] \[u30FC]    \" KATAKANA-HIRAGANA PROLONGED SOUND MARK
.char \[*6] \[u30FD]    \" KATAKANA ITERATION MARK
.char \[+6] \[u30FE]    \" KATAKANA VOICED ITERATION MARK
.char \[b4] \[u3105]    \" BOPOMOFO LETTER B
.char \[p4] \[u3106]    \" BOPOMOFO LETTER P
.char \[m4] \[u3107]    \" BOPOMOFO LETTER M
.char \[f4] \[u3108]    \" BOPOMOFO LETTER F
.char \[d4] \[u3109]    \" BOPOMOFO LETTER D
.char \[t4] \[u310A]    \" BOPOMOFO LETTER T
.char \[n4] \[u310B]    \" BOPOMOFO LETTER N
.char \[l4] \[u310C]    \" BOPOMOFO LETTER L
.char \[g4] \[u310D]    \" BOPOMOFO LETTER G
.char \[k4] \[u310E]    \" BOPOMOFO LETTER K
.char \[h4] \[u310F]    \" BOPOMOFO LETTER H
.char \[j4] \[u3110]    \" BOPOMOFO LETTER J
.char \[q4] \[u3111]    \" BOPOMOFO LETTER Q
.char \[x4] \[u3112]    \" BOPOMOFO LETTER X
.char \[zh] \[u3113]    \" BOPOMOFO LETTER ZH
.char \[ch] \[u3114]    \" BOPOMOFO LETTER CH
.char \[sh] \[u3115]    \" BOPOMOFO LETTER SH
.char \[r4] \[u3116]    \" BOPOMOFO LETTER R
.char \[z4] \[u3117]    \" BOPOMOFO LETTER Z
.char \[c4] \[u3118]    \" BOPOMOFO LETTER C
.char \[s4] \[u3119]    \" BOPOMOFO LETTER S
.char \[a4] \[u311A]    \" BOPOMOFO LETTER A
.char \[o4] \[u311B]    \" BOPOMOFO LETTER O
.char \[e4] \[u311C]    \" BOPOMOFO LETTER E
.char \[eh4] \[u311D]    \" BOPOMOFO LETTER EH
.char \[ai] \[u311E]    \" BOPOMOFO LETTER AI
.char \[ei] \[u311F]    \" BOPOMOFO LETTER EI
.char \[au] \[u3120]    \" BOPOMOFO LETTER AU
.char \[ou] \[u3121]    \" BOPOMOFO LETTER OU
.char \[an] \[u3122]    \" BOPOMOFO LETTER AN
.char \[en] \[u3123]    \" BOPOMOFO LETTER EN
.char \[aN] \[u3124]    \" BOPOMOFO LETTER ANG
.char \[eN] \[u3125]    \" BOPOMOFO LETTER ENG
.char \[er] \[u3126]    \" BOPOMOFO LETTER ER
.char \[i4] \[u3127]    \" BOPOMOFO LETTER I
.char \[u4] \[u3128]    \" BOPOMOFO LETTER U
.char \[iu] \[u3129]    \" BOPOMOFO LETTER IU
.char \[v4] \[u312A]    \" BOPOMOFO LETTER V
.char \[nG] \[u312B]    \" BOPOMOFO LETTER NG
.char \[gn] \[u312C]    \" BOPOMOFO LETTER GN
.char \[(JU)] \[u321C]    \" PARENTHESIZED HANGUL JU
.char \[1c] \[u3220]    \" PARENTHESIZED IDEOGRAPH ONE
.char \[2c] \[u3221]    \" PARENTHESIZED IDEOGRAPH TWO
.char \[3c] \[u3222]    \" PARENTHESIZED IDEOGRAPH THREE
.char \[4c] \[u3223]    \" PARENTHESIZED IDEOGRAPH FOUR
.char \[5c] \[u3224]    \" PARENTHESIZED IDEOGRAPH FIVE
.char \[6c] \[u3225]    \" PARENTHESIZED IDEOGRAPH SIX
.char \[7c] \[u3226]    \" PARENTHESIZED IDEOGRAPH SEVEN
.char \[8c] \[u3227]    \" PARENTHESIZED IDEOGRAPH EIGHT
.char \[9c] \[u3228]    \" PARENTHESIZED IDEOGRAPH NINE
.char \[10c] \[u3229]    \" PARENTHESIZED IDEOGRAPH TEN
.char \[KSC] \[u327F]    \" KOREAN STANDARD SYMBOL
.char \[ff] \[uFB00]    \" LATIN SMALL LIGATURE FF
.char \[fi] \[uFB01]    \" LATIN SMALL LIGATURE FI
.char \[fl] \[uFB02]    \" LATIN SMALL LIGATURE FL
.char \[ffi] \[uFB03]    \" LATIN SMALL LIGATURE FFI
.char \[ffl] \[uFB04]    \" LATIN SMALL LIGATURE FFL
.char \[ft] \[uFB05]    \" LATIN SMALL LIGATURE FT
.char \[st] \[uFB06]    \" LATIN SMALL LIGATURE ST
.char \[3+;] \[uFE7D]    \" ARABIC SHADDA MEDIAL FORM
.char \[aM.] \[uFE82]    \" ARABIC LETTER ALEF WITH MADDA ABOVE FINAL FORM
.char \[aH.] \[uFE84]    \" ARABIC LETTER ALEF WITH HAMZA ABOVE FINAL FORM
.char \[a+-] \[uFE8D]    \" ARABIC LETTER ALEF ISOLATED FORM
.char \[a+.] \[uFE8E]    \" ARABIC LETTER ALEF FINAL FORM
.char \[b+-] \[uFE8F]    \" ARABIC LETTER BEH ISOLATED FORM
.char \[b+,] \[uFE90]    \" ARABIC LETTER BEH INITIAL FORM
.char \[b+;] \[uFE91]    \" ARABIC LETTER BEH MEDIAL FORM
.char \[b+.] \[uFE92]    \" ARABIC LETTER BEH FINAL FORM
.char \[tm-] \[uFE93]    \" ARABIC LETTER TEH MARBUTA ISOLATED FORM
.char \[tm.] \[uFE94]    \" ARABIC LETTER TEH MARBUTA FINAL FORM
.char \[t+-] \[uFE95]    \" ARABIC LETTER TEH ISOLATED FORM
.char \[t+,] \[uFE96]    \" ARABIC LETTER TEH INITIAL FORM
.char \[t+;] \[uFE97]    \" ARABIC LETTER TEH MEDIAL FORM
.char \[t+.] \[uFE98]    \" ARABIC LETTER TEH FINAL FORM
.char \[tk-] \[uFE99]    \" ARABIC LETTER THEH ISOLATED FORM
.char \[tk,] \[uFE9A]    \" ARABIC LETTER THEH INITIAL FORM
.char \[tk;] \[uFE9B]    \" ARABIC LETTER THEH MEDIAL FORM
.char \[tk.] \[uFE9C]    \" ARABIC LETTER THEH FINAL FORM
.char \[g+-] \[uFE9D]    \" ARABIC LETTER JEEM ISOLATED FORM
.char \[g+,] \[uFE9E]    \" ARABIC LETTER JEEM INITIAL FORM
.char \[g+;] \[uFE9F]    \" ARABIC LETTER JEEM MEDIAL FORM
.char \[g+.] \[uFEA0]    \" ARABIC LETTER JEEM FINAL FORM
.char \[hk-] \[uFEA1]    \" ARABIC LETTER HAH ISOLATED FORM
.char \[hk,] \[uFEA2]    \" ARABIC LETTER HAH INITIAL FORM
.char \[hk;] \[uFEA3]    \" ARABIC LETTER HAH MEDIAL FORM
.char \[hk.] \[uFEA4]    \" ARABIC LETTER HAH FINAL FORM
.char \[x+-] \[uFEA5]    \" ARABIC LETTER KHAH ISOLATED FORM
.char \[x+,] \[uFEA6]    \" ARABIC LETTER KHAH INITIAL FORM
.char \[x+;] \[uFEA7]    \" ARABIC LETTER KHAH MEDIAL FORM
.char \[x+.] \[uFEA8]    \" ARABIC LETTER KHAH FINAL FORM
.char \[d+-] \[uFEA9]    \" ARABIC LETTER DAL ISOLATED FORM
.char \[d+.] \[uFEAA]    \" ARABIC LETTER DAL FINAL FORM
.char \[dk-] \[uFEAB]    \" ARABIC LETTER THAL ISOLATED FORM
.char \[dk.] \[uFEAC]    \" ARABIC LETTER THAL FINAL FORM
.char \[r+-] \[uFEAD]    \" ARABIC LETTER REH ISOLATED FORM
.char \[r+.] \[uFEAE]    \" ARABIC LETTER REH FINAL FORM
.char \[z+-] \[uFEAF]    \" ARABIC LETTER ZAIN ISOLATED FORM
.char \[z+.] \[uFEB0]    \" ARABIC LETTER ZAIN FINAL FORM
.char \[s+-] \[uFEB1]    \" ARABIC LETTER SEEN ISOLATED FORM
.char \[s+,] \[uFEB2]    \" ARABIC LETTER SEEN INITIAL FORM
.char \[s+;] \[uFEB3]    \" ARABIC LETTER SEEN MEDIAL FORM
.char \[s+.] \[uFEB4]    \" ARABIC LETTER SEEN FINAL FORM
.char \[sn-] \[uFEB5]    \" ARABIC LETTER SHEEN ISOLATED FORM
.char \[sn,] \[uFEB6]    \" ARABIC LETTER SHEEN INITIAL FORM
.char \[sn;] \[uFEB7]    \" ARABIC LETTER SHEEN MEDIAL FORM
.char \[sn.] \[uFEB8]    \" ARABIC LETTER SHEEN FINAL FORM
.char \[c+-] \[uFEB9]    \" ARABIC LETTER SAD ISOLATED FORM
.char \[c+,] \[uFEBA]    \" ARABIC LETTER SAD INITIAL FORM
.char \[c+;] \[uFEBB]    \" ARABIC LETTER SAD MEDIAL FORM
.char \[c+.] \[uFEBC]    \" ARABIC LETTER SAD FINAL FORM
.char \[dd-] \[uFEBD]    \" ARABIC LETTER DAD ISOLATED FORM
.char \[dd,] \[uFEBE]    \" ARABIC LETTER DAD INITIAL FORM
.char \[dd;] \[uFEBF]    \" ARABIC LETTER DAD MEDIAL FORM
.char \[dd.] \[uFEC0]    \" ARABIC LETTER DAD FINAL FORM
.char \[tj-] \[uFEC1]    \" ARABIC LETTER TAH ISOLATED FORM
.char \[tj,] \[uFEC2]    \" ARABIC LETTER TAH INITIAL FORM
.char \[tj;] \[uFEC3]    \" ARABIC LETTER TAH MEDIAL FORM
.char \[tj.] \[uFEC4]    \" ARABIC LETTER TAH FINAL FORM
.char \[zH-] \[uFEC5]    \" ARABIC LETTER ZAH ISOLATED FORM
.char \[zH,] \[uFEC6]    \" ARABIC LETTER ZAH INITIAL FORM
.char \[zH;] \[uFEC7]    \" ARABIC LETTER ZAH MEDIAL FORM
.char \[zH.] \[uFEC8]    \" ARABIC LETTER ZAH FINAL FORM
.char \[e+-] \[uFEC9]    \" ARABIC LETTER AIN ISOLATED FORM
.char \[e+,] \[uFECA]    \" ARABIC LETTER AIN INITIAL FORM
.char \[e+;] \[uFECB]    \" ARABIC LETTER AIN MEDIAL FORM
.char \[e+.] \[uFECC]    \" ARABIC LETTER AIN FINAL FORM
.char \[i+-] \[uFECD]    \" ARABIC LETTER GHAIN ISOLATED FORM
.char \[i+,] \[uFECE]    \" ARABIC LETTER GHAIN INITIAL FORM
.char \[i+;] \[uFECF]    \" ARABIC LETTER GHAIN MEDIAL FORM
.char \[i+.] \[uFED0]    \" ARABIC LETTER GHAIN FINAL FORM
.char \[f+-] \[uFED1]    \" ARABIC LETTER FEH ISOLATED FORM
.char \[f+,] \[uFED2]    \" ARABIC LETTER FEH INITIAL FORM
.char \[f+;] \[uFED3]    \" ARABIC LETTER FEH MEDIAL FORM
.char \[f+.] \[uFED4]    \" ARABIC LETTER FEH FINAL FORM
.char \[q+-] \[uFED5]    \" ARABIC LETTER QAF ISOLATED FORM
.char \[q+,] \[uFED6]    \" ARABIC LETTER QAF INITIAL FORM
.char \[q+;] \[uFED7]    \" ARABIC LETTER QAF MEDIAL FORM
.char \[q+.] \[uFED8]    \" ARABIC LETTER QAF FINAL FORM
.char \[k+-] \[uFED9]    \" ARABIC LETTER KAF ISOLATED FORM
.char \[k+,] \[uFEDA]    \" ARABIC LETTER KAF INITIAL FORM
.char \[k+;] \[uFEDB]    \" ARABIC LETTER KAF MEDIAL FORM
.char \[k+.] \[uFEDC]    \" ARABIC LETTER KAF FINAL FORM
.char \[l+-] \[uFEDD]    \" ARABIC LETTER LAM ISOLATED FORM
.char \[l+,] \[uFEDE]    \" ARABIC LETTER LAM INITIAL FORM
.char \[l+;] \[uFEDF]    \" ARABIC LETTER LAM MEDIAL FORM
.char \[l+.] \[uFEE0]    \" ARABIC LETTER LAM FINAL FORM
.char \[m+-] \[uFEE1]    \" ARABIC LETTER MEEM ISOLATED FORM
.char \[m+,] \[uFEE2]    \" ARABIC LETTER MEEM INITIAL FORM
.char \[m+;] \[uFEE3]    \" ARABIC LETTER MEEM MEDIAL FORM
.char \[m+.] \[uFEE4]    \" ARABIC LETTER MEEM FINAL FORM
.char \[n+-] \[uFEE5]    \" ARABIC LETTER NOON ISOLATED FORM
.char \[n+,] \[uFEE6]    \" ARABIC LETTER NOON INITIAL FORM
.char \[n+;] \[uFEE7]    \" ARABIC LETTER NOON MEDIAL FORM
.char \[n+.] \[uFEE8]    \" ARABIC LETTER NOON FINAL FORM
.char \[h+-] \[uFEE9]    \" ARABIC LETTER HEH ISOLATED FORM
.char \[h+,] \[uFEEA]    \" ARABIC LETTER HEH INITIAL FORM
.char \[h+;] \[uFEEB]    \" ARABIC LETTER HEH MEDIAL FORM
.char \[h+.] \[uFEEC]    \" ARABIC LETTER HEH FINAL FORM
.char \[w+-] \[uFEED]    \" ARABIC LETTER WAW ISOLATED FORM
.char \[w+.] \[uFEEE]    \" ARABIC LETTER WAW FINAL FORM
.char \[j+-] \[uFEEF]    \" ARABIC LETTER ALEF MAKSURA ISOLATED FORM
.char \[j+.] \[uFEF0]    \" ARABIC LETTER ALEF MAKSURA FINAL FORM
.char \[y+-] \[uFEF1]    \" ARABIC LETTER YEH ISOLATED FORM
.char \[y+,] \[uFEF2]    \" ARABIC LETTER YEH INITIAL FORM
.char \[y+;] \[uFEF3]    \" ARABIC LETTER YEH MEDIAL FORM
.char \[y+.] \[uFEF4]    \" ARABIC LETTER YEH FINAL FORM
.char \[lM-] \[uFEF5]    \" ARABIC LIGATURE LAM WITH ALEF WITH MADDA ABOVE ISOLATED FORM
.char \[lM.] \[uFEF6]    \" ARABIC LIGATURE LAM WITH ALEF WITH MADDA ABOVE FINAL FORM
.char \[lH-] \[uFEF7]    \" ARABIC LIGATURE LAM WITH ALEF WITH HAMZA ABOVE ISOLATED FORM
.char \[lH.] \[uFEF8]    \" ARABIC LIGATURE LAM WITH ALEF WITH HAMZA ABOVE FINAL FORM
.char \[lh-] \[uFEF9]    \" ARABIC LIGATURE LAM WITH ALEF WITH HAMZA BELOW ISOLATED FORM
.char \[lh.] \[uFEFA]    \" ARABIC LIGATURE LAM WITH ALEF WITH HAMZA BELOW FINAL FORM
.char \[la-] \[uFEFB]    \" ARABIC LIGATURE LAM WITH ALEF ISOLATED FORM
.char \[la.] \[uFEFC]    \" ARABIC LIGATURE LAM WITH ALEF FINAL FORM
