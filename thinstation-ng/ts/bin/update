#!/bin/bash
# Description: Thinstation Utility to use a .dna to update a Thinstation Package
# URL: http://www.thinstation.org
# Maintainer: <PERSON> (don cupp jr at ya hoo dot com)

IFS=$'\n\t'

showhelp()
{
	echo -e "This utility is designed to use a .dna file to update a Thinstation Package"
	echo -e "It should be invoked as   'update <tspackage>' \n"
	echo -e "Although it is in the executable path and can be called from anywhere,"
	echo -e "it will always go to the working directory for Thinstation, which is defined"
	echo -e "in the environment variable \$TSWRKNG. This variable can be adjusted for a "
	echo -e "single session or permanently by editing /ts/TS_ENV. \n"
	echo -e "This script will use the .dna file in the root of the package dir for the "
	echo -e "package specified at the time of invocation."
	echo -e "\n\nOptions: \n -c = Clean Only \n -d = Do a Dry Run \n -h = Give this Help \n -b = Update boot-images \n -t = Update tools"
}

clean()
{
	if [ "$versioned" == "0" ] ; then
		if [ -e $DEST/$loc/$file ] ; then
			cmdlist="${cmdlist}rm $DEST/$loc/$file\n"
		fi
	else
		version $file
		if [ "$suffix" != "" ] ; then
			rmfile=`find $DEST/$loc -mindepth 1 -maxdepth 1 -type f|grep -e ${prefix}[0-9] |grep -e $suffix$`
		else
			rmfile=`find $DEST/$loc -mindepth 1 -maxdepth 1 -type f|grep -e ${prefix}[0-9] |grep -e [0-9]$`
		fi
		if [ "$rmfile" != "" ] ; then
			cmdlist="${cmdlist}rm $rmfile\n"
		fi
	fi
	for i in ${link[@]} ; do
		if [ -h $DEST/$i ] ; then
			cmdlist="${cmdlist}rm -f $DEST/$i\n"
		fi
	done
}

clean_kernel()
{
#	cmdlist="${cmdlist}rm -rf kernel/vmlinuz*\n"
#	cmdlist="${cmdlist}rm -rf kernel/System.map*\n"
#	cmdlist="${cmdlist}rm -rf kernel/modules*\n"
#	cmdlist="${cmdlist}rm -rf kernel/firmware-*\n"
#	cmdlist="${cmdlist}rm -rf kernel/config*\n"
#	find ./kernel -type l |xargs rm -rf
#        cmdlist="${cmdlist}find ./kernel -mindepth 1 -maxdepth 1 -type l -delete \n"
#        cmdlist="${cmdlist}find ./kernel/lib -mindepth 1 -maxdepth 1 -type l -delete \n"
	echo "clean_kernel deprecated"
}

isversioned()
{
	if [ "$head" == "0" ] && [ "$tail" == "0" ] ; then
		versioned="0"
	else
		versioned="1"
	fi
}

filedna()
{
	srcpkg="`echo $1 |cut -d , -f1`"
	file="`echo $1 |cut -d , -f2`"
	head="`echo $1 |cut -d , -f3`"
	tail="`echo $1 |cut -d , -f4`"
	loc="`echo $1 |cut -d , -f5`"
	deletable="`echo $1 |cut -d , -f6`"
	link[0]="`echo $1 |cut -d , -f7`"
	link[1]="`echo $1 |cut -d , -f8`"
	link[2]="`echo $1 |cut -d , -f9`"
	link[3]="`echo $1 |cut -d , -f10`"
	link[4]="`echo $1 |cut -d , -f11`"
	link[5]="`echo $1 |cut -d , -f12`"
	link[6]="`echo $1 |cut -d , -f13`"
	link[7]="`echo $1 |cut -d , -f14`"
	link[8]="`echo $1 |cut -d , -f15`"
}

empty_file()
{
        if [ "$loc" == "" ] ; then
		cmdlist="${cmdlist}touch $DEST/$file\n"
        elif    [ ! -d $DEST/$loc ] || $DRYRUN ; then
                cmdlist="${cmdlist}mkdir -p $DEST/$loc\n"
		cmdlist="${cmdlist}touch $DEST/$loc/$file\n"
        else
		cmdlist="${cmdlist}touch $DEST/$loc/$file\n"
        fi
}

getpackageloc()
{
	if [ -z "`rpm -q $srcpkg >/dev/null 2>&1 |grep -v 'is not installed'`" ]; then
		dnf install --setopt=keepcache=1 -y $srcpkg >/dev/null 2>&1
		pkgver="`rpm -q --queryformat '%{VERSION}-%{RELEASE}\n' $srcpkg`"
	fi
}

count_files()
{
	filecount=0
	for i in $lfile ; do
		filecount=$(( $filecount + `echo $i |grep -c -e $ffile`))
	done
}

updatefiles()
{
	lfile="`rpm -qlv $srcpkg |grep -Ev 'drwx|lrwx' |awk '{print $9}'|grep -e /$file$`"
	if [ -n "$lfile" ] ; then
		ffile="$file"
	elif	[ "${#link[@]}" -lt "1" ] && [ "$versioned" == "1" ] ; then
		version $file
		lfile="`rpm -qlv $srcpkg |grep -Ev 'drwx|lrwx' |awk '{print $9}' |grep -e /${prefix}[0-9] |grep -e $suffix$`"
		ffile=${lfile##*/}
	else
		slink="`rpm -qlv $srcpkg|grep -e lrwx |awk '{print $9}'|grep -e $file$`"
		if [ -n "$slink" ];then
			lfile="`readlink -f $slink`"
			ffile=${lfile##*/}
		else
			echo "Error processing $file"
		fi
	fi
	transdepthvar=$loc
	count_files
	unset sloc
	while [ "$filecount" -gt "1" ]; do
		sloc=`basename $transdepthvar`/$sloc
		transdepthvar=`dirname $transdepthvar`
		lfile="`rpm -ql $srcpkg|grep -e $sloc$ffile$`"
		count_files
	done
	if [ "$versioned" == "1" ]; then
		dfile="."
	else
		dfile="$file"
	fi
	if [ -n "$lfile" ]; then
		if [ "$loc" == "" ] ; then
			cmdlist="${cmdlist}ln $lfile $DEST/$dfile\n"
		elif	[ ! -d $DEST/$loc ] || $DRYRUN ; then
			cmdlist="${cmdlist}mkdir -p $DEST/$loc\n"
			cmdlist="${cmdlist}ln $lfile $DEST/$loc/$dfile\n"
		else
			cmdlist="${cmdlist}ln $lfile $DEST/$loc/$dfile\n"
		fi
	else
		echo "Error processing $file"
	fi
}

update_kernel()
{
#	cmdlist="${cmdlist}cp -rf /ts/components/$kernel/kernel .\n"
#	cmdlist="${cmdlist}cp -rf /ts/components/$kernel/utils .\n"
#        cmdlist=${cmdlist}'for moddir in `ls /lib/modules`; do ln -sf /lib/modules/$moddir kernel/modules-$moddir ; done\n'
#	cmdlist=${cmdlist}'for kern_obj in `ls /boot`; do ln -sf /boot/$kern_obj kernel/$kern_obj ; done\n'
	echo "update_kernel deprecated"
}

finish_tools()
{
	if [ "`file $DEST/$loc/$file |grep -c -E ELF`" -gt "0" ] && [ "$file" != "smbpasswd" ]; then
		cmdlist="${cmdlist}patchelf --set-rpath ./packages/lib $DEST/$loc/$file\n"
	fi
}

makelinks()
{
	index="0"
	for i in ${link[@]} ; do
		lndepth=`echo "${link[$index]}" |grep -c -e /`
		linkname=`basename ${link[$index]}`
		if [ "$lndepth" -gt "0" ] ; then
			linkloc=`dirname ${link[$index]}`
			if [ ! -d $DEST/$linkloc ] || $DRYRUN ; then
			    cmdlist="${cmdlist}mkdir -p $DEST/$linkloc\n"
			fi
		else
			linkloc=""
		fi
		if [ "$loc" == "$linkloc" ] ; then
			cmdlist="${cmdlist}ln -sf $ffile $DEST/${link[$index]}\n"
		else
			cmdlist="${cmdlist}ln -sf /$loc/$ffile $DEST/${link[$index]}\n"
		fi
		let index=index+1
	done
}

version()
{
	local total=`echo $1 |wc -m`
	local hhead=$(( $head + 1 ))
	local ttail=""
	if [ "$head" == "0" -a "$tail" == "0" ] ; then
		prefix=$1
		version=""
		suffix=""
	else
		prefix=`echo $1|cut -c -$head`
		if [ "$tail" == "0" ] ;	then
			version=`echo $1|cut -c $hhead-`
			suffix=""
		else
			ttail=$(( $total - $tail - 1 ))
			version=`echo $1|cut -c $hhead-$ttail`
			ttail=$(( $total - $tail ))
			suffix=`echo $1|cut -c $ttail-`
		fi
 	fi
}

init()
{
	CLEAN=false
	DRYRUN=false
	HELP=false
	KERNEL=false
	OTHER=false
	TOOLS=false
	IMAGES=false
	unset PACKAGE
	until [ -z $1 ] ; do
		case $1 in
			-c)	CLEAN=true ;;
			-d)	DRYRUN=true ;;
			-h)	HELP=true ;;
			-k)	KERNEL=true ; OTHER=true ;;
			-t)	TOOLS=true ; OTHER=true ;;
			-b)	IMAGES=true ; OTHER=true ;;
			*)	PACKAGE="$1" ;;
		esac
		shift
	done
	if $HELP || [ -z $PACKAGE ] && ! $OTHER ; then
		showhelp
		exit
	elif	$KERNEL ; then
		if [ -n "$PACKAGE" ]; then
			kernel=kernel_$PACKAGE
		else
			kernel=kernel
		fi
		DEST=$TSWRKNG
	elif	$TOOLS ; then
		DEST="$TSWRKNG/utils/tools"
		FILES="`cat $DEST/.dna`"
	elif	$IMAGES ; then
		DEST="$TSWRKNG/boot-images"
		FILES="`cat $DEST/.dna`"
	else
		for i in $TSWRKNG $XTRWRKNG; do
			if [ -d $i/packages/$PACKAGE ]; then
				DEST="$i/packages/$PACKAGE"
			fi
		done
		if [ -z "$DEST" ]; then
			echo -e "\nThe package '$PACKAGE' was not found."
			showhelp
			exit
		elif [ ! -e $DEST/.dna ]; then
			echo -e "\nNo .dna exists for package '$PACKAGE'."
			exit
		else
			FILES="`cat $DEST/.dna`"
		fi
	fi
}

main()
{
	cd $TSWRKNG
	if $KERNEL ; then
		clean_kernel
		if $CLEAN ; then
			update_kernel
		fi
	else
		if [ -e $DEST/build/installed ] && grep -qe repackage $DEST/build/install; then
			$DEST/build/remove
			reinstall=true
		else
			reinstall=false
		fi
		for i in $FILES ; do
			filedna $i
			if [ "$deletable" == "0" ] ; then
				echo "not deletable" >/dev/null
			else
				isversioned
				clean
				if ! $CLEAN ; then
					if [ "$srcpkg" == "EMPTY" ] ; then
						empty_file
					else
						getpackageloc
						updatefiles
					fi
					makelinks
					if $TOOLS ; then
						finish_tools
					fi
				fi
			fi
		done
		if $reinstall && ! $CLEAN; then
			$DEST/build/install
		fi
	fi
	if $DRYRUN ; then
		echo -e $cmdlist
	else
		echo -e $cmdlist >> /tmp/$PACKAGE.dothis
		source /tmp/$PACKAGE.dothis
		rm /tmp/$PACKAGE.dothis
	fi
}

init $@
main
exit
