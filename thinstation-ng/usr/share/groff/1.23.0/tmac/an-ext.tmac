.\" groff extension macros for man(7) package
.\"
.\" Copyright (C) 2007-2022 Free Software Foundation, Inc.
.\"
.\" Written by <PERSON> <<EMAIL>>
.\"            <PERSON> <<EMAIL>>
.\"            <PERSON><PERSON> <PERSON> <<EMAIL>>
.\"
.\" You may freely use, modify and/or distribute this file.
.\"
.\" The code below provides extension macros for the 'man' macro
.\" package.  Care has been taken to make the code portable; groff
.\" extensions are properly hidden so that all troff implementations can
.\" use it without changes.
.\"
.\" With groff, this file is sourced by the 'man' macro package itself.
.\" Man page authors who are concerned about portability might add the
.\" used macros directly to the prologue of the man page(s).
.
.
.\" Convention: Auxiliary macros and registers start with 'm' followed
.\"             by an uppercase letter or digit.
.
.\" Setting the `mG` register to a positive value (e.g., on the command
.\" line) enables usage of macros defined here that have alternative
.\" definitions in the main groff man macro file.  This is for testing.
.\" The logic uses subtraction due to frustrating, AT&T troff-compatible
.\" limitations on the '!' operator.
.
.
.\" Protect against being sourced twice.
.nr mZ +1
.if \n(mZ>1 \
.  nx
.
.\" Define this to your implementation's constant-width typeface.
.ds mC CW
.if n .ds mC R
.
.\" Save the automatic hyphenation mode.
.\"
.\" In AT&T troff, there was no register exposing the hyphenation mode,
.\" and no way to save and restore it.  Set `mH` to a reasonable value
.\" for your implementation and preference.
.de mY
.  ie !\\n(.g \
.    nr mH 14
.  el \
.    do nr mH \\n[.hy] \" groff extension register
..
.
.nr mS 0 \" in a synopsis (SY/YS)?
.nr mE 0 \" in an example (EX/EE)?
.
.
.\" Declare start of command synopsis.  Sets up hanging indentation.
.de SY
.  ie !\\n(mS \{\
.    mY
.    nh
.    nr mS 1
.    nr mA \\n(.j
.    ad l
.    nr mI \\n(.i
.  \}
.  el \{\
.    br
.    ns
.  \}
.
.  nr mT \w'\fB\\$1\fP\ '
.  HP \\n(mTu
.  rr mT
.  B "\\$1"
..
.
.
.\" End of command synopsis.  Restores adjustment.
.de YS
.  in \\n(mIu
.  ad \\n(mA
.  hy \\n(mH
.  rr mA
.  rr mI
.  nr mS 0
..
.
.
.\" Prepare link text for mail/web hyperlinks.  `MT` and `UR` call this.
.de mV
.  ds m1 \\$1\"
..
.
.
.\" Emit hyperlink.  The optional argument supplies trailing punctuation
.\" after link text.  `ME` and `UE` call this.
.de mQ
.  mY
.  nh
<\\*(m1>\\$1
.  hy \\n(mH
..
.
.
.\" Start URL.
.if \n(.g-\n(mG \{\
.de UR
.  mV \\$1
..
.\}
.
.
.\" End URL.
.if \n(.g-\n(mG \{\
.de UE
.  mQ \\$1
..
.\}
.
.
.\" Start email address.
.if \n(.g-\n(mG \{\
.de MT
.  mV \\$1
..
.\}
.
.
.\" End email address.
.if \n(.g-\n(mG \{\
.de ME
.  mQ \\$1
..
.\}
.
.
.\" Set a man page cross reference.
.\" .MR page-topic page-section [trailing-text]
.if \n(.g-\n(mG \{\
.de MR
.  mY
.  nh
.  ie \\n(.$=1 \
.    I \\$1
.  el \
.    IR \\$1 (\\$2)\\$3
.  hy \\n(mH
..
.\}
.
.
.\" Continuation line for .TP header.
.de TQ
.  br
.  ns
.  TP \\$1\" no doublequotes around argument!
..
.
.
.\" Start example.
.if \n(.g-\n(mG \{\
.de EX
.  br
.  if !\\n(mE \{\
.    nr mF \\n(.f
.    nr mP \\n(PD
.    nr PD 1v
.    nf
.    ft \\*(mC
.    nr mE 1
.  \}
..
.\}
.
.
.\" End example.
.if \n(.g-\n(mG \{\
.de EE
.  br
.  if \\n(mE \{\
.    ft \\n(mF
.    nr PD \\n(mP
.    fi
.    nr mE 0
.  \}
..
.\}
.
.
.\" Start display.
.de DS
.  \" XXX to be written
..
.
.
.\" End display.
.de DE
.  \" XXX to be written
..
.
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set filetype=groff textwidth=72:
