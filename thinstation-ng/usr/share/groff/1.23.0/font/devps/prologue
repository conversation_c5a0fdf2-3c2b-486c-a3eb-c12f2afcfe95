%!PS-Adobe-3.0 Resource-ProcSet
/setpacking where{
pop
currentpacking
true setpacking
}if
/grops 120 dict dup begin
% The ASCII code of the space character.
/SC 32 def
/A/show load def
/B{0 SC 3 -1 roll widthshow}bind def
/C{0 exch ashow}bind def
/D{0 exch 0 SC 5 2 roll awidthshow}bind def
/E{0 rmoveto show}bind def
/F{0 rmoveto 0 SC 3 -1 roll widthshow}bind def
/G{0 rmoveto 0 exch ashow}bind def
/H{0 rmoveto 0 exch 0 SC 5 2 roll awidthshow}bind def
/I{0 exch rmoveto show}bind def
/J{0 exch rmoveto 0 SC 3 -1 roll widthshow}bind def
/K{0 exch rmoveto 0 exch ashow}bind def
/L{0 exch rmoveto 0 exch 0 SC 5 2 roll awidthshow}bind def
/M{rmoveto show}bind def
/N{rmoveto 0 SC 3 -1 roll widthshow}bind def
/O{rmoveto 0 exch ashow}bind def
/P{rmoveto 0 exch 0 SC 5 2 roll awidthshow}bind def
/Q{moveto show}bind def
/R{moveto 0 SC 3 -1 roll widthshow}bind def
/S{moveto 0 exch ashow}bind def
/T{moveto 0 exch 0 SC 5 2 roll awidthshow}bind def
% name size font SF -
/SF{
findfont exch
[exch dup 0 exch 0 exch neg 0 0]makefont
dup setfont
[exch/setfont cvx]cvx bind def
}bind def
% name a c d font MF -
/MF{
findfont
[5 2 roll
0 3 1 roll % b
neg 0 0]makefont
dup setfont
[exch/setfont cvx]cvx bind def
}bind def
/level0 0 def
/RES 0 def
/PL 0 def
/LS 0 def
% Enable manual feed.
% MANUAL -
/MANUAL{
statusdict begin/manualfeed true store end
}bind def
% Guess the page length.
% This assumes that the imageable area is vertically centered on the page.
% PLG - length
/PLG{
gsave newpath clippath pathbbox grestore
exch pop add exch pop
}bind def
% BP -
/BP{
/level0 save def
1 setlinecap
1 setlinejoin
DEFS/BPhook known{DEFS begin BPhook end}if
72 RES div dup scale
LS{
90 rotate
}{
0 PL translate
}ifelse
1 -1 scale
}bind def
/EP{
level0 restore
showpage
}def
% centerx centery radius startangle endangle DA -
/DA{
newpath arcn stroke
}bind def
% x y SN - x' y'
% round a position to nearest (pixel + (.25,.25))
/SN{
transform
.25 sub exch .25 sub exch
round .25 add exch round .25 add exch
itransform
}bind def
% endx endy startx starty DL -
% we round the endpoints of the line, so that parallel horizontal
% and vertical lines will appear even
/DL{
SN
moveto
SN
lineto stroke
}bind def
% centerx centery radius DC -
/DC{
newpath 0 360 arc closepath
}bind def
/TM matrix def
%  width height centerx centery DE -
/DE{
TM currentmatrix pop
translate scale newpath 0 0 .5 0 360 arc closepath
TM setmatrix
}bind def
% these are for splines
/RC/rcurveto load def
/RL/rlineto load def
/ST/stroke load def
/MT/moveto load def
/CL/closepath load def
% fill the last path
% r g b Fr -
/Fr{
setrgbcolor fill
}bind def
% c m y k Fk -
/setcmykcolor where{
pop
/Fk{
setcmykcolor fill
}bind def
}if
% g Fg -
/Fg{
setgray fill
}bind def
% fill with the "current color"
/FL/fill load def
/LW/setlinewidth load def
/Cr/setrgbcolor load def
/setcmykcolor where{
pop
/Ck/setcmykcolor load def
}if
/Cg/setgray load def
% new_font_name encoding_vector old_font_name RE -
/RE{
findfont
dup maxlength 1 index/FontName known not{1 add}if dict begin
{
1 index/FID ne
2 index/UniqueID ne
and
{def}{pop pop}ifelse
}forall
/Encoding exch def
dup/FontName exch def
currentdict end definefont pop
}bind def
/DEFS 0 def
% hpos vpos EBEGIN -
/EBEGIN{
moveto
DEFS begin
}bind def
/EEND/end load def
/CNT 0 def
/level1 0 def
% llx lly newwid wid newht ht newllx newlly PBEGIN -
/PBEGIN{
/level1 save def
translate
div 3 1 roll div exch scale
neg exch neg exch translate
% set the graphics state to default values
0 setgray
0 setlinecap
1 setlinewidth
0 setlinejoin
10 setmiterlimit
[]0 setdash
/setstrokeadjust where{
pop
false setstrokeadjust
}if
/setoverprint where{
pop
false setoverprint
}if
newpath
/CNT countdictstack def
userdict begin
/showpage{}def
%
%  Any included setpagedevice should be ignored.
%  See: http://www.w-beer.de/doc/ps/.
%
/setpagedevice{}def
mark
}bind def
/PEND{
cleartomark
countdictstack CNT sub{end}repeat
level1 restore
}bind def
end def
/setpacking where{
pop
setpacking
}if
