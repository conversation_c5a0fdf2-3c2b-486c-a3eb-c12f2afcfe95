.\" startup file for GNU troff
.\"
.\" Use .do for any groff extensions so that this file works with -C.
.
.\" This is tested by pic.
.nr 0p 0
.
.\" Load composite mappings.
.do mso composite.tmac
.
.\" Load generic fallback mappings.
.do mso fallbacks.tmac
.
.\" The groff command defines the .X register if -X was given.
.do ie r .X \
.	do ds troffrc!ps Xps.tmac
.el \
.	do ds troffrc!ps ps.tmac
.do ds troffrc!pdf pdf.tmac
.do ds troffrc!dvi dvi.tmac
.do ds troffrc!X75 X.tmac
.do ds troffrc!X75-12 X.tmac
.do ds troffrc!X100 X.tmac
.do ds troffrc!X100-12 X.tmac
.do ds troffrc!ascii tty.tmac
.do ds troffrc!latin1 tty.tmac
.do ds troffrc!utf8 tty.tmac
.do ds troffrc!cp1047 tty.tmac
.do ds troffrc!lj4 lj4.tmac
.do ds troffrc!lbp lbp.tmac
.do ds troffrc!html html.tmac
.do if d troffrc!\*[.T] \
.	do mso \*[troffrc!\*[.T]]
.do rm \
troffrc!ps \
troffrc!pdf \
troffrc!dvi \
troffrc!X75 \
troffrc!X75-12 \
troffrc!X100 \
troffrc!X100-12 \
troffrc!ascii \
troffrc!latin1 \
troffrc!utf8 \
troffrc!cp1047 \
troffrc!lj4 \
troffrc!lbp \
troffrc!html
.
.\" Test whether we work under EBCDIC and map the no-break space
.\" character accordingly.
.do ie '\[char97]'a' \
.	do tr \[char160]\~
.el \
.	do tr \[char65]\~
.
.\" Set the input localization to English.
.do mso en.tmac
.
.\" Handle paper formats on typesetting devices.
.if t .do mso papersize.tmac
.
.\" Handle Encapsulated PostScript images.
.do mso pspic.tmac
.do mso pdfpic.tmac
.
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set filetype=groff textwidth=72:
