AUDIO_LEVEL=90
MIC_LEVEL=0
SESSION_0_TYPE=xfwm4
SESSION_0_AUTOSTART=on
XFWM4_COMPOSITING=true
XFWM4_RED=0
XFWM4_GREEN=0
XFWM4_BLUE=0
XFWM4_START_POSITION="BOTTOM"
DESKTOP_ICON_SIZE=48
DESKTOP_FONT_SIZE=8
DESKTOP_SHOWTRASH=false
DEKSTOP_SHOWHOME=false
DESKTOP_SHOWREMOVEABLE=true
DESKTOP_SHOWFILESYSTEM=false
GTK_THEME=Adwaita
XFWM4_THEME=Default
PANEL_PAGER=false
PANEL_USER=false
PANEL_REV_CLOCKSYSTRAY=true
ICONS_THEME="Adwaita"
ICONS_CUT_SIZES="256x256 scalable"
XFWM4_START_ICON=ThinStation
XTERM_CMD="xfce4-terminal"
NET_USE=BOTH
NET_USE_DHCP=on
NET_HOSTNAME=ts-*
NET_TELNETD_ENABLED=ON
TIME_ZONE=America/Los_Angeles
NET_TIME_SERVER=us.pool.ntp.org
NET_REMOTE_ACCESS_FROM="0.0.0.0"
TFTP_BLOCKSIZE=1024
BIND_MOUNT0="storage:/mnt/storage"
STORAGE_CONFIG1=/mnt/storage
USB_STORAGE_SYNC=on
LOCALE=en_US
MOUNT_0="LABEL=boot     /boot           auto    x-mount.mkdir,defaults  0       0"
MOUNT_1="LABEL=home     /home           auto    x-mount.mkdir,defaults  0       0"
MOUNT_2="LABEL=swap     swap            swap    defaults        0       0"
SET_RESOLUTION_MULTIMONITOR_EXPAND='mirror'
SET_RESOLUTION_MULTIMONITOR_AUTOSCALE='scale'

