#!/bin/sh

for work in $TSWRKNG $XTRWRKNG; do
  if [ $work = $TSWRKNG ]; then
    root=/ts
  else
    root=$XTRWRKNG
  fi
  gitignore=$root/.gitignore

  echo -n "" > $gitignore

  for i in $root/wind_cache/*; do
    j=`basename $i | sed -e 's/\.turbo$//'`
    cat $i | grep -v "\.dna" | sed -e "s,rm [-f ]*,," -e "s,//,/,g" -e "s,$root,," >> $gitignore
  done

  if [ -e $work/tools/gitignore ]; then
    $work/tools/gitignore
  fi
done

update -c -d -t | sed -e "s,rm [-f ]*,," -e "s,//,/,g" >> /ts/.gitignore

