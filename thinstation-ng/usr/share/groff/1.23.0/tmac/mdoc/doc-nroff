.\" Copyright (c) 1991, 1993
.\"   The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that the following conditions
.\" are met:
.\" 1. Redistributions of source code must retain the above copyright
.\"    notice, this list of conditions and the following disclaimer.
.\" 2. Redistributions in binary form must reproduce the above copyright
.\"    notice, this list of conditions and the following disclaimer in
.\"    the documentation and/or other materials provided with the
.\"    distribution.
.\" 3. [Deleted.  See
.\"     ftp://ftp.cs.berkeley.edu/pub/4bsd/README.Impt.License.Change]
.\" 4. Neither the name of the University nor the names of its
.\"    contributors may be used to endorse or promote products derived
.\"    from this software without specific prior written permission.
.\"
.\" THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS "AS IS"
.\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
.\" TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
.\" PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR
.\" CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
.\" SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
.\" LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
.\" USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
.\" ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
.\" OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
.\" OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
.\" SUCH DAMAGE.
.\"
.\"     @(#)doc-nroff	8.1 (Berkeley) 06/08/93
.
.
.\" nroff devices can't change the type size; this value is notional.
.nr S 10
.
.\" Map monospaced fonts to standard styles.
.ftr CR R
.ftr CI I
.ftr CB B
.ftr CBI BI
.
.\" the 'doc-xx-font' strings must not be empty!
.
.ds doc-page-topic-font \f[I]
.ds doc-page-section-font \f[R]
.ds doc-Ad-font \f[I]
.ds doc-Ar-font \f[I]
.ds doc-Cm-font \f[B]
.ds doc-Em-font \f[I]
.ds doc-Er-font \f[R]
.ds doc-Ev-font \f[R]
.ds doc-Fa-font \f[I]
.ds doc-Fd-font \f[B]
.ds doc-Fl-font \f[B]
.ds doc-Fn-font \f[B]
.ds doc-Ft-font \f[I]
.ds doc-Ic-font \f[B]
.ds doc-Li-font \f[B]
.ds doc-Lk-font \f[R]\"
.ds doc-Me-font \f[B]
.ds doc-Nm-font \f[B]
.ds doc-No-font \f[R]
.ds doc-Pa-font \f[I]
.ds doc-Sh-font \f[\*[HF]]\"
.ds doc-Sy-font \f[B]
.ds doc-Tn-font \f[R]
.ds doc-Va-font \f[I]
.ds doc-Xr-font \f[I]
.
.ds doc-left-parenthesis \f[R](\f[]
.ds doc-right-parenthesis \f[R])\f[]
.ds lp \f[R](\f[]
.ds rp \f[R])\f[]
.ds doc-left-bracket \f[R][\f[]
.ds doc-right-bracket \f[R]]\f[]
.
.\" miscellaneous
.nr doc-paragraph-space 1v
.
.nr doc-digit-width \w'\0\0'u
.nr doc-fixed-width \w'0'
.
.
.\" NS doc-display-vertical global register
.\" NS   vertical space between list elements etc.
.
.nr doc-display-vertical 0
.
.
.\" NS doc-setup-page-layout macro
.\" NS   set up page layout
.\" NS
.\" NS modifies:
.\" NS   doc-display-vertical
.\" NS   doc-line-length
.
.eo
.de doc-setup-page-layout
.  ie r LL \
.    ll \n[LL]u
.  el \
.    ll 78n
.
.  ie r LT \
.    lt \n[LT]u
.  el \
.    lt \n[.l]u
.
.  po 0i
.
.  nr doc-display-vertical 1v
.  nr doc-line-length \n[.l]
..
.ec
.
.ds doc-left-singlequote \[oq]
.ds doc-right-singlequote \[cq]
.
.\" the following strings are 'official'
.ds <= \[<=]
.ds >= \[>=]
.ds aa \[aa]
.ds ga \[ga]
.ds q \[dq]
.ds Lq \[lq]
.ds Rq \[rq]
.ds Ne \[!=]
.ds Le \[<=]
.ds Ge \[>=]
.ds Lt <
.ds Gt >
.ds Pm \[+-]
.ds Na \f[I]NaN\f[]
.ds Ba \f[R]|\f[]
.ds Am &
.
.\" Unicode TTYs have all glyph forms; for other TTY character sets we
.\" need character representations which are different from GNU troff's
.\" standard forms.
.ie '\*[.T]'utf8' \{\
.  ds ua \[ua]
.  ds Pi \[*p]
.  ds If \[if]
.\}
.el \{\
.  ds ua ^
.  ds Pi pi
.  ds If infinity
.\}
.
.
.\" NS doc-get-width macro
.\" NS   computes the width of a string as a multiple of
.\" NS   'doc-fixed-width': '.doc-get-width string'
.\" NS
.\" NS modifies:
.\" NS   doc-width
.
.eo
.de doc-get-width
.  nr doc-width \w'\$1'
.  ie (\n[doc-width] >= \n[doc-fixed-width]) \{\
.    ie (\n[doc-width] % \n[doc-fixed-width]) \
.      nr doc-width ((\n[doc-width] / \n[doc-fixed-width]) + 1)
.    el \
.      nr doc-width (\n[doc-width] / \n[doc-fixed-width])
.  \}
.  el \
.    nr doc-width 0
..
.ec
.
.
.\" NS doc-get-arg-width macro
.\" NS   computes the width of an argument as a multiple of
.\" NS   'doc-fixed-width': '.doc-get-arg-width arg-index'
.\" NS
.\" NS modifies:
.\" NS   doc-width
.
.eo
.de doc-get-arg-width
.  nr doc-width \w'\*[doc-arg\$1]'
.  ie (\n[doc-width] >= \n[doc-fixed-width]) \{\
.    ie (\n[doc-width] % \n[doc-fixed-width]) \
.      nr doc-width ((\n[doc-width] / \n[doc-fixed-width]) + 1)
.    el \
.      nr doc-width (\n[doc-width] / \n[doc-fixed-width])
.  \}
.  el \
.    nr doc-width 0
..
.ec
.
.
.\" NS Ql user macro
.\" NS   quoted literal define
.\" NS
.\" NS modifies:
.\" NS   doc-macro-name
.\" NS   doc-quote-left
.\" NS   doc-quote-right
.\" NS
.\" NS width register 'Ql' set in doc-common
.
.eo
.de Ql
.  if !\n[doc-arg-count] \{\
.    ie \n[.$] \
.      ds doc-macro-name Ql
.    el \
.      tm Usage: .Ql argument ... (#\n[.c])
.  \}
.
.  ds doc-quote-left "\*[doc-left-singlequote]
.  ds doc-quote-right "\*[doc-right-singlequote]
.
.  doc-enclose-string \$@
..
.ec
.
.
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set filetype=groff textwidth=72:
