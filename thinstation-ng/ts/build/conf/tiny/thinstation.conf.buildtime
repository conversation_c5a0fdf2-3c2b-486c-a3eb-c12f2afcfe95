AUDIO_LEVEL=90
MIC_LEVEL=0
SESSION_0_TYPE=sh
SESSION_0_AUTOSTART=on
NET_USE=BOTH
NET_USE_DHCP=on
NET_HOSTNAME=ts-*
NET_FILE_ENABLED=Off
NET_TELNETD_ENABLED=ON
#DEBUGPAUSE=TRUE
TIME_ZONE=America/Los_Angeles
NET_TIME_SERVER=us.pool.ntp.org
NET_REMOTE_ACCESS_FROM="0.0.0.0"
#WIRELESS_ESSID="Example"
#WIRELESS_MODE="managed"
#WIRELESS_WPAKEY="3x@mpl39@559hr@53"
#WIRELESS_DRIVER="wext"
#TFTP_BLOCKSIZE=8192
TFTP_BLOCKSIZE=1024
#BIND_MOUNT0="home:/root"
#BIND_MOUNT1="boot:/boot"
#BIND_MOUNT2="tsdev:/thinstation"
#USB_STORAGE_SYNC=on
#MOUNT_0="LABEL=prstnt   /var/prstnt     auto    X-mount.mkdir,defaults  0       0"
#MOUNT_1="LABEL=log	/var/log        auto    X-mount.mkdir,defaults  0	0"
#SSSD_USER_GROUP="Domain Admins"
