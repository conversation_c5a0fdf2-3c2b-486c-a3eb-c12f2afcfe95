#!/bin/bash
. /etc/thinstation.global
umask 022

mounted()
{
        if [ "`cat /proc/mounts |grep -e $1 -c`" -ne "0" ]; then
                return 0
        else
                return 1
        fi
}

do_unmounts()
{
        for mount in dev/pts dev tmp proc sys ; do
                while mounted /thinstation/$mount ; do
                        umount /thinstation/$mount
                done
        done
}

x_echo()
{
        Xdialog --title "$1" --msgbox "$2" 0 0 2> /dev/null
}

do_update()
{
	rm -f /tmp/update.status
	$XTERM_CMD -T "Update initrd" $ARG script -c "update-initrd -w" /home/<USER>/Desktop/update.log
	STATUS="`cat /tmp/update.status`"
	case $STATUS in
                0) x_echo Update "No Update Needed" ;;
		1) x_echo Update "Update Complete, Click OK to reboot" ;reboot;;
		*) x_echo Update "Something went wrong with the update. Check the log." ;;
	esac
}

toggle_con()
{
	if [ ! -e /thinstation/build/CON ]; then
		touch /thinstation/build/CON
		x_echo "Toggle Console" "Console kernel enabled for Builds"
	else
		rm /thinstation/build/CON
		x_echo "Toggle Console" "Console disabled"
	fi
}

toggle_allmodules()
{
        if [ ! -e /thinstation/build/ALLMODULES ]; then
                touch /thinstation/build/ALLMODULES
                x_echo "Toggle ALLMODULES" "Enabled allmodules for Builds"
        else
                rm /thinstation/build/ALLMODULES
                x_echo "Toggle ALLMODULES" "Disabled allmodules - machine profiles will be used for Builds"
        fi
}

toggle_pxe()
{
	if [ -n "`find /thinstation/build/boot-images/ -type d -perm -o=w -name pxe`" ] ; then
		chmod 755 /thinstation/build/boot-images/pxe
		x_echo "Toggle PXE Read/Write" "PXE Server Write Disabled"
	else
		chmod 777 /thinstation/build/boot-images/pxe
		x_echo "Toggle PXE Read/Write" "PXE Server Write Enabled"
	fi
}

do_build()
{
	cd /thinstation
	$XTERM_CMD -T Build $ARG script -c "./setup-chroot -b -o --autodl --license ACCEPT" /home/<USER>/Desktop/build.log
	chown $TSUSER /home/<USER>/Desktop/build.log
}

make_machine()
{
	if [ -e /thinstation/build/boot-images/pxe/module.list ] ; then
		DIALOG_RESPONSE=`Xdialog --stdout --title "Machine Name" --left --inputbox "What is the model of the machine" 15 55 "Dell-FX170" ` 2>>/dev/null
		if [ "$?" == "0" ] && [ ! -z "$DIALOG_RESPONSE" ] ; then
			cd /thinstation/build
			./mkmachine $DIALOG_RESPONSE
		fi
	else
		x_echo "No Source Files" "Please run hwlister.sh on the target machine first"
	fi
}

do_config()
{
	geany -c /etc/geany-build -s /thinstation/build/build.conf /thinstation/build/thinstation.conf.buildtime /thinstation/build/thinstation.conf.sample
}

do_term()
{
	xfce4-terminal --working-directory=/thinstation -x ./setup-chroot
}

do_reset()
{
	Xdialog --title "Warning: Factory Reset" --default-no --yesno "\
This will wipe out your build env and pull updates from git. \n\
If you have any customizations, they will be lost. You should \n\
consider backing them up before proceding. \n\
\n\
Are you really sure?" 20 100
        retval=$?
        if [ "$retval" -gt "0" ]; then exit $retval ; fi
	cd /thinstation
	while pidof setup-chroot > /dev/null; do
		kill -HUP `pidof setup-chroot`
		sleep .1
	done
	do_unmounts
	$XTERM_CMD -T "Wipe" $ARG rm -rf *
	$XTERM_CMD -T "GIT Pull" $ARG git pull
	$XTERM_CMD -T "GIT Checkout" $ARG git checkout -f
	$XTERM_CMD -T "Setup" $ARG ./setup-chroot -i -a
	x_echo "Done" "You build env has been reset and updated"
}

do_test_cd()
{
        $XTERM_CMD -T Test-CD --working-directory=/thinstation $ARG script -c "./setup-chroot -e bt cd" /home/<USER>/Desktop/test.log
}

do_test_cd_efi()
{
        $XTERM_CMD -T Test-CD-EFI --working-directory=/thinstation $ARG script -c "./setup-chroot -e bt cd-efi" /home/<USER>/Desktop/test.log
}
do_test_hd()
{
        $XTERM_CMD -T Test-HD --working-directory=/thinstation $ARG script -c "./setup-chroot -e bt hd" /home/<USER>/Desktop/test.log
}
do_test_hd_efi()
{
        $XTERM_CMD -T Test-HD-EFI --working-directory=/thinstation $ARG script -c "./setup-chroot -e bt hd-efi" /home/<USER>/Desktop/test.log
}
do_test_net()
{
        cd /thinstation
        $XTERM_CMD -T Test-Network --working-directory=/thinstation $ARG script -c "./setup-chroot -e bt net" /home/<USER>/Desktop/test.log
}
do_test_net_efi()
{
        cd /thinstation
        $XTERM_CMD -T Test-Network --working-directory=/thinstation $ARG script -c "./setup-chroot -e bt net-efi" /home/<USER>/Desktop/test.log
}

terminal_arg()
{
	if [ "`basename $XTERM_CMD`" == "xfce4-terminal" ]; then
		ARG="-x"
	else
		ARG="-e"
	fi
}

terminal_arg

case $1 in
	toggle-con)
		toggle_con
		;;
	toggle-modules)
		toggle_allmodules
		;;
	toggle-pxe)
		toggle_pxe
		;;
	make-machine)
		make_machine
		;;
	build)
		do_build
		;;
	configure)
		do_config
		;;
	reset)
		do_reset
		;;
	update)
		do_update
		;;
	terminal)
		do_term
		;;
	test-cd)
		do_test_cd
		;;
	test-cd-efi)
                do_test_cd_efi
		;;
	test-hd)
                do_test_hd
		;;
	test-hd-efi)
                do_test_hd_efi
		;;
	test-net)
                do_test_net
		;;
        test-net-efi)
		do_test_net_efi
		;;
esac
exit 0
