################################################
### ---     Thinstation Build Config     --- ###
################################################
#
#
# This is Thinstation s basic setup file. In this file you decide which
# hardware, programs and features to be included in the Thinstation
# boot image generated by running "./build".
#
# You can customize/limit the possibilities in the thinstation.conf
# files later.
#
# Anything after a "#" is a comment. You activate "things" by removing
# the "#" and remove "things" by putting a "#" in front of them.
#
# First we define which modules to include. Modules are hardware
# drivers. NEVER include more than necessary - the more modules, the
# longer boot time.

########################################################
### --- Machine Modules to include in boot image --- ###
########################################################
# A machine profile is a set of modules for a specific set of hardware
# components like a thinclient with adapters. It does not include any
# filesystem modules, those need to be selected individually. Making a
# a machine profile is a two step process. First you will need to make
# an allmodules build like "./build --allmodules" with the extensions-x
# package included. Once the client machine is booted with that image
# you can open and xterm or telnet in and run "hwlister.sh". hwlister.sh
# will compile some lists and attempt to post them to the pxe server assigned
# to the client. If the pxe server is this workstation and you are hosting
# files directly out of this build env, you can then run
# "./mkmachine SOME-MODEL" and the files will be put into the machine folder
# under that model name. Now all you have to do is add a line to this file
# like "machine SOME-MODEL" and you will no longer have to make
# allmodules builds. If this is not the pxe server, you will have to
# move stuff around on your own, but the most important files are
# modules.list and firmware.list.

################################################
### --- Modules to include in boot image --- ###
################################################
# Any modules may be added as "module" or "module_pkg".
# If defined as a "module", the driver is included in the boot image.
# If defined as a "module_pkg", the driver is added from outside the
# image, such as from a tftp server.  See note below for more details.
#
# Normally you should use "module".  Only use "module_pkg" if you want
# to dynamically load modules.
# Note: If you create a module_pkg network driver, and decide to load it
#       from a tftp server, it won t work.  This is because the image
#       won t have a network driver to get the module_pkg in the first
#       place.

#!Hardware
#!!Machine module list
#machine NT-A3500			# Foxconn AMD E-350 Barebone
#machine NT-535				# Foxconn Intel D525 Barebone
#machine Dell-FX170			# Dell FX170 D525 Atom Machine
machine VMWare				# A good set of modules for a VmWare Virtual Machine
#machine Virtualbox-4.1			# A good set of modules for a Virtualbox Virtual Machine
#machine classic_generic		# Big list of modules that you can choose by unremarking them
#machine foxconn-nT-330i		# Foxconn nT 330i Barebone
#machine foxconn-nT-435			# Foxconn nT 435 Barebone
#machine foxconn-nT-535			# Foxconn nT 535 Barebone
#machine foxconn-nT-A3800		# Foxconn nT A3800 Barebone
#machine foxconn-nT-i2847		# Foxconn nT i2847 Barebone
#machine foxconn-nT-iBT18		# Foxconn nT iBT18 Barebone
#machine foxconn-nT-n270		# Foxconn nT n270 Barebone
machine qemu

#!!Wireless Stuff
package wpa_supplicant
package wireless
#package broadcom-sta
#package wifi-connect
#!!!Misc Modules

#!!Filesystem Support
# Every mounted device needs a filesystem, so choose which ones you need.
module usb-storage
#module autofs4          	# Automount and autofs support
module isofs			# ISO9960 file system support for CDRoms
module udf			# CDRom UDF file system support
module vfat			# Fat and VFat file system support
module ntfs			# NTFS file system support
module ext2			# Ext2 file system support
module ext4			# Ext4 file system support
module nfs			# NFS file system support
module cifs			# CIFS support (use either this or smbfs - not both)

#################################################
### --- Packages to include in boot image --- ###
#################################################

# A package is a program or utility.
# Any packages may be added as "package" or "pkg".
# If defined as a "package", the program is included in the boot image.
# If defined as a "pkg", the program is added from outside the image.
# Normally, you should use "package".  Only use "pkg" if you want to
# dynamically load programs.


# --- Packages to include in boot image
# --- Packages below may be "package" or "pkg"
#!!Miscellaneous
#package ts-classic              # The classic ts logic for network/netfiles/hostname/mounting/telnetd/telnet/playcd
#package telnetd
package audit
package logrotate
#package automount
#package autonet
package networkmanager
package sssd
package bluetooth
#package udisks-glue
package udisks
#package ntpdate		# Classic utility to obtain date & time from network
package ntpd			# Modern NTP Daemon for time synchronization
package alsa
#package pulseaudio
package pipewire
#package pasystray
package xfce4-pulseaudio-plugin
package gnome-core
package gst-plugins-base
package gst-plugins-good
package gst-libav
package libva
package firewalld		# Firewall daemon
package cpupower
#package wine			# Windows Application Execution Platform
package nmap
package dnsmasq
package plymouth

#!!X related
# --- XOrg 7.7
package xorg7-vmware
package xorg7-amdgpu		# ATI/AMD video driver for most modern ATI chipsets. Usually requires several firmwares
package xorg7-nouveau		# New opensource nvidia driver
#package xorg7_nvidia		# Nividia proprietary driver
package xorg7-intel		# Intel video driver for most modern chipsets except the GMA3500/3600
package xorg7-qxl

#!!Locale or localization files for keyboard and fonts.  (Language/Country)
package locale-cs_CZ  # Czech
package locale-da_DK  # Danish
package locale-de_DE  # German
package locale-en_GB  # English-Great Britain
package locale-en_US  # English-US
package locale-es_ES  # Spanish
package locale-fi_FI  # Finnish
package locale-fr_BE  # French Belgium
package locale-fr_CA  # French Canadian
package locale-fr_CH  # French Switzerland
package locale-fr_FR  # French
package locale-hr_HR  # Croation
package locale-hu_HU  # Hungarian
package locale-it_IT  # Italian
package locale-ja_JP  # Japanese
package locale-nb_NO  # Norwegian (bokmål)
package locale-pl_PL  # Polish
package locale-pt_PT  # Portuguese
package locale-pt_BR  # Portuguese-Brazil
package locale-ru_RU  # Russian
package locale-sv_SE  # Swedish
package locale-tr_TR  # Turkish

#!Applications
#!!Connection Package types.  Choose *at least* one!
#package freerdp		# X RDP Client - fork of rdesktop
#package rdesktop		# X RDP client for Windows Terminal Services (ver 1.7)
#package vncviewer		# VNC client (vncviewer)
#package xorg7vnc		# Remote Control Client Workstation
#package ica			# Citrix ICA client support
#package tarantella		# Tarantella client support
#package xnest			# XDM in a window client
#package urxvt            	# Light Xterm Client (vt102)
#package xterm           	# Xterm Client (vt220)
#package ssh			# Secure Shell client
#package tn5250          	# 5250 terminal emulator
#package dillo			# Dillo light web browser
#package thinlinc		# Cendio ThinLinc client support
#package nx              	# No Machine NX client
#package 2x			# 2X client
#package java			# Java runtime
package firefox                 # Firefox Web Browser
#package chrome			# Google Chrome Web Browser
package filezilla		# FileZilla FTP Client for X
package git			# GIT Version Control System
#package gparted		# Gnome Partition Manager
package open-vm-tools		# Guest utils for VMWare
package vboxguest		# Guest utils for VirtualBox
package xe-guest-utils		# Guest utils for XCP-ng
package geany			# Lightweight IDE
package mplayer			# DVD and video player
#package vmview            # VMware Open-View Client, PCOIP

#!!Window Managers. Choose no more than 1 window manager.
# Not needed if you don t want to manage windows.
#package openbox		# A spartin clean/fast window manager.
#package xdesktop-kiosk		# A locked down desktop kiosk using the openbox wm (See packages/xdesktop-kiosk/build/conf for 
				# more help.

package xfwm4
package xfce4-power-manager
#package xfwm4-extra
package terminal		# Terminal emulator for xfwm4 -- Pulls in xfwm4
package thunar			# File Manager for xfwm4 -- Pulls in xfwm4

#!!Window Manager Utils
#package idle-shutdown		# Package that lets you specify automatic shutdown settings and logic.

#!!Other services
#package www 			# Web access to client.  From a browser: "http://<IP number>"
                        	#  The standard page is for general user settings, administrative access
                        	#  is on port 6800.
#package sshd			# Dropbear secure shell server
package openssh                 # openssh secure shell server
#package tftpd 			# Built in tftpd server. Useful for making a tftpserver
				#  for thinstation on a WAN over a slow link.
package cups
package system-config-printer
package gutenprint
package hplip
package avahi
#package samba-server		# Samba server FS Support, allows you to share
#package samba-base
                        	#  local floppy/cdrom/hd/printer to other Windows
				#  PCs. Needs supermount for removeable media.
#package samba-client		# Samba smbclient, gives a shell like environment to access an samba server
#package hdupdate       	# Package for updating TS images on a hardisk over a network connection
#package scp	            	# Add ability to download files over internet using scp.  This package
                        	#  adds some networking based options for downloading configuration files or
                        	#  in using hdupdate package.
#package openvpn                # OpenVPN Client Support


#!!PCSCD Card Readers
#package ccidreader             # Generic USB card reader


#!!Miscellaneous
package gtk-2.0			# Full gtk-2.0 plus clearlooks theme
package gtk-3.0
package gtk-theme-adwaita
package icons-cursor		# Anti Aliased Mouse Cursor Theme
package icons-hicolor           # Hi-Color icons for some applications and themes. Does not really contain any icons.
package icons-gnome             # gnome Hi-Color icons theme. Needed by most other icon themes.
package icons-adwaita
#package icons-tango             # tango Hi-Color icons theme
#package icons-neu               # neu Hi-Color icons theme
#package fonts-misc
#package fonts-jis-misc		# Japanese Industrial
#package fonts-util
#package fonts-cyrillic
#package fonts-Speedo-Bitstream
package fonts-TTF-BH		# This one works nicely most of the time and is small.
package fonts-TTF-vera
package fonts-TTF-liberation
package fonts-TTF-noto		# Google NoTofoo Fonts
#package fonts-75dpi-Adobe
#package fonts-75dpi-Adobe-Utopia
#package fonts-75dpi-BH
#package fonts-75dpi-BH-Typewriter
#package fonts-75dpi-Bitstream
#package fonts-100dpi-Adobe
#package fonts-100dpi-Adobe-Utopia
#package fonts-100dpi-BH
#package fonts-100dpi-BH-Typewriter
#package fonts-100dpi-Bitstream
#package fonts-Type1-Adobe-Utopia
#package fonts-Type1-BH
#package fonts-Type1-Bitstream
#package fonts-Type1-IBM
#package fonts-Type1-xfree86
#package lshw			# list hardware
#package debug			# metapackage to include other debugging packages and stop during bootup to check things.
#package extensions		# Adds various shell commands, will increase image size.
package extensions-x		# Adds various utility s for the X Environment !!!! INCLUDES hwlister.sh !!!!
package gtkdialog
#package installer		# Wipes a drive, makes partitions and downloads thinstation from a pxe server
package devstation		# Starts a tftp server that hosts files from the boot-images/pxe folder

# Parameters
########################################
### --- Miscellaneous Parameters --- ###
########################################
#!!Basic

param fastboot       lotsofmem				# Mangles the filesystem a special way as to improve boot speed and reduce
							# memory utilization. Cool/Dangerous . Harder to dubug other packages. (Finishing Touch)
							# Set to 'true' to enable or 'lotsofmem' for slightly slower booting but no squash lag on app launch.
param tsuser         tsuser                            # Name of the user that thinstation will run as.
param tsuserpasswd   pleasechangeme                    # Do Change! Console/telnet password for non-root

param rootpasswd     pleasechangeme			# Do Change!  Console/telnet password for root
                                        		# If this is enabled, Telnetd will be enabled.
param xorgvncpasswd  pleasechangeme			# VNC Access Password
param storagepasswd  pleasechangeme			# Password for storage server
param dialuppasswd   pleasechangeme			# Password for dialin account
param sambapasswd    pleasechangeme			# Password for samba shares when using user mode security
#param kernelcmdline "radeon.modeset=0"                 # Add additional kernel command lines. e.g. disable radeon framebuffer
param kernelcmdline "selinux=0 fips=1 vt.global_cursor_default=0 loglevel=3 udev.log_priority=3 systemd.show_status=false rd.systemd.show_status=false quiet" 
#param stripelf       yes				# Strip extraneous information from elf binaries (Maybe not Safe)
#param acpisupport    disable				# Tells the kernel not to load acpi modules. (Breaks some Intel Chipsets if disabled)
param bootlogo       true				# Enable or Disable the use of the Boot splash.
param boottheme	     default				# Backgound picture during boot

param desktop file:./backgrounds/Glider.svg		# Custom image to load as desktop background
param defaultconfig  thinstation.conf.buildtime		# The file with default setup.  No other config file is found
                                                 	#  during boot.
param basename       thinstation			# Used for all config/tftp/scp file names
param basepath       ts7.2				# Used to determine path to tftp/scp files
param baseurl        http://www.doncuppjr.net		# Used to determine url to wget files
#param keyfile        ./id_rsa       			# Path for private key file used for ssh/scp
#param knownhosts     ./known_hosts  			# Path for ssh known_hosts file for ssh/scp
#param localpkgs      false				# to determine is PKG files are to be loaded locally
#param fulllocales    true	      			# Use full locale support for packages
#param icaencryption  false      			# Use ica encryption support, add 290k to image
param haltonerror    false				# Will halt on error, default is true
param hardlinkfs     true
param sametimestmp   true                               # When enabled, the timestamps for all files and folders will be set to 00:00 of todays date in your timezone
param initrdcmd	     "gzip"				# Compression mode and level of initrd file. none, gzip -9, lzma -9 ,bzip2 -9
param squashopt      "-comp xz"
param bootverbosity   3					# Increased vebosity when booting TS
                                			#   0 no verbose messages
                                			#   1 boot
                                			#   2 network
                                			#   4 init
                                			#   8 kernel
                                			#  16 modules
                                			#  32 packages
                                			#  64 email bootlog file to SMTP server & user set in
                                			#     thinstation.conf file.  This will only work
                                			#     if networking is working.
                                			#
                                			#  Combinations can be used (e.g.12 does Kernel and Module Messages)

#!!Advanced
# Leave this alone unless you can t download (wget required on your Linux box):
param downloads         /downloads
#param bootprefix "test/" # Add a prefix directory to /boot for PXE based launch, used to allow multiple builds on same PXE server
                          # Value will be inserted as follows: /${bootprefix}boot
                          # Note: This is incompatible with non-network grub based boot images
param grubtheme		"default"
#param httpproxy	http://***********:8080


param livecd		true		# Adds a multi-resolution boot menu to cd images.
package alltimezone
param allres		true		# Includes a lot of resolution splash images for live-cd s
param allfirmware	true		# Includes a lot of firmwares for live-cd s
param blacklist "snd-pcsp.ko pcspkr.ko"
#package overlayfs
#param earlymicrocode	true
