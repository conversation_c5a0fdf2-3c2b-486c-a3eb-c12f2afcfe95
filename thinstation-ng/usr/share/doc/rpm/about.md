---
layout: default
title: rpm.org - About the Reference Manual
---
# About this manual

This is the Refence Manual for the RPM Package Manager. Unfortunately
it is still incomplete. Recently added features are described with
decent detail but more basic properties are only being added step by
step.

While this manual describes how rpm and rpmbuild are working it does
not teach how to use them properly - especially when it comes to
packaging. This is similar to how a reference manual for a programming
language does not teach you programming or good coding style.

Though the RPM Reference Manual needs to be used in conjunction with
Packaging Guidelines from the distribution the packages are aimed
for. For enterprise distributions this may mean looking at the
guidelines from the upstream (community) distribution. If you are not
packaging for a distribution or one that doesn't have guideline choose
one from a distribution that is similar to your target environment.

This manual describes RPM as it is setup in the upstream release. Many
distibution alter RPM in various ways. Either by changing macro values
or by disabling - or forbidding the use of - some features. This is
especially true for new features that may require some effort to
enable properly throughout a distribution. The distributions decisions
overwrite the upstream defaults and though this reference manual.
