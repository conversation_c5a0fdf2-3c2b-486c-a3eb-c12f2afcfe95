
##
# --- Networking Options
#
# NET_HOSTNAME           Hostname to use if not using a thinstation.hosts file, note
#                        that the machine MAC address will replace any * if used.
# NET_SMTP_SERVER        Email server address for error logs
# NET_SMTP_EMAIL         Email address of administrator
# NET_TIME_SERVER        Time server on network with which to sync. NTP Time Server. Recommend <country code>.pool.ntp.org
# NET_TELNETD            Enables built-in telnetd server package
# NET_REMOTE_ACCESS_FROM List of hostnames/ip address accepted by the server
#                        for remote control, used by telnetd and www packages
# NET_USE		 Sets which interfaces to try and enable at boot time
#			 LAN  Only try and initialize wired interfaces
#			 WLAN Only try and initialize wireless interfaces (You still need to set other parameters)
#			 BOTH Try and enable all interfaces
# 
# -- Mounting networked filesystems --
#
# NET_NFS_SERVER         This is the path to the NFS server, ie
#                        server:/path/to/share.
# NET_SMB_SERVER         This is the path to the SMB server, ie
#                        server:/path/to/share. 
# NET_SMB_WORKGROUP      Workgroup of samba server
# NET_SMB_USER           This is the username for SMB mounts, password is
#                        defined at build time in build.conf
# NET_LINKWAIT		 Amount of time to wait for a link.

NET_LINKWAIT=6
NET_USE=BOTH
#NET_HOSTNAME=donald
NET_HOSTNAME=ts-*
NET_TIME_SERVER=pool.ntp.org
NET_DHCP_TIMEOUT=30
#NET_SMTP_SERVER=donald
#NET_SMTP_EMAIL=<EMAIL>
#NET_TIME_SERVER=mickey
NET_TELNETD_ENABLED=On
NET_REMOTE_ACCESS_FROM="duck.quak.org.au *********** .disney.us"
#NET_NFS_SERVER=bigserver:/opt/thinstation
#NET_SMB_USER=duck


# -- For use in thinstation.conf.buildtime only:
#
# NET_USE_DHCP           Enable DHCP Resolution
#                        ON     Use DHCP, fail if it doesn't get a lease
#                        OFF    Don't use DHCP, manual configuration required
#                        BOTH   Use DHCP in first instance, failing to get a lease, try manual config
# NET_IP_ADDRESS         IP Address of client
# NET_MASK               Network mask of client
# NET_GATEWAY            IP Address of gateway
# NET_DHCP_DELAY	 Delay before continuing script after bringing up interface, only needed if
#                        using spanning tree "port discovery" in a switched environment.  Default is 0 seconds.
# NET_FILE_METHOD        Method to download files. Can be either of the following values: tftp, wget, http, scp.
#                        scp is only available if package ssh is selected in build as a "package".
#                        With the package netfiles you can use NET_FILE_METHOD, NET_FILE_ENABLED and NET_FILE_ALTERNATE
#                        to download configuration files (thinstation.conf.group-<my group>, thinstation.conf.<mac-address> etc.)
# NET_FILE_ENABLED       Use server for config file, On/Off
# NET_FILE_USER          User to connect to SCP server as
# NET_FILE_ALTERNATE     Alternate server for vmware or non-standard dhcp servers
# NET_DNS1               DNS Server 1
# NET_DNS2               DNS Server 2
# NET_DNS_SEARCH         Default DNS domain to search

#NET_USE_DHCP=Off
#NET_IP_ADDRESS=***********
#NET_MASK=*************
#NET_GATEWAY=***********54
#NET_DHCP_DELAY=30
#NET_FILE_ENABLED=Off
#NET_FILE_METHOD=http
#NET_FILE_USER=not_needed_for_tftp
#NET_FILE_ALTERNATE=***********
#NET_DNS1=***********
#NET_DNS2=***********
#NET_DNS_SEARCH=cartoons.org.nz


