===============
Duktape authors
===============

Copyright
=========

Duktape copyrights are held by its authors.  Each author has a copyright
to their contribution, and agrees to irrevocably license the contribution
under the Duktape ``LICENSE.txt``.

Authors
=======

Please include an e-mail address, a link to your GitHub profile, or something
similar to allow your contribution to be identified accurately.

The following people have contributed code, website contents, or Wiki contents,
and agreed to irrevocably license their contributions under the Duktape
``LICENSE.txt`` (in order of appearance):

* <PERSON> <<EMAIL>>
* <PERSON><PERSON>
* <PERSON> <<EMAIL>>
* <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
* Legimet <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON> (https://github.com/crazyjul)
* <PERSON> (https://github.com/jaseg)
* <PERSON><PERSON> (https://github.com/magul)
* \D. Bohdan (https://github.com/dbohdan)
* Ondřej Jirman (https://github.com/megous)
* Saúl Ibarra Corretgé <<EMAIL>>
* Jeremy HU <<EMAIL>>
* Ole André Vadla Ravnås (https://github.com/oleavr)
* Harold <PERSON>renes (https://github.com/harold-b)
* Oliver Crow (https://github.com/ocrow)
* Jakub Chłapiński (https://github.com/jchlapinski)
* Brett Vickers (https://github.com/beevik)
* Dominik Okwieka (https://github.com/okitec)
* Remko Tronçon (https://el-tramo.be)
* Romero Malaquias (<EMAIL>)
* Michael Drake <<EMAIL>>
* Steven Don (https://github.com/shdon)
* Simon Stone (https://github.com/sstone1)
* \J. McC. (https://github.com/jmhmccr)
* Jakub Nowakowski (https://github.com/jimvonmoon)
* Tommy Nguyen (https://github.com/tn0502)
* Fabrice Fontaine (https://github.com/ffontaine)
* Christopher Hiller (https://github.com/boneskull)
* Gonzalo Diethelm (https://github.com/gonzus)
* Michal Kasperek (https://github.com/michalkas)
* Andrew Janke (https://github.com/apjanke)
* Steve Fan (https://github.com/stevefan1999)
* Edward Betts (https://github.com/edwardbetts)
* Ozhan Duz (https://github.com/webfolderio)
* Akos Kiss (https://github.com/akosthekiss)
* TheBrokenRail (https://github.com/TheBrokenRail)
* Jesse Doyle (https://github.com/jessedoyle)
* Gero Kuehn (https://github.com/dc6jgk)
* James Swift (https://github.com/phraemer)
* Luis de Bethencourt (https://github.com/luisbg)
* Ian Whyman (https://github.com/v00d00)
* Rick Sayre (https://github.com/whorfin)
* Craig Leres (https://github.com/leres)
* Maurici Abad (https://github.com/mauriciabad)
* Nancy Li (https://github.com/NancyLi1013)
* William Parks (https://github.com/WilliamParks)
* Sam Hellawell (https://github.com/samhellawell)
* Vladislavs Sokurenko (https://github.com/sokurenko)

Other contributions
===================

The following people have contributed something other than code (e.g. reported
bugs, provided ideas, etc; roughly in order of appearance):

* Greg Burns
* Anthony Rabine
* Carlos Costa
* Aurélien Bouilland
* Preet Desai (Pris Matic)
* judofyr (http://www.reddit.com/user/judofyr)
* Jason Woofenden
* Michał Przybyś
* Anthony Howe
* Conrad Pankoff
* Jim Schimpf
* Rajaran Gaunker (https://github.com/zimbabao)
* Andreas Öman
* Doug Sanden
* Josh Engebretson (https://github.com/JoshEngebretson)
* Remo Eichenberger (https://github.com/remoe)
* Mamod Mehyar (https://github.com/mamod)
* David Demelier (https://github.com/markand)
* Tim Caswell (https://github.com/creationix)
* Mitchell Blank Jr (https://github.com/mitchblank)
* https://github.com/yushli
* Seo Sanghyeon (https://github.com/sanxiyn)
* Han ChoongWoo (https://github.com/tunz)
* Joshua Peek (https://github.com/josh)
* Bruce E. Pascoe (https://github.com/fatcerberus)
* https://github.com/Kelledin
* https://github.com/sstruchtrup
* Michael Drake (https://github.com/tlsa)
* https://github.com/chris-y
* Laurent Zubiaur (https://github.com/lzubiaur)
* Neil Kolban (https://github.com/nkolban)
* Wilhelm Wanecek (https://github.com/wanecek)
* Andrew Janke (https://github.com/apjanke)
* Unamer (https://github.com/unamer)
* Karl Dahlke (<EMAIL>)

If you are accidentally missing from this list, send me an e-mail
(``<EMAIL>``) and I'll fix the omission.
