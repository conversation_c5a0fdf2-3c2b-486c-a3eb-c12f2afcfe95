# Gemalto reader with serial communication
#  - n is the serial port to use n in [0..3]
#  - reader is the reader name. It is needed for multi-slot readers.
#    Possible reader values are: 
#     GemCorePOSPro
#     GemCoreSIMPro
#     GemCoreSIMPro2
#     GemPCPinPad
#     GemPCTwin (default value)
#     SEC1210 (Dual slot Reader)
# example: /dev/ttyS0:GemPCPinPad
#DEVICENAME        /dev/ttySn[:reader]
#FRIENDLYNAME      "GemPCTwin serial"
#LIBPATH           /usr/lib64/pcsc/drivers/serial/libccidtwin.so
