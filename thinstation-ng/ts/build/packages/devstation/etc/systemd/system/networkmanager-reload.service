[Unit]
Description=Restart NetworkManager After Clearing State
After=tsinit.target profile-setup.service net.profile-setup.service
Before=sssd.init.service display-manager.service lightdm.service
ConditionPathExistsGlob=/etc/NetworkManager/system-connections/*

[Service]
Type=oneshot
ExecStartPre=/bin/rm -rf /run/NetworkManager
ExecStart=systemctl restart NetworkManager
ExecStartPost=nm-online -s -q

[Install]
WantedBy=tsinit.target
