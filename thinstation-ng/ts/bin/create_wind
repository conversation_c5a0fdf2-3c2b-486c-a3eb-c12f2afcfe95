#!/bin/sh

create_wind()
{
	printf "\n\tCreating fast clean cache for $i"
	echo "#`md5sum .dna`" > $cache/wind_cache/$i.turbo
        update -c -d $i >> $cache/wind_cache/$i.turbo
        . $cache/wind_cache/$i.turbo
	errata=true
}

create_unwind()
{
	printf "\n\tCreating fast install cache for $i"
        echo "#`md5sum .dna`" > $cache/unwind_cache/$i.turbo
        update -d $i |
        sed -e 's/^mkdir \-/2:/g' -e 's/^ln \-/4:/g' -e 's/^touch /5:/g' -e 's/^ln \//3:/g' |
        sort |
        uniq |
        sed -e 's/^2:/mkdir \-/g' -e 's/^3:/ln \//g' -e 's/^4:/ln \-/g' -e 's/^5:/touch /g' >> $cache/unwind_cache/$i.turbo
	errata=true
}

i=$1
for working in $TSWRKNG $XTRWRKNG; do
	if [ $working = $TSWRKNG ]; then cache=/ts;
	else cache=$working; fi

	if [ -d $working/packages/$i ]; then
		if [ ! -e $working/packages/$i/.dna ]; then
			echo "Missing .dna file for package '$i'."
			exit
		fi

		cd $working/packages/$i
		create_wind
		create_unwind
		. $cache/unwind_cache/$i.turbo
	fi
done

if [ "$errata" != true ]; then
	echo "Package directory for '$i' not found."
fi
