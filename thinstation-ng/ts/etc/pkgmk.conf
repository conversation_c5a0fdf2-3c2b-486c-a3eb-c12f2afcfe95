#
# /etc/pkgmk.conf: pkgmk(8) configuration
#

export CFLAGS="-O2 -pipe -D_FORTIFY_SOURCE=2"
export CXXFLAGS="${CFLAGS}"
export CFLAGS="${CFLAGS} -mno-tls-direct-seg-refs"
export CFLAGS="${CFLAGS} -fstack-protector-strong"

case ${PKGMK_ARCH} in
        "64"|"")
                export CFLAGS="${CFLAGS} -march=x86-64"
                export CXXFLAGS="${CXXFLAGS} -march=x86-64"
                ;;
        "32")
                export CFLAGS="${CFLAGS} -m32"
                export CXXFLAGS="${CXXFLAGS} -m32"
                export LDFLAGS="${LDFLAGS} -m32"
                export PKG_CONFIG_LIBDIR="/usr/lib32/pkgconfig"
                ;;
        *)
                echo "Unknown architecture selected! Exiting."
                exit 1
                ;;
esac

procs=`nproc`
let procs-=1
JOBS=$procs

export MAKEFLAGS="-j$procs"
# PKGMK_SOURCE_MIRRORS=()
# PKGMK_SOURCE_DIR="$PWD"
# PKGMK_PACKAGE_DIR="$PWD"
# PKGMK_WORK_DIR="$PWD/work"
# PKGMK_DOWNLOAD="no"
# PKGMK_IGNORE_FOOTPRINT="no"
# PKGMK_IGNORE_NEW="no"
# PKGMK_NO_STRIP="no"
# PKGMK_WGET_OPTS=""
PKGMK_COMPRESSION_MODE="xz"

# End of file
