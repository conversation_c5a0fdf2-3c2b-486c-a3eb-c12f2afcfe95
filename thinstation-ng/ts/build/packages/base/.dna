,checklogs,0,0,bin,0,,,,,,,,,
,lsb_release,0,0,bin,0,,,,,,,,,
,pkg,0,0,bin,0,,,,,,,,,
,update-initrd,0,0,bin,0,,,,,,,,,
,used,0,0,bin,0,,,,,,,,,
vmtouch,vmtouch,0,0,bin,1,,,,,,,,,
procps-ng,ps,0,0,bin,1,,,,,,,,,
glibc-common,getent,0,0,bin,1,,,,,,,,,
,10master,0,0,build/conf,0,,,,,,,,,
,62packages,0,0,build/conf,0,,,,,,,,,
,64network,0,0,build/conf,0,,,,,,,,,
,68storage,0,0,build/conf,0,,,,,,,,,
,dependencies,0,0,,0,,,,,,,,,
util-linux,adjtime,0,0,etc,1,,,,,,,,,
,ashrc,0,0,etc,0,,,,,,,,,
,authselect.conf,0,0,etc/authselect,0,,,,,,,,,
,dconf-db,0,0,etc/authselect,0,,,,,,,,,
,dconf-locks,0,0,etc/authselect,0,,,,,,,,,
authselect-libs,nsswitch.conf,0,0,etc/authselect,1,,,,,,,,,
authselect-libs,password-auth,0,0,etc/authselect,1,etc/pam.d/password-auth,,,,,,,,
authselect-libs,postlogin,0,0,etc/authselect,1,etc/pam.d/postlogin,,,,,,,,
authselect-libs,system-auth,0,0,etc/authselect,1,etc/pam.d/system-auth,,,,,,,,
authselect-libs,fingerprint-auth,0,0,etc/authselect,1,etc/pam.d/fingerprint-auth,,,,,,,,
authselect-libs,smartcard-auth,0,0,etc/authselect,1,etc/pam.d/smartcard-auth,,,,,,,,
authselect-libs,authselect.conf,0,0,etc/authselect,1,,,,,,,,,
authselect-libs,dconf-db,0,0,etc/authselect,1,,,,,,,,,
authselect-libs,dconf-locks,0,0,etc/authselect,1,,,,,,,,,
,busybox.conf,0,0,etc,0,,,,,,,,,
,sh,0,0,etc/console,0,,,,,,,,,
,passwd,0,0,etc/default,0,,,,,,,,,
,dialog.functions,0,0,etc,0,,,,,,,,,
,fstab,0,0,etc,0,,,,,,,,,
,gettydefs,0,0,etc,0,,,,,,,,,
,group,0,0,etc,0,,,,,,,,,
,index.html,0,0,etc,0,,,,,,,,,
,common,0,0,etc/init.d,0,,,,,,,,,
,crontab,0,0,etc/init.d,0,,,,,,,,,
,debug,0,0,etc/init.d,0,,,,,,,,,
,fastboot,0,0,etc/init.d,0,,,,,,,,,
,mounts,0,0,etc/init.d,0,,,,,,,,,
,pkg,0,0,etc/init.d,0,,,,,,,,,
,post-udev,0,0,etc/init.d,0,,,,,,,,,
,profile-setup,0,0,etc/init.d,0,,,,,,,,,
,rtc_cmos,0,0,etc/init.d,0,,,,,,,,,
,session,0,0,etc/init.d,0,,,,,,,,,
,session-setup,0,0,etc/init.d,0,,,,,,,,,
,sh,0,0,etc/init.d,0,,,,,,,,,
,ld.so.conf,0,0,etc,0,,,,,,,,,
,blacklist.conf,0,0,etc/modprobe.d,0,,,,,,,,,
,modules.conf,0,0,etc,0,,,,,,,,,
,nsswitch.conf,0,0,etc,0,,,,,,,,,
,login,0,0,etc/pam.d,0,,,,,,,,,
pam,other,0,0,etc/pam.d,1,,,,,,,,,
util-linux,chfn,0,0,etc/pam.d,1,,,,,,,,,
util-linux,chsh,0,0,etc/pam.d,1,,,,,,,,,
util-linux,remote,0,0,etc/pam.d,1,,,,,,,,,
util-linux,runuser,0,0,etc/pam.d,1,,,,,,,,,
util-linux,runuser-l,0,0,etc/pam.d,1,,,,,,,,,
util-linux,su,0,0,etc/pam.d,1,,,,,,,,,
util-linux,su-l,0,0,etc/pam.d,1,,,,,,,,,
util-linux,login,0,0,etc/pam.d,1,,,,,,,,,
pam,config-util,0,0,etc/pam.d,1,,,,,,,,,
shadow-utils,passwd,0,0,etc/pam.d,1,,,,,,,,,
kbd,vlock,0,0,etc/pam.d,1,,,,,,,,,
krb5-workstation,ksu,0,0,etc/pam.d,1,,,,,,,,,
,passwd,0,0,etc/pam.d,0,,,,,,,,,
,passwd,0,0,etc,0,,,,,,,,,
,profile,0,0,etc,0,,,,,,,,,
,protocols,0,0,etc,0,,,,,,,,,
,securetty,0,0,etc,0,,,,,,,,,
pam,access.conf,0,0,etc/security,1,,,,,,,,,
pam,sepermit.conf,0,0,etc/security,1,,,,,,,,,
libpwquality,pwquality.conf,0,0,etc/security,1,,,,,,,,,
pam,pam_env.conf,0,0,etc/security,1,,,,,,,,,
pam,group.conf,0,0,etc/security,1,,,,,,,,,
pam,limits.conf,0,0,etc/security,1,,,,,,,,,
pam,namespace.conf,0,0,etc/security,1,,,,,,,,,
pam,namespace.init,0,0,etc/security,1,,,,,,,,,
pam,time.conf,0,0,etc/security,1,,,,,,,,,
,services,0,0,etc,0,,,,,,,,,
setup,shells,0,0,etc,1,,,,,,,,,
,splash.functions,0,0,etc,0,,,,,,,,,
,crond.service,0,0,etc/systemd/system,0,etc/systemd/system/multi-user.target.wants/crond.service,,,,,,,,
,crontab.service,0,0,etc/systemd/system,0,etc/systemd/system/multi-user.target.wants/crontab.service,,,,,,,,
,debug.service,0,0,etc/systemd/system,0,etc/systemd/system/local-fs.target.wants/debug.service,,,,,,,,
,display-manager.service,0,0,etc/systemd/system,0,,,,,,,,,
,fastboot.service,0,0,etc/systemd/system,0,etc/systemd/system/sysinit.target.wants/fastboot.service,,,,,,,,
,mounts.service,0,0,etc/systemd/system,0,etc/systemd/system/local-fs.target.wants/mounts.service,,,,,,,,
,pkg.service,0,0,etc/systemd/system,0,etc/systemd/system/multi-user.target.wants/pkg.service,,,,,,,,
,post-udev.service,0,0,etc/systemd/system,0,etc/systemd/system/multi-user.target.wants/post-udev.service,,,,,,,,
,profile-setup.service,0,0,etc/systemd/system,0,,,,,,,,,
,rtc_cmos.service,0,0,etc/systemd/system,0,etc/systemd/system/sysinit.target.wants/rtc_cmos.service,,,,,,,,
,session-setup.service,0,0,etc/systemd/system,0,etc/systemd/system/multi-user.target.wants/session-setup.service,,,,,,,,
,session.service,0,0,etc/systemd/system,0,etc/systemd/system/multi-user.target.wants/session.service,,,,,,,,
,syslog-bb.service,0,0,etc/systemd/system,0,etc/systemd/system/multi-user.target.wants/syslog.service,etc/systemd/system/syslog.service,,,,,,,
,vmtouch.service,0,0,etc/systemd/system,0,,,,,,,,,
,thinstation.defaults,0,0,etc,0,,,,,,,,,
,thinstation.env,0,0,etc,0,,,,,,,,,
,thinstation.exports,0,0,etc,0,,,,,,,,,
,thinstation.functions,0,0,etc,0,,,,,,,,,
,thinstation.global,0,0,etc,0,,,,,,,,,
,thinstation.packages,0,0,etc,0,,,,,,,,,
,70-u2f.rules,0,0,etc/udev/rules.d,0,,,,,,,,,
,pre-net.sh,0,0,etc/udev/scripts,0,,,,,,,,,
setup,environment,0,0,etc,1,,,,,,,,,
setup,inputrc,0,0,etc,1,,,,,,,,,
EMPTY,pam_debug,0,0,etc,1,,,,,,,,,
,session,0,0,sbin,0,,,,,,,,,
,shutdown,0,0,sbin,0,,,,,,,,,
glibc,ldconfig,0,0,sbin,1,,,,,,,,,
libcap,setcap,0,0,sbin,1,,,,,,,,,
libcap,getcap,0,0,sbin,1,,,,,,,,,
pciutils,lspci,0,0,sbin,1,,,,,,,,,
util-linux-core,agetty,0,0,sbin,1,,,,,,,,,
util-linux-core,blkid,0,0,bin,1,sbin/blkid,,,,,,,,
util-linux-core,swapon,0,0,bin,1,sbin/swapon,,,,,,,,
util-linux,login,0,0,bin,1,,,,,,,,,
,charset.alias,0,0,lib64,0,,,,,,,,,
,LICENSE,0,0,lib64/licenses,0,,,,,,,,,
,LICENSE_GPL,0,0,lib64/licenses,0,,,,,,,,,
,init-functions,0,0,lib64/lsb,0,,,,,,,,,
,session.conf,0,0,lib64/tmpfiles.d,0,,,,,,,,,
,pam_hooks.so,0,0,lib64/security,0,,,,,,,,,
libpwquality,pam_pwquality.so,0,0,lib64/security,1,,,,,,,,,
pam,unix_chkpwd,0,0,bin,1,,,,,,,,,
pam,pam_sepermit.so,0,0,lib64/security,1,lib64/security/pam_selinux_permit.so,,,,,,,,
pam,pam_access.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_deny.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_env.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_faildelay.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_keyinit.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_lastlog.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_limits.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_localuser.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_loginuid.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_namespace.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_nologin.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_permit.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_rootok.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_shells.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_succeed_if.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_umask.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_unix.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_usertype.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_warn.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_wheel.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_xauth.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_securetty.so,0,0,lib64/security,1,,,,,,,,,
pam,pam_selinux.so,0,0,lib64/security,1,,,,,,,,,
glibc,ld-linux-x86-64.so.2,0,0,lib64,1,,,,,,,,,
libgcc,libgcc_s.so.1,0,0,lib64,1,,,,,,,,,
shadow-utils,pwconv,0,0,sbin,1,,,,,,,,,
shadow-utils,grpconv,0,0,sbin,1,,,,,,,,,
pam,unix_chkpwd,0,0,sbin,1,,,,,,,,,
util-linux-core,mount,0,0,bin,1,,,,,,,,,
findutils,find,0,0,bin,1,,,,,,,,,
