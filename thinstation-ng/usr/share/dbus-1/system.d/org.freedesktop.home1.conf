<?xml version="1.0"?> <!--*-nxml-*-->
<!DOCTYPE busconfig PUBLIC "-//freedesktop//DTD D-BUS Bus Configuration 1.0//EN"
        "https://www.freedesktop.org/standards/dbus/1.0/busconfig.dtd">

<!-- SPDX-License-Identifier: LGPL-2.1-or-later -->

<busconfig>

        <policy user="root">
                <allow own="org.freedesktop.home1"/>
                <allow send_destination="org.freedesktop.home1"/>
                <allow receive_sender="org.freedesktop.home1"/>
        </policy>

        <policy context="default">
                <deny send_destination="org.freedesktop.home1"/>

                <!-- generic interfaces -->

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.DBus.Introspectable"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.DBus.Peer"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.DBus.Properties"
                       send_member="Get"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.DBus.Properties"
                       send_member="GetAll"/>

                <!-- Manager object -->

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="GetHomeByName"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="GetHomeByUID"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="GetUserRecordByName"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="GetUserRecordByUID"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="ListHomes"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="ActivateHome"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="ActivateHomeIfReferenced"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="DeactivateHome"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="RegisterHome"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="UnregisterHome"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="CreateHome"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="CreateHomeEx"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="RealizeHome"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="RemoveHome"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="FixateHome"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="AuthenticateHome"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="UpdateHome"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="UpdateHomeEx"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="ResizeHome"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="ChangePasswordHome"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="LockHome"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="UnlockHome"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="AcquireHome"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="RefHome"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="RefHomeUnrestricted"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="ReleaseHome"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="LockAllHomes"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="DeactivateAllHomes"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Manager"
                       send_member="Rebalance"/>

                <!-- Home object -->

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="Activate"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="ActivateIfReferenced"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="Deactivate"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="Unregister"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="Realize"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="Remove"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="Fixate"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="Authenticate"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="Update"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="UpdateEx"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="Resize"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="ChangePassword"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="Lock"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="Unlock"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="Acquire"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="Ref"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="RefUnrestricted"/>

                <allow send_destination="org.freedesktop.home1"
                       send_interface="org.freedesktop.home1.Home"
                       send_member="Release"/>

                <allow receive_sender="org.freedesktop.home1"/>
        </policy>

</busconfig>
