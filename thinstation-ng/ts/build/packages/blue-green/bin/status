#!/bin/sh

# Report the kernel version
echo "Kernel Version: $(uname -r)"

# Parse the boot command line for the BOOT_IMAGE
CMDLINE=$(cat /proc/cmdline)
BOOT_IMAGE=""

# Use grep and cut to extract the BOOT_IMAGE from /proc/cmdline
BOOT_IMAGE_PATH=$(echo "$CMDLINE" | grep -o 'BOOT_IMAGE=[^ ]*' | cut -d= -f2)

if [ -n "$BOOT_IMAGE_PATH" ]; then
    BOOT_IMAGE=$(basename "$BOOT_IMAGE_PATH")
#    echo "Boot Image used during boot: $BOOT_IMAGE"
else
    echo "Boot Image information not found in /proc/cmdline."
    exit 1
fi

# Determine the configuration based on the BOOT_IMAGE filename
case "$BOOT_IMAGE" in
    *-blue*)
        echo "Current Configuration: Blue"
        ;;
    *-green*)
        echo "Current Configuration: Green"
        ;;
    *)
        echo "Current Configuration: Unknown"
        ;;
esac

#echo "Current Environment: $CURRENT_ENV"

LIB_SQUASH_PATH="/boot/boot"
# Calculate and print checksums of lib.squash files
echo "Checksums of lib.squash files:"
if [ -f "$LIB_SQUASH_PATH/lib.squash-blue" ]; then
    echo -n "lib.squash-blue: "
    sha256sum "$LIB_SQUASH_PATH/lib.squash-blue" | cut -d' ' -f1
else
    echo "lib.squash-blue: File not found"
fi

if [ -f "$LIB_SQUASH_PATH/lib.squash-green" ]; then
    echo -n "lib.squash-green: "
    sha256sum "$LIB_SQUASH_PATH/lib.squash-green" | cut -d' ' -f1
else
    echo "lib.squash-green: File not found"
fi
