#! /bin/sh
. `dirname $0`/common

#set -x
#exec </dev/null >>/var/log/choose-network.log  2>&1

# Choose Preferred Network for extra files.
if [ -n "$DEBUG_INIT" ] ; then
        echo_log "Choosing Network"
fi
#PICK ADAPTER

network_break()
{
	if [ -n "$1" ]; then
		if [ "`cat /sys/class/net/$1/carrier`" != "1" ]; then
	        	echo_log "No cable for $1,"
			echo_log "or you don't have the firmware loaded."
		else
		        echo_log "DHCP server not found for $1! Network not initialized."
		        echo_log "This error has probably occured because you haven't got the corrrect"
	        	echo_log "module loaded for you network card."
		fi
	fi
	echo_log "Error, Network not initialized!"
	if pkg_initialized debug || is_disabled $HALTONERROR ; then
		echo_log "Debug enabled...continuing boot."
	else
		echo_log "Debug not enabled, boot halted."
		force_splash_exit
		halt
	fi
}

timeout=300
while [ -z "$devicelist" ] && [ "$timeout" != "0" ]; do
	devicelist=`grep -e NETWORKUP=TRUE /run/log/net/* |cut -d : -f1`
	sleep .1
	let timeout-=1
done

devicecount=`echo $devicelist |wc -l`
if [ -z "$devicelist" ]; then
	network_break
elif [ "$devicecount" -gt "1" ]; then
	for interface in "$devicelist" ; do
		. $interface
		case $DEVTYPE in
			eth)	lan=`basename $interface`;;
			wlan)	wlan=`basename $interface`;;
		esac
	done
	if [ -n "$lan" ] && [ -z "$wlan" ]; then
		device=$lan
	elif [ -n "$lan" ] && [ -n "$wlan" ]; then
		if [ "$NET_USE" == "LAN" ]; then
			device=$lan
			ifconfig $wlan down
		elif "$NET_USE" == "WLAN" ]; then
			device=$wlan
			ifconfig $lan down
		elif [ "$NET_USE" == "BOTH" ]; then
			device=$lan
		fi
	elif [ -z "$lan" ] && [ -n "$wlan" ]; then
		device=$wlan
	fi
else
	device=`basename $devicelist`
fi

echo "NET_DEVICE=$device" >> $TS_RUNTIME
