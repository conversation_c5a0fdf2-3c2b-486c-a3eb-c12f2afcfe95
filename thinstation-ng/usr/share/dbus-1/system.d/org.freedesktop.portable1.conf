<?xml version="1.0"?> <!--*-nxml-*-->
<!DOCTYPE busconfig PUBLIC "-//freedesktop//DTD D-BUS Bus Configuration 1.0//EN"
        "https://www.freedesktop.org/standards/dbus/1.0/busconfig.dtd">

<!-- SPDX-License-Identifier: LGPL-2.1-or-later -->

<busconfig>

        <policy user="root">
                <allow own="org.freedesktop.portable1"/>
                <allow send_destination="org.freedesktop.portable1"/>
                <allow receive_sender="org.freedesktop.portable1"/>
        </policy>

        <policy context="default">
                <deny send_destination="org.freedesktop.portable1"/>

                <!-- generic interfaces -->

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.DBus.Introspectable"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.DBus.Peer"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.DBus.Properties"
                       send_member="Get"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.DBus.Properties"
                       send_member="GetAll"/>

                <!-- Manager object -->

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Manager"
                       send_member="GetImage"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Manager"
                       send_member="ListImages"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Manager"
                       send_member="GetImageOSRelease"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Manager"
                       send_member="GetImageMetadata"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Manager"
                       send_member="GetImageState"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Manager"
                       send_member="AttachImage"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Manager"
                       send_member="DetachImage"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Manager"
                       send_member="ReattachImage"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Manager"
                       send_member="RemoveImage"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Manager"
                       send_member="MarkImageReadOnly"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Manager"
                       send_member="SetImageLimit"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Manager"
                       send_member="SetPoolLimit"/>

                <!-- Image object -->

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Image"
                       send_member="GetOSRelease"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Image"
                       send_member="GetMetadata"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Image"
                       send_member="GetState"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Image"
                       send_member="Attach"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Image"
                       send_member="Detach"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Image"
                       send_member="Reattach"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Image"
                       send_member="Remove"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Image"
                       send_member="MarkReadOnly"/>

                <allow send_destination="org.freedesktop.portable1"
                       send_interface="org.freedesktop.portable1.Image"
                       send_member="SetLimit"/>

                <allow receive_sender="org.freedesktop.portable1"/>
        </policy>

</busconfig>
