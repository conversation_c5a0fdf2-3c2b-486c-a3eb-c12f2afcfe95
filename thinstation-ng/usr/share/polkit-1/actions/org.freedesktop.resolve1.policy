<?xml version="1.0" encoding="UTF-8"?> <!--*-nxml-*-->
<!DOCTYPE policyconfig PUBLIC "-//freedesktop//DTD PolicyKit Policy Configuration 1.0//EN"
        "https://www.freedesktop.org/standards/PolicyKit/1/policyconfig.dtd">

<!--
  SPDX-License-Identifier: LGPL-2.1-or-later

  This file is part of systemd.

  systemd is free software; you can redistribute it and/or modify it
  under the terms of the GNU Lesser General Public License as published by
  the Free Software Foundation; either version 2.1 of the License, or
  (at your option) any later version.
-->

<policyconfig>

        <vendor>The systemd Project</vendor>
        <vendor_url>https://systemd.io</vendor_url>

        <action id="org.freedesktop.resolve1.register-service">
                <description gettext-domain="systemd">Register a DNS-SD service</description>
                <message gettext-domain="systemd">Authentication is required to register a DNS-SD service.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.owner">unix-user:systemd-resolve</annotate>
        </action>

        <action id="org.freedesktop.resolve1.unregister-service">
                <description gettext-domain="systemd">Unregister a DNS-SD service</description>
                <message gettext-domain="systemd">Authentication is required to unregister a DNS-SD service.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.owner">unix-user:systemd-resolve</annotate>
        </action>

        <action id="org.freedesktop.resolve1.set-dns-servers">
                <description gettext-domain="systemd">Set DNS servers</description>
                <message gettext-domain="systemd">Authentication is required to set DNS servers.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.owner">unix-user:systemd-resolve</annotate>
        </action>

        <action id="org.freedesktop.resolve1.set-domains">
                <description gettext-domain="systemd">Set domains</description>
                <message gettext-domain="systemd">Authentication is required to set domains.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.owner">unix-user:systemd-resolve</annotate>
        </action>

        <action id="org.freedesktop.resolve1.set-default-route">
                <description gettext-domain="systemd">Set default route</description>
                <message gettext-domain="systemd">Authentication is required to set default route.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.owner">unix-user:systemd-resolve</annotate>
        </action>

        <action id="org.freedesktop.resolve1.set-llmnr">
                <description gettext-domain="systemd">Enable/disable LLMNR</description>
                <message gettext-domain="systemd">Authentication is required to enable or disable LLMNR.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.owner">unix-user:systemd-resolve</annotate>
        </action>

        <action id="org.freedesktop.resolve1.set-mdns">
                <description gettext-domain="systemd">Enable/disable multicast DNS</description>
                <message gettext-domain="systemd">Authentication is required to enable or disable multicast DNS.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.owner">unix-user:systemd-resolve</annotate>
        </action>

        <action id="org.freedesktop.resolve1.set-dns-over-tls">
                <description gettext-domain="systemd">Enable/disable DNS over TLS</description>
                <message gettext-domain="systemd">Authentication is required to enable or disable DNS over TLS.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.owner">unix-user:systemd-resolve</annotate>
        </action>

        <action id="org.freedesktop.resolve1.set-dnssec">
                <description gettext-domain="systemd">Enable/disable DNSSEC</description>
                <message gettext-domain="systemd">Authentication is required to enable or disable DNSSEC.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.owner">unix-user:systemd-resolve</annotate>
        </action>

        <action id="org.freedesktop.resolve1.set-dnssec-negative-trust-anchors">
                <description gettext-domain="systemd">Set DNSSEC Negative Trust Anchors</description>
                <message gettext-domain="systemd">Authentication is required to set DNSSEC Negative Trust Anchors.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.owner">unix-user:systemd-resolve</annotate>
        </action>

        <action id="org.freedesktop.resolve1.revert">
                <description gettext-domain="systemd">Revert name resolution settings</description>
                <message gettext-domain="systemd">Authentication is required to reset name resolution settings.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.owner">unix-user:systemd-resolve</annotate>
        </action>

        <action id="org.freedesktop.resolve1.subscribe-query-results">
                <description gettext-domain="systemd">Subscribe query results</description>
                <message gettext-domain="systemd">Authentication is required to subscribe query results.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.owner">unix-user:systemd-resolve</annotate>
        </action>

        <action id="org.freedesktop.resolve1.dump-cache">
                <description gettext-domain="systemd">Dump cache</description>
                <message gettext-domain="systemd">Authentication is required to dump cache.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.owner">unix-user:systemd-resolve</annotate>
        </action>

        <action id="org.freedesktop.resolve1.dump-server-state">
                <description gettext-domain="systemd">Dump server state</description>
                <message gettext-domain="systemd">Authentication is required to dump server state.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.owner">unix-user:systemd-resolve</annotate>
        </action>

        <action id="org.freedesktop.resolve1.dump-statistics">
                <description gettext-domain="systemd">Dump statistics</description>
                <message gettext-domain="systemd">Authentication is required to dump statistics.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.owner">unix-user:systemd-resolve</annotate>
        </action>

        <action id="org.freedesktop.resolve1.reset-statistics">
                <description gettext-domain="systemd">Reset statistics</description>
                <message gettext-domain="systemd">Authentication is required to reset statistics.</message>
                <defaults>
                        <allow_any>auth_admin</allow_any>
                        <allow_inactive>auth_admin</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
                <annotate key="org.freedesktop.policykit.owner">unix-user:systemd-resolve</annotate>
        </action>

</policyconfig>
