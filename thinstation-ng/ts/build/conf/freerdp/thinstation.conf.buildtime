# Have a selection dialog for multiple servers.
FREERDP_SELECT_SERVER=true
FREERDP_SERVER_0="Laptop"
FREERDP_SERVER_0_OPTIONS="+drives"
FREERDP_KNOWN_HOST_0="Laptop 7a:a4:af:8a:98:da:50:52:ba:33:8d:e6:15:4e:49:7b:b3:f4:27:e6"
FREERDP_SERVER_1="HTPC"
FREERDP_SERVER_1_OPTIONS="/sec:rdp /cert-ignore +drives"

# Or use a single session
#SESSION_0_FREERDP_SERVER=***********
#SESSION_0_FREERDP_OPTIONS="/cert-ignore +drives /p:'password'"
#SESSION_0_FREERDP_USER="thinstation\user"
#SESSION_0_FREERDP_PASS_ENABLE=false

LOG_COMMANDS=true

SESSION_0_TYPE=freerdp
SESSION_0_AUTOSTART=on

ALWAYS_ENTER_SERVER=true
ALLOW_SERVER_EDITS=false

NO_SESSION="shutdown"

AUDIO_LEVEL=90
MIC_LEVEL=0

NET_USE=BOTH
NET_USE_DHCP=on
NET_HOSTNAME=ts-*

TIME_ZONE=America/Los_Angeles

BIND_MOUNT0="home:/root"
BIND_MOUNT1="boot:/boot"

USB_STORAGE_SYNC=on
BASE_MOUNT_PATH=/media
USB_MOUNT_DIR="."
#USB_MOUNT_OPTIONS="gid=1000,uid=1000"

GTK_THEME=Adwaita
ICONS_THEME=Adwaita
