<?xml version="1.0" encoding="UTF-8"?> <!--*-nxml-*-->
<!DOCTYPE policyconfig PUBLIC "-//freedesktop//DTD PolicyKit Policy Configuration 1.0//EN"
        "https://www.freedesktop.org/standards/PolicyKit/1/policyconfig.dtd">

<!--
  SPDX-License-Identifier: LGPL-2.1-or-later

  This file is part of systemd.

  systemd is free software; you can redistribute it and/or modify it
  under the terms of the GNU Lesser General Public License as published by
  the Free Software Foundation; either version 2.1 of the License, or
  (at your option) any later version.
-->

<policyconfig>

        <vendor>The systemd Project</vendor>
        <vendor_url>https://systemd.io</vendor_url>

        <action id="io.systemd.credentials.encrypt">
                <description gettext-domain="systemd">Allow encryption and signing of system credentials.</description>
                <message gettext-domain="systemd">Authentication is required for an application to encrypt and sign a system credential.</message>
                <defaults>
                        <allow_any>auth_admin_keep</allow_any>
                        <allow_inactive>auth_admin_keep</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
        </action>

        <action id="io.systemd.credentials.decrypt">
                <description gettext-domain="systemd">Allow decryption of system credentials.</description>
                <message gettext-domain="systemd">Authentication is required for an application to decrypt a system credential.</message>
                <defaults>
                        <allow_any>auth_admin_keep</allow_any>
                        <allow_inactive>auth_admin_keep</allow_inactive>
                        <allow_active>auth_admin_keep</allow_active>
                </defaults>
        </action>
</policyconfig>
