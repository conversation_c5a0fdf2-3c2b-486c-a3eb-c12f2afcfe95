.\" Copyright (c) 1991, 1993
.\"   The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that the following conditions
.\" are met:
.\" 1. Redistributions of source code must retain the above copyright
.\"    notice, this list of conditions and the following disclaimer.
.\" 2. Redistributions in binary form must reproduce the above copyright
.\"    notice, this list of conditions and the following disclaimer in
.\"    the documentation and/or other materials provided with the
.\"    distribution.
.\" 3. [Deleted.  See
.\"     ftp://ftp.cs.berkeley.edu/pub/4bsd/README.Impt.License.Change]
.\" 4. Neither the name of the University nor the names of its
.\"    contributors may be used to endorse or promote products derived
.\"    from this software without specific prior written permission.
.\"
.\" THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS "AS IS"
.\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
.\" TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
.\" PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR
.\" CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
.\" SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
.\" LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
.\" USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
.\" ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
.\" OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
.\" OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
.\" SUCH DAMAGE.
.\"
.\"     @(#)doc-syms	8.1 (Berkeley) 06/08/93
.
.
.\" NS Ux user macro
.\" NS   format Unix
.\" NS
.\" NS modifies:
.\" NS   doc-arg-ptr
.\" NS   doc-curr-font
.\" NS   doc-macro-name
.\" NS
.\" NS local variable:
.\" NS   doc-str-Ux
.\" NS
.\" NS width register 'Ux' defined in doc-common
.
.eo
.de Ux
.  nr doc-curr-font \n[.f]
.  ds doc-str-Ux \f[\n[doc-curr-font]]
.
.  if !\n[doc-arg-count] \
.    if \n[.$] \{\
.      ds doc-macro-name Ux
.      doc-parse-args \$@
.    \}
.
.  \" replace current argument with result
.  ds doc-arg\n[doc-arg-ptr] Unix\*[doc-str-Ux]
.  rm doc-str-Ux
.  nr doc-type\n[doc-arg-ptr] 2
.  ds doc-space\n[doc-arg-ptr] "\*[doc-space]
.
.  \" recompute space vector for remaining arguments
.  nr doc-num-args (\n[doc-arg-count] - \n[doc-arg-ptr])
.  nr doc-arg-count \n[doc-arg-ptr]
.  if \n[doc-num-args] \
.    doc-parse-space-vector
.
.  doc-print-recursive
..
.ec
.
.
.\" NS Bx user macro
.\" NS   print BSD (fix smaller nroff version)
.\" NS
.\" NS modifies:
.\" NS   doc-arg-ptr
.\" NS   doc-curr-font
.\" NS   doc-macro-name
.\" NS
.\" NS local variable:
.\" NS   doc-str-Bx
.\" NS   doc-str-Bx1
.\" NS   doc-str-Bx-XXX
.\" NS
.\" NS width register 'Bx' defined in doc-common
.
.ds doc-str-Bx-Reno  \-Reno
.ds doc-str-Bx-reno  \-Reno
.ds doc-str-Bx-Tahoe \-Tahoe
.ds doc-str-Bx-tahoe \-Tahoe
.ds doc-str-Bx-Lite  \-Lite
.ds doc-str-Bx-lite  \-Lite
.ds doc-str-Bx-Lite2 \-Lite2
.ds doc-str-Bx-lite2 \-Lite2
.
.eo
.de Bx
.  nr doc-curr-font \n[.f]
.  ds doc-str-Bx \f[\n[doc-curr-font]]
.
.  \" default value if no argument
.  ds doc-str-Bx1 BSD\*[doc-str-Bx]
.
.  if !\n[doc-arg-count] \
.    if \n[.$] \{\
.      ds doc-macro-name Bx
.      doc-parse-args \$@
.    \}
.
.  if (\n[doc-arg-count] > \n[doc-arg-ptr]) \{\
.    nr doc-arg-ptr +1
.    ie (\n[doc-type\n[doc-arg-ptr]] == 2) \{\
.      ie        "\*[doc-arg\n[doc-arg-ptr]]"-alpha" \
.        as doc-str-Bx1 " (currently in alpha test)
.      el \{ .ie "\*[doc-arg\n[doc-arg-ptr]]"-beta" \
.        as doc-str-Bx1 " (currently in beta test)
.      el \{ .ie "\*[doc-arg\n[doc-arg-ptr]]"-devel" \
.        as doc-str-Bx1 " (currently under development)
.      el \{\
.        ds doc-str-Bx1 \&\*[doc-arg\n[doc-arg-ptr]]\^
.        as doc-str-Bx1 BSD\*[doc-str-Bx]
.
.        if (\n[doc-arg-count] > \n[doc-arg-ptr]) \{\
.          nr doc-arg-ptr +1
.          ie (\n[doc-type\n[doc-arg-ptr]] == 2) \{\
.            ie (\n[doc-type\n[doc-arg-ptr]] == 2) \{\
.              ie d doc-str-Bx-\*[doc-arg\n[doc-arg-ptr]] \
.                as doc-str-Bx1 "\*[doc-str-Bx-\*[doc-arg\n[doc-arg-ptr]]]
.              el \
.                nr doc-arg-ptr -1
.            \}
.            el \
.              nr doc-arg-ptr -1
.          \}
.          el \
.            nr doc-arg-ptr -1
.    \}\}\}\}\}
.    el \
.      nr doc-arg-ptr -1
.  \}
.
.  \" replace current argument with result
.  ds doc-arg\n[doc-arg-ptr] "\*[doc-str-Bx1]
.  nr doc-type\n[doc-arg-ptr] 2
.  ds doc-space\n[doc-arg-ptr] "\*[doc-space]
.
.  \" recompute space vector for remaining arguments
.  nr doc-num-args (\n[doc-arg-count] - \n[doc-arg-ptr])
.  nr doc-arg-count \n[doc-arg-ptr]
.  if \n[doc-num-args] \
.    doc-parse-space-vector
.
.  doc-print-recursive
..
.ec
.
.
.\" NS Ud user macro (not parsed, not callable)
.\" NS   print "currently under development" (HISTORY section)
.\" NS
.\" NS width register 'Ud' defined in doc-common
.
.eo
.de Ud
.  nop \&currently under development.
..
.ec
.
.
.\" NS At user macro
.\" NS   print AT&T UNIX
.\" NS
.\" NS modifies:
.\" NS   doc-arg-ptr
.\" NS   doc-curr-font
.\" NS   doc-macro-name
.\" NS
.\" NS local variable:
.\" NS   doc-str-At
.\" NS   doc-str-At1
.\" NS   doc-str-At-XXX
.\" NS
.\" NS width register 'At' defined in doc-common
.
.eo
.ds doc-str-At-32v \&Version\~7
.as doc-str-At-32v " AT&T UNIX\*[doc-str-At]/32V
.ds doc-str-At-v1  \&Version\~1
.as doc-str-At-v1  " AT&T UNIX\*[doc-str-At]
.ds doc-str-At-v2  \&Version\~2
.as doc-str-At-v2  " AT&T UNIX\*[doc-str-At]
.ds doc-str-At-v3  \&Version\~3
.as doc-str-At-v3  " AT&T UNIX\*[doc-str-At]
.ds doc-str-At-v4  \&Version\~4
.as doc-str-At-v4  " AT&T UNIX\*[doc-str-At]
.ds doc-str-At-v5  \&Version\~5
.as doc-str-At-v5  " AT&T UNIX\*[doc-str-At]
.ds doc-str-At-v6  \&Version\~6
.as doc-str-At-v6  " AT&T UNIX\*[doc-str-At]
.ds doc-str-At-v7  \&Version\~7
.as doc-str-At-v7  " AT&T UNIX\*[doc-str-At]
.ds doc-str-At-III AT&T\*[doc-str-At] System\~III
.as doc-str-At-III " UNIX\*[doc-str-At]
.ds doc-str-At-V   AT&T\*[doc-str-At] System\~V
.as doc-str-At-V   " UNIX\*[doc-str-At]
.ds doc-str-At-V.1 AT&T\*[doc-str-At] System\~V Release\~1
.as doc-str-At-V.1 " UNIX\*[doc-str-At]
.ds doc-str-At-V.2 AT&T\*[doc-str-At] System\~V Release\~2
.as doc-str-At-V.2 " UNIX\*[doc-str-At]
.ds doc-str-At-V.3 AT&T\*[doc-str-At] System\~V Release\~3
.as doc-str-At-V.3 " UNIX\*[doc-str-At]
.ds doc-str-At-V.4 AT&T\*[doc-str-At] System\~V Release\~4
.as doc-str-At-V.4 " UNIX\*[doc-str-At]
.
.de At
.  nr doc-curr-font \n[.f]
.  ds doc-str-At \f[\n[doc-curr-font]]
.
.  \" default value if no argument
.  ds doc-str-At1 AT&T UNIX\*[doc-str-At]
.
.  if !\n[doc-arg-count] \
.    if \n[.$] \{\
.      ds doc-macro-name At
.      doc-parse-args \$@
.    \}
.
.  if (\n[doc-arg-count] > \n[doc-arg-ptr]) \{\
.    nr doc-arg-ptr +1
.    ie (\n[doc-type\n[doc-arg-ptr]] == 2) \{\
.      ie \A'\*[doc-arg\n[doc-arg-ptr]]' \{\
.        ie d doc-str-At-\*[doc-arg\n[doc-arg-ptr]] \
.          ds doc-str-At1 "\*[doc-str-At-\*[doc-arg\n[doc-arg-ptr]]]
.        el \{\
.          tmc mdoc warning: .At: Unknown AT&T UNIX version
.          tm1 " '\*[doc-arg\n[doc-arg-ptr]]' (#\n[.c])
.          nr doc-arg-ptr -1
.      \}\}
.      el \
.        nr doc-arg-ptr -1
.    \}
.    el \
.      nr doc-arg-ptr -1
.  \}
.
.  \" replace current argument with result
.  ds doc-arg\n[doc-arg-ptr] "\*[doc-str-At1]
.  nr doc-type\n[doc-arg-ptr] 2
.  ds doc-space\n[doc-arg-ptr] "\*[doc-space]
.
.  \" recompute space vector for remaining arguments
.  nr doc-num-args (\n[doc-arg-count] - \n[doc-arg-ptr])
.  nr doc-arg-count \n[doc-arg-ptr]
.  if \n[doc-num-args] \
.    doc-parse-space-vector
.
.  doc-print-recursive
..
.ec
.
.
.\" NS Dx user macro
.\" NS   print DragonFly
.\" NS
.\" NS modifies:
.\" NS   doc-arg-ptr
.\" NS   doc-curr-font
.\" NS   doc-macro-name
.\" NS
.\" NS local variable:
.\" NS   doc-str-Dx
.\" NS   doc-str-Dx1
.\" NS
.\" NS width register 'Dx' defined in doc-common
.
.\" we use the doc-operating-system-DragonFly-* strings defined in
.\" doc-common
.
.eo
.de Dx
.  nr doc-curr-font \n[.f]
.  ds doc-str-Dx \f[\n[doc-curr-font]]
.
.  \" default value if no argument
.  ds doc-str-Dx1 \%DragonFly\*[doc-str-Dx]
.
.  if !\n[doc-arg-count] \
.    if \n[.$] \{\
.      ds doc-macro-name Dx
.      doc-parse-args \$@
.    \}
.
.  if (\n[doc-arg-count] > \n[doc-arg-ptr]) \{\
.    nr doc-arg-ptr +1
.    ie (\n[doc-type\n[doc-arg-ptr]] == 2) \{\
.      ie \A'\*[doc-arg\n[doc-arg-ptr]]' \{\
.        ie d doc-operating-system-DragonFly-\*[doc-arg\n[doc-arg-ptr]] \
.          as doc-str-Dx1 \~\*[doc-operating-system-DragonFly-\*[doc-arg\n[doc-arg-ptr]]]
.        el \{\
.          tmc mdoc warning: .Dx: Unknown DragonFly version
.          tm1 " '\*[doc-arg\n[doc-arg-ptr]]' (#\n[.c])
.          as doc-str-Dx1 \~\*[doc-arg\n[doc-arg-ptr]]
.      \}\}
.      el \
.        as doc-str-Dx1 \~\*[doc-arg\n[doc-arg-ptr]]
.    \}
.    el \
.      nr doc-arg-ptr -1
.  \}
.
.  \" replace current argument with result
.  ds doc-arg\n[doc-arg-ptr] "\*[doc-str-Dx1]
.  nr doc-type\n[doc-arg-ptr] 2
.  ds doc-space\n[doc-arg-ptr] "\*[doc-space]
.
.  \" recompute space vector for remaining arguments
.  nr doc-num-args (\n[doc-arg-count] - \n[doc-arg-ptr])
.  nr doc-arg-count \n[doc-arg-ptr]
.  if \n[doc-num-args] \
.    doc-parse-space-vector
.
.  doc-print-recursive
..
.ec
.
.
.\" NS Fx user macro
.\" NS   print FreeBSD
.\" NS
.\" NS modifies:
.\" NS   doc-arg-ptr
.\" NS   doc-curr-font
.\" NS   doc-macro-name
.\" NS
.\" NS local variable:
.\" NS   doc-str-Fx
.\" NS   doc-str-Fx1
.\" NS
.\" NS width register 'Fx' defined in doc-common
.
.\" we use the doc-operating-system-FreeBSD-* strings defined in
.\" doc-common
.
.eo
.de Fx
.  nr doc-curr-font \n[.f]
.  ds doc-str-Fx \f[\n[doc-curr-font]]
.
.  \" default value if no argument
.  ds doc-str-Fx1 \%FreeBSD\*[doc-str-Fx]
.
.  if !\n[doc-arg-count] \
.    if \n[.$] \{\
.      ds doc-macro-name Fx
.      doc-parse-args \$@
.    \}
.
.  if (\n[doc-arg-count] > \n[doc-arg-ptr]) \{\
.    nr doc-arg-ptr +1
.    ie (\n[doc-type\n[doc-arg-ptr]] == 2) \{\
.      ie \A'\*[doc-arg\n[doc-arg-ptr]]' \{\
.        ie d doc-operating-system-FreeBSD-\*[doc-arg\n[doc-arg-ptr]] \
.          as doc-str-Fx1 \~\*[doc-operating-system-FreeBSD-\*[doc-arg\n[doc-arg-ptr]]]
.        el \{\
.          tmc mdoc warning: .Fx: Unknown FreeBSD version
.          tm1 " '\*[doc-arg\n[doc-arg-ptr]]' (#\n[.c])
.          as doc-str-Fx1 \~\*[doc-arg\n[doc-arg-ptr]]
.      \}\}
.      el \
.        as doc-str-Fx1 \~\*[doc-arg\n[doc-arg-ptr]]
.    \}
.    el \
.      nr doc-arg-ptr -1
.  \}
.
.  \" replace current argument with result
.  ds doc-arg\n[doc-arg-ptr] "\*[doc-str-Fx1]
.  nr doc-type\n[doc-arg-ptr] 2
.  ds doc-space\n[doc-arg-ptr] "\*[doc-space]
.
.  \" recompute space vector for remaining arguments
.  nr doc-num-args (\n[doc-arg-count] - \n[doc-arg-ptr])
.  nr doc-arg-count \n[doc-arg-ptr]
.  if \n[doc-num-args] \
.    doc-parse-space-vector
.
.  doc-print-recursive
..
.ec
.
.
.\" NS Nx user macro
.\" NS   print NetBSD
.\" NS
.\" NS modifies:
.\" NS   doc-arg-ptr
.\" NS   doc-curr-font
.\" NS   doc-macro-name
.\" NS
.\" NS local variable:
.\" NS   doc-str-Nx
.\" NS   doc-str-Nx1
.\" NS
.\" NS width register 'Nx' defined in doc-common
.
.\" we use the doc-operating-system-NetBSD-* strings defined in
.\" doc-common
.
.eo
.de Nx
.  nr doc-curr-font \n[.f]
.  ds doc-str-Nx \f[\n[doc-curr-font]]
.
.  \" default value if no argument
.  ds doc-str-Nx1 \%Net
.  as doc-str-Nx1 BSD\*[doc-str-Nx]
.
.  if !\n[doc-arg-count] \
.    if \n[.$] \{\
.      ds doc-macro-name Nx
.      doc-parse-args \$@
.    \}
.
.  if (\n[doc-arg-count] > \n[doc-arg-ptr]) \{\
.    nr doc-arg-ptr +1
.    ie (\n[doc-type\n[doc-arg-ptr]] == 2) \{\
.      ie \A'\*[doc-arg\n[doc-arg-ptr]]' \{\
.        ie d doc-operating-system-NetBSD-\*[doc-arg\n[doc-arg-ptr]] \
.          as doc-str-Nx1 \~\*[doc-operating-system-NetBSD-\*[doc-arg\n[doc-arg-ptr]]]
.        el \{\
.          tmc mdoc warning: .Nx: Unknown NetBSD version
.          tm1 " '\*[doc-arg\n[doc-arg-ptr]]' (#\n[.c])
.          as doc-str-Nx1 \~\*[doc-arg\n[doc-arg-ptr]]
.      \}\}
.      el \
.        as doc-str-Nx1 \~\*[doc-arg\n[doc-arg-ptr]]
.    \}
.    el \
.      nr doc-arg-ptr -1
.  \}
.
.  \" replace current argument with result
.  ds doc-arg\n[doc-arg-ptr] "\*[doc-str-Nx1]
.  nr doc-type\n[doc-arg-ptr] 2
.  ds doc-space\n[doc-arg-ptr] "\*[doc-space]
.
.  \" recompute space vector for remaining arguments
.  nr doc-num-args (\n[doc-arg-count] - \n[doc-arg-ptr])
.  nr doc-arg-count \n[doc-arg-ptr]
.  if \n[doc-num-args] \
.    doc-parse-space-vector
.
.  doc-print-recursive
..
.ec
.
.
.\" NS Ox user macro
.\" NS   print OpenBSD
.\" NS
.\" NS modifies:
.\" NS   doc-arg-ptr
.\" NS   doc-curr-font
.\" NS   doc-macro-name
.\" NS
.\" NS local variable:
.\" NS   doc-str-Ox
.\" NS   doc-str-Ox1
.\" NS
.\" NS width register 'Ox' defined in doc-common
.
.eo
.de Ox
.  nr doc-curr-font \n[.f]
.  ds doc-str-Ox \f[\n[doc-curr-font]]
.
.  \" default value if no argument
.  ds doc-str-Ox1 \%OpenBSD\*[doc-str-Ox]
.
.  if !\n[doc-arg-count] \
.    if \n[.$] \{\
.      ds doc-macro-name Ox
.      doc-parse-args \$@
.    \}
.
.  if (\n[doc-arg-count] > \n[doc-arg-ptr]) \{\
.    nr doc-arg-ptr +1
.    ie (\n[doc-type\n[doc-arg-ptr]] == 2) \
.      as doc-str-Ox1 \~\*[doc-arg\n[doc-arg-ptr]]
.    el \
.      nr doc-arg-ptr -1
.  \}
.
.  \" replace current argument with result
.  ds doc-arg\n[doc-arg-ptr] "\*[doc-str-Ox1]
.  nr doc-type\n[doc-arg-ptr] 2
.  ds doc-space\n[doc-arg-ptr] "\*[doc-space]
.
.  \" recompute space vector for remaining arguments
.  nr doc-num-args (\n[doc-arg-count] - \n[doc-arg-ptr])
.  nr doc-arg-count \n[doc-arg-ptr]
.  if \n[doc-num-args] \
.    doc-parse-space-vector
.
.  doc-print-recursive
..
.ec
.
.
.\" NS Bsx user macro
.\" NS   print BSD/OS
.\" NS
.\" NS modifies:
.\" NS   doc-arg-ptr
.\" NS   doc-curr-font
.\" NS   doc-macro-name
.\" NS
.\" NS local variable:
.\" NS   doc-str-Bsx
.\" NS   doc-str-Bsx1
.\" NS
.\" NS width register 'Bsx' defined in doc-common
.
.eo
.de Bsx
.  nr doc-curr-font \n[.f]
.  ds doc-str-Bsx \f[\n[doc-curr-font]]
.
.  \" default value if no argument
.  ds doc-str-Bsx1 BSD/OS\*[doc-str-Bsx]
.
.  if !\n[doc-arg-count] \
.    if \n[.$] \{\
.      ds doc-macro-name Bsx
.      doc-parse-args \$@
.    \}
.
.  if (\n[doc-arg-count] > \n[doc-arg-ptr]) \{\
.    nr doc-arg-ptr +1
.    ie (\n[doc-type\n[doc-arg-ptr]] == 2) \
.      as doc-str-Bsx1 \~\*[doc-arg\n[doc-arg-ptr]]
.    el \
.      nr doc-arg-ptr -1
.  \}
.
.  \" replace current argument with result
.  ds doc-arg\n[doc-arg-ptr] "\*[doc-str-Bsx1]
.  nr doc-type\n[doc-arg-ptr] 2
.  ds doc-space\n[doc-arg-ptr] "\*[doc-space]
.
.  \" recompute space vector for remaining arguments
.  nr doc-num-args (\n[doc-arg-count] - \n[doc-arg-ptr])
.  nr doc-arg-count \n[doc-arg-ptr]
.  if \n[doc-num-args] \
.    doc-parse-space-vector
.
.  doc-print-recursive
..
.ec
.
.
.\" The Bt macro should go away now
.
.\" NS Bt user macro (not parsed, not callable)
.\" NS   print "is currently in beta test." (HISTORY section)
.\" NS
.\" NS width register 'Bt' defined in doc-common
.
.eo
.de Bt
.  nop \&is currently in beta test.
..
.ec
.
.
.\" NS Px user macro
.\" NS   print POSIX
.
.eo
.ds Px \%POSIX
.ec
.
.
.\" NS Ai user macro
.\" NS   print ANSI
.
.eo
.ds Ai \%ANSI
.ec
.
.
.\" NS St user macro
.\" NS   standards (posix, ansi - formal standard names)
.\" NS
.\" NS modifies:
.\" NS   doc-arg-ptr
.\" NS   doc-curr-font
.\" NS   doc-macro-name
.\" NS
.\" NS local variable:
.\" NS   doc-reg-St
.\" NS   doc-str-St
.\" NS   doc-str-St1
.\" NS   doc-str-St-XXX
.\" NS
.\" NS width register 'St' defined in doc-common
.
.\" ANSI/ISO C
.eo
.ds doc-str-St--ansiC-89       \*[Ai] \*[doc-str-St]X\^3.159-1989
.as doc-str-St--ansiC-89       " (\*[Lq]\)\*[Ai]\~C89\*[doc-str-St]\*[Rq])
.als doc-str-St--ansiC doc-str-St--ansiC-89
.ds doc-str-St--isoC           ISO/IEC\*[doc-str-St] 9899:1990
.as doc-str-St--isoC           " (\*[Lq]ISO\~C\^90\*[doc-str-St]\*[Rq])
.als doc-str-St--isoC-90 doc-str-St--isoC
.ds doc-str-St--isoC-2011      ISO/IEC\*[doc-str-St] 9899:2011
.as doc-str-St--isoC-2011      " (\*[Lq]ISO\~C\^11\*[doc-str-St]\*[Rq])
.ds doc-str-St--isoC-99        ISO/IEC\*[doc-str-St] 9899:1999
.as doc-str-St--isoC-99        " (\*[Lq]ISO\~C\^99\*[doc-str-St]\*[Rq])
.ds doc-str-St--isoC-amd1       ISO/IEC\*[doc-str-St] 9899/AMD1:1995
.as doc-str-St--isoC-amd1       " (\*[Lq]ISO\~C\^90\*[doc-str-St], Amendment 1\*[Rq])
.ds doc-str-St--isoC-tcor1      ISO/IEC\*[doc-str-St] 9899/TCOR1:1994
.as doc-str-St--isoC-tcor1      " (\*[Lq]ISO\~C\^90\*[doc-str-St], Technical Corrigendum 1\*[Rq])
.ds doc-str-St--isoC-tcor2      ISO/IEC\*[doc-str-St] 9899/TCOR2:1995
.as doc-str-St--isoC-tcor2      " (\*[Lq]ISO\~C\^90\*[doc-str-St], Technical Corrigendum 2\*[Rq])
.ec
.
.\" POSIX Part 1: System API
.eo
.ds doc-str-St--p1003.1        \%IEEE\*[doc-str-St] Std 1003.1
.as doc-str-St--p1003.1        " (\*[Lq]\)\*[Px]\*[doc-str-St].1\*[Rq])
.ds doc-str-St--p1003.1b       \%IEEE\*[doc-str-St] Std 1003.1b
.as doc-str-St--p1003.1b       " (\*[Lq]\)\*[Px]\*[doc-str-St].1\*[Rq])
.ds doc-str-St--p1003.1-88     \%IEEE\*[doc-str-St] Std 1003.1-1988
.as doc-str-St--p1003.1-88     " (\*[Lq]\)\*[Px]\*[doc-str-St].1\*[Rq])
.ds doc-str-St--p1003.1-90     ISO/IEC\*[doc-str-St] 9945-1:1990
.as doc-str-St--p1003.1-90     " (\*[Lq]\)\*[Px]\*[doc-str-St].1\*[Rq])
.als doc-str-St--iso9945-1-90 doc-str-St--p1003.1-90
.ds doc-str-St--p1003.1b-93    \%IEEE\*[doc-str-St] Std 1003.1b-1993
.as doc-str-St--p1003.1b-93    " (\*[Lq]\)\*[Px]\*[doc-str-St].1\*[Rq])
.ds doc-str-St--p1003.1c-95    \%IEEE\*[doc-str-St] Std 1003.1c-1995
.as doc-str-St--p1003.1c-95    " (\*[Lq]\)\*[Px]\*[doc-str-St].1\*[Rq])
.ds doc-str-St--p1003.1i-95    \%IEEE\*[doc-str-St] Std 1003.1i-1995
.as doc-str-St--p1003.1i-95    " (\*[Lq]\)\*[Px]\*[doc-str-St].1\*[Rq])
.ds doc-str-St--p1003.1-96     ISO/IEC\*[doc-str-St] 9945-1:1996
.as doc-str-St--p1003.1-96     " (\*[Lq]\)\*[Px]\*[doc-str-St].1\*[Rq])
.als doc-str-St--iso9945-1-96 doc-str-St--p1003.1-96
.ds doc-str-St--p1003.1g-2000  \%IEEE\*[doc-str-St] Std 1003.1g-2000
.as doc-str-St--p1003.1g-2000  " (\*[Lq]\)\*[Px]\*[doc-str-St].1\*[Rq])
.ds doc-str-St--p1003.1-2001   \%IEEE\*[doc-str-St] Std 1003.1-2001
.as doc-str-St--p1003.1-2001   " (\*[Lq]\)\*[Px]\*[doc-str-St].1\*[Rq])
.ds doc-str-St--p1003.1-2004   \%IEEE\*[doc-str-St] Std 1003.1-2004
.as doc-str-St--p1003.1-2004   " (\*[Lq]\)\*[Px]\*[doc-str-St].1\*[Rq])
.ds doc-str-St--p1003.1-2008   \%IEEE\*[doc-str-St] Std 1003.1-2008
.as doc-str-St--p1003.1-2008   " (\*[Lq]\)\*[Px]\*[doc-str-St].1\*[Rq])
.ec
.
.\" POSIX Part 2: Shell and Utilities
.eo
.ds doc-str-St--p1003.2        \%IEEE\*[doc-str-St] Std 1003.2
.as doc-str-St--p1003.2        " (\*[Lq]\)\*[Px]\*[doc-str-St].2\*[Rq])
.ds doc-str-St--p1003.2-92     \%IEEE\*[doc-str-St] Std 1003.2-1992
.as doc-str-St--p1003.2-92     " (\*[Lq]\)\*[Px]\*[doc-str-St].2\*[Rq])
.ds doc-str-St--p1003.2a-92    \%IEEE\*[doc-str-St] Std 1003.2a-1992
.as doc-str-St--p1003.2a-92    " (\*[Lq]\)\*[Px]\*[doc-str-St].2\*[Rq])
.ds doc-str-St--iso9945-2-93   ISO/IEC\*[doc-str-St] 9945-2:1993
.as doc-str-St--iso9945-2-93   " (\*[Lq]\)\*[Px]\*[doc-str-St].2\*[Rq])
.ec
.
.\" X/Open
.eo
.ds doc-str-St--susv1          Version\~1 of the Single UNIX\*[doc-str-St] Specification
.as doc-str-St--susv1          " (\*[Lq]SUSv1\*[doc-str-St]\*[Rq])
.ds doc-str-St--susv2          Version\~2 of the Single UNIX\*[doc-str-St] Specification
.as doc-str-St--susv2          " (\*[Lq]SUSv2\*[doc-str-St]\*[Rq])
.ds doc-str-St--susv3          Version\~3 of the Single UNIX\*[doc-str-St] Specification
.as doc-str-St--susv3          " (\*[Lq]SUSv3\*[doc-str-St]\*[Rq])
.ds doc-str-St--susv4          Version\~4 of the Single UNIX\*[doc-str-St] Specification
.as doc-str-St--susv4          " (\*[Lq]SUSv4\*[doc-str-St]\*[Rq])
.ds doc-str-St--svid4          System\~V Interface Definition, Fourth Edition
.as doc-str-St--svid4          " (\*[Lq]SVID\*[doc-str-St]\^4\*[Rq])
.ds doc-str-St--xbd5           X/Open\*[doc-str-St] Base Definitions Issue\~5
.as doc-str-St--xbd5           " (\*[Lq]XBD\*[doc-str-St]\^5\*[Rq])
.ds doc-str-St--xcu5           X/Open\*[doc-str-St] Commands and Utilities Issue\~5
.as doc-str-St--xcu5           " (\*[Lq]XCU\*[doc-str-St]\^5\*[Rq])
.ds doc-str-St--xcurses4.2     X/Open\*[doc-str-St] Curses Issue\~4, Version\~2
.as doc-str-St--xcurses4.2     " (\*[Lq]XCURSES\*[doc-str-St]\^4.2\*[Rq])
.ds doc-str-St--xns5           X/Open\*[doc-str-St] Networking Services Issue\~5
.as doc-str-St--xns5           " (\*[Lq]XNS\*[doc-str-St]\^5\*[Rq])
.ds doc-str-St--xns5.2         X/Open\*[doc-str-St] Networking Services Issue\~5.2
.as doc-str-St--xns5.2         " (\*[Lq]XNS\*[doc-str-St]\^5.2\*[Rq])
.ds doc-str-St--xpg3           X/Open\*[doc-str-St] Portability Guide Issue\~3
.as doc-str-St--xpg3           " (\*[Lq]XPG\*[doc-str-St]\^3\*[Rq])
.ds doc-str-St--xpg4           X/Open\*[doc-str-St] Portability Guide Issue\~4
.as doc-str-St--xpg4           " (\*[Lq]XPG\*[doc-str-St]\^4\*[Rq])
.ds doc-str-St--xpg4.2         X/Open\*[doc-str-St] Portability Guide Issue\~4, Version\~2
.as doc-str-St--xpg4.2         " (\*[Lq]XPG\*[doc-str-St]\^4.2\*[Rq])
.ds doc-str-St--xsh5           X/Open\*[doc-str-St] System Interfaces and Headers Issue\~5
.as doc-str-St--xsh5           " (\*[Lq]XSH\*[doc-str-St]\^5\*[Rq])
.ec
.
.\" Miscellaneous
.eo
.ds doc-str-St--ieee754        \%IEEE\*[doc-str-St] Std 754-1985
.ds doc-str-St--ieee1275-94     \%IEEE\*[doc-str-St] Std 1275-1994
.as doc-str-St--ieee1275-94     " (\*[Lq]Open Firmware\*[doc-str-St]\*[Rq])
.ds doc-str-St--iso8601        ISO\*[doc-str-St] 8601
.ds doc-str-St--iso8802-3      ISO/IEC\*[doc-str-St] 8802-3:1989
.ec
.
.eo
.de St
.  if !\n[doc-arg-count] \{\
.    ie \n[.$] \{\
.      ds doc-macro-name St
.      doc-parse-args \$@
.    \}
.    el \
.      doc-St-usage
.  \}
.
.  if !\n[doc-arg-count] \
.    return
.
.  nr doc-arg-ptr +1
.  ie (\n[doc-arg-count] >= \n[doc-arg-ptr]) \{\
.    nr doc-curr-font \n[.f]
.    ds doc-str-St \f[\n[doc-curr-font]]
.
.    ds doc-str-St1
.    ie \A'\*[doc-arg\n[doc-arg-ptr]]' \{\
.      ie d doc-str-St-\*[doc-arg\n[doc-arg-ptr]] \
.        ds doc-str-St1 "\*[doc-str-St-\*[doc-arg\n[doc-arg-ptr]]]
.      el \{\
.        tmc "mdoc warning: .St: Unknown standard abbreviation
.        tm1 " '\*[doc-arg\n[doc-arg-ptr]]' (#\n[.c])
.        tm1 "              Please refer to the groff_mdoc(7) manpage for a
.        tm1 "              list of available standard abbreviations.
.    \}\}
.    el \
.      doc-St-usage
.
.    \" replacing argument with result
.    ds doc-arg\n[doc-arg-ptr] "\*[doc-str-St1]
.
.    doc-print-recursive
.  \}
.  el \{\
.    doc-St-usage
.    doc-reset-args
.  \}
..
.ec
.
.
.\" NS doc-St-usage macro
.
.eo
.de doc-St-usage
.  tm1 "Usage: .St standard (#\n[.c])
.  tm1 "       Please refer to the groff_mdoc(7) manpage for a list of
.  tm1 "       available standard abbreviations.
..
.ec
.
.
.\" NS Lb user macro
.\" NS   formal library names for LIBRARY sections
.\" NS
.\" NS modifies:
.\" NS   doc-arg-ptr
.\" NS   doc-curr-font
.\" NS   doc-macro-name
.\" NS
.\" NS local variable:
.\" NS   doc-reg-Lb
.\" NS   doc-str-Lb
.\" NS   doc-str-Lb1
.\" NS   doc-str-Lb-XXX
.\" NS
.\" NS width register 'Lb' defined in doc-common
.
.eo
.ds doc-str-Lb-libarchive  Reading and Writing Streaming Archives Library (libarchive, \-larchive)
.ds doc-str-Lb-libarm      ARM Architecture Library (libarm, \-larm)
.ds doc-str-Lb-libarm32    ARM32 Architecture Library (libarm32, \-larm32)
.ds doc-str-Lb-libbluetooth Bluetooth Library (libbluetooth, \-lbluetooth)
.ds doc-str-Lb-libbsm      Basic Security Module Library (libbsm, \-lbsm)
.ds doc-str-Lb-libc        Standard C\~Library (libc, \-lc)
.ds doc-str-Lb-libc_r      Reentrant C\~Library (libc_r, \-lc_r)
.ds doc-str-Lb-libcalendar Calendar Arithmetic Library (libcalendar, \-lcalendar)
.ds doc-str-Lb-libcam      Common Access Method User Library (libcam, \-lcam)
.ds doc-str-Lb-libcdk      Curses Development Kit Library (libcdk, \-lcdk)
.ds doc-str-Lb-libcipher   FreeSec Crypt Library (libcipher, \-lcipher)
.ds doc-str-Lb-libcompat   Compatibility Library (libcompat, \-lcompat)
.ds doc-str-Lb-libcrypt    Crypt Library (libcrypt, \-lcrypt)
.ds doc-str-Lb-libcurses   Curses Library (libcurses, \-lcurses)
.ds doc-str-Lb-libdevinfo  Device and Resource Information Utility Library (libdevinfo, \-ldevinfo)
.ds doc-str-Lb-libdevstat  Device Statistics Library (libdevstat, \-ldevstat)
.ds doc-str-Lb-libdisk     Interface to Slice and Partition Labels Library (libdisk, \-ldisk)
.ds doc-str-Lb-libdwarf    DWARF Access Library (libdwarf, \-ldwarf)
.ds doc-str-Lb-libedit     Command Line Editor Library (libedit, \-ledit)
.ds doc-str-Lb-libelf      ELF Access Library (libelf, \-lelf)
.ds doc-str-Lb-libevent    Event Notification Library (libevent, \-levent)
.ds doc-str-Lb-libfetch    File Transfer Library for URLs (libfetch, \-lfetch)
.ds doc-str-Lb-libform     Curses Form Library (libform, \-lform)
.ds doc-str-Lb-libgeom     Userland API Library for kernel GEOM subsystem (libgeom, \-lgeom)
.ds doc-str-Lb-libgpib     General-Purpose Instrument Bus (GPIB) library (libgpib, \-lgpib)
.ds doc-str-Lb-libi386     i386 Architecture Library (libi386, \-li386)
.ds doc-str-Lb-libintl     Internationalized Message Handling Library (libintl, \-lintl)
.ds doc-str-Lb-libipsec    IPsec Policy Control Library (libipsec, \-lipsec)
.ds doc-str-Lb-libipx      IPX Address Conversion Support Library (libipx, \-lipx)
.ds doc-str-Lb-libiscsi    iSCSI protocol library (libiscsi, \-liscsi)
.ds doc-str-Lb-libjail     Jail Library (libjail, \-ljail)
.ds doc-str-Lb-libkiconv   Kernel side iconv library (libkiconv, \-lkiconv)
.ds doc-str-Lb-libkse      N:M Threading Library (libkse, \-lkse)
.ds doc-str-Lb-libkvm      Kernel Data Access Library (libkvm, \-lkvm)
.ds doc-str-Lb-libm        Math Library (libm, \-lm)
.ds doc-str-Lb-libm68k     m68k Architecture Library (libm68k, \-lm68k)
.ds doc-str-Lb-libmagic    Magic Number Recognition Library (libmagic, \-lmagic)
.ds doc-str-Lb-libmd       Message Digest (MD4, MD5, etc.) Support Library (libmd, \-lmd)
.ds doc-str-Lb-libmemstat  Kernel Memory Allocator Statistics Library (libmemstat, \-lmemstat)
.ds doc-str-Lb-libmenu     Curses Menu Library (libmenu, \-lmenu)
.ds doc-str-Lb-libnetgraph Netgraph User Library (libnetgraph, \-lnetgraph)
.ds doc-str-Lb-libnetpgp   Netpgp signing, verification, encryption and decryption (libnetpgp, \-lnetpgp)
.ds doc-str-Lb-libossaudio OSS Audio Emulation Library (libossaudio, \-lossaudio)
.ds doc-str-Lb-libpam      Pluggable Authentication Module Library (libpam, \-lpam)
.ds doc-str-Lb-libpcap     Packet Capture Library (libpcap, \-lpcap)
.ds doc-str-Lb-libpci      PCI Bus Access Library (libpci, \-lpci)
.ds doc-str-Lb-libpmc      Performance Counters Library (libpmc, \-lpmc)
.ds doc-str-Lb-libposix    \*[Px] \*[doc-str-Lb]Compatibility Library (libposix, \-lposix)
.ds doc-str-Lb-libprop     Property Container Object Library (libprop, \-lprop)
.ds doc-str-Lb-libpthread  \*[Px] \*[doc-str-Lb]Threads Library (libpthread, \-lpthread)
.ds doc-str-Lb-libpuffs    puffs Convenience Library (libpuffs, \-lpuffs)
.ds doc-str-Lb-librefuse   File System in Userspace Convenience Library (librefuse, \-lrefuse)
.ds doc-str-Lb-libresolv   DNS Resolver Library (libresolv, \-lresolv)
.ds doc-str-Lb-librpcsec_gss RPC GSS-API Authentication Library (librpcsec_gss, \-lrpcsec_gss)
.ds doc-str-Lb-librpcsvc   RPC Service Library (librpcsvc, \-lrpcsvc)
.ds doc-str-Lb-librt       \*[Px] \*[doc-str-Lb]Real-time Library (librt, \-lrt)
.ds doc-str-Lb-libsdp      Bluetooth Service Discovery Protocol User Library (libsdp, \-lsdp)
.ds doc-str-Lb-libssp      Buffer Overflow Protection Library (libssp, \-lssp)
.ds doc-str-Lb-libSystem   System Library (libSystem, \-lSystem)
.ds doc-str-Lb-libtermcap  Termcap Access Library (libtermcap, \-ltermcap)
.ds doc-str-Lb-libterminfo Terminal Information Library (libterminfo, \-lterminfo)
.ds doc-str-Lb-libthr      1:1 Threading Library (libthr, \-lthr)
.ds doc-str-Lb-libufs      UFS File System Access Library (libufs, \-lufs)
.ds doc-str-Lb-libugidfw   File System Firewall Interface Library (libugidfw, \-lugidfw)
.ds doc-str-Lb-libulog     User Login Record Library (libulog, \-lulog)
.ds doc-str-Lb-libusbhid   USB Human Interface Devices Library (libusbhid, \-lusbhid)
.ds doc-str-Lb-libutil     System Utilities Library (libutil, \-lutil)
.ds doc-str-Lb-libvgl      Video Graphics Library (libvgl, \-lvgl)
.ds doc-str-Lb-libx86_64   x86_64 Architecture Library (libx86_64, \-lx86_64)
.ds doc-str-Lb-libz        Compression Library (libz, \-lz)
.ec
.
.eo
.de Lb
.  if !\n[doc-arg-count] \{\
.    ie \n[.$] \{\
.      ds doc-macro-name Lb
.      doc-parse-args \$@
.    \}
.    el \
.      tm Usage: .Lb library_name ... (#\n[.c])
.  \}
.
.  if !\n[doc-arg-count] \
.    return
.
.  nr doc-arg-ptr +1
.  ie (\n[doc-arg-count] >= \n[doc-arg-ptr]) \{\
.    nr doc-curr-font \n[.f]
.    ds doc-str-Lb \f[\n[doc-curr-font]]
.
.    ie d doc-str-Lb-\*[doc-arg\n[doc-arg-ptr]] \
.      ds doc-str-Lb1 "\*[doc-str-Lb-\*[doc-arg\n[doc-arg-ptr]]]
.    el \{\
.      tmc "mdoc warning: .Lb: no description for library
.      tm1 " '\*[doc-arg\n[doc-arg-ptr]]' available (#\n[.c])
.      ds doc-str-Lb1 library \*[Lq]\*[doc-arg\n[doc-arg-ptr]]\*[Rq]
.    \}
.
.    \" replacing argument with result
.    ds doc-arg\n[doc-arg-ptr] "\*[doc-str-Lb1]
.
.    if \n[doc-in-library-section] \
.      br
.    doc-print-recursive
.    if \n[doc-in-library-section] \
.      br
.  \}
.  el \{\
.    tm Usage: .Lb library_name ... (#\n[.c])
.    doc-reset-args
.  \}
..
.ec
.
.
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set filetype=groff textwidth=72:
