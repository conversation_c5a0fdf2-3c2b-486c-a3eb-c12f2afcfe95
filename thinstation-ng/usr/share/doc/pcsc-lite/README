Title   : PCSC Lite
Authors : <PERSON>, <PERSON><PERSON><PERSON>
Version : 1.2.9-beta10
License : See file COPYING
Document: docs/pcsc-lite.pdf
          docs/ifdhandler-3.pdf
Requires: A C compiler
          Make, linker tools
          Lex (Lexical Analyzer).

Tested Platforms:
	Linux 2.4 (Redhat 7.1, Suse 7.1/7.2, Debian 3.0)
	Mac OS X 10.2, 10.3
	FreeBSD, NetBSD, OpenBSD
	Solaris 8 / 9
	Tru64 UNIX 5.1B
	AIX 4.3, 5.2
	HP-UX 11

Welcome to PCSC Lite.  The purpose of PCSC Lite is to
provide a Windows(R) SCard interface in a very small form factor for
communicating to smart cards and readers. PCSC Lite can be compiled
directly to a desired reader driver or can be used to dynamically
allocate/deallocate reader drivers at runtime (default).

PCSC Lite uses the same winscard API as used under Windows(R).

For security aware persons please read the SECURITY file on possible
vulnerabilities to pcsclite, how you can fix some, and how some will
be fixed.

For information on how to install driver please read the DRIVERS file.

Memory cards are supported through MCT specifications which is an APDU
like manner sent normally through the SCardTransmit() function.  This
functionality is done in the driver.

INSTALLATION:

Installation is simple.  Type ./configure and then make and make install.
Options:

   ./configure

          --enable-libusb        Enable USB libusb support
                                 (on Linux, *BSD, etc.).
          --enable-usbdropdir=DIR directory containing USB drivers
                                  (default /usr/local/pcsc/drivers)
          --disable-musclecarddebug Disable MuscleCard debugging messages
          --enable-debugatr      Enable ATR parsing debug output
          --enable-scf           use SCF for reader support
          --prefix=location      Install to <location>
          --enable-confdir=DIR   Use DIR as the configuration directory
                                 (/etc by default)
          --enable-runpid=FILE   Store the daemon pid in file FILE
          --enable-ipcdir=DIR    directory containing IPC files
                                 (default /var/run)


By running pcscd under a privileged account you can link to
libpcsclite.so and it will act as a client to the pcscd allowing multiple
applications to be run under non-privileged accounts.

Then type "make install" to copy the libraries to /usr/local/lib. If you
choose not to have your reader configuration file in /etc/reader.conf.d/
then use --enable-confdir=DIR

You must be root to do make install in the default locations.

Be sure to edit the /etc/reader.conf.d/my_reader.conf file for your
reader to fit your needs and make sure your IFD Handler driver is
located in the path specified as LIBRARYPATH in my_reader.conf.  If you
are using a reader with multiple smart card slots all you have to do is
define a different FRIENDLYNAME in the my_reader.conf for each reader.
Both will have the same information otherwise.

USB users shall not use /etc/reader.conf.d/ to configure USB drivers.

There is a test program with this package:

testpcsc: Linked to libpcsclite.  Must run /usr/local/pcsc/bin/pcscd
          and then ./testpcsc.  pcscd must be run as root or a hardware
          privileged user.  ./testpcsc can be run under any account.

LIBUSB SUPPORT:
Versions after 1.2.0 support libusb. Available on GNU/Linux and *BSD.

If you do not have libusb (or do not want to use it) on your system you
should use --disable-libusb. If you use --disable-libusb under Linux the
old USB code will be used instead.

In order to use dynamic driver loading, you need to supply a directory
called "XXXX.bundle" in the usb dropdir directory (--enable-usbdropdir=PATH).
Here, the string "XXXX" stands for an arbitrary driver name, like in
"ifd-GemPC430.bundle". An example .bundle-directory can be found in the
source distribution of the ifd-gempc driver
(https://ifd-gempc.apdu.fr/)

SOLARIS:
Solaris PC/SC applications must link with -lsocket since
the libraries are not statically linked.

QUESTIONS:

The API documentation is provided in PDF format under the docs
directory.  For questions, please email me at:
<David Corcoran> <EMAIL>

On some plateforms (GNU/Linux on mips and mipsel, FreeBSD and possibly
other *BSD systems) you must use:
$ ./configure LDFLAGS="-lpthread"
