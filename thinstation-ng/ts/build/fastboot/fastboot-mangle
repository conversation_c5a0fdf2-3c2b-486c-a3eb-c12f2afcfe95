#!/bin/bash
# Fastboot by <PERSON>. don cupp jr at yahoo dot com

LANG=C
INITDIR=$1
HARDLINK=$3
FB_COMP_EXTRA=$4
echo FASTBOOT=$2 >> etc/thinstation.env
cp --remove-destination /bin/mount bin/.

if [ "$2" == "true" ] ; then
	mv bin lib64/.
	mv sbin lib64/.
	ln -sf /lib64/bin bin
	ln -sf /lib64/sbin sbin
	ln -sf . lib64/lib64
	mkdir lib64/etc
	mkdir -p fastboot/bin
	mkdir -p fastboot/sbin
	for i in opt jre lib32; do
		if [ -e $i ]; then
			mv $i lib64/.
			ln -sf /lib64/$i $i
		fi
	done
	for i in `cat ../fastboot/etc-ro` ; do
		if [ -e etc/$i ]; then
			mv etc/$i lib64/etc/.
			ln -sf /lib64/etc/$i etc/$i
		fi
	done
	for i in `cat ../fastboot/lib-rw` ; do
		if [ -e lib64/$<PERSON> ]; then
			mv lib64/$i var/.
			ln -sf /var/$i lib64/$i
		fi
	done
	for i in `ls --color=never usr |grep -Ewvf ../fastboot/usr-rw` ;do
		mv usr/$i lib64/.
		ln -sf /lib64/$i usr/$i
	done
	rm -f fastlibneed
	for i in `cat ../fastboot/bin-boot` ; do
		if [ -e lib64/bin/$i ]; then
			mv lib64/bin/$i fastboot/bin/.
			ln -sf /fastboot/bin/$i lib64/bin/$i
			ldd fastboot/bin/$i 2>/dev/null >> /tmp/fastlibneed
		fi
		if [ -e lib64/sbin/$i ]; then
			mv lib64/sbin/$i fastboot/sbin/.
			ln -sf /fastboot/sbin/$i lib64/sbin/$i
			ldd fastboot/sbin/$i 2>/dev/null >> /tmp/fastlibneed
		fi
	done
	if [ -e lib64/plymouth ]; then
		for so in `dir lib64/plymouth/*.so`; do
			ldd $so 2>/dev/null >> /tmp/fastlibneed
		done
		for so in lib64/plymouth/renderers/drm.so; do
			ldd $so 2>/dev/null >> /tmp/fastlibneed
		done
	fi
	for so in `dir lib64/security/*`; do
		ldd $so 2>/dev/null >> /tmp/fastlibneed
	done
	cat /tmp/fastlibneed |grep -v "not a dyn"|grep -v statically|grep -v ":"| sed -e 's/\t//g' |cut -d " " -f1 |sed -e 's|/lib64/||g' |sed -e 's|/lib/||g' |sort -u > /tmp/fastlib
	for lib in `cat ../fastboot/lib-boot; cat /tmp/fastlib; ls --color=never lib64 |grep -E 'libgcc_s.so.[0-1]+|libnss_files.so.[0-9]+|libnss_dns.so.[0-9]+'` ; do
		if [ -e lib64/$lib ]; then
			mv lib64/$lib fastboot/.
			ln -sf /fastboot/$lib lib64/$lib
		fi
	done
	if [ -e lib64/fontconfig/conf.avail/57-dejavu-sans-fonts.conf ] && [ -e lib64/fonts/dejavu-sans-fonts/DejaVuSans-Bold.ttf ]; then
		mkdir -p fastboot/fontconfig/conf.avail
		mkdir -p fastboot/fonts/dejavu-sans-fonts
		mv lib64/fontconfig/conf.avail/57-dejavu-sans-fonts.conf fastboot/fontconfig/conf.avail/.
		mv lib64/fonts/dejavu-sans-fonts/DejaVuSans-Bold.ttf fastboot/fonts/dejavu-sans-fonts/.
	fi
	echo -e "\tMaking lib.squash"
	timestamp="`date +%Y%m%d`0000"
	for file in `find lib64`; do
		touch -c -h -t $timestamp lib64/$file
	done
	if [ "$HARDLINK" == "true" ]; then
		hardlink lib64
	fi
	mksquashfs lib64 ../$INITDIR/lib.squash -always-use-fragments -no-recovery -noappend $FB_COMP_EXTRA
	for i in `ls --color=never lib64/ |grep -xvf ../fastboot/lib-boot |grep -xvf /tmp/fastlib |grep -Ewv 'bin|sbin|dbus-1' |grep -Ev 'libgcc_s.so.[0-1]+|libnss_files.so.[0-9]+|libnss_dns.so.[0-9]+'` ; do
		if [ -n "$i" ] ; then
			rm -rf lib64/$i
		fi
	done
	for i in `ls --color=never lib64/bin/ |grep -xvf ../fastboot/bin-boot` ; do
		if [ -n "$i" ]; then
			rm  -rf lib64/bin/$i
		fi
	done
	for i in `ls --color=never lib64/sbin/ |grep -xvf ../fastboot/bin-boot` ; do
		if [ -n "$i" ]; then
			rm  -rf lib64/sbin/$i
		fi
	done
	cd ..
	echo -e "\tRe-Linking BusyBox\n"
	chroot ./tmp-tree /sbin/busybox.shared --install -s
	cd tmp-tree
fi
if [ "$2" == "lotsofmem" ]; then
	mkdir -p ../fastboot-tmp/bin
	mkdir -p ../fastboot-tmp/sbin
	mkdir -p ../fastboot-tmp/lib64
	mkdir -p ../fastboot-tmp/etc
	mkdir -p ../fastboot-tmp/usr
	cp --remove-destination `which cp`		bin/.
	cp --remove-destination `which mv`		bin/.
	cp --remove-destination `which unsquashfs`	sbin/.
	for i in opt jre ; do
		if [ -e $i ] ; then
			mv $i ../fastboot-tmp/.
		fi
	done
	for i in `ls --color=never usr |grep -xvf ../fastboot/usr-rw` ; do
		mv usr/$i ../fastboot-tmp/usr/.
	done
	for i in `cat ../fastboot/etc-ro` ; do
		if [ -e etc/$i ]; then
			mv etc/$i ../fastboot-tmp/etc/.
		fi
	done
	for i in `ls --color=never bin/ |grep -xvf ../fastboot/bin-boot` ; do
		if [ -n "$i" ] && [ "`readlink bin/$i`" != "/sbin/busybox.shared" ]; then
			mv bin/$i ../fastboot-tmp/bin/.
		fi
	done
	for i in `ls --color=never sbin/ |grep -xvf ../fastboot/bin-boot` ; do
		if [ -n "$i" ] && [ "`readlink sbin/$i`" != "/sbin/busybox.shared" ]; then
			mv sbin/$i ../fastboot-tmp/sbin/.
		fi
	done
	ldd bin/* 2>/dev/null > /tmp/fastlibneed
	ldd sbin/* 2>/dev/null >> /tmp/fastlibneed
	if [ -e lib64/plymouth ]; then
		for so in `dir lib64/plymouth/*.so`; do
			ldd $so 2>/dev/null >> /tmp/fastlibneed
		done
                for so in lib64/plymouth/renderers/drm.so; do
                        ldd $so 2>/dev/null >> /tmp/fastlibneed
                done
	fi
        for so in `dir lib64/security/*`; do
                ldd $so 2>/dev/null >> /tmp/fastlibneed
        done
	cat /tmp/fastlibneed |grep -v "not a dyn"|grep -v statically|grep -v ":" |sed -e 's/\t//g' |cut -d " " -f1 |sed -e 's|/lib64/||g' |sed -e 's|/lib/||g' |sort -u > /tmp/fastlib
	for lib in `cat /tmp/fastlib |grep -v linux-vdso.so`; do
		if [ ! -e lib64/$lib ]; then
			libsource="`find /usr/lib /usr/lib64 -name $lib |grep -v vmware`"
			cp $libsource lib64/.
		fi
	done
	for i in `ls --color=never lib64/ |grep -xv fonts |grep -xv fontconfig |grep -xvf ../fastboot/lib-boot |grep -xvf /tmp/fastlib |grep -Ev 'libgcc_s.so.[0-1]+|libnss_files.so.[0-9]+|libnss_dns.so.[0-9]+'` ; do
		if [ -n "$i" ] ; then
			mv lib64/$i ../fastboot-tmp/lib64/.
		fi
	done
	if [ -e lib64/fontconfig/conf.avail/57-dejavu-sans-fonts.conf ] && [ -e lib64/fonts/dejavu-sans-fonts/DejaVuSans-Bold.ttf ]; then
	    mkdir -p ../fastboot-tmp/lib64/fonts/dejavu-sans-fonts
	    mkdir -p ../fastboot-tmp/lib64/fontconfig/conf.avail

	    # Move all font files except DejaVuSans-Bold.ttf
	    for font_file in lib64/fonts/dejavu-sans-fonts/*; do
	        if [[ "$font_file" != "lib64/fonts/dejavu-sans-fonts/DejaVuSans-Bold.ttf" ]]; then
        	    mv "$font_file" ../fastboot-tmp/lib64/fonts/dejavu-sans-fonts/.
	        fi
	    done
            for font in lib64/fonts/*; do
                if [[ "$font" != "lib64/fonts/dejavu-sans-fonts" ]]; then
                   mv "$font" ../fastboot-tmp/lib64/fonts/.
                fi
            done

	    # Move all font config files except 57-dejavu-sans-fonts.conf
	    for conf_file in lib64/fontconfig/conf.avail/*; do
	        if [[ "$conf_file" != "lib64/fontconfig/conf.avail/57-dejavu-sans-fonts.conf" ]]; then
	            mv "$conf_file" ../fastboot-tmp/lib64/fontconfig/conf.avail/.
	        fi
	    done
        fi
	if [ "$HARDLINK" == "true" ]; then
		hardlink ../fastboot-tmp
	fi
	echo -e "\tMaking lib.squash"
	mksquashfs ../fastboot-tmp/. ../$INITDIR/lib.squash -always-use-fragments -no-recovery -noappend $FB_COMP_EXTRA
fi

cd ..
chmod 755 $INITDIR/lib.squash
cd tmp-tree
