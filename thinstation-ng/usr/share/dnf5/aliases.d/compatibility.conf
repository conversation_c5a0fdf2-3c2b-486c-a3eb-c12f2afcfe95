version = '1.0'

########################################
# Global options compatibility aliases
########################################

['options-compatibility-aliases']
type = 'group'
header = "Options Compatibility aliases:"

['c']
type = 'cloned_named_arg'
short_name = 'c'
source = 'config'
group_id = 'options-compatibility-aliases'
complete = false

['nobest']
type = 'cloned_named_arg'
long_name = 'nobest'
source = 'no-best'
group_id = 'options-compatibility-aliases'

['nodocs']
type = 'cloned_named_arg'
long_name = 'nodocs'
source = 'no-docs'
group_id = 'options-compatibility-aliases'

['enablerepo']
type = 'cloned_named_arg'
long_name = 'enablerepo'
source = 'enable-repo'
group_id = 'options-compatibility-aliases'

['disablerepo']
type = 'cloned_named_arg'
long_name = 'disablerepo'
source = 'disable-repo'
group_id = 'options-compatibility-aliases'

['repoid']
type = 'cloned_named_arg'
long_name = 'repoid'
source = 'repo'
group_id = 'options-compatibility-aliases'

['nogpgcheck']
type = 'cloned_named_arg'
long_name = 'nogpgcheck'
source = 'no-gpgchecks'
group_id = 'options-compatibility-aliases'

['noplugins']
type = 'cloned_named_arg'
long_name = 'noplugins'
source = 'no-plugins'
group_id = 'options-compatibility-aliases'

['enableplugin']
type = 'cloned_named_arg'
long_name = 'enableplugin'
source = 'enable-plugin'
group_id = 'options-compatibility-aliases'

['disableplugin']
type = 'cloned_named_arg'
long_name = 'disableplugin'
source = 'disable-plugin'
group_id = 'options-compatibility-aliases'


########################################
# Commands compatibility aliases
########################################

['commands-compatibility-aliases']
type = 'group'
header = "Compatibility Aliases:"

# TODO(jkolarik): Uncomment when unneeded argument is ready
# ['autoremove']
# type = 'command'
# attached_command = 'remove'
# descr = "Alias for 'remove --unneeded'"
# group_id = 'commands-compatibility-aliases'
# complete = true
# attached_named_args = [
#  { id_path = 'remove.unneeded' }
# ]

['check-update']
type = 'command'
attached_command = 'check-upgrade'
descr = "Alias for 'check-upgrade'"
group_id = 'commands-compatibility-aliases'
complete = true

['dg']
type = 'command'
attached_command = 'downgrade'
descr = "Alias for 'downgrade'"
group_id = 'commands-compatibility-aliases'
complete = false

['dsync']
type = 'command'
attached_command = 'distro-sync'
descr = "Alias for 'distro-sync'"
group_id = 'commands-compatibility-aliases'
complete = false

['grp']
type = 'command'
attached_command = 'group'
descr = "Alias for 'group'"
group_id = 'commands-compatibility-aliases'
complete = true

['if']
type = 'command'
attached_command = 'info'
descr = "Alias for 'info'"
group_id = 'commands-compatibility-aliases'
complete = false

['in']
type = 'command'
attached_command = 'install'
descr = "Alias for 'install'"
group_id = 'commands-compatibility-aliases'
complete = false

['ls']
type = 'command'
attached_command = 'list'
descr = "Alias for 'list'"
group_id = 'commands-compatibility-aliases'
complete = false

['mc']
type = 'command'
attached_command = 'makecache'
descr = "Alias for 'makecache'"
group_id = 'commands-compatibility-aliases'
complete = false

['offline-distrosync.download']
type = 'command'
attached_command = 'distro-sync'
descr = "Alias for 'distro-sync --offline'"
complete = true
attached_named_args = [
  { id_path = 'distro-sync.offline' }
]

['offline-upgrade.download']
type = 'command'
attached_command = 'upgrade'
descr = "Alias for 'upgrade --offline'"
complete = true
attached_named_args = [
  { id_path = 'upgrade.offline' }
]

['rei']
type = 'command'
attached_command = 'reinstall'
descr = "Alias for 'reinstall'"
group_id = 'commands-compatibility-aliases'
complete = false

['repoinfo']
type = 'command'
attached_command = 'repo.info'
descr = "Alias for 'repo info'"
group_id = 'commands-compatibility-aliases'
complete = true

['repolist']
type = 'command'
attached_command = 'repo.list'
descr = "Alias for 'repo list'"
group_id = 'commands-compatibility-aliases'
complete = true

['rm']
type = 'command'
attached_command = 'remove'
descr = "Alias for 'remove'"
group_id = 'commands-compatibility-aliases'
complete = false

['rq']
type = 'command'
attached_command = 'repoquery'
descr = "Alias for 'repoquery'"
group_id = 'commands-compatibility-aliases'
complete = false

['se']
type = 'command'
attached_command = 'search'
descr = "Alias for 'search'"
group_id = 'commands-compatibility-aliases'
complete = false

['up']
type = 'command'
attached_command = 'upgrade'
descr = "Alias for 'upgrade'"
group_id = 'commands-compatibility-aliases'
complete = false

['update']
type = 'command'
attached_command = 'upgrade'
descr = "Alias for 'upgrade'"
group_id = 'commands-compatibility-aliases'
complete = false

['updateinfo']
type = 'command'
attached_command = 'advisory'
descr = "Alias for 'advisory'"
group_id = 'commands-compatibility-aliases'
complete = true

['upgrade-minimal']
type = 'command'
attached_command = 'upgrade'
descr = "Alias for 'upgrade --minimal'"
group_id = 'commands-compatibility-aliases'
complete = true
attached_named_args = [
 { id_path = 'upgrade.minimal' }
]


########################################
# List aliases
########################################

['list.updates']
type = 'cloned_named_arg'
long_name = 'updates'
source = 'list.upgrades'

########################################
# Info aliases
########################################

['info.updates']
type = 'cloned_named_arg'
long_name = 'updates'
source = 'info.upgrades'

########################################
# Advisory aliases
########################################

['advisory.info.bz']
type = 'cloned_named_arg'
long_name = 'bz'
source = 'advisory.info.bzs'

['advisory.info.cve']
type = 'cloned_named_arg'
long_name = 'cve'
source = 'advisory.info.cves'

['advisory.list.bz']
type = 'cloned_named_arg'
long_name = 'bz'
source = 'advisory.list.bzs'

['advisory.list.cve']
type = 'cloned_named_arg'
long_name = 'cve'
source = 'advisory.list.cves'

['advisory.summary.bz']
type = 'cloned_named_arg'
long_name = 'bz'
source = 'advisory.summary.bzs'

['advisory.summary.cve']
type = 'cloned_named_arg'
long_name = 'cve'
source = 'advisory.summary.cves'

['install.advisory']
type = 'cloned_named_arg'
long_name = 'advisory'
source = 'install.advisories'

['install.bz']
type = 'cloned_named_arg'
long_name = 'bz'
source = 'install.bzs'

['install.cve']
type = 'cloned_named_arg'
long_name = 'cve'
source = 'install.cves'

['repoquery.advisory']
type = 'cloned_named_arg'
long_name = 'advisory'
source = 'repoquery.advisories'

['repoquery.bz']
type = 'cloned_named_arg'
long_name = 'bz'
source = 'repoquery.bzs'

['repoquery.cve']
type = 'cloned_named_arg'
long_name = 'cve'
source = 'repoquery.cves'

['upgrade.advisory']
type = 'cloned_named_arg'
long_name = 'advisory'
source = 'upgrade.advisories'

['upgrade.bz']
type = 'cloned_named_arg'
long_name = 'bz'
source = 'upgrade.bzs'

['upgrade.cve']
type = 'cloned_named_arg'
long_name = 'cve'
source = 'upgrade.cves'

########################################
# Remove aliases
########################################

['remove.noautoremove']
type = 'cloned_named_arg'
long_name = 'noautoremove'
source = 'remove.no-autoremove'

########################################
# Repoquery aliases
########################################

['repoquery.qf']
type = 'cloned_named_arg'
long_name = 'qf'
source = 'repoquery.queryformat'
group_id = 'repoquery_formatting'

['repoquery.list']
type = 'cloned_named_arg'
long_name = 'list'
short_name = 'l'
source = 'repoquery.files'
group_id = 'repoquery_formatting'

########################################
# Download aliases
########################################

['download.source']
type = 'cloned_named_arg'
long_name = 'source'
source = 'download.srpm'
