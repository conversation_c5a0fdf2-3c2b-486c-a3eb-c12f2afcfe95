
##
# --- Permanent Storage Options
#
# STORAGE_PATH           Path to where storage device is mounted to save
#                        profile settings.  This should be one of
#                        /mnt/usbdevice/sdX/partX or disc
#                        /mnt/floppy
#                        /mnt/disc/hdX/partX
#                        /mnt/nfs
#                        /mnt/smb
#
#                        Note that the profile settings are stored under
#                        a subfolder for this path.  So the path to the
#                        stored settings would be something like
#                        /mnt/floppy/thinstation.profile
#                        See the FAQ on the website for more details on this
#
#                        Also checkout README.IMPORTANT for the valid
#                        config files which you can place here
#                        a typical file to store config file settings is
#                        /mnt/floppy/thinstation.profile/thinstation.conf.user
#
#                        Also note that the .profile can be changed by using
#                        the below STORAGE_PREFIX
# STORAGE_PREFIX         This is prefex for the folder name to store settings
#                        in on the storage device.   You can also use one of the
#                        special characters below.
#
#                        M = Mac Address
#                        H = Hostname
#                        I = Ip Address
#
#                        Note default prefix for storing the profile is
#                        .profile
# STORAGE_CONFIG1-8      This is the path for any user defined settings
#                        which will always override the profile path above
#                        The files are tried on order on each device specified


#STORAGE_PATH=/mnt/nfs
#STORAGE_PREFIX=H
#STORAGE_CONFIG1=/mnt/floppy
#STORAGE_CONFIG2=/mnt/cdrom
#STORAGE_CONFIG3=/mnt/disc/hda/part1
#STORAGE_CONFIG4=/mnt/usbdevice/sda/part1

