.ig
devtag.tmac - macro package for adding tags to roff documents.

------------------------------------------------------------------------
    Legalese
------------------------------------------------------------------------

This file is part of groff, the GNU roff type-setting system.

Copyright (C) 2004-2020 Free Software Foundation, Inc.
written by <PERSON> <<EMAIL>>.

groff is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License as published by the Free
Software Foundation, either version 3 of the License, or
(at your option) any later version.

groff is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
for more details.

You should have received a copy of the GNU General Public License
along with this program.  If not, see <http://www.gnu.org/licenses/>.


------------------------------------------------------------------------
    Description
------------------------------------------------------------------------

A simple set of macros to provide markup devices (currently only
grohtml) with tags that define the meaning of the formatted text and
also basic formatting instructions.  It generalizes the tag concept used
within grohtml and in the future it is hoped that more markup based
devices can capitalize on this work.  It also clearly defines those tags
which are honoured by grohtml.  Note that not all tags are included
here.  Some of the grohtml specific tags (header specific and jobname,
etc.) are called directly from within www.tmac.  The tags defined here
are reasonably generic and could be applied to other devices.
..
.
.do if d DEVTAG-NH .nx
.
.do nr *groff_devtag_tmac_C \n[.cp]
.cp 0
.
.\" --------------------------------------------------------------------
.\" DEVTAG <name>
.\"
.\"   Emit a tag <name>
.\"
.de1 DEVTAG
.  tag devtag:\\$*
..
.\" --------------------------------------------------------------------
.\" DEVTAG-NEXT <name>
.\"
.\"   When the troff state changes, emit tag <name>
.\"
.de1 DEVTAG-NEXT
.  taga devtag:\\$*
..
.
.\" --------------------------------------------------------------------
.\"  SH <level>
.\"  NH <level>
.\"       tell device we are starting a numbered heading
.\"       Takes a single parameter <level>. <level> 1
.\"       is the outer most level.
.
.de1 DEVTAG-NH
.   DEVTAG ".NH \\$1"
..
.als DEVTAG-SH DEVTAG-NH
.
.\" --------------------------------------------------------------------
.\"  COL <n>
.\"     indicate that the following text is aligned for the column <n>
.\"     n: [1..MAX(n)]
.
.de1 DEVTAG-COL
.   DEVTAG ".col \\$1"
..
.
.\" --------------------------------------------------------------------
.\"  EO-H
.\"     indicate that a header has finished.
.
.de1 DEVTAG-EO-H
.   DEVTAG ".eo.h"
..
.\" --------------------------------------------------------------------
.\"  EO-TL
.\"     indicate that a title has finished.
.
.de1 DEVTAG-EO-TL
.   DEVTAG ".eo.tl"
..
.\" --------------------------------------------------------------------
.\"  TL
.\"     indicate that the following text forms a title.
.
.de1 DEVTAG-TL
.   DEVTAG ".tl"
..
.
.\" --------------------------------------------------------------------
.\"  COL-NEXT <n>
.\"     emit a column tag just before the next glyph.
.
.de1 DEVTAG-COL-NEXT
.   DEVTAG-NEXT ".col \\$1"
..
.
.
.cp \n[*groff_devtag_tmac_C]
.do rr *groff_devtag_tmac_C
.
.
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set filetype=groff textwidth=72:
