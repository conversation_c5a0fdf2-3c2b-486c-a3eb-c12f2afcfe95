#! /bin/sh
# TS_GLOBAL

_shutdown ()
{
	UPID=`pidof udhcpc`
	if [ -n "$UPID" ] ; then
		kill -SIGUSR2 $UPID > /dev/null 2>&1
		kill -SIGHUP $UPID > /dev/null 2>&1
	fi

	busybox.shared umount -a -r -t ext2,ext3,ext4,fat,fat16,fat32,vfat,ntfs,nfs,hfs,affs,msdos,cifs,reiserfs,xfs,btrfs,romfs,jfs,hfsplus,jbd2,befs,ceph,orangefs
}

_systemfail ()
{
	echo "The system was not able to go graphical. This could indicate a problem with missing kernel modules or Xorg drivers."
	echo
	echo "The system is going to shutdown now"
	sleep 5
}

case $1 in
	-r)		_shutdown	;/bin/reboot			>/dev/null;;
	-h|*)		_shutdown	;/bin/poweroff			>/dev/null;;
	--systemfail)   _systemfail	;_shutdown ;/bin/poweroff	>/dev/null;;
esac
