#!/bin/bash
# Description: Thinstation Utility to strip out extra files from a binary Package
# URL: http://www.thinstation.org
# Maintainer: <PERSON> (don cupp jr at ya hoo dot com)

#set -x
show_help()
{
echo "
This utility is designed to simplify the folder layout of a crux port into a
ts package. It will decompress the compiled port archive that belongs to the
specified package and mangle it for integration into thinstation. By default,
the destination will be in $TSWRKNG/packages/(specified package) folder.
This can be overriden with the -t or --temp (path to temp folder)
syntax.

Usage: repackage (-t (path to temp folder)) (crux port)

"
}

get_opts()
{
	if [ -z "$1" ] ; then show_help ; exit 255 ; fi
	until [ -z "$1" ] ; do
		case "$1" in
			-h|--help)		show_help ; exit 255 ;;
			-t|--temp)		temp_folder="$2" ; shift ;;
			-e|--env)		from_env ; exit 0 ;;
			-c|--clean)		clean ; exit 0 ;;
			*)			package="$1" ;;
		esac
		shift
	done
}

package_path()
{
	if [ -n "$temp_folder" ] ; then
		pkgpath="$temp_folder/$package"
		return 0
	else
		pkgpath="$TSWRKNG/packages/$package"
		for work in $TSWRKNG $XTRWRKNG; do
			if [ -d "$work/packages/$package" ]; then
				pkgpath="$work/packages/$package"
				return 0
			fi
		done
		return 1
	fi
}

extract_scripts()
{
	local rpm_file="$1"
	local script_dir="$pkgpath/build/scripts"
	local rpm_name

	# Create script directory
	mkdir -p "$script_dir"

	# Extract all scripts into a temporary file
	rpm -qp --scripts "$rpm_file" > all_scripts.txt

	# Extract the base RPM name without version
	rpm_name=$(basename "$rpm_file" | sed -r 's/-[0-9].*//')

	# Split the scripts into separate files
	csplit --digits=2 --quiet --prefix=script_ all_scripts.txt '/scriptlet (/' '{*}'
	rm all_scripts.txt

	# Process scripts
	for file in script_*; do
		local output_file=""
		if grep -q "preinstall scriptlet" "$file"; then
			output_file="$script_dir/${rpm_name}_preinstall.sh"
		elif grep -q "postinstall scriptlet" "$file"; then
			output_file="$script_dir/${rpm_name}_postinstall.sh"
		else
			rm "$file" # Remove unwanted scriptlets
			continue
		fi

		# Extract the interpreter and add a shebang
		local interpreter
		interpreter=$(grep -oP '(?<=scriptlet \(using ).*?(?=\))' "$file")
		if [ -n "$interpreter" ]; then
			echo "#!$interpreter" > "$output_file"
		else
			echo "#!/bin/sh" > "$output_file"
		fi

		# Append script content, skipping the header line
		sed '1d' "$file" >> "$output_file"
		rm "$file" # Clean up temporary split file
		sed -i 's/^\s*exit\s\+0\s*$/:/g' "$output_file"
	done
	if [ -z "`ls -A $pkgpath/build/scripts`" ]; then
		rmdir $pkgpath/build/scripts
	fi
	if [ -z "`ls -A $pkgpath/build`" ]; then
                rmdir $pkgpath/build
        fi
}

apply_caps() {
    if [ -e $pkgpath/file_caps.txt ]; then
	    while IFS= read -r line; do
        	if [[ "$line" =~ ^(.+?)\ (.+)$ ]]; then
	            file="${BASH_REMATCH[1]}"
	            caps="${BASH_REMATCH[2]}"
	            if [[ -n "$caps" && -e "$pkgpath/$file" ]] && [[ "$caps" != '(none)' ]]; then
	                setcap "$caps" "$pkgpath/$file"
	            fi
	        fi
	    done < $pkgpath/file_caps.txt
    fi
    rm -f $pkgpath/file_caps.txt
}

extract_archive()
{
	local archive
	if [ -n "$RPM" ]; then
		archive=`basename "$RPM" .rpm`
		if ! ${IGNORE_DEPS:=false}; then
			dnf install --setopt=keepcache=1 -y /build/wget_tmp/$archive.rpm
		fi
	fi
	while [ -z "$archive" ]; do
		archive="`rpm -q $package |grep -v 'not installed'`"
		if [ -z "$archive" ]; then
			printf "Package $package has not yet been installed.\n"
			if [ -n "$AUTODL" ] && $AUTODL; then
	                        install_port=true
	                else
	                        answered=false
	                        while ! $answered; do
	                                printf "Would you like to try and install it now? Y/N:"
	                                read -n 1 answer
	                                case $answer in
	                                        Y|y)    install_port=true
	                                                answered=true
	                                        ;;
	                                        n|n) answered=true; exit 1;;
	                                        *) answered=false;;
	                                esac
	                        done
	                fi
			if $install_port; then
	                        dnf install --setopt=keepcache=1 -y $package $PORTS
	                fi
		fi
	done
	if [ -z "$RPM" ]; then
		ppath=`find /var/cache -name $archive.rpm -printf '%h'`
		if [ ! -d $pkgpath ]; then
			mkdir -p $pkgpath
		fi
	else
		ppath=/build/wget_tmp
	fi
        extract_scripts "$ppath/$archive.rpm"
	rpm2archive $ppath/$archive.rpm | tar --xattrs --selinux -xzf - -C $pkgpath #2>/dev/null
        rpm -qp --qf '[%{filenames} %{filecaps}\n]' $ppath/$archive.rpm > $pkgpath/file_caps.txt
	apply_caps

	rm -rf $pkgpath/usr/lib/.build-id $pkgpath/usr/lib64/.build-id
	if [ -e $pkgpath/usr/lib ] && [ -z "$(ls -A $pkgpath/usr/lib)" ]; then
		rmdir $pkgpath/usr/lib
	fi
        if [ -e $pkgpath/usr/lib64 ] && [ -z "$(ls -A $pkgpath/usr/lib64)" ]; then
                rmdir $pkgpath/usr/lib64
        fi

        # Fix for ncurses folding
	if [ "$package" == "ncurses" ]; then
	        find $pkgpath/usr/lib64 -type l -name terminfo -delete
	fi
}

preserve_empty_working()
{
	for usefuldir in `find $pkgpath -type d -empty`; do
		touch $usefuldir/.gitignore
	done
}

shallow_usr()
{
	for usr_dir in usr usr/local ; do
		for essential_dir in sbin bin lib lib64 etc var libexec; do
			if [ -d $pkgpath/$usr_dir/$essential_dir ] && [ -n "`ls -A $pkgpath/$usr_dir/$essential_dir`" ]; then
				if [ "$essential_dir" == "lib" ] ; then
					mkdir -p $pkgpath/${essential_dir}64
					cp -a $pkgpath/$usr_dir/$essential_dir/* $pkgpath/${essential_dir}64/.
				else
					mkdir -p $pkgpath/${essential_dir}
					cp -a $pkgpath/$usr_dir/$essential_dir $pkgpath/.
				fi
				rm -rf $pkgpath/$usr_dir/$essential_dir
				if [ -z "`ls -A $pkgpath/$usr_dir`" ]; then
					rmdir $pkgpath/$usr_dir
				fi
			fi
		done
	done
}

redirect_sbin()
{
	if [ -e $pkgpath/sbin ] && [ -n "`ls -A $pkgpath/sbin`" ]; then
		mkdir -p $pkgpath/bin
		cp -a $pkgpath/sbin/* $pkgpath/bin
		rm -rf $pkgpath/sbin
	fi
}

redirect_shared()
{
	for share_dir in share usr/share usr/local/share ; do
		if [ -d $pkgpath/$share_dir ] && [ -n "`ls -A $pkgpath/$share_dir`" ]; then
#			if [ -d $pkgpath/$share_dir/dbus-1 ] && [ -n "`ls -A $pkgpath/$share_dir/dbus-1`" ]; then
#				mkdir -p $pkgpath/etc/dbus-1
#				cp -a $pkgpath/$share_dir/dbus-1/* $pkgpath/etc/dbus-1
#				rm -rf $pkgpath/$share_dir/dbus-1
#			fi
			for shared_object in `ls --color=never $pkgpath/$share_dir` ; do
				mkdir -p $pkgpath/lib64
				cp -a $pkgpath/$share_dir/$shared_object $pkgpath/lib64/.
				rm -rf $pkgpath/$share_dir/$shared_object
			done
		fi
		if [ -d $pkgpath/$share_dir ] ; then
			rmdir $pkgpath/$share_dir
		fi
	done
}

move_run()
{
	if [ -e $pkgpath/var/run ]; then
		cp -a $pkgpath/var/run $pkgpath/.
		rm -rf $pkgpath/var/run
	fi
}

extra_dirs()
{
	extra_dirs="man pkgconfig aclocal gtk-doc help vala gir-1.0 locale bash-completion cmake omf"
	if [ "$package" != "python" ] ; then
		extra_dirs="$extra_dirs include"
	fi
	for extra_dir in $extra_dirs ; do
		if [ -d $pkgpath/$extra_dir ] ; then
			rm -rf $pkgpath/$extra_dir
		fi
		if [ -d $pkgpath/usr/$extra_dir ] ; then
			rm -rf $pkgpath/usr/$extra_dir
		fi
		if [ -d $pkgpath/usr/local/$extra_dir ] ; then
			rm -rf $pkgpath/usr/local/$extra_dir
		fi
		if [ -d $pkgpath/lib64/$extra_dir ] ; then
			rm -rf $pkgpath/lib64/$extra_dir
		fi
	done
	for  include in include local/include ; do
		if [ -d $pkgpath/usr/$include ] ; then
			cp -a $pkgpath/usr/$include/* $pkgpath/lib64/.
			rm -rf $pkgpath/usr/$include
			ln -sf /lib64 $pkgpath/usr/$include
		fi
	done
}

extra_files()
{
	find $pkgpath -name *.a -delete
	find $pkgpath -name *.la -delete
}

remove_empty()
{
	find $pkgpath -type d -empty -delete
}

clean()
{
	package=$PACKAGE
	if [ -n "$PACKAGE" ] && package_path; then
		for object in `ls -A --color=never $pkgpath |grep -E -v 'build|.dna|.gitignore|.wind|.unwind|dependencies'`; do
			rm -rf $pkgpath/$object
		done
		rm -f $pkgpath/build/installed
		rm -rf $pkgpath/build/scripts
	else
		echo "$PACKAGE does not exist"
		exit 1
	fi
}

cut_bloat()
{
	for bloat in $DROP_FILES; do
		find $INSTALLDIR -type f -name $bloat -delete
	done
	for bloat in $DROP_FILES; do
		find $INSTALLDIR -type l -name $bloat -delete
	done
	for bloat in $DROP_DIRS; do
		rm -rf $INSTALLDIR/$bloat
	done
}

create_empty()
{
	for folder in $CREATE_EMPTY_DIRS; do
		mkdir -p $INSTALLDIR/$folder
	done
}

merge_trunk()
{
	if [ -e $INSTALLDIR/build/extra ]; then
		for dir in `find $INSTALLDIR/build/extra -type d -printf "%P\n"`; do
			mkdir -p $INSTALLDIR/$dir
		done
		IFS=$'\n\t'
		for file in `find $INSTALLDIR/build/extra ! -type d -printf "%P\n"`; do
			if [ -e $INSTALLDIR/"$file" ]; then
				rm $INSTALLDIR/"$file"
			fi
			ln $INSTALLDIR/build/extra/"$file" $INSTALLDIR/"$file"
		done
		unset IFS
	fi
}

from_env()
{
	for project in $TSWRKNG $XTRWRKNG; do
		if [ -d $project/packages/$PACKAGE ]; then
			INSTALLDIR=$project/packages/$PACKAGE
		fi
	done
	temp_folder=./build-$PACKAGE
	for package in $PORTS $RPM; do
		main
		cp -a $temp_folder/$package/* $INSTALLDIR/.
		rm -rf $temp_folder
	done
	cut_bloat
	create_empty
	merge_trunk
	touch $INSTALLDIR/build/installed
}

reduce_libs()
{
	if [ -e $pkgpath/lib64 ]; then
		for link in `find $pkgpath/lib64 -mindepth 1 -maxdepth 1 -type l \
		| grep -E 'lib64/lib' \
		| grep -E '\.so' | sort -u`; do
			# Normalize the link
			linkdest=`readlink $link | sed -e 's,\.\./\.\./lib64/,,' -e 's,^/lib64/,,'`
			linkdir=`dirname $link`
			# If the link is to another directory, leave it alone
			if [ `echo $linkdest | grep -E '/'` ]; then
				continue
			fi
			# Ignore TCL/TK, it doesn't ever declare an SONAME
			# ncurses has some wierd scripts named .so ??
			# llvm has an ascii file with a .so extension
			if [ -n "`basename $link |grep -E 'libtcl.so$|libtk.so$|libncurses.so$|libcurses.so$|libform.so$|libmenu.so$|libpanel.so$|libcursesw.so$|libc\+\+.so$|libVkLayer_khronos_validation.so$'`" ]; then
				continue
			fi
			soname=`objdump -p $linkdir/$linkdest | grep SONAME | awk '{print $2}'`
			base=`basename $link`
			if [ -z "$soname" ]; then
				echo "MISSING SONAME $link"
#				exit 1
			elif [ "$base" == "$soname" ]; then
				rm $link
				cp $linkdir/$linkdest $link
			else
				rm $link
			fi
		done
		for lib in `find $pkgpath/lib64 -mindepth 1 -maxdepth 1 -type f \
		| grep -E 'lib64/lib' \
		| grep -E '\.so' |sort -u`; do
			# Ignore TCL/TK, it doesn't ever declare an SONAME
			# ncurses has some wierd scripts named .so ??
			# llvm has an ascii file with a .so extension
			if [ -n "`basename $lib |grep -E 'libtcl8.6.so$|libtk8.6.so$|libncurses.so$|libcurses.so$|libform.so$|libmenu.so$|libpanel.so$|libcursesw.so|libc\+\+.so$|libVkLayer_khronos_validation.so$'`" ]; then
				continue
			fi
			soname=`objdump -p $lib | grep SONAME | awk '{print $2}'`
			base=`basename $lib`
			if [ -z "$soname" ]; then
				echo "MISSING SONAME $lib"
#				exit 1
			elif [ "$base" != "$soname" ]; then
				rm $lib
			fi
		done
	fi
}

remove_libs()
{
	if [ -e $pkgpath/lib64 ]; then
		find $pkgpath/lib64 -mindepth 1 -maxdepth 1 -name lib\*.so\* -delete
	fi
}

main()
{
	package_path
	extract_archive
	preserve_empty_working
	shallow_usr
	redirect_shared
	move_run
	extra_dirs
	extra_files
	remove_empty
#	remove_libs
	reduce_libs
}

get_opts $@
echo $package
main $@
exit 0
