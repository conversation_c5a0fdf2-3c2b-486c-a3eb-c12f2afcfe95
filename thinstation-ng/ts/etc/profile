#
# /etc/profile: system-wide defaults for bash(1) login shells
#

if [ ! -f ~/.inputrc ]; then
	export INPUTRC="/etc/inputrc"
fi

export LESSCHARSET="latin1"
export LESS="-R"
export CHARSET="ISO-8859-1"
export PS1="\n\[\033[1;30m\][\[\033[1;34m\]\u\[\033[1;30m\]@\[\033[0;34m\]TS_chroot\[\033[1;30m\]]\[\033[1;37m\]\w\[\033[1;30m\]\\$\[\033[0m\] "
export PS2="\[\033[1m\]> \[\033[0m\]"
LS_COLORS='rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.cmd=01;32:*.exe=01;32:*.com=01;32:*.btm=01;32:*.bat=01;32:*.conf=01;33:*.tar=01;31:*.tgz=01;31:*.arj=01;31:*.taz=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.zip=01;31:*.z=01;31:*.Z=01;31:*.dz=01;31:*.gz=01;31:*.lz=01;31:*.xz=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.rar=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.jpg=01;35:*.jpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.axv=01;35:*.anx=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.axa=00;36:*.oga=00;36:*.spx=00;36:*.xspf=00;36:';
export LS_COLORS

umask 022
alias ls='ls --color'
if [ -f ~/.ssh/id_rsa ]; then
	if [ -f ~/.agent.env ] ; then
	    . ~/.agent.env > /dev/null
	    if ! kill -0 $SSH_AGENT_PID > /dev/null 2>&1; then
	        echo "Stale agent file found. Spawning new agent… "
	        eval `ssh-agent | tee ~/.agent.env`
	        ssh-add
	    fi 
	else
	    echo "Starting ssh-agent"
	    eval `ssh-agent | tee ~/.agent.env`
	    ssh-add
	fi
fi

# System-wide Options: Uncomment lines and reboot for them to take effect.
# bind "set show-all-if-ambiguous on" # All options for autocomplete will be shown when pressing TAB, if they exist.
# bind "TAB:menu-complete" # Autocomplete will cycle through possible options each time you press TAB, instead of choosing the longest common prefix.

# End of file
