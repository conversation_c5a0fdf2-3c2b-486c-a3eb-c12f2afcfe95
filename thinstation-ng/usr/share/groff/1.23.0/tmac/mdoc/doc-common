.\" Copyright (c) 1991, 1993
.\"   The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that the following conditions
.\" are met:
.\" 1. Redistributions of source code must retain the above copyright
.\"    notice, this list of conditions and the following disclaimer.
.\" 2. Redistributions in binary form must reproduce the above copyright
.\"    notice, this list of conditions and the following disclaimer in
.\"    the documentation and/or other materials provided with the
.\"    distribution.
.\" 3. [Deleted.  See
.\"     ftp://ftp.cs.berkeley.edu/pub/4bsd/README.Impt.License.Change]
.\" 4. Neither the name of the University nor the names of its
.\"    contributors may be used to endorse or promote products derived
.\"    from this software without specific prior written permission.
.\"
.\" THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS "AS IS"
.\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
.\" TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
.\" PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR
.\" CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
.\" SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
.\" LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
.\" USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
.\" ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
.\" OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
.\" OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
.\" SUCH DAMAGE.
.\"
.\"     @(#)doc-common	8.1 (Berkeley) 06/08/93
.
.
.\" Macro Identifiers.  For each user macro a corresponding register
.\" with the same name must exist.  Its value must not be zero.
.
.nr %A 1
.nr %B 1
.nr %C 1
.nr %D 1
.nr %I 1
.nr %J 1
.nr %N 1
.nr %O 1
.nr %P 1
.nr %Q 1
.nr %R 1
.nr %T 1
.nr %U 1
.nr %V 1
.nr Ac 3
.nr Ad 12n
.nr An 12n
.nr Ao 12n
.nr Ap 2
.nr Aq 12n
.nr Ar 12n
.nr At 1
.nr Bc 3
.nr Bf 8n\" ?
.nr Bk 8n\" ?
.nr Bl 1
.nr Bo 12n
.nr Bq 12n
.nr Brc 3
.nr Bro 12n
.nr Brq 12n
.nr Bsx 1
.nr Bt 8n\" ?
.nr Bx 1
.nr Cd 12n
.nr Cm 10n
.nr D1 8n\" ?
.nr Dc 3
.nr Dl 8n\" ?
.nr Dt 8n\" ?
.nr Do 12n
.nr Dq 12n
.nr Ds 6n\" many manpages still use this as a -width value
.nr Dv 12n
.nr Dx 1
.nr Ec 3
.nr Ef 8n\" ?
.nr Ek 8n\" ?
.nr El 1
.nr Em 10n
.nr En 12n
.nr Eo 12n
.nr Eq 12n
.nr Er 17n
.nr Es 12n
.nr Ev 15n
.nr Ex 1
.nr Fa 12n
.nr Fc 3
.nr Fd 12n\" ?
.nr Fl 10n
.nr Fn 16n
.nr Fo 16n
.nr Fr 12n\" ?
.nr Ft 8n\" ?
.nr Fx 1
.nr Ic 10n
.nr In 12n
.nr It 8n\" ?
.nr Lb 11n
.nr Li 16n
.nr Lk 6n\" ?
.nr Lp 8n\" ?
.nr Me 6n
.nr Ms 6n
.nr Mt 6n\" ?
.nr Nd 8n\" ?
.nr Nm 10n
.nr No 12n
.nr Ns 2
.nr Nx 1
.nr Oc 3
.nr Oo 10n
.nr Op 14n
.nr Os 6n\" ?
.nr Ox 1
.nr Pa 32n
.nr Pc 3
.nr Pf 12n
.nr Po 12n
.nr Pp 8n\" ?
.nr Pq 12n
.nr Qc 3
.nr Ql 16n
.nr Qo 12n
.nr Qq 12n
.nr Rv 1
.nr Sc 3
.nr Sh 8n
.nr Sm 8n\" ?
.nr So 12n
.nr Sq 12n
.nr Ss 8n
.nr St 8n\" ?
.nr Sx 16n
.nr Sy 6n
.nr Ta 2
.nr Tn 10n
.nr Ud 8n\" ?
.nr Ux 1
.nr Va 12n
.nr Vt 8n\" ?
.nr Xc 3
.nr Xo 1
.nr Xr 10n
.
.
.\" macros which must be processed after the closing delimiter of 'Op'
.\" and friends
.ds doc-after-Ao
.ds doc-after-Bo
.ds doc-after-Bro
.ds doc-after-Do
.ds doc-after-Eo
.ds doc-after-Fo
.ds doc-after-Ns
.ds doc-after-Oo
.ds doc-after-Po
.ds doc-after-Qo
.ds doc-after-So
.ds doc-after-Xo
.
.
.nr doc-display-indent 6n
.
.
.\" space strings
.
.ds doc-soft-space " \"
.ds doc-hard-space \~
.ds doc-tab \t
.
.
.\" punctuation values (suffix=3, prefix=4)
.
.nr doc-punct. 3
.nr doc-punct, 3
.nr doc-punct: 3
.nr doc-punct; 3
.nr doc-punct( 4
.nr doc-punct) 3
.nr doc-punct[ 4
.nr doc-punct] 3
.nr doc-punct? 3
.nr doc-punct! 3
.
.
.\" Define alternate requests to handle continuous rendering.
.\"
.\" This .ne replacement avoids page breaks; instead, the page length is
.\" increased to the necessary amount (this is needed for tables).
.
.eo
.de doc-ne
.  ie \n[.$] .nr doc-amount (v;\$*)
.  el        .nr doc-amount 1v
.  if (\n[doc-amount] >= \n[.t]) \
.    pl +(\n[doc-amount]u - \n[.t]u + 1v)
.  rr doc-amount
..
.ec
.
.\" This .bp replacement for continuous rendering mode adjusts the page
.\" length to the current position so that no empty lines are inserted.
.
.eo
.de doc-bp
.  pl \n[nl]u
..
.ec
.
.
.\" NS doc-set-up-continuous-rendering
.\"   Move macros into place for continuous rendering.  An end-of-input
.\"   macro is set up by doc-set-up-titles.
.de doc-set-up-continuous-rendering
.  rn ne doc-real-ne
.  rn bp doc-real-bp
.  rn doc-ne ne
.  rn doc-bp bp
..
.
.if \n[cR] \
.  doc-set-up-continuous-rendering
.
.
.\" header assembly macros
.
.\" NS doc-page-topic global string
.\" NS   the title of the manual page
.
.ds doc-page-topic UNTITLED\"
.
.
.\" NS doc-volume global string
.\" NS   the volume to which the manual page belongs
.
.ds doc-volume LOCAL\"
.
.
.\" NS doc-section global string
.\" NS   the manual section (1, 2, 3, ..., 3perl, 9)
.
.ds doc-section \" empty
.
.
.\" NS doc-set-up-titles macro
.\" NS   install and initialize header and footer support
.\" NS
.\" NS local variables:
.\" NS   doc-footer-location
.
.eo
.de doc-set-up-titles
.  br
.
.  if !\n[cR] \{\
.    wh 0 doc-header
.    ie r FT .nr doc-footer-location \n[FT]
.    el      .nr doc-footer-location (-.5i)
.    wh \n[doc-footer-location]u doc-footer
.    wh (\n[doc-footer-location]u - .5i) doc-break-body-text
.    rr doc-footer-location
.  \}
.
.  e@ doc-end-macro
..
.ec
.
.
.\" NS doc-date-string global string
.\" NS   the manual page date as set by 'Dd'
.
.ds doc-date-string UNDATED\"
.
.
.\" NS Dd user macro (not parsed, not callable)
.\" NS   set document date
.\" NS
.\" NS modifies:
.\" NS   doc-date-string
.\" NS
.\" NS width register 'Dd' set above
.
.eo
.de Dd
.  \" If batch processing (rendering multiple) man page documents, we
.  \" must handle the end of a previous document.
.  \"
.  \" If also continuously rendering, cause a page transition to a new
.  \" mdoc(7) document.
.  if \n[doc-need-titles-reset] \{\
.    if \n[cR] .doc-end-macro
.
.    \" Clear the page header trap so it is not sprung with stale
.    \" information.
.    ch doc-header
.    doc-break-page-with-new-number
.  \}
.  if \n[C] .rr P
.
.  if !\n[.$] \
.    tm mdoc warning: .Dd directive expects an argument (#\n[.c])
.  if \n[.$] \{\
.    ie "\$1"$Mdocdate:" .ds doc-date-string \$2\~\$3, \$4\"
.    el                  .ds doc-date-string \$*\"
.  \}
..
.ec
.
.
.\" NS Dt user macro (not parsed, not callable)
.\" NS   document title
.\" NS
.\" NS modifies:
.\" NS   doc-page-topic
.\" NS   doc-section
.\" NS   doc-volume
.\" NS
.\" NS local variables:
.\" NS   doc-volume-as-XXX
.\" NS   doc-volume-ds-XXX
.\" NS
.\" NS width register 'Dt' set above
.
.\" an alternative, more detailed scheme for naming the manual sections
.\"
.ds doc-volume-ds-1 General Commands Manual
.ds doc-volume-ds-2 System Calls Manual
.ds doc-volume-ds-3 Library Functions Manual
.ds doc-volume-ds-4 Kernel Interfaces Manual
.ds doc-volume-ds-5 File Formats Manual
.ds doc-volume-ds-6 Games Manual
.ds doc-volume-ds-7 Miscellaneous Information Manual
.ds doc-volume-ds-8 System Manager's Manual
.ds doc-volume-ds-9 Kernel Developer's Manual
.
.ds doc-volume-ds-USD   User's Supplementary Documents
.ds doc-volume-ds-PS1   Programmer's Supplementary Documents
.ds doc-volume-ds-AMD   Ancestral Manual Documents
.ds doc-volume-ds-SMM   System Manager's Manual
.ds doc-volume-ds-URM   User's Reference Manual
.ds doc-volume-ds-PRM   Programmer's Manual
.ds doc-volume-ds-KM    Kernel Manual
.ds doc-volume-ds-IND   Manual Master Index
.ds doc-volume-ds-LOCAL Local Manual
.ds doc-volume-ds-CON   Contributed Software Manual
.
.als doc-volume-ds-MMI doc-volume-ds-IND
.als doc-volume-ds-LOC doc-volume-ds-LOCAL
.
.ds doc-volume-as-alpha        alpha
.als doc-volume-as-Alpha doc-volume-as-alpha
.ds doc-volume-as-acorn26      acorn26
.ds doc-volume-as-acorn32      acorn32
.ds doc-volume-as-algor        algor
.ds doc-volume-as-amd64        amd64
.ds doc-volume-as-amiga        amiga
.ds doc-volume-as-amigappc     amigappc
.ds doc-volume-as-arc          arc
.ds doc-volume-as-arm          arm
.ds doc-volume-as-arm26        arm26
.ds doc-volume-as-arm32        arm32
.ds doc-volume-as-armish       armish
.ds doc-volume-as-atari        atari
.ds doc-volume-as-aviion       aviion
.ds doc-volume-as-beagle       beagle
.ds doc-volume-as-bebox        bebox
.ds doc-volume-as-cats         cats
.ds doc-volume-as-cesfic       cesfic
.ds doc-volume-as-cobalt       cobalt
.ds doc-volume-as-dreamcast    dreamcast
.ds doc-volume-as-emips        emips
.ds doc-volume-as-evbarm       evbarm
.ds doc-volume-as-evbmips      evbmips
.ds doc-volume-as-evbppc       evbppc
.ds doc-volume-as-evbsh3       evbsh3
.ds doc-volume-as-ews4800mips  ews4800mips
.ds doc-volume-as-hp300        hp300
.ds doc-volume-as-hp700        hp700
.ds doc-volume-as-hpcarm       hpcarm
.ds doc-volume-as-hpcmips      hpcmips
.ds doc-volume-as-hpcsh        hpcsh
.ds doc-volume-as-hppa         hppa
.ds doc-volume-as-hppa64       hppa64
.ds doc-volume-as-i386         i386
.ds doc-volume-as-ia64         ia64
.ds doc-volume-as-ibmnws       ibmnws
.ds doc-volume-as-iyonix       iyonix
.ds doc-volume-as-landisk      landisk
.ds doc-volume-as-loongson     loongson
.ds doc-volume-as-luna68k      luna68k
.ds doc-volume-as-luna88k      luna88k
.ds doc-volume-as-m68k         m68k
.ds doc-volume-as-mac68k       mac68k
.ds doc-volume-as-macppc       macppc
.ds doc-volume-as-mips         mips
.ds doc-volume-as-mips64       mips64
.ds doc-volume-as-mipsco       mipsco
.ds doc-volume-as-mmeye        mmeye
.ds doc-volume-as-mvme68k      mvme68k
.ds doc-volume-as-mvme88k      mvme88k
.ds doc-volume-as-mvmeppc      mvmeppc
.ds doc-volume-as-netwinder    netwinder
.ds doc-volume-as-news68k      news68k
.ds doc-volume-as-newsmips     newsmips
.ds doc-volume-as-next68k      next68k
.ds doc-volume-as-ofppc        ofppc
.ds doc-volume-as-palm         palm
.ds doc-volume-as-pc532        pc532
.ds doc-volume-as-playstation2 playstation2
.ds doc-volume-as-pmax         pmax
.ds doc-volume-as-pmppc        pmppc
.ds doc-volume-as-powerpc      powerpc
.ds doc-volume-as-prep         prep
.ds doc-volume-as-rs6000       rs6000
.ds doc-volume-as-sandpoint    sandpoint
.ds doc-volume-as-sbmips       sbmips
.ds doc-volume-as-sgi          sgi
.ds doc-volume-as-sgimips      sgimips
.ds doc-volume-as-sh3          sh3
.ds doc-volume-as-shark        shark
.ds doc-volume-as-socppc       socppc
.ds doc-volume-as-solbourne    solbourne
.ds doc-volume-as-sparc        sparc
.ds doc-volume-as-sparc64      sparc64
.ds doc-volume-as-sun2         sun2
.ds doc-volume-as-sun3         sun3
.ds doc-volume-as-tahoe        tahoe
.ds doc-volume-as-vax          vax
.ds doc-volume-as-x68k         x68k
.ds doc-volume-as-x86_64       x86_64
.ds doc-volume-as-xen          xen
.ds doc-volume-as-zaurus       zaurus
.
.eo
.de Dt
.  if !\n[.$] \
.    tm mdoc warning: .Dt directive expects one or more arguments \
(#\n[.c])
.  if !"\$1"" \
.    ds doc-page-topic "\$1
.
.  if \n[CT] \
.    stringup doc-page-topic
.
.  if !"\$2"" \{\
.    ds doc-section \$2
.    ie \B'\$2' \{\
.      if ((\$2 >= 1) & (\$2 <= 9)) \{\
.        ds doc-volume \" empty (not "LOCAL")
.        if \A'\$3' \{\
.          if d doc-volume-as-\$3 \
.            as doc-volume "\*[doc-volume-as-\$3]
.          \}
.        as doc-volume " \*[doc-volume-ds-\$2]
.      \}
.    \}
.    el \{\
.      ie "\$2"unass" \
.        ds doc-volume DRAFT
.      el \{ .ie "\$2"draft" \
.        ds doc-volume DRAFT
.      el .if "\$2"paper" \
.        ds doc-volume UNTITLED
.      \}
.   \}
.   if \A'\$3' \{\
.     if d doc-volume-ds-\$3 \
.       ds doc-volume "\*[doc-volume-ds-\$3]
.   \}
. \}
.
.  if !"\$3"" \
.    if "\*[doc-volume]"LOCAL" \
.      ds doc-volume \$3
..
.ec
.
.
.\" NS doc-default-operating-system global string
.\" NS   the default OS to associate with man pages
.\" NS
.\" NS override this in 'mdoc.local', if necessary
.
.ds doc-default-operating-system GNU\"
.
.
.\" NS doc-operating-system global string
.\" NS   the OS or software project associated with the man page
.
.ds doc-operating-system \" empty
.
.
.\" NS Os user macro (not parsed, not callable)
.\" NS   operating system
.\" NS
.\" NS modifies:
.\" NS   doc-operating-system
.\" NS
.\" NS local variables:
.\" NS   doc-operating-system-XXX-XXX
.\" NS
.\" NS width register 'Os' set above
.
.ds doc-operating-system-ATT-7   7th\~Edition
.als doc-operating-system-ATT-7th doc-operating-system-ATT-7
.ds doc-operating-system-ATT-3   System\~III
.als doc-operating-system-ATT-III doc-operating-system-ATT-3
.ds doc-operating-system-ATT-V   System\~V
.ds doc-operating-system-ATT-V.2 System\~V Release\~2
.ds doc-operating-system-ATT-V.3 System\~V Release\~3
.ds doc-operating-system-ATT-V.4 System\~V Release\~4
.
.ds doc-operating-system-BSD-3    3rd\~Berkeley Distribution
.ds doc-operating-system-BSD-4    4th\~Berkeley Distribution
.ds doc-operating-system-BSD-4.1  4.1\~Berkeley Distribution
.ds doc-operating-system-BSD-4.2  4.2\~Berkeley Distribution
.ds doc-operating-system-BSD-4.3  4.3\~Berkeley Distribution
.ds doc-operating-system-BSD-4.3T 4.3-Tahoe Berkeley Distribution
.ds doc-operating-system-BSD-4.3R 4.3-Reno Berkeley Distribution
.als doc-operating-system-BSD-4.3t doc-operating-system-BSD-4.3T
.als doc-operating-system-BSD-4.3r doc-operating-system-BSD-4.3R
.ds doc-operating-system-BSD-4.4  4.4BSD
.
.ds doc-operating-system-NetBSD-0.8   0.8
.ds doc-operating-system-NetBSD-0.8a  0.8A
.ds doc-operating-system-NetBSD-0.9   0.9
.ds doc-operating-system-NetBSD-0.9a  0.9A
.ds doc-operating-system-NetBSD-1.0   1.0
.ds doc-operating-system-NetBSD-1.0a  1.0A
.ds doc-operating-system-NetBSD-1.1   1.1
.ds doc-operating-system-NetBSD-1.2   1.2
.ds doc-operating-system-NetBSD-1.2a  1.2A
.ds doc-operating-system-NetBSD-1.2b  1.2B
.ds doc-operating-system-NetBSD-1.2c  1.2C
.ds doc-operating-system-NetBSD-1.2d  1.2D
.ds doc-operating-system-NetBSD-1.2e  1.2E
.ds doc-operating-system-NetBSD-1.3   1.3
.ds doc-operating-system-NetBSD-1.3a  1.3A
.ds doc-operating-system-NetBSD-1.4   1.4
.ds doc-operating-system-NetBSD-1.4.1 1.4.1
.ds doc-operating-system-NetBSD-1.4.2 1.4.2
.ds doc-operating-system-NetBSD-1.4.3 1.4.3
.ds doc-operating-system-NetBSD-1.5   1.5
.ds doc-operating-system-NetBSD-1.5.1 1.5.1
.ds doc-operating-system-NetBSD-1.5.2 1.5.2
.ds doc-operating-system-NetBSD-1.5.3 1.5.3
.ds doc-operating-system-NetBSD-1.6   1.6
.ds doc-operating-system-NetBSD-1.6.1 1.6.1
.ds doc-operating-system-NetBSD-1.6.2 1.6.2
.ds doc-operating-system-NetBSD-1.6.3 1.6.3
.ds doc-operating-system-NetBSD-2.0   2.0
.ds doc-operating-system-NetBSD-2.0.1 2.0.1
.ds doc-operating-system-NetBSD-2.0.2 2.0.2
.ds doc-operating-system-NetBSD-2.0.3 2.0.3
.ds doc-operating-system-NetBSD-2.1   2.1
.ds doc-operating-system-NetBSD-3.0   3.0
.ds doc-operating-system-NetBSD-3.0.1 3.0.1
.ds doc-operating-system-NetBSD-3.0.2 3.0.2
.ds doc-operating-system-NetBSD-3.0.3 3.0.3
.ds doc-operating-system-NetBSD-3.1   3.1
.ds doc-operating-system-NetBSD-3.1.1 3.1.1
.ds doc-operating-system-NetBSD-4.0   4.0
.ds doc-operating-system-NetBSD-4.0.1 4.0.1
.ds doc-operating-system-NetBSD-5.0   5.0
.ds doc-operating-system-NetBSD-5.0.1 5.0.1
.ds doc-operating-system-NetBSD-5.0.2 5.0.2
.ds doc-operating-system-NetBSD-5.1   5.1
.ds doc-operating-system-NetBSD-5.1.2 5.1.2
.ds doc-operating-system-NetBSD-5.1.3 5.1.3
.ds doc-operating-system-NetBSD-5.1.4 5.1.4
.ds doc-operating-system-NetBSD-5.2   5.2
.ds doc-operating-system-NetBSD-5.2.1 5.2.1
.ds doc-operating-system-NetBSD-5.2.2 5.2.2
.ds doc-operating-system-NetBSD-6.0   6.0
.ds doc-operating-system-NetBSD-6.0.1 6.0.1
.ds doc-operating-system-NetBSD-6.0.2 6.0.2
.ds doc-operating-system-NetBSD-6.0.3 6.0.3
.ds doc-operating-system-NetBSD-6.0.4 6.0.4
.ds doc-operating-system-NetBSD-6.0.5 6.0.5
.ds doc-operating-system-NetBSD-6.0.6 6.0.6
.ds doc-operating-system-NetBSD-6.1   6.1
.ds doc-operating-system-NetBSD-6.1.1 6.1.1
.ds doc-operating-system-NetBSD-6.1.2 6.1.2
.ds doc-operating-system-NetBSD-6.1.3 6.1.3
.ds doc-operating-system-NetBSD-6.1.4 6.1.4
.ds doc-operating-system-NetBSD-6.1.5 6.1.5
.ds doc-operating-system-NetBSD-7.0   7.0
.ds doc-operating-system-NetBSD-7.0.1 7.0.1
.ds doc-operating-system-NetBSD-7.0.2 7.0.2
.ds doc-operating-system-NetBSD-7.1   7.1
.ds doc-operating-system-NetBSD-7.1.1 7.1.1
.ds doc-operating-system-NetBSD-7.1.2 7.1.2
.ds doc-operating-system-NetBSD-7.2   7.2
.ds doc-operating-system-NetBSD-8.0   8.0
.ds doc-operating-system-NetBSD-8.1   8.1
.
.ds doc-operating-system-OpenBSD-2.0  2.0
.ds doc-operating-system-OpenBSD-2.1  2.1
.ds doc-operating-system-OpenBSD-2.2  2.2
.ds doc-operating-system-OpenBSD-2.3  2.3
.ds doc-operating-system-OpenBSD-2.4  2.4
.ds doc-operating-system-OpenBSD-2.5  2.5
.ds doc-operating-system-OpenBSD-2.6  2.6
.ds doc-operating-system-OpenBSD-2.7  2.7
.ds doc-operating-system-OpenBSD-2.8  2.8
.ds doc-operating-system-OpenBSD-2.9  2.9
.ds doc-operating-system-OpenBSD-3.0  3.0
.ds doc-operating-system-OpenBSD-3.1  3.1
.ds doc-operating-system-OpenBSD-3.2  3.2
.ds doc-operating-system-OpenBSD-3.3  3.3
.ds doc-operating-system-OpenBSD-3.4  3.4
.ds doc-operating-system-OpenBSD-3.5  3.5
.ds doc-operating-system-OpenBSD-3.6  3.6
.ds doc-operating-system-OpenBSD-3.7  3.7
.ds doc-operating-system-OpenBSD-3.8  3.8
.ds doc-operating-system-OpenBSD-3.9  3.9
.ds doc-operating-system-OpenBSD-4.0  4.0
.ds doc-operating-system-OpenBSD-4.1  4.1
.ds doc-operating-system-OpenBSD-4.2  4.2
.ds doc-operating-system-OpenBSD-4.3  4.3
.ds doc-operating-system-OpenBSD-4.4  4.4
.ds doc-operating-system-OpenBSD-4.5  4.5
.ds doc-operating-system-OpenBSD-4.6  4.6
.ds doc-operating-system-OpenBSD-4.7  4.7
.ds doc-operating-system-OpenBSD-4.8  4.8
.ds doc-operating-system-OpenBSD-4.9  4.9
.ds doc-operating-system-OpenBSD-5.0  5.0
.ds doc-operating-system-OpenBSD-5.1  5.1
.ds doc-operating-system-OpenBSD-5.2  5.2
.ds doc-operating-system-OpenBSD-5.3  5.3
.ds doc-operating-system-OpenBSD-5.4  5.4
.ds doc-operating-system-OpenBSD-5.5  5.5
.ds doc-operating-system-OpenBSD-5.6  5.6
.ds doc-operating-system-OpenBSD-5.7  5.7
.ds doc-operating-system-OpenBSD-5.8  5.8
.ds doc-operating-system-OpenBSD-5.9  5.9
.ds doc-operating-system-OpenBSD-6.0  6.0
.ds doc-operating-system-OpenBSD-6.1  6.1
.ds doc-operating-system-OpenBSD-6.2  6.2
.ds doc-operating-system-OpenBSD-6.3  6.3
.ds doc-operating-system-OpenBSD-6.4  6.4
.ds doc-operating-system-OpenBSD-6.5  6.5
.ds doc-operating-system-OpenBSD-6.6  6.6
.
.ds doc-operating-system-FreeBSD-1.0     1.0
.ds doc-operating-system-FreeBSD-1.1     1.1
.ds doc-operating-system-FreeBSD-1.1.5   1.1.5
.ds doc-operating-system-FreeBSD-1.1.5.1 1.1.5.1
.ds doc-operating-system-FreeBSD-2.0     2.0
.ds doc-operating-system-FreeBSD-2.0.5   2.0.5
.ds doc-operating-system-FreeBSD-2.1     2.1
.ds doc-operating-system-FreeBSD-2.1.5   2.1.5
.ds doc-operating-system-FreeBSD-2.1.6   2.1.6
.ds doc-operating-system-FreeBSD-2.1.7   2.1.7
.ds doc-operating-system-FreeBSD-2.2     2.2
.ds doc-operating-system-FreeBSD-2.2.1   2.2.1
.ds doc-operating-system-FreeBSD-2.2.2   2.2.2
.ds doc-operating-system-FreeBSD-2.2.5   2.2.5
.ds doc-operating-system-FreeBSD-2.2.6   2.2.6
.ds doc-operating-system-FreeBSD-2.2.7   2.2.7
.ds doc-operating-system-FreeBSD-2.2.8   2.2.8
.ds doc-operating-system-FreeBSD-2.2.9   2.2.9
.ds doc-operating-system-FreeBSD-3.0     3.0
.ds doc-operating-system-FreeBSD-3.1     3.1
.ds doc-operating-system-FreeBSD-3.2     3.2
.ds doc-operating-system-FreeBSD-3.3     3.3
.ds doc-operating-system-FreeBSD-3.4     3.4
.ds doc-operating-system-FreeBSD-3.5     3.5
.ds doc-operating-system-FreeBSD-4.0     4.0
.ds doc-operating-system-FreeBSD-4.1     4.1
.ds doc-operating-system-FreeBSD-4.1.1   4.1.1
.ds doc-operating-system-FreeBSD-4.2     4.2
.ds doc-operating-system-FreeBSD-4.3     4.3
.ds doc-operating-system-FreeBSD-4.4     4.4
.ds doc-operating-system-FreeBSD-4.5     4.5
.ds doc-operating-system-FreeBSD-4.6     4.6
.ds doc-operating-system-FreeBSD-4.6.2   4.6.2
.ds doc-operating-system-FreeBSD-4.7     4.7
.ds doc-operating-system-FreeBSD-4.8     4.8
.ds doc-operating-system-FreeBSD-4.9     4.9
.ds doc-operating-system-FreeBSD-4.10    4.10
.ds doc-operating-system-FreeBSD-4.11    4.11
.ds doc-operating-system-FreeBSD-5.0     5.0
.ds doc-operating-system-FreeBSD-5.1     5.1
.ds doc-operating-system-FreeBSD-5.2     5.2
.ds doc-operating-system-FreeBSD-5.2.1   5.2.1
.ds doc-operating-system-FreeBSD-5.3     5.3
.ds doc-operating-system-FreeBSD-5.4     5.4
.ds doc-operating-system-FreeBSD-5.5     5.5
.ds doc-operating-system-FreeBSD-6.0     6.0
.ds doc-operating-system-FreeBSD-6.1     6.1
.ds doc-operating-system-FreeBSD-6.2     6.2
.ds doc-operating-system-FreeBSD-6.3     6.3
.ds doc-operating-system-FreeBSD-6.4     6.4
.ds doc-operating-system-FreeBSD-7.0     7.0
.ds doc-operating-system-FreeBSD-7.1     7.1
.ds doc-operating-system-FreeBSD-7.2     7.2
.ds doc-operating-system-FreeBSD-7.3     7.3
.ds doc-operating-system-FreeBSD-7.4     7.4
.ds doc-operating-system-FreeBSD-8.0     8.0
.ds doc-operating-system-FreeBSD-8.1     8.1
.ds doc-operating-system-FreeBSD-8.2     8.2
.ds doc-operating-system-FreeBSD-8.3     8.3
.ds doc-operating-system-FreeBSD-8.4     8.4
.ds doc-operating-system-FreeBSD-9.0     9.0
.ds doc-operating-system-FreeBSD-9.1     9.1
.ds doc-operating-system-FreeBSD-9.2     9.2
.ds doc-operating-system-FreeBSD-9.3     9.3
.ds doc-operating-system-FreeBSD-10.0    10.0
.ds doc-operating-system-FreeBSD-10.1    10.1
.ds doc-operating-system-FreeBSD-10.2    10.2
.ds doc-operating-system-FreeBSD-10.3    10.3
.ds doc-operating-system-FreeBSD-10.4    10.4
.ds doc-operating-system-FreeBSD-11.0    11.0
.ds doc-operating-system-FreeBSD-11.1    11.1
.ds doc-operating-system-FreeBSD-11.2    11.2
.ds doc-operating-system-FreeBSD-11.3    11.3
.ds doc-operating-system-FreeBSD-12.0    12.0
.ds doc-operating-system-FreeBSD-12.1    12.1
.
.ds doc-operating-system-Darwin-8.0.0  8.0.0
.ds doc-operating-system-Darwin-8.1.0  8.1.0
.ds doc-operating-system-Darwin-8.2.0  8.2.0
.ds doc-operating-system-Darwin-8.3.0  8.3.0
.ds doc-operating-system-Darwin-8.4.0  8.4.0
.ds doc-operating-system-Darwin-8.5.0  8.5.0
.ds doc-operating-system-Darwin-8.6.0  8.6.0
.ds doc-operating-system-Darwin-8.7.0  8.7.0
.ds doc-operating-system-Darwin-8.8.0  8.8.0
.ds doc-operating-system-Darwin-8.9.0  8.9.0
.ds doc-operating-system-Darwin-8.10.0 8.10.0
.ds doc-operating-system-Darwin-8.11.0 8.11.0
.ds doc-operating-system-Darwin-9.0.0  9.0.0
.ds doc-operating-system-Darwin-9.1.0  9.1.0
.ds doc-operating-system-Darwin-9.2.0  9.2.0
.ds doc-operating-system-Darwin-9.3.0  9.3.0
.ds doc-operating-system-Darwin-9.4.0  9.4.0
.ds doc-operating-system-Darwin-9.5.0  9.5.0
.ds doc-operating-system-Darwin-9.6.0  9.6.0
.ds doc-operating-system-Darwin-9.7.0  9.7.0
.ds doc-operating-system-Darwin-9.8.0  9.8.0
.ds doc-operating-system-Darwin-10.0.0 10.0.0
.ds doc-operating-system-Darwin-10.1.0 10.1.0
.ds doc-operating-system-Darwin-10.2.0 10.2.0
.ds doc-operating-system-Darwin-10.3.0 10.3.0
.ds doc-operating-system-Darwin-10.4.0 10.4.0
.ds doc-operating-system-Darwin-10.5.0 10.5.0
.ds doc-operating-system-Darwin-10.6.0 10.6.0
.ds doc-operating-system-Darwin-10.7.0 10.7.0
.ds doc-operating-system-Darwin-10.8.0 10.8.0
.ds doc-operating-system-Darwin-11.0.0 11.0.0
.ds doc-operating-system-Darwin-11.1.0 11.1.0
.ds doc-operating-system-Darwin-11.2.0 11.2.0
.ds doc-operating-system-Darwin-11.3.0 11.3.0
.ds doc-operating-system-Darwin-11.4.0 11.4.0
.ds doc-operating-system-Darwin-11.5.0 11.5.0
.ds doc-operating-system-Darwin-12.0.0 12.0.0
.ds doc-operating-system-Darwin-12.1.0 12.1.0
.ds doc-operating-system-Darwin-12.2.0 12.2.0
.ds doc-operating-system-Darwin-13.0.0 13.0.0
.ds doc-operating-system-Darwin-13.1.0 13.1.0
.ds doc-operating-system-Darwin-13.2.0 13.2.0
.ds doc-operating-system-Darwin-13.3.0 13.3.0
.ds doc-operating-system-Darwin-13.4.0 13.4.0
.ds doc-operating-system-Darwin-14.0.0 14.0.0
.ds doc-operating-system-Darwin-14.1.0 14.1.0
.ds doc-operating-system-Darwin-14.2.0 14.2.0
.ds doc-operating-system-Darwin-14.3.0 14.3.0
.ds doc-operating-system-Darwin-14.4.0 14.4.0
.ds doc-operating-system-Darwin-14.5.0 14.5.0
.ds doc-operating-system-Darwin-15.0.0 15.0.0
.ds doc-operating-system-Darwin-15.1.0 15.1.0
.ds doc-operating-system-Darwin-15.2.0 15.2.0
.ds doc-operating-system-Darwin-15.3.0 15.3.0
.ds doc-operating-system-Darwin-15.4.0 15.4.0
.ds doc-operating-system-Darwin-15.5.0 15.5.0
.ds doc-operating-system-Darwin-15.6.0 15.6.0
.ds doc-operating-system-Darwin-16.0.0 16.0.0
.ds doc-operating-system-Darwin-16.1.0 16.1.0
.ds doc-operating-system-Darwin-16.2.0 16.2.0
.ds doc-operating-system-Darwin-16.3.0 16.3.0
.ds doc-operating-system-Darwin-16.4.0 16.4.0
.ds doc-operating-system-Darwin-16.5.0 16.5.0
.ds doc-operating-system-Darwin-16.6.0 16.6.0
.ds doc-operating-system-Darwin-17.0.0 17.0.0
.ds doc-operating-system-Darwin-17.1.0 17.1.0
.ds doc-operating-system-Darwin-17.2.0 17.2.0
.ds doc-operating-system-Darwin-17.3.0 17.3.0
.ds doc-operating-system-Darwin-17.4.0 17.4.0
.ds doc-operating-system-Darwin-17.5.0 17.5.0
.ds doc-operating-system-Darwin-17.6.0 17.6.0
.ds doc-operating-system-Darwin-17.7.0 17.7.0
.ds doc-operating-system-Darwin-18.0.0 18.0.0
.ds doc-operating-system-Darwin-18.1.0 18.1.0
.ds doc-operating-system-Darwin-18.2.0 18.2.0
.ds doc-operating-system-Darwin-18.3.0 18.3.0
.ds doc-operating-system-Darwin-18.4.0 18.4.0
.ds doc-operating-system-Darwin-18.5.0 18.5.0
.ds doc-operating-system-Darwin-18.6.0 18.6.0
.ds doc-operating-system-Darwin-18.7.0 18.7.0
.ds doc-operating-system-Darwin-19.0.0 19.0.0
.ds doc-operating-system-Darwin-19.1.0 19.1.0
.ds doc-operating-system-Darwin-19.2.0 19.2.0
.
.ds doc-operating-system-DragonFly-1.0    1.0
.ds doc-operating-system-DragonFly-1.1    1.1
.ds doc-operating-system-DragonFly-1.2    1.2
.ds doc-operating-system-DragonFly-1.3    1.3
.ds doc-operating-system-DragonFly-1.4    1.4
.ds doc-operating-system-DragonFly-1.5    1.5
.ds doc-operating-system-DragonFly-1.6    1.6
.ds doc-operating-system-DragonFly-1.7    1.7
.ds doc-operating-system-DragonFly-1.8    1.8
.ds doc-operating-system-DragonFly-1.8.1  1.8.1
.ds doc-operating-system-DragonFly-1.9    1.9
.ds doc-operating-system-DragonFly-1.10   1.10
.ds doc-operating-system-DragonFly-1.11   1.11
.ds doc-operating-system-DragonFly-1.12   1.12
.ds doc-operating-system-DragonFly-1.12.2 1.12.2
.ds doc-operating-system-DragonFly-1.13   1.13
.ds doc-operating-system-DragonFly-2.0    2.0
.ds doc-operating-system-DragonFly-2.1    2.1
.ds doc-operating-system-DragonFly-2.2    2.2
.ds doc-operating-system-DragonFly-2.3    2.3
.ds doc-operating-system-DragonFly-2.4    2.4
.ds doc-operating-system-DragonFly-2.5    2.5
.ds doc-operating-system-DragonFly-2.6    2.6
.ds doc-operating-system-DragonFly-2.7    2.7
.ds doc-operating-system-DragonFly-2.8    2.8
.ds doc-operating-system-DragonFly-2.9    2.9
.ds doc-operating-system-DragonFly-2.9.1  2.9.1
.ds doc-operating-system-DragonFly-2.10   2.10
.ds doc-operating-system-DragonFly-2.10.1 2.10.1
.ds doc-operating-system-DragonFly-2.11   2.11
.ds doc-operating-system-DragonFly-2.12   2.12
.ds doc-operating-system-DragonFly-2.13   2.13
.ds doc-operating-system-DragonFly-3.0    3.0
.ds doc-operating-system-DragonFly-3.0.1  3.0.1
.ds doc-operating-system-DragonFly-3.0.2  3.0.2
.ds doc-operating-system-DragonFly-3.1    3.1
.ds doc-operating-system-DragonFly-3.2    3.2
.ds doc-operating-system-DragonFly-3.2.1  3.2.1
.ds doc-operating-system-DragonFly-3.2.2  3.2.2
.ds doc-operating-system-DragonFly-3.3    3.3
.ds doc-operating-system-DragonFly-3.4    3.4
.ds doc-operating-system-DragonFly-3.4.1  3.4.1
.ds doc-operating-system-DragonFly-3.4.2  3.4.2
.ds doc-operating-system-DragonFly-3.4.3  3.4.3
.ds doc-operating-system-DragonFly-3.5    3.5
.ds doc-operating-system-DragonFly-3.6    3.6
.ds doc-operating-system-DragonFly-3.6.1  3.6.1
.ds doc-operating-system-DragonFly-3.6.2  3.6.2
.ds doc-operating-system-DragonFly-3.7    3.7
.ds doc-operating-system-DragonFly-3.8    3.8
.ds doc-operating-system-DragonFly-3.8.1  3.8.1
.ds doc-operating-system-DragonFly-3.8.2  3.8.2
.ds doc-operating-system-DragonFly-4.0    4.0
.ds doc-operating-system-DragonFly-4.0.1  4.0.1
.ds doc-operating-system-DragonFly-4.0.2  4.0.2
.ds doc-operating-system-DragonFly-4.0.3  4.0.3
.ds doc-operating-system-DragonFly-4.0.4  4.0.4
.ds doc-operating-system-DragonFly-4.0.5  4.0.5
.ds doc-operating-system-DragonFly-4.0.6  4.0.6
.ds doc-operating-system-DragonFly-4.1    4.1
.ds doc-operating-system-DragonFly-4.2    4.2
.ds doc-operating-system-DragonFly-4.2.1  4.2.1
.ds doc-operating-system-DragonFly-4.2.2  4.2.2
.ds doc-operating-system-DragonFly-4.2.3  4.2.3
.ds doc-operating-system-DragonFly-4.2.4  4.2.4
.ds doc-operating-system-DragonFly-4.3    4.3
.ds doc-operating-system-DragonFly-4.4    4.4
.ds doc-operating-system-DragonFly-4.4.1  4.4.1
.ds doc-operating-system-DragonFly-4.4.2  4.4.2
.ds doc-operating-system-DragonFly-4.4.3  4.4.3
.ds doc-operating-system-DragonFly-4.5    4.5
.ds doc-operating-system-DragonFly-4.6    4.6
.ds doc-operating-system-DragonFly-4.6.1  4.6.1
.ds doc-operating-system-DragonFly-4.6.2  4.6.2
.ds doc-operating-system-DragonFly-4.7    4.7
.ds doc-operating-system-DragonFly-4.8    4.8
.ds doc-operating-system-DragonFly-4.8.1  4.8.1
.ds doc-operating-system-DragonFly-4.9    4.9
.ds doc-operating-system-DragonFly-5.0    5.0
.ds doc-operating-system-DragonFly-5.0.1  5.0.1
.ds doc-operating-system-DragonFly-5.0.2  5.0.2
.ds doc-operating-system-DragonFly-5.1    5.1
.ds doc-operating-system-DragonFly-5.2    5.2
.ds doc-operating-system-DragonFly-5.2.1  5.2.1
.ds doc-operating-system-DragonFly-5.2.2  5.2.2
.ds doc-operating-system-DragonFly-5.3    5.3
.ds doc-operating-system-DragonFly-5.4    5.4
.ds doc-operating-system-DragonFly-5.4.1  5.4.1
.ds doc-operating-system-DragonFly-5.4.2  5.4.2
.ds doc-operating-system-DragonFly-5.4.3  5.4.3
.ds doc-operating-system-DragonFly-5.5    5.5
.ds doc-operating-system-DragonFly-5.6    5.6
.ds doc-operating-system-DragonFly-5.6.1  5.6.1
.ds doc-operating-system-DragonFly-5.6.2  5.6.2
.
.eo
.de Os
.  ie "\$1"" \
.    ds doc-operating-system "\*[doc-default-operating-system]
.  el \{ .ie "\$1"ATT" \{\
.    ds doc-operating-system AT&T
.    if \A'\$2' \{\
.      ie d doc-operating-system-ATT-\$2 \
.        as doc-operating-system " \*[doc-operating-system-ATT-\$2]
.      el \
.        as doc-operating-system " UNIX
.  \}\}
.  el \{ .ie "\$1"BSD" \{\
.    if \A'\$2' \{\
.      ie d doc-operating-system-BSD-\$2 \
.        ds doc-operating-system "\*[doc-operating-system-BSD-\$2]
.      el \
.        tm mdoc warning: .Os: Unknown BSD version '\$2' (#\n[.c])
.  \}\}
.  el \{ .ie "\$1"FreeBSD" \{\
.    ds doc-operating-system FreeBSD
.    if \A'\$2' \{\
.      ie d doc-operating-system-FreeBSD-\$2 \
.        as doc-operating-system \~\*[doc-operating-system-FreeBSD-\$2]
.      el \
.        tm mdoc warning: .Os: Unknown FreeBSD version '\$2' (#\n[.c])
.  \}\}
.  el \{ .ie "\$1"DragonFly" \{\
.    ds doc-operating-system DragonFly
.    if \A'\$2' \{\
.      ie d doc-operating-system-DragonFly-\$2 \
.        as doc-operating-system \~\*[doc-operating-system-DragonFly-\$2]
.      el \
.        tm mdoc warning: .Os: Unknown DragonFly version '\$2' (#\n[.c])
.  \}\}
.  el \{ .ie "\$1"NetBSD" \{\
.    ds doc-operating-system NetBSD
.    if \A'\$2' \{\
.      ie d doc-operating-system-NetBSD-\$2 \
.        as doc-operating-system \~\*[doc-operating-system-NetBSD-\$2]
.      el \
.        tm mdoc warning: .Os: Unknown NetBSD version '\$2' (#\n[.c])
.  \}\}
.  el \{ .ie "\$1"OpenBSD" \{\
.    ds doc-operating-system OpenBSD
.    if \A'\$2' \{\
.      ie d doc-operating-system-OpenBSD-\$2 \
.        as doc-operating-system \~\*[doc-operating-system-OpenBSD-\$2]
.      el \
.        tm mdoc warning: .Os: Unknown OpenBSD version '\$2' (#\n[.c])
.  \}\}
.  el \{ .ie "\$1"Darwin" \{\
.    ds doc-operating-system Darwin
.    if \A'\$2' \{\
.      ie d doc-operating-system-Darwin-\$2 \
.        as doc-operating-system \~\*[doc-operating-system-Darwin-\$2]
.      el \
.        tm mdoc warning: .Os: Unknown Darwin version '\$2' (#\n[.c])
.  \}\}
.  el \{\
.    ds doc-operating-system \$1
.    if !"\$2"" \
.      as doc-operating-system " \$2
.  \}\}\}\}\}\}\}\}
.
.  doc-set-up-titles
.
.  if '\*[.T]'pdf' \
.    pdfbookmark 1 "\*[doc-page-topic](\*[doc-section])"
.
.  doc-header
.  nr doc-need-titles-reset 1
..
.ec
.
.
.\" NS doc-hyphen-flags global register
.\" NS   the parameter for the '.hy' request
.ie \n[HY] \{\
.  ie \n[cR] .nr doc-hyphen-flags \n[\*[locale]*hyphenation-mode-base]
.  el        .nr doc-hyphen-flags \n[\*[locale]*hyphenation-mode-trap]
.\}
.el .nr doc-hyphen-flags 0
.
.
.\" NS doc-header macro
.\" NS   print page header
.\" NS
.\" NS local variables:
.\" NS   doc-xref
.\" NS   doc-abbv
.\" NS   doc-reg-dh
.\" NS   doc-reg-dh1
.\" NS   doc-hs-len
.\" NS   doc-hs-len-prev
.
.eo
.de doc-header
.  ds doc-xref \*[doc-page-topic-font]\*[doc-page-topic]\f[]\"
.  as doc-xref \*[doc-page-section-font](\*[doc-section])\f[]\"
.  ds doc-abbv \*[doc-page-topic]\"
.  ev doc-env-dh
.  doc-setup-page-layout
.  ie \n[cR] .pl +1v
.  el        .sp .5i
.  nr doc-reg-dh \w'\*[doc-page-topic-font]\*[doc-xref]\f[]'
.  nr doc-reg-dh1 \w'\*[doc-volume]'
.  if (\n[doc-reg-dh] + \n[doc-reg-dh1] + \n[doc-reg-dh] >= \n[.lt]) \{\
.    while (\n[doc-reg-dh] + \n[doc-reg-dh1] + \n[doc-reg-dh] >= \n[.lt]) \{\
.      ds doc-xref \*[doc-page-topic-font]\*[doc-abbv]\f[]\"
.      as doc-xref \*[doc-page-section-font](\*[doc-section])\f[]\"
.      length doc-abbv-len-prev \*[doc-abbv]
.      substring doc-abbv 0 -2
.      length doc-abbv-len \*[doc-abbv]
.      nr doc-reg-dh \w'\*[doc-page-topic-font]\*[doc-abbv]\|.\|.\|.\f[]'
.      \" If header string didn't actually get shorter, stop trying.
.      if (\n[doc-abbv-len-prev] <= \n[doc-abbv-len]) \
.        break
.    \}
.    rr doc-abbv-len
.    rr doc-abbv-len-prev
.    as doc-abbv \|.\|.\|.
.  \}
.  tl '\*[doc-xref]'\*[doc-volume]\f[]'\*[doc-xref]'
.  ie \n[cR] \{\
.    pl +1v
.    sp 1v
.  \}
.  el .sp |1i
.  ev
.  ns
.  rm doc-xref
.  rm doc-abbv
..
.ec
.
.
.\" NS doc-break-body-text
.\" NS   Schedule a page break when the next output line is written (not
.\" NS   called if continuously rendering).
.de doc-break-body-text
'  bp
..
.
.
.\" NS doc-footer macro
.\" NS   print page footer
.\"
.\" NS local variables:
.\" NS   doc-xref
.\" NS   doc-page-id
.
.eo
.de doc-footer
.  ds doc-xref \*[doc-page-topic-font]\*[doc-page-topic]\f[]\"
.  as doc-xref \*[doc-page-section-font](\*[doc-section])\f[]\"
.  ds doc-page-id \n[%]
.  if r X \{\
.    if (\n[%] > \n[X]) \{\
.      nr doc-page-letter (\n[%] - \n[X])
.      ds doc-page-id \n[X]\n[doc-page-letter]\"
.    \}
.  \}
.  ev doc-caption-enviroment
.  doc-setup-page-layout
.  ie \n[D] \{\
.    ie e \
.      tl '%'\*[doc-date-string]'\*[doc-operating-system]'
.    el \
.      tl '\*[doc-operating-system]'\*[doc-date-string]'\*[doc-page-id]'
.  \}
.  el \{\
.    ie \n[cR] \
.      tl '\*[doc-operating-system]'\*[doc-date-string]'\*[doc-xref]'
.    el \
.      tl '\*[doc-operating-system]'\*[doc-date-string]'\*[doc-page-id]'
.  \}
.  if !\n[cR] .bp
.  ev
.  rm doc-page-id
.  rm doc-xref
..
.ec
.
.
.\" NS doc-check-depth macro
.\" NS   check paired macros
.
.eo
.de doc-check-depth
.  if \n[doc-list-depth] \{\
.    tm mdoc warning: A .Bl directive has no matching .El (#\n[.c])
.    nr doc-list-depth 0
.  \}
.  if \n[doc-display-depth] \{\
.    tm mdoc warning: A .Bd directive has no matching .Ed (#\n[.c])
.    nr doc-display-depth 0
.  \}
.  if \n[doc-fontmode-depth] \{\
.    tm mdoc warning: A .Bf directive has no matching .Ef (#\n[.c])
.    nr doc-fontmode-depth 0
.  \}
..
.ec
.
.
.\" NS doc-end-macro macro
.\" NS   finish output
.\" NS
.\" NS modifies:
.\" NS   doc-need-titles-reset
.
.eo
.de doc-end-macro
.  doc-check-depth
.
.  if \n[cR] \{\
.    \" We might have a pending output line that is not yet broken, and
.    \" also be 1v from the bottom of the page.  If we break (or flush)
.    \" the output line now, the page will get ejected afterward and
.    \" troff will exit because we're in an end-of-input macro--our
.    \" footer will never be output.  So, if that is the case, further
.    \" extend the page length by 1v.
.    if ((\n[.p] - \n[nl]) <= \n[.V]) .pl +1v
.    br
.    pl +1v
.    sp 1v
.    doc-footer
.    \" If we're processing multiple documents and have started a new
.    \" one, draw a line between this footer and the next header.
.    if !'\n[.F]'' \{\
.      pl +1v
.      nf
.      ti 0
\D'l \n[doc-line-length]u 0'
.      fi
.    \}
.    \" suppress empty lines after the footer
.    pl \n[nl]u
.  \}
.  ch doc-header
.  doc-break-page-with-new-number
.
.  \" Reset strings to reduce info leaks from one man page to the next.
.  ds doc-date-string UNDATED\"
.  ds doc-page-topic UNTITLED\"
.  ds doc-volume LOCAL\"
.  ds doc-section \" empty
.  ds doc-operating-system \" empty
.  ds doc-topic-name \" empty
..
.ec
.
.
.\" NS doc-break-page-with-new-number macro
.\" NS   Break the page and update its number depending on the C
.\" NS   (consecutive numbering) register.
.\" NS
.\" NS  Corner case: if formatting multiple documents and P (starting
.\" NS  page number) is defined but C is not set, start numbering each
.\" NS  document at \n[P].  Not strictly necessary if not switching
.\" NS  macro packages.
.
.eo
.de doc-break-page-with-new-number
.  ie \n[C] .bp (\n[%] + 1) \" argument NOT redundant before page 1
.  el \{\
.    ie r P .bp \n[P]
.    el     .bp 1
.  \}
..
.ec
.
.
.\" NS doc-paragraph macro
.\" NS   insert a paragraph
.
.eo
.de doc-paragraph
.  sp \n[doc-paragraph-space]u
.  if !\n[cR] \
.    ne 2
.  ns
..
.ec
.
.
.\" NS Pp user macro (not parsed, not callable)
.\" NS   new paragraph
.\" NS
.\" NS width register 'Pp' set above
.
.als Pp doc-paragraph
.
.
.\" NS Lp user macro (not parsed, not callable)
.\" NS   same as .Pp
.\" NS
.\" NS width register 'Lp' set above
.
.als Lp doc-paragraph
.
.
.eo
.de LP
.  tm Not a \-mdoc command: .LP (#\n[.c])
..
.ec
.
.
.eo
.de PP
.  tm Not a \-mdoc command: .PP (#\n[.c])
..
.ec
.
.
.eo
.de pp
.  tm Not a \-mdoc command: .pp (#\n[.c])
..
.ec
.
.
.eo
.de SH
.  tm Not a \-mdoc command: .SH (#\n[.c])
..
.ec
.
.
.\" NS Nd user macro (not parsed, not callable)
.\" NS   print name description
.\" NS
.\" NS width register 'Nd' set above
.
.eo
.de Nd
.  nop \[em] \$*
..
.ec
.
.
.\" NS doc-in-name-section global register (bool)
.\" NS   whether we are in the 'name' section
.
.nr doc-in-name-section 0
.
.
.\" NS doc-in-synopsis-section global register (bool)
.\" NS   whether we are in the 'synopsis' section
.
.nr doc-in-synopsis-section 0
.
.
.\" NS doc-in-library-section global register (bool)
.\" NS   whether we are in the 'library' section
.
.nr doc-in-library-section 0
.
.
.\" NS doc-in-see-also-section global register (bool)
.\" NS   whether we are in the 'see also' section
.
.nr doc-in-see-also-section 0
.
.
.\" NS doc-in-files-section global register (bool)
.\" NS   whether we are in the 'files' section
.
.nr doc-in-files-section 0
.
.
.\" NS doc-in-authors-section global register (bool)
.\" NS   whether we are in the 'authors' section
.
.nr doc-in-authors-section 0
.
.
.\" NS doc-need-titles-reset global register (bool)
.\" NS   whether the strings that set header and footer text need to be
.\" NS   reconfigured
.\" NS
.\" NS This happens when batch-rendering and starting a new page.
.
.nr doc-need-titles-reset 0
.
.
.\" NS doc-first-parameter macro
.\" NS   return first parameter
.\" NS
.\" NS local variables:
.\" NS   doc-str-dfp
.
.eo
.de doc-first-parameter
.  ds doc-str-dfp "\$1
..
.ec
.
.
.\" NS doc-prepare-section-heading macro
.\" NS   define `doc-sec-head`, `macro` prepared for string matching
.\"
.\" NS
.\" NS local variables:
.\" NS   doc-str-tmp1
.\" NS   doc-str-tmp2
.\" NS   doc-tmp-strlen
.
.eo
.de doc-prepare-section-heading
.  ds doc-str-tmp1 "\$*
.  ds doc-str-tmp2 "\$*
.  length doc-tmp-strlen \$*
.  \" Leave (nonstandard) section headings of length 0 or 1 unchanged.
.  ie \n[doc-tmp-strlen]>1 \{\
.    substring doc-str-tmp1 0 0
.    substring doc-str-tmp2 1
.    stringdown doc-str-tmp2
.    ds doc-sec-head \*[doc-str-tmp1]\*[doc-str-tmp2]\"
.  \}
.  el \
.    ds doc-sec-head "\$*
.  rm doc-str-tmp1
.  rm doc-str-tmp2
.  rr doc-tmp-strlen
..
.ec
.
.
.\" NS Sh user macro (not callable)
.\" NS   section headers
.\" NS
.\" NS modifies:
.\" NS   doc-func-args-processed
.\" NS   doc-func-count
.\" NS   doc-in-authors-section
.\" NS   doc-in-files-section
.\" NS   doc-in-library-section
.\" NS   doc-in-name-section
.\" NS   doc-in-see-also-section
.\" NS   doc-in-synopsis-section
.\" NS   doc-indent-synopsis
.\" NS   doc-indent-synopsis-active
.\" NS   doc-is-func
.\" NS   doc-num-func-args
.\" NS
.\" NS local variables:
.\" NS   doc-reg-Sh
.\" NS   doc-reg-Sh1
.\" NS   doc-section-XXX
.\" NS
.\" NS width register 'Sh' set in doc-common
.
.ds doc-section-name        Name\"
.ds doc-section-synopsis    Synopsis\"
.ds doc-section-library     Library\"
.ds doc-section-description Description\"
.ds doc-section-see-also    See also\"
.ds doc-section-files       Files\"
.ds doc-section-authors     Authors\"
.
.eo
.de Sh
.  \" Tell doc-print-recursive whether to force capitalization.
.  nr doc-do-capitalize \n[CS]
.
.  \" Normalize capitalization of section heading.
.  doc-prepare-section-heading \$*
.
.  ie "\*[doc-sec-head]"\*[doc-section-name]" \
.    nr doc-in-name-section 1
.  el \
.    nr doc-in-name-section 0
.
.  ie \n[doc-arg-count] \{\
.    \" we only allow 'Sh' within 'Sh'; it will change the font back to
.    \" 'doc-Sh-font'
.    ie "\*[doc-macro-name]"Sh" \{\
.      nr doc-arg-ptr +1
.      ie (\n[doc-arg-count] >= \n[doc-arg-ptr]) \{\
.        nr doc-curr-font \n[.f]
.        nop \*[doc-Sh-font]\c
.        doc-print-recursive
.      \}
.      el \{\
.        tm Usage: .Sh section_name ... (#\n[.c])
.        doc-reset-args
.    \}\}
.    el \{\
.      tm Usage: .Sh not callable by other macros (#\n[.c])
.      doc-reset-args
.  \}\}
.  el \{\
.    if !\n[.$] \{\
.      tm Usage: .Sh section_name ... (#\n[.c])
.      return
.    \}
.
.    ds doc-macro-name Sh
.    doc-parse-args \$@
.
.    ad \*[AD]
.
.    ie "\*[doc-sec-head]"\*[doc-section-name]" \{\
.      doc-set-up-titles
.      in 0
.    \}
.    el \{\
.      nr doc-in-name-section 0
.      nr doc-in-synopsis-section 0
.      nr doc-in-library-section 0
.      nr doc-in-see-also-section 0
.      nr doc-in-files-section 0
.      nr doc-in-authors-section 0
.
.      ie        "\*[doc-sec-head]"\*[doc-section-synopsis]" \{\
.        if t \
.          na
.        nr doc-in-synopsis-section 1
.        nr doc-indent-synopsis 0
.        nr doc-indent-synopsis-active 0
.      \}
.      el \{ .ie "\*[doc-sec-head]"\*[doc-section-library]" \{\
.        nr doc-in-library-section 1
.      \}
.      el \{ .ie "\*[doc-sec-head]"\*[doc-section-description]" \{\
.        nr doc-is-func 0
.        nr doc-func-count 0
.        nr doc-func-args-processed 0
.        nr doc-num-func-args 0
.      \}
.      el \{ .ie "\*[doc-sec-head]"\*[doc-section-see-also]" \{\
.        if t \
.          na
.        nr doc-in-see-also-section 1
.      \}
.      el \{ .ie "\*[doc-sec-head]"\*[doc-section-files]" \
.        nr doc-in-files-section 1
.      el .if    "\*[doc-sec-head]"\*[doc-section-authors]" \
.        nr doc-in-authors-section 1
.      \}\}\}\}
.
.      in 0
.      nr doc-have-author 0
.    \}
.
.    doc-setup-page-layout
.    sp \n[doc-paragraph-space]u
.    ns
.    ta T .5i
.    if !\n[cR] \
.      ne 3
.    fi
.
.  if '\*[.T]'pdf' \
.    pdfbookmark 2 "\*[doc-sec-head]"
.
.    if t \{\
.      nr doc-reg-Sh \n[.ss]
.      nr doc-reg-Sh1 \n[.sss]
.      ss (\n[.ss] * 5 / 3) (\n[.sss] * 5 / 3)
.    \}
.
.  if \n[doc-remap-I-style-in-headings] \
.    ftr \*[doc-heading-family]I \*[doc-heading-family]BI
.
.    nr doc-arg-ptr +1
.    nr doc-curr-font \n[.f]
.    nop \*[doc-Sh-font]\c
.    doc-print-recursive
.
.    if t \
.      ss \n[doc-reg-Sh] \n[doc-reg-Sh1]
.
.    in \n[IN]u
.    ns
.
.    doc-check-depth
.  if \n[doc-remap-I-style-in-headings] \
.    ftr \*[doc-heading-family]I \*[doc-heading-family]I
.  \}
.
.
.  \" Don't let doc-print-recursive force caps on anything else.
.  nr doc-do-capitalize 0
..
.ec
.
.
.\" NS Ss user macro (not callable)
.\" NS   subsection
.\" NS
.\" NS modifies:
.\" NS   doc-subsection-heading
.\" NS
.\" NS local variable:
.\" NS   doc-reg-Ss
.\" NS   doc-reg-Ss1
.\" NS
.\" NS width register 'Ss' set above
.
.eo
.de Ss
.  ie \n[doc-arg-count] \{\
.    \" we only allow 'Ss' within 'Ss'; it will change the font back to
.    \" 'doc-Sh-font'
.    ie "\*[doc-macro-name]"Ss" \{\
.      nr doc-arg-ptr +1
.      ie (\n[doc-arg-count] >= \n[doc-arg-ptr]) \{\
.        nr doc-curr-font \n[.f]
.        nop \*[doc-Sh-font]\c
.        doc-print-recursive
.      \}
.      el \{\
.        tm Usage: .Ss subsection_name ... (#\n[.c])
.        doc-reset-args
.    \}\}
.    el \{\
.      tm Usage: .Ss not callable by other macros (#\n[.c])
.      doc-reset-args
.  \}\}
.  el \{\
.    if !\n[.$] \{\
.      tm Usage: .Ss subsection_name ... (#\n[.c])
.      return
.    \}
.
.    ds doc-macro-name Ss
.    doc-parse-args \$@
.
.    ds doc-subsection-heading \$*
.
.    sp \n[doc-paragraph-space]u
.    if !\n[cR] \
.      ne 3
.    in \n[SN]u
.
.    if '\*[.T]'pdf' \
.      pdfbookmark 3 "\*[doc-subsection-heading]"
.
.    nr doc-reg-Ss \n[.ss]
.    nr doc-reg-Ss1 \n[.sss]
.    ss (\n[.ss] * 5 / 4) (\n[.sss] * 5 / 4)
.
.  if \n[doc-remap-I-style-in-headings] \
.    ftr \*[doc-heading-family]I \*[doc-heading-family]BI
.
.    nr doc-arg-ptr +1
.    nr doc-curr-font \n[.f]
.    nop \*[doc-Sh-font]\c
.    doc-print-recursive
.
.    ss \n[doc-reg-Ss] \n[doc-reg-Ss1]
.
.    ta T .5i
.    in
.    if !\n[cR] \
.      ne 2
.    br
.    ns
.
.    doc-check-depth
.  if \n[doc-remap-I-style-in-headings] \
.    ftr \*[doc-heading-family]I \*[doc-heading-family]I
.  \}
.
..
.ec
.
.
.\" NS Rd macro (not parsed, not callable)
.\" NS   print global register dump to stderr
.\" NS
.\" NS local variables:
.\" NS   doc-reg-Rd
.
.eo
.de Rd
.  tm MDOC GLOBAL REGISTER DUMP
.  tm doc-macro-name == '\*[doc-macro-name]'
.  tm doc-arg-count == \n[doc-arg-count]
.  tm doc-num-args == \n[doc-num-args]
.  tm doc-arg-ptr == \n[doc-arg-ptr]
.
.  nr doc-reg-Rd 1
.  while (\n[doc-reg-Rd] <= \n[doc-arg-count]) \{\
.    tm doc-arg\n[doc-reg-Rd] == '\*[doc-arg\n[doc-reg-Rd]]'
.    tm doc-type\n[doc-reg-Rd] == \n[doc-type\n[doc-reg-Rd]]
.    tm doc-space\n[doc-reg-Rd] == '\*[doc-space\n[doc-reg-Rd]]'
.    nr doc-reg-Rd +1
.  \}
.
.  tm doc-curr-font == \n[doc-curr-font]
.  tm doc-indent-synopsis == \n[doc-indent-synopsis]
.  tm doc-indent-synopsis-active == \n[doc-indent-synopsis-active]
.  tm doc-have-decl == \n[doc-have-decl]
.  tm doc-have-var == \n[doc-have-var]
.  tm doc-topic-name == '\*[doc-topic-name]'
.  tm doc-quote-left == '\*[doc-quote-left]'
.  tm doc-quote-right == '\*[doc-quote-right]'
.  tm doc-nesting-level == \n[doc-nesting-level]
.  tm doc-in-list == \n[doc-in-list]
.  tm doc-space == '\*[doc-space]'
.  tm doc-saved-space == '\*[doc-saved-space]'
.  tm doc-space-mode == \n[doc-space-mode]
.  tm doc-have-space == \n[doc-have-space]
.  tm doc-have-slot == \n[doc-have-slot]
.  tm doc-keep-type == \n[doc-keep-type]
.  tm doc-display-depth == \n[doc-display-depth]
.  tm doc-is-compact == \n[doc-is-compact]
.
.  nr doc-reg-Rd 0
.  while (\n[doc-reg-Rd] <= \n[doc-display-depth]) \{\
.    tm doc-display-type-stack\n[doc-reg-Rd] == '\*[doc-display-type-stack\n[doc-reg-Rd]]'
.    tm doc-display-indent-stack\n[doc-reg-Rd] == \n[doc-display-indent-stack\n[doc-reg-Rd]]
.    tm doc-display-ad-stack\n[doc-reg-Rd] == \n[doc-display-ad-stack\n[doc-reg-Rd]]
.    tm doc-display-fi-stack\n[doc-reg-Rd] == \n[doc-display-fi-stack\n[doc-reg-Rd]]
.    tm doc-display-ft-stack\n[doc-reg-Rd] == \n[doc-display-ft-stack\n[doc-reg-Rd]]
.    tm doc-display-ps-stack\n[doc-reg-Rd] == \n[doc-display-ps-stack\n[doc-reg-Rd]]
.    nr doc-reg-Rd +1
.  \}
.
.  tm doc-fontmode-depth == \n[doc-fontmode-depth]
.
.  nr doc-reg-Rd 1
.  while (\n[doc-reg-Rd] <= \n[doc-fontmode-depth]) \{\
.    tm doc-fontmode-font-stack\n[doc-reg-Rd] == '\n[doc-fontmode-font-stack\n[doc-reg-Rd]]'
.    tm doc-fontmode-size-stack\n[doc-reg-Rd] == '\n[doc-fontmode-size-stack\n[doc-reg-Rd]]'
.    nr doc-reg-Rd +1
.  \}
.
.  tm doc-list-depth == \n[doc-list-depth]
.
.  nr doc-reg-Rd 1
.  while (\n[doc-reg-Rd] <= \n[doc-list-depth]) \{\
.    tm doc-list-type-stack\n[doc-reg-Rd] == '\*[doc-list-type-stack\n[doc-reg-Rd]]'
.    tm doc-list-have-indent-stack\n[doc-reg-Rd] == \n[doc-list-have-indent-stack\n[doc-reg-Rd]]
.    tm doc-list-indent-stack\n[doc-reg-Rd] == \n[doc-list-indent-stack\n[doc-reg-Rd]]
.    tm doc-compact-list-stack\n[doc-reg-Rd] == \n[doc-compact-list-stack\n[doc-reg-Rd]]
.    tm doc-tag-prefix-stack\n[doc-reg-Rd] == '\*[doc-tag-prefix-stack\n[doc-reg-Rd]]'
.    tm doc-tag-width-stack\n[doc-reg-Rd] == '\*[doc-tag-width-stack\n[doc-reg-Rd]]'
.    tm doc-list-offset-stack\n[doc-reg-Rd] == \n[doc-list-offset-stack\n[doc-reg-Rd]]
.    tm doc-enum-list-count-stack\n[doc-reg-Rd] == \n[doc-enum-list-count-stack\n[doc-reg-Rd]]
.    nr doc-reg-Rd +1
.  \}
.
.  tm doc-saved-Pa-font == '\*[doc-saved-Pa-font]'
.  tm doc-curr-type == \n[doc-curr-type]
.  tm doc-curr-arg == '\*[doc-curr-arg]'
.  tm doc-diag-list-input-line-count == \n[doc-diag-list-input-line-count]
.  tm doc-num-columns == \n[doc-num-columns]
.  tm doc-column-indent-width == \n[doc-column-indent-width]
.  tm doc-is-func == \n[doc-is-func]
.  tm doc-have-old-func == \n[doc-have-old-func]
.  tm doc-func-arg-count == \n[doc-func-arg-count]
.  tm doc-func-arg == '\*[doc-func-arg]'
.  tm doc-num-func-args == \n[doc-num-func-args]
.  tm doc-func-args-processed == \n[doc-func-args-processed]
.  tm doc-have-func == \n[doc-have-func]
.  tm doc-is-reference == \n[doc-is-reference]
.  tm doc-reference-count == \n[doc-reference-count]
.  tm doc-author-count == \n[doc-author-count]
.
.  nr doc-reg-Rd 0
.  while (\n[doc-reg-Rd] <= \n[doc-author-count]) \{\
.    tm doc-author-name\n[doc-reg-Rd] == '\*[doc-author-name\n[doc-reg-Rd]]'
.    nr doc-reg-Rd +1
.  \}
.
.  tm doc-book-count == \n[doc-book-count]
.  tm doc-book-name == '\*[doc-book-name]'
.  tm doc-date-count == \n[doc-date-count]
.  tm doc-date == '\*[doc-date]'
.  tm doc-publisher-count == \n[doc-publisher-count]
.  tm doc-publisher-name == '\*[doc-publisher-name]'
.  tm doc-journal-count == \n[doc-journal-count]
.  tm doc-journal-name == '\*[doc-journal-name]'
.  tm doc-issue-count == \n[doc-issue-count]
.  tm doc-issue-name == '\*[doc-issue-name]'
.  tm doc-optional-count == \n[doc-optional-count]
.  tm doc-optional-string == '\*[doc-optional-string]'
.  tm doc-page-number-count == \n[doc-page-number-count]
.  tm doc-page-number-string == '\*[doc-page-number-string]'
.  tm doc-corporate-count == \n[doc-corporate-count]
.  tm doc-corporate-name == '\*[doc-corporate-name]'
.  tm doc-report-count == \n[doc-report-count]
.  tm doc-report-name == '\*[doc-report-name]'
.  tm doc-reference-title-count == \n[doc-reference-title-count]
.  tm doc-reference-title-name == '\*[doc-reference-title-name]'
.  tm doc-reference-title-name-for-book == '\*[doc-reference-title-name-for-book]'
.  tm doc-url-count == \n[doc-url-count]
.  tm doc-url-name == '\*[doc-url-name]'
.  tm doc-volume-count == \n[doc-volume-count]
.  tm doc-volume-name == '\*[doc-volume-name]'
.  tm doc-have-author == \n[doc-have-author]
.
.  tm doc-page-topic == '\*[doc-page-topic]'
.  tm doc-volume == '\*[doc-volume]'
.  tm doc-section == '\*[doc-section]'
.  tm doc-operating-system == '\*[doc-operating-system]'
.  tm doc-date-string == '\*[doc-date-string]'
.  tm doc-display-vertical == \n[doc-display-vertical]
.  tm doc-in-name-section == \n[doc-in-name-section]
.  tm doc-in-synopsis-section == \n[doc-in-synopsis-section]
.  tm doc-in-library-section == \n[doc-in-library-section]
.  tm doc-in-see-also-section == \n[doc-in-see-also-section]
.  tm doc-in-files-section == \n[doc-in-files-section]
.  tm doc-in-authors-section == \n[doc-in-authors-section]
.
.  tm END OF GLOBAL REGISTER DUMP
..
.ec
.
.
.\" Local Variables:
.\" mode: nroff
.\" fill-column: 72
.\" End:
.\" vim: set filetype=groff textwidth=72:
