## [2.16.0](https://github.com/scop/bash-completion/compare/2.15.0...2.16.0) (2024-12-25)


### Features

* **alp:** add 3rd party fallback completion loader ([606017f](https://github.com/scop/bash-completion/commit/606017f0b77734140a2612132985ee4dc01fd227))
* **apko:** add 3rd party fallback completion loader ([e906c75](https://github.com/scop/bash-completion/commit/e906c75236fc961e6dbb21c4707e454952ab09cc))
* **aqua:** add 3rd party fallback completion loader ([#1275](https://github.com/scop/bash-completion/issues/1275)) ([379a951](https://github.com/scop/bash-completion/commit/379a951454283e64ad6b5f55f885ff25bfc7454e))
* **argc:** add 3rd party fallback completion loader ([f0c3b59](https://github.com/scop/bash-completion/commit/f0c3b59fd21edb99f6aec83043eb4ce0eaa64d29))
* **argo:** add 3rd party fallback completion loader ([c069996](https://github.com/scop/bash-completion/commit/c06999686590810805b1b755da468025c7f21d54))
* **atlas:** add 3rd party fallback completion loader ([ce0abdd](https://github.com/scop/bash-completion/commit/ce0abdd70617bdb41f018d26eee8542107cd5770))
* **atmos:** add 3rd party fallback completion loader ([8796f86](https://github.com/scop/bash-completion/commit/8796f86938758396ae3e89c167f3dc1e834612e1))
* **bashbot:** add 3rd party fallback completion loader ([494e625](https://github.com/scop/bash-completion/commit/494e6251e8ab7f8a707fcdcd40255a994357d824))
* **bosh:** add 3rd party fallback completion loader ([21fcca5](https://github.com/scop/bash-completion/commit/21fcca5e44793cf235de1dd281e2d84ca5ee0263))
* **caddy:** add 3rd party fallback completion loader ([6b999e5](https://github.com/scop/bash-completion/commit/6b999e5bd6167154d54e516fe631716713356740))
* **chamber:** add 3rd party fallback completion loader ([d6f2798](https://github.com/scop/bash-completion/commit/d6f2798960b4597ee40206e80a140cfc40e85b92))
* **changie:** add 3rd party fallback completion loader ([5268e0b](https://github.com/scop/bash-completion/commit/5268e0bd7700edd956ea3566216b3536cc7cbdfd))
* **clusterctl:** add 3rd party fallback completion loader ([6124044](https://github.com/scop/bash-completion/commit/612404438fc99954e2fa21334cbfd078fc6de44b))
* **cmctl:** add 3rd party fallback completion loader ([4dcef0b](https://github.com/scop/bash-completion/commit/4dcef0b6598fde5c141843ee7b3051f38d55d298))
* **coder:** add 3rd party fallback completion loader ([4dc827a](https://github.com/scop/bash-completion/commit/4dc827a3c91d6f5badc73f479e8d75db72ff607e))
* **colima:** add 3rd party fallback completion loader ([55a75aa](https://github.com/scop/bash-completion/commit/55a75aae8a21b11d65eef27e115d853a2e24819a))
* **conform:** add 3rd party fallback completion loader ([676c062](https://github.com/scop/bash-completion/commit/676c0629ee1f7fdba5f480387b54b708cb211b43))
* **conftest:** add 3rd party fallback completion loader ([123c8d5](https://github.com/scop/bash-completion/commit/123c8d599d1b818a3e775ab16854fee7ccb62f40))
* **container-structure-test:** add 3rd party fallback completion loader ([42d9241](https://github.com/scop/bash-completion/commit/42d9241eee14f8cbbf7e71df1ba8d7cb56badbfa))
* **cosign:** add 3rd party fallback completion loader ([#1286](https://github.com/scop/bash-completion/issues/1286)) ([2dba9c6](https://github.com/scop/bash-completion/commit/2dba9c66a42b0d0bbbcde678d54123f3f56c12d5))
* **crane,gcrane,krane:** add 3rd party fallback completion loaders ([24918db](https://github.com/scop/bash-completion/commit/24918dbca1d0adfee7b88c761d4a48f36061c864))
* **crictl:** add 3rd party fallback completion loader ([7823df8](https://github.com/scop/bash-completion/commit/7823df8716e32486d83a3ece16f688bca011191b))
* **ctlptl:** add 3rd party fallback completion loader ([c3784cc](https://github.com/scop/bash-completion/commit/c3784cc41acf1bf984e4279ba34fb4cdfb13e61c))
* **cue:** add 3rd party fallback completion loader ([b9b56e0](https://github.com/scop/bash-completion/commit/b9b56e031a1902a76b9a6fb55ce3bd17ccce413f))
* **dagger:** add 3rd party fallback completion loader ([fb50c08](https://github.com/scop/bash-completion/commit/fb50c0860507518bec63f9b42d0116af5a9cd4b3))
* **dapr:** add 3rd party fallback completion loader ([12b91f1](https://github.com/scop/bash-completion/commit/12b91f11ab9dd3335222099c7d02462375829181))
* **dasel:** add 3rd party fallback completion loader ([6c85159](https://github.com/scop/bash-completion/commit/6c8515988c6cc9523c1798e64a99762a1c76fc5e))
* **deck:** add 3rd party fallback completion loader ([254f76a](https://github.com/scop/bash-completion/commit/254f76a30838f624326346737c7b434fd20eac78))
* **delta:** add 3rd party fallback completion loader ([164852f](https://github.com/scop/bash-completion/commit/164852f9555d8fcb1959f06f2cbaed75a24ab6d7))
* **depot:** add 3rd party fallback completion loader ([ba56206](https://github.com/scop/bash-completion/commit/ba5620612b7e7c4ec2ca57ef92cbaa599fc42814))
* **devspace:** add 3rd party fallback completion loader ([ad11337](https://github.com/scop/bash-completion/commit/ad113374f6808a9edd304055bbadc383b1cb0e1e))
* **driftctl:** add 3rd party fallback completion loader ([fffafbc](https://github.com/scop/bash-completion/commit/fffafbc83720d49bc4c9531d712e583dafef8ee6))
* **dyff:** add 3rd party fallback completion loader ([3a37c60](https://github.com/scop/bash-completion/commit/3a37c60699fa34a1a81afb654cbd79f7f5d94fa5))
* **esc:** add 3rd party fallback completion loader ([48a2811](https://github.com/scop/bash-completion/commit/48a281172efcced5ee688e0e5c93530a0fed17a3))
* **flux:** add 3rd party fallback completion loader ([ddd108b](https://github.com/scop/bash-completion/commit/ddd108b34ccc25eaa889c79ef23d2c36af30aada))
* **furyctl:** add 3rd party fallback completion loader ([01ee387](https://github.com/scop/bash-completion/commit/01ee387a8d5181ebd6594a481405d960942d4f24))
* **fx:** add 3rd party fallback completion loader ([86b838c](https://github.com/scop/bash-completion/commit/86b838c7028c5c2bea8c6664f4f25aaee17b4d9c))
* **ghorg:** add 3rd party fallback completion loader ([7313401](https://github.com/scop/bash-completion/commit/731340147be6a66b1a7506df6ffeccdc2aaad680))
* **gitconfig:** add 3rd party fallback completion loader ([1fe9904](https://github.com/scop/bash-completion/commit/1fe99041ab155648f77b3f3c67fa85a7247cddb9))
* **gitleaks:** add 3rd party fallback completion loader ([a642fc8](https://github.com/scop/bash-completion/commit/a642fc87fb41e78a4cb3747afd325004cb33d184))
* **gitsign:** add 3rd party fallback completion loader ([635d734](https://github.com/scop/bash-completion/commit/635d734ba8adcafb710402c79e105de7a73e986d))
* **glen:** add 3rd party fallback completion loader ([89ad58f](https://github.com/scop/bash-completion/commit/89ad58f63def8459bfc13f3072ed94d33a705473))
* **glow:** add 3rd party fallback completion loader ([8c3166b](https://github.com/scop/bash-completion/commit/8c3166bbdcfc8fc273c0e88c09c4cf92eb3f3148))
* **gopass:** add 3rd party fallback completion loader ([81c6273](https://github.com/scop/bash-completion/commit/81c62737cb938fb21237d1c992c513a143a8b0e4))
* **grype:** add 3rd party fallback completion loader ([a1b3831](https://github.com/scop/bash-completion/commit/a1b3831c8a5cffedd9295fb5ab5c9418a3089945))
* **helmfile:** add 3rd party fallback completion loader ([3dec92b](https://github.com/scop/bash-completion/commit/3dec92b0f7f0a7f34c5540c3b2983938d2b64bca))
* **hostctl:** add 3rd party fallback completion loader ([25700b6](https://github.com/scop/bash-completion/commit/25700b615424cd01974c81a5d2dbca84ac45f5a2))
* **imgpkg:** add 3rd party fallback completion loader ([c7da7bb](https://github.com/scop/bash-completion/commit/c7da7bbc0c91d5c1fbbcd56407a908d54ef27f7e))
* **jj:** add 3rd party fallback completion loader ([d70381e](https://github.com/scop/bash-completion/commit/d70381e15894ef0bab28e5c5079dd65f831cb53b))
* **just:** add 3rd party fallback completion loader ([de59a20](https://github.com/scop/bash-completion/commit/de59a2070334af9d89aebbc1bd47d214a15b5d92))
* **jwt:** add 3rd party fallback completion loader ([03229b3](https://github.com/scop/bash-completion/commit/03229b3bde066f281e5223302ee774f4f9870caa))
* **k0sctl:** add 3rd party fallback completion loader ([9a78b89](https://github.com/scop/bash-completion/commit/9a78b899b093b0b599f389ac1fa1c909d5458de8))
* **k3d:** add 3rd party fallback completion loader ([9bb60a3](https://github.com/scop/bash-completion/commit/9bb60a3d527746808b946d9113ecd2f6de61ae5e))
* **k3sup:** add 3rd party fallback completion loader ([c58ae25](https://github.com/scop/bash-completion/commit/c58ae258383694488a726a58a5425968bfcf38ae))
* **k6:** add 3rd party fallback completion loader ([05ec5d8](https://github.com/scop/bash-completion/commit/05ec5d8facaa7ae89b06e8ea583115b0de810d5d))
* **k9s:** add 3rd party fallback completion loader ([cb5005c](https://github.com/scop/bash-completion/commit/cb5005c7e0922ede9b25b7b2d8ce28844a92b97c))
* **kafkactl:** add 3rd party fallback completion loader ([baeb267](https://github.com/scop/bash-completion/commit/baeb267176894a962f942fa044938ee62e9b7564))
* **kapp:** add 3rd party fallback completion loader ([dd6c685](https://github.com/scop/bash-completion/commit/dd6c68575690715ed7e970914c8a210274f75c97))
* **kconf:** add 3rd party fallback completion loader ([9609b50](https://github.com/scop/bash-completion/commit/9609b50be2a875260ba7e94f40b600ceb7db069d))
* **kind:** add 3rd party fallback completion loader ([bf54a1f](https://github.com/scop/bash-completion/commit/bf54a1fe40b3af77f3a538b97be8a50ccf74a2d5))
* **ko:** add 3rd party fallback completion loader ([ae1b9a2](https://github.com/scop/bash-completion/commit/ae1b9a283ec2dcc08590573a022446fea0e9a552))
* **kompose:** add 3rd party fallback completion loader ([e291111](https://github.com/scop/bash-completion/commit/e29111166e339ce38bbb7cb0ba58ae84d249dd5c))
* **kops:** add 3rd party fallback completion loader ([6dad3f5](https://github.com/scop/bash-completion/commit/6dad3f5118730f97a66558269e5d19a78b73d06f))
* **kube-capacity:** add 3rd party fallback completion loader ([ce05e4c](https://github.com/scop/bash-completion/commit/ce05e4c6f0cb0a8903904b91ed5dd2e01c9d4d64))
* **kube-linter:** add 3rd party fallback completion loader ([37d645d](https://github.com/scop/bash-completion/commit/37d645df5efe28e333292098f8ba5dac9c7acd49))
* **kubebuilder:** add 3rd party fallback completion loader ([1d6650c](https://github.com/scop/bash-completion/commit/1d6650c114072254bae56b9a65cac54d0c3cc052))
* **kubecm:** add 3rd party fallback completion loader ([dbd94cb](https://github.com/scop/bash-completion/commit/dbd94cb85762d1c1195ef28752a3c12e858e2d56))
* **kubectl-argo-rollouts:** add 3rd party fallback completion loader ([0282c1e](https://github.com/scop/bash-completion/commit/0282c1e06cf179a7b8b9ad59ce24b274f20336da))
* **kubectl-kuttl:** add 3rd party fallback completion loader ([a9b0084](https://github.com/scop/bash-completion/commit/a9b00849f5f14b6bb89e2cef066990eeef6cc419))
* **kubelogin:** add 3rd party fallback completion loader ([e130911](https://github.com/scop/bash-completion/commit/e13091122c4fa0b77f9782dcc38d7252cfbdaa38))
* **kubemqctl:** add 3rd party fallback completion loader ([4c9798d](https://github.com/scop/bash-completion/commit/4c9798d26247bd96f457c9277d48c39ecd1428b4))
* **kubesec:** add 3rd party fallback completion loader ([19c6c67](https://github.com/scop/bash-completion/commit/19c6c67a3a70c6697ff6b5022986f75110d06626))
* **kubeshark:** add 3rd party fallback completion loader ([fdd3c4a](https://github.com/scop/bash-completion/commit/fdd3c4aa7fb33d17f32861317ad3eaf3f8d8274a))
* **kubespy:** add 3rd party fallback completion loader ([7aa7ac1](https://github.com/scop/bash-completion/commit/7aa7ac18d11e3814fd517e4f1f2c1c62909fedd7))
* **kustomize:** add 3rd party fallback completion loader ([b780527](https://github.com/scop/bash-completion/commit/b7805270088483b3b8e8498400ea8645ad916c83))
* **kyverno:** add 3rd party fallback completion loader ([2ca5975](https://github.com/scop/bash-completion/commit/2ca59759892423db45a93972e91215fc5af16b1e))
* **limactl:** add 3rd party fallback completion loader ([01bafcb](https://github.com/scop/bash-completion/commit/01bafcb87c74e76d87c73efbe7a3c00f387ff291))
* **melange:** add 3rd party fallback completion loader ([6d4ad73](https://github.com/scop/bash-completion/commit/6d4ad73420506be2d6b69f5e9eb6005eb8bff865))
* **minishift:** add 3rd party fallback completion loader ([d8d3fe1](https://github.com/scop/bash-completion/commit/d8d3fe1c3f4229ef6692de4d15653b6de15d39e1))
* **mockery:** add 3rd party fallback completion loader ([c79bc1c](https://github.com/scop/bash-completion/commit/c79bc1c772f3a61ee8a453303b2ab7c7c45bb28f))
* **notation:** add 3rd party fallback completion loader ([cbaef0f](https://github.com/scop/bash-completion/commit/cbaef0fd0790da8f0db22dc75d68b284ffa3854c))
* **nova:** add 3rd party fallback completion loader ([ce7b092](https://github.com/scop/bash-completion/commit/ce7b0927d84b596c2064f245edcef3ff3fb2b287))
* **odo:** add 3rd party fallback completion loader ([48f00ce](https://github.com/scop/bash-completion/commit/48f00cec63473f6ddb3ed2d34b0c7a20e7b0f518))
* **opa:** add 3rd party fallback completion loader ([e0b4c7f](https://github.com/scop/bash-completion/commit/e0b4c7f5acf68e1365ee0231f61a971825239817))
* **oras:** add 3rd party fallback completion loader ([cb5d152](https://github.com/scop/bash-completion/commit/cb5d152e9d79e752f62a3764e00fface687b4ea7))
* **pitchfork:** add 3rd party fallback completion loader ([538344e](https://github.com/scop/bash-completion/commit/538344e8c1230d977237abb3ebaed1d9acbaaad6))
* **pluto:** add 3rd party fallback completion loader ([f519f21](https://github.com/scop/bash-completion/commit/f519f2184fda8b81a4759e52856c21e96474d338))
* **popeye:** add 3rd party fallback completion loader ([23140b2](https://github.com/scop/bash-completion/commit/23140b225fee9febe4a355c382c8625d2e484685))
* **regal:** add 3rd party fallback completion loader ([a18e23c](https://github.com/scop/bash-completion/commit/a18e23cd90c6f4e3ea1d6213d6d864eead772110))
* **regctl:** add 3rd party fallback completion loader ([b3eec76](https://github.com/scop/bash-completion/commit/b3eec7618c39b9a76013defe6a1e08f786e45422))
* **rustic:** add 3rd party fallback completion loader ([8ec77d0](https://github.com/scop/bash-completion/commit/8ec77d0f530bfdd04b610f8a9ef2a0fee76a5bfb))
* **sentry-cli:** add 3rd party fallback completion loader ([7b3ac92](https://github.com/scop/bash-completion/commit/7b3ac928b94ae12000017e8e29f0bccaa35601f8))
* **sinker:** add 3rd party fallback completion loader ([69567f7](https://github.com/scop/bash-completion/commit/69567f7ed78db8b94766632296af8d897a5960ed))
* **slsa-verifier:** add 3rd party fallback completion loader ([ac0bb34](https://github.com/scop/bash-completion/commit/ac0bb34d948e1690c639a1f9ae666d426faec6b3))
* **sopstool:** add 3rd party fallback completion loader ([4c17fa0](https://github.com/scop/bash-completion/commit/4c17fa0c50867903fad137718f88a6850629a107))
* **spacectl:** add 3rd party fallback completion loader ([542fb01](https://github.com/scop/bash-completion/commit/542fb01d9c23dbc0aaac60e6f0f01fc2389afe2a))
* **steampipe:** add 3rd party fallback completion loader ([aa6f0fd](https://github.com/scop/bash-completion/commit/aa6f0fdc3b4795d40343fb2217824302fba25f8d))
* **stern:** add 3rd party fallback completion loader ([0192486](https://github.com/scop/bash-completion/commit/01924865cc7883a5f912622d19bdba7b0e6ae6c3))
* **stripe:** add 3rd party fallback completion loader ([7cf1b3f](https://github.com/scop/bash-completion/commit/7cf1b3f4117827be36a0f462d6923d859d89c472))
* **syft:** add 3rd party fallback completion loader ([44427f4](https://github.com/scop/bash-completion/commit/44427f4c6723ec4843b3d7352c976685f0709729))
* **talhelper:** add 3rd party fallback completion loader ([c6fc803](https://github.com/scop/bash-completion/commit/c6fc803c863a66e333290d76ffb68a13cb8abf05))
* **task:** add 3rd party fallback completion loader ([6239823](https://github.com/scop/bash-completion/commit/6239823d558a990fedd9050df8ca79df5a120a6f))
* **tctl:** add 3rd party fallback completion loader ([0348cbf](https://github.com/scop/bash-completion/commit/0348cbfcadb3be234789805798f3d0348727508f))
* **tfctl:** add 3rd party fallback completion loader ([014faa5](https://github.com/scop/bash-completion/commit/014faa51c77c9334ea6db26302de11ee1859c038))
* **tilt:** add 3rd party fallback completion loader ([eeeef1c](https://github.com/scop/bash-completion/commit/eeeef1cf717eabd627742599fe9efe8af89b9c56))
* **timoni:** add 3rd party fallback completion loader ([d51ceba](https://github.com/scop/bash-completion/commit/d51ceba69a504f1fe36143c83f2780bf769c02e3))
* **vela:** add 3rd party fallback completion loader ([34c8b32](https://github.com/scop/bash-completion/commit/34c8b328629eaaa280bc3fc4fbf36612d236e488))
* **velero:** add 3rd party fallback completion loader ([b058145](https://github.com/scop/bash-completion/commit/b058145dadee09789daa38edcb2729fa597a15a9))
* **venom:** add 3rd party fallback completion loader ([c77de6c](https://github.com/scop/bash-completion/commit/c77de6c214ed36707671566a4827d6db2efd7203))
* **wasmer,wasmer-headless:** add 3rd party fallback completion loader ([5986022](https://github.com/scop/bash-completion/commit/5986022a18a6581891b1a8aee878ec5b3b1f582c))
* **xc:** add 3rd party fallback completion loader ([97d8eae](https://github.com/scop/bash-completion/commit/97d8eaeca4473e60b4c83ba2eefbfe1fd2df221a))
* **ytt:** add 3rd party fallback completion loader ([11cb8a6](https://github.com/scop/bash-completion/commit/11cb8a637e799823362be68f06bdd8e87d1b33c1))
* **zola:** add 3rd party fallback completion loader ([34b1e24](https://github.com/scop/bash-completion/commit/34b1e2424561b039b93976b937b5fc6d2e6e9e87))


### Bug Fixes

* **mfiutil:** more link and style issues ([cbd2661](https://github.com/scop/bash-completion/commit/cbd266155020194ec824028fb5c0d7ac95c89474))

## [2.15.0](https://github.com/scop/bash-completion/compare/2.14.0...2.15.0) (2024-11-24)


### Features

* **_comp_abspath:** handle ".." ([d599dcf](https://github.com/scop/bash-completion/commit/d599dcfb7f33146baa143d99067c7e71d2992ee6))
* **_comp_compgen_filedir,_comp_compgen_filedir_xspec:** don’t suggest . and .. ([#1230](https://github.com/scop/bash-completion/issues/1230)) ([0e7a0cf](https://github.com/scop/bash-completion/commit/0e7a0cf04bd9dbb190e977323ff16a24c6e172e7))
* associate unzip and friends with *.vsix ([3c3ab17](https://github.com/scop/bash-completion/commit/3c3ab1781bf6061607d327a72a77ea93cf8aaedf))
* **curl:** Complete "%", "-" for --trace[-ascii] ([b44b29c](https://github.com/scop/bash-completion/commit/b44b29ca1078043d2957d68e932e1a27c7e10283))
* **curl:** Complete files for --json, --write-out ([62c0ecb](https://github.com/scop/bash-completion/commit/62c0ecbc6b8d018f4eb313961d888ab1cb18ecb5))
* **curl:** Complete stdin "-" in file locations ([9c72cf8](https://github.com/scop/bash-completion/commit/9c72cf85c72f1cc0ad31ab1a5b12bbc78f06956e))
* **fprintd-*:** new completions ([8aa4914](https://github.com/scop/bash-completion/commit/8aa4914a9ce9896d2461b2e09ba3f7649e001d99))
* **iftop:** -G, -s, -L, and -o arg (non-)completions ([35293af](https://github.com/scop/bash-completion/commit/35293af0aaa2d37cee5549d3687004a045003578))
* **ip:** complete ip ntable show ([432f95f](https://github.com/scop/bash-completion/commit/432f95f2ac29e83a705cfeb1b2de2a1eba45b5c2))
* **java:** enable .java filename completion for java command ([959ce82](https://github.com/scop/bash-completion/commit/959ce82920bf20329a4586a325dd4579f8d5b822)), closes [#1196](https://github.com/scop/bash-completion/issues/1196)
* **kata-runtime:** add 3rd party fallback completion loader ([5ff8055](https://github.com/scop/bash-completion/commit/5ff805532d45768ff2ddb55bb8ac0ea7869c023c))
* **nerdctl:** add 3rd party fallback completion loader ([cf0591a](https://github.com/scop/bash-completion/commit/cf0591a01eec9350d294dae6b8f9c04d314dfcc9))
* **ngrok:** add 3rd party fallback completion loader ([#1252](https://github.com/scop/bash-completion/issues/1252)) ([df48031](https://github.com/scop/bash-completion/commit/df48031e9fb75420ff569accc2152068ab37b8df))
* **openssl:** Stop having subcommands hardcoded ([300b936](https://github.com/scop/bash-completion/commit/300b93682e3b27ed7fd7b1299a78dfc6ceaf6e80))
* **python,pyvenv:** versioned 3.13 executable support ([3039122](https://github.com/scop/bash-completion/commit/3039122fecf228e96174d23dc7a5dafa6bff2002))
* **set:** more featureful completion ([1cb3eae](https://github.com/scop/bash-completion/commit/1cb3eaee7166f81ff3f0b8e0b178ddfe0f5c230c))
* **slabtop:** new completion ([64be5ea](https://github.com/scop/bash-completion/commit/64be5ea8d90d7d743976dd9c439c6aab0ab14ae5))
* **sops:** add 3rd party fallback completion loader ([47af482](https://github.com/scop/bash-completion/commit/47af482d43ad91b6aba0bb736ec68e1ab47458b6))
* **tar:** use long option compression options ([0518a21](https://github.com/scop/bash-completion/commit/0518a21b3419c53c2dc13753320a4b205ea5a493))
* **todoist:** add 3rd party fallback completion loader ([75f5d26](https://github.com/scop/bash-completion/commit/75f5d2684b2c36b2ac066648d33d70a13936e942))
* **uv:** add 3rd party fallback completion loader ([ba003af](https://github.com/scop/bash-completion/commit/ba003af9684da4c416058f54be338b45a7e9eed4))
* **uvx:** add 3rd party fallback completion loader ([499ecab](https://github.com/scop/bash-completion/commit/499ecab89f90e5006f3137e3094ec1c6a1977164))
* **wine:** complete with `*.bat` and `*.cmd` ([01b34cb](https://github.com/scop/bash-completion/commit/01b34cb855ebb672646fc27795096c46fdd2e9ad))
* **xv:** associate with *.webp and *.pnm ([#1227](https://github.com/scop/bash-completion/issues/1227)) ([2dab633](https://github.com/scop/bash-completion/commit/2dab63379b90619f93873976944f4f9ad338dc0d))
* **xxd:** add -n, -o, and -R arg (non-)completions ([8304d33](https://github.com/scop/bash-completion/commit/8304d3342a07e0f1a402ff0a6752e513c2a0541a))


### Bug Fixes

* **_comp_compgen:** use `compgen -V array_name` in `bash &gt;= 5.3` ([2b5f9fa](https://github.com/scop/bash-completion/commit/2b5f9fa8d557a4376f10cf69da07c11b9f5db0be))
* **_comp_expand_glob:** set LC_COLLATE for the sorting order ([ce98f68](https://github.com/scop/bash-completion/commit/ce98f686c0aac19bc5b0938cbfa245a2f06d8b79))
* **_comp_expand_glob:** work around GLOBSORT in Bash 5.3 ([0fda821](https://github.com/scop/bash-completion/commit/0fda8211079e32e6fea29dabb46fe1a55a80cccd))
* **compatdir:** use _comp_expand_glob for the correct order ([51e680a](https://github.com/scop/bash-completion/commit/51e680a684c5e0e8eb8a485797aea68975901fef))
* **curl:** Complete all and category for --help ([7eb2eb2](https://github.com/scop/bash-completion/commit/7eb2eb2ceabfc882a1407f34c9dadb8e194a4ec2))
* **dpkg:** Add missing short option -R ([9891762](https://github.com/scop/bash-completion/commit/9891762cc88f13a05b300466a9d317f39d95f7dd))
* **rsync,ssh:** do not overescape spaces in remote filenames ([#910](https://github.com/scop/bash-completion/issues/910)) ([e8dc253](https://github.com/scop/bash-completion/commit/e8dc253ddda79f83532551e45deeeab0c3afc5f4))
* source files using absolute paths for absolute BASH_SOURCE ([e1a70c6](https://github.com/scop/bash-completion/commit/e1a70c66433788e2d9d08e89d16329bb2fb340b9))
* **tar:** Complete added files with long opts ([c94bebb](https://github.com/scop/bash-completion/commit/c94bebbe919ce696d57466d7c9c9328d82be9fd8))
* **update-alternatives:** fix the "--help" parsing ([07605cb](https://github.com/scop/bash-completion/commit/07605cb3e0a3aca8963401c8f7a8e7ee42dbc399))

## [2.14.0](https://github.com/scop/bash-completion/compare/2.13.0...2.14.0) (2024-05-09)


### Features

* **bao:** add 3rd party fallback completion loader ([af7fbb9](https://github.com/scop/bash-completion/commit/af7fbb97b470abce4025144fe4676c38cf4514d4))
* **cryptsetup:** complete --header with filenames ([4c9fc87](https://github.com/scop/bash-completion/commit/4c9fc87975bd2dc04a6851017d9a71116ec1e44f))
* **dlv:** add 3rd party fallback completion loader ([eefe71d](https://github.com/scop/bash-completion/commit/eefe71d8681d0735abc541cd0b7d2c7f9c535212))
* **env:** complete commands and variable assignments ([5c75fa3](https://github.com/scop/bash-completion/commit/5c75fa332b5f86863f14c2ef45cdc8732c9a2c35))
* **env:** treat `-*` as the command name after `-` and `--` ([0cd2883](https://github.com/scop/bash-completion/commit/0cd2883ca17e65a86fe81c650cea869f9fc70839))
* **env:** treat `-*` as the command name after assignments ([71b7fb2](https://github.com/scop/bash-completion/commit/71b7fb2bc6fd9eef901e959aaf089419cc6f74a4))
* **gup:** add 3rd party fallback completion loader (&gt;= 0.25.0) ([1641f1a](https://github.com/scop/bash-completion/commit/1641f1a3043e3a378b4dd08a0f07240dd1d56548))
* **incus:** add 3rd party fallback completion loader ([7108d9c](https://github.com/scop/bash-completion/commit/7108d9ca84a2617ae36e88181d39aeeabe3225dd))
* **ip:** Complete 'route get' options ([12fbdb1](https://github.com/scop/bash-completion/commit/12fbdb163b9ffabb5f673c910a98edc8eaa6ac65))
* **ip:** Complete addr add/change/replace options ([8262664](https://github.com/scop/bash-completion/commit/82626648c75406509187d3775b25430da6347fea))
* **ip:** Complete ip route list options ([43beafc](https://github.com/scop/bash-completion/commit/43beafcfde8f6c8630ad9197ffbae76bfae010a5))
* **ip:** Complete link afstats command ([6721346](https://github.com/scop/bash-completion/commit/672134688efceb71dc58c1c00463f8c38a4b0477))
* **ip:** Complete neigh add, del, change, replace ([ba80203](https://github.com/scop/bash-completion/commit/ba8020349c965e92e25ae66fc1f8eceda50bc450))
* **ip:** Complete route save/showdump ([606f650](https://github.com/scop/bash-completion/commit/606f6509ddff9c30bb8b4971fb16a90fa39e4dfe))
* **iperf:** --tos/-S argument completion ([d2f59bb](https://github.com/scop/bash-completion/commit/d2f59bb6fa55e2f4becd21bdd21ff75fe627ad38))
* **ssh-copy-id:** (non-)complete args to `-t` and `-F` ([53fdce4](https://github.com/scop/bash-completion/commit/53fdce4bd972a71dba3e8e5be05eadb443ec93f3))
* **ssh-keygen:** complete -r/-Y specific -O args ([d6977fc](https://github.com/scop/bash-completion/commit/d6977fcd43302c8b3820fe5f90ce56c31db6e5ad))
* **ssh-keyscan:** complete -O argument ([39ea543](https://github.com/scop/bash-completion/commit/39ea5435abfc4b13445bb0eec086e246e3cadb38))
* **tofu:** add 3rd party fallback completion loader ([4edd9a2](https://github.com/scop/bash-completion/commit/4edd9a2bde503d3897eb1906275d6509a3fafc97))
* **zarf:** add 3rd party fallback completion loader ([032f0f0](https://github.com/scop/bash-completion/commit/032f0f0f35b53856c3be642bb3101a4d01c81aee))


### Bug Fixes

* **_comp_{compgen,xfunc}:** use `declare -F --` for arbitrary funcs ([8998491](https://github.com/scop/bash-completion/commit/899849127707f6d616d5312c0efab0092b8caea8))
* **_comp_{load,realcommand}:** handle option-like command name ([883946d](https://github.com/scop/bash-completion/commit/883946d84d13d7947eb0c21a536bd947b3dbc31a))
* **available_interfaces:** strip only trailing colon from entries ([a452aee](https://github.com/scop/bash-completion/commit/a452aee8fb58720ecfadbdaad95277ac9d9072ac))
* **bash_completion,conftest:** use `complete -p --` for arbitrary cmds ([dafd338](https://github.com/scop/bash-completion/commit/dafd3382e019cf90156cc6abc4266a82ab4ebe5f))
* **fio:** engines completion ([27a0ef8](https://github.com/scop/bash-completion/commit/27a0ef80a2dbd84d8a0d2f90945cc66577149726))
* **ip:** Complete link change as well as set ([956bd5d](https://github.com/scop/bash-completion/commit/956bd5dfd26f40ba23d1708bb15174d18b142087))
* **ip:** Don't stop at proxy and nomaster in neigh ([d58ea76](https://github.com/scop/bash-completion/commit/d58ea763364f234ed90e8152f36b99f02e82ec84))
* **scp remote_files:** do not filter generated paths with "$cur" ([bc812df](https://github.com/scop/bash-completion/commit/bc812dff7246e5186c7ae752081605787942f8f9))
* **scp remote_files:** localize variable `cur` ([34f7d0c](https://github.com/scop/bash-completion/commit/34f7d0c321977016e3191cb7e9ab7ebd1a4083a0))
* **ssh-keygen:** handling of bundled short options ([18e436d](https://github.com/scop/bash-completion/commit/18e436d0a0a25809bc1c8e6457aaa78a1667fa51))
* **ssh-keygen:** make work with custom IFS ([b71340b](https://github.com/scop/bash-completion/commit/b71340bb411fb5d8ddb0c96836e484ace7eeb8f3))
* **ssh-keygen:** suggest -O arg completions depending on mode ([b49ab1d](https://github.com/scop/bash-completion/commit/b49ab1d9f5515d12920ddb1548790fa0bf78d0b1))
* use -- to pass arbitrary cmdnames to `_comp_load` ([3c1d9bc](https://github.com/scop/bash-completion/commit/3c1d9bc3f5432dee7de0935e8a4b6bf66c2fad77))
* use `pathcmd=$(type -P -- "$1")` for arbitrary cmds ([8795ca9](https://github.com/scop/bash-completion/commit/8795ca99f3eb147e26d49f06e535f0ce64b4d162))

## [2.13.0](https://github.com/scop/bash-completion/compare/2.12.0...2.13.0) (2024-04-03)


### Features

* **curl:** Complete protocols for --proto-default ([7051379](https://github.com/scop/bash-completion/commit/7051379e448147407c3fe43c89872dafb76ebb27))
* **ip:** Add completion for netconf subcommand ([03a10ff](https://github.com/scop/bash-completion/commit/03a10ff63226782e61dae4407138c3240ff0c7c2))
* **ip:** Complete commands for netns exec ([1f03796](https://github.com/scop/bash-completion/commit/1f03796cd930ddad6207d46814da820674f16edc))
* **ip:** Complete help for unknown subcommands ([21f7e32](https://github.com/scop/bash-completion/commit/21f7e32f9009c2064d1659668425b6b6ccb537bd))
* **ip:** Complete ip link property ([efa663c](https://github.com/scop/bash-completion/commit/efa663cd0dd63d54fd2d2987ee66fb954ccf4a86))
* **ip:** Complete link types for address show ([ca5ea03](https://github.com/scop/bash-completion/commit/ca5ea037e2ec1b0b5ec4d31295df88125d51b43e))
* **ip:** Complete neigh show and flush ([c7c3c03](https://github.com/scop/bash-completion/commit/c7c3c039bf5a462ea577e8fcc92ebd94d6afad49))
* **ip:** Complete stats subcommand ([cd73e8c](https://github.com/scop/bash-completion/commit/cd73e8c1689e3e014c4a75d5101ee3d932013120))
* **ip:** Create function to get link types ([8e60245](https://github.com/scop/bash-completion/commit/8e60245c7531e1615dc96b032035bb4f59972f4a))
* **rg:** add fallback 3rd party completion loader ([7e4cc2f](https://github.com/scop/bash-completion/commit/7e4cc2fb199f1c88bd9a358a157fe06327fc2b28))
* **xmllint,xmlwf:** also suggest *.rss files ([a89cde2](https://github.com/scop/bash-completion/commit/a89cde2216b1634521b4a264b2dbc5cda7522061))


### Bug Fixes

* **available_interfaces:** fix regression of unwanted trailing colons ([c2f83e0](https://github.com/scop/bash-completion/commit/c2f83e0436208ef2bfa9c762bc28ff6374ba0c73))
* **ip:** Complete addrlabel add/del properties ([ea07616](https://github.com/scop/bash-completion/commit/ea076166e9a5cce9d22a27e63f95bbf00be9b894))
* **ip:** Complete ip delete with type correctly ([f3a9be3](https://github.com/scop/bash-completion/commit/f3a9be3e2f6eaf0d94bb66220fd02cf0e3c76481))
* **ip:** Complete more variations of subcommands ([c8920c5](https://github.com/scop/bash-completion/commit/c8920c57f83199a14230485cc44b426f028acafb))
* **ip:** Complete netns attach subcommand ([bfb1de6](https://github.com/scop/bash-completion/commit/bfb1de64a53d1eba749c9921cea3809460cd2319))
* **ip:** Complete only relevant addrlabel subcmds ([05147f1](https://github.com/scop/bash-completion/commit/05147f19d3d04040fa8dacbd9e1705bfc1432073))
* **ip:** Keep completing after -netns name ([1a5df4f](https://github.com/scop/bash-completion/commit/1a5df4fe22eb374424b18e7db27b8446e919f4b2))
* **ip:** Quote all instantiation of ip as "$1" ([ef25163](https://github.com/scop/bash-completion/commit/ef25163e6bd9095e528b57d44cb31d32f0321bb9))
* **ip:** Quote network namespace names ([216734b](https://github.com/scop/bash-completion/commit/216734bed7ba02655128bf1dbc2f184420ad69ef))
* **Makefile:** include api-and-naming.md in dist ([fdd8048](https://github.com/scop/bash-completion/commit/fdd80487ba6944e639baf32ba991f8665840728d))

## [2.12.0](https://github.com/scop/bash-completion/compare/v2.11.0...2.12.0) (2024-02-21)


### Features

* **_comp_backup_glob:** add `ucf` generated backup files ([351be1c](https://github.com/scop/bash-completion/commit/351be1c81ecd97135d3df64475a93d57526d5630))
* **_comp_backup_glob:** require dash in dpkg backup files ([59a57f9](https://github.com/scop/bash-completion/commit/59a57f9f8c6a47dc9d21c61d13ffb90a5f3b82f0))
* **_comp_compgen_{filedir,set}:** define exit status ([7920c9d](https://github.com/scop/bash-completion/commit/7920c9dbe46bd5ad0a92e78b744f00ffe8b14e76))
* **_comp_compgen_commands:** align return value with other compgens ([9d3362e](https://github.com/scop/bash-completion/commit/9d3362e4a092efaecda2fa2a5f01feca75e7b24e))
* **_comp_compgen_commands:** auto set `-o filenames` when appropriate ([4d4839e](https://github.com/scop/bash-completion/commit/4d4839e50210de4d3a8780caa4b9d8ab44b8f86d))
* **_comp_compgen_commands:** include dirs ([b9c7b5d](https://github.com/scop/bash-completion/commit/b9c7b5dc414c09d27a6b157d37bede70e662b91c))
* **_comp_compgen_known_hosts:** return 2 on usage error ([43bb8f0](https://github.com/scop/bash-completion/commit/43bb8f03798811d95a3068a8a53d2eac9ad877b5))
* **_comp_compgen:** support `-i cmd` and `-x cmd` ([39cc200](https://github.com/scop/bash-completion/commit/39cc200f9e6d9b03d1e76a924371f85304e786d5))
* **_comp_compgen:** support `-U var` to unlocal var ([b603535](https://github.com/scop/bash-completion/commit/b60353508eb470515f1063a1e1a75bdf1fda730f))
* **_comp_compgen:** support option -C ([6b3dfa5](https://github.com/scop/bash-completion/commit/6b3dfa529950f3bc51b8ead9323a82f65c5cfeb9))
* **_comp_expand_glob:** fail when no paths are generated ([6b0a466](https://github.com/scop/bash-completion/commit/6b0a46644d587182c20f880755a821a7680753fe))
* **_comp_get_fist_arg:** support "-o GLOB" to skip optargs ([0f14cc0](https://github.com/scop/bash-completion/commit/0f14cc030ebdb54f3d120d8cadc08651dad00624))
* **_ip_addresses:** auto ltrim colon completions when appropriate ([ccdf953](https://github.com/scop/bash-completion/commit/ccdf953b7956d6fc65334e9049e157a3480a5cfa))
* add _comp_compgen_split ([542bf73](https://github.com/scop/bash-completion/commit/542bf73f571312659293dac664bf257ef66fbfef))
* add `_comp_locate_first_arg` ([0384bd5](https://github.com/scop/bash-completion/commit/0384bd51df3f68a3e065aeead4e921462fff6655))
* **airflow:** add fallback 3rd party completion loader ([bf5550b](https://github.com/scop/bash-completion/commit/bf5550b5fc304ed956b6061221f173516408d82d))
* **ansible*:** add fallback 3rd party completion loader ([5f8384e](https://github.com/scop/bash-completion/commit/5f8384ef71acd1dc6e214177e5bbe3b0b83ee976))
* **apt-get:** prefer `apt-cache` in same dir as command ([a731bfd](https://github.com/scop/bash-completion/commit/a731bfd0acf77c7e5ec52315e3b470cffa177b4a))
* **b2sum:** new completion ([cd985df](https://github.com/scop/bash-completion/commit/cd985df6d8e613fd5421066248db1c50dc1686bd))
* **bash_completion:** add function _comp_compgen_ltrim_colon ([ce5889b](https://github.com/scop/bash-completion/commit/ce5889ba3b1364d9afe7096c6fa14d7c65d08cc6))
* **black,blackd:** add fallback 3rd party completion loader ([47a1f05](https://github.com/scop/bash-completion/commit/47a1f05e75399d25dcf0281ae6f8ba363a82e6bc))
* **carton:** support exec command completions ([0eb3a21](https://github.com/scop/bash-completion/commit/0eb3a21afdcd4c52966e0894f43f501dc3b727e3))
* **chezmoi:** add 3rd-party completion loader (cobra) ([31baa69](https://github.com/scop/bash-completion/commit/31baa69954351a2d30047f707692d361646aea53))
* **conda:** add 3rd-party completion loader (argcomplete) ([c0f5ba2](https://github.com/scop/bash-completion/commit/c0f5ba2e19abea70dcde4bf0aafe36de801b28c0))
* **crc:** add 3rd-party completion loader (cobra) ([f10866e](https://github.com/scop/bash-completion/commit/f10866e1aaf810ae5cb29ccd5c1e8ac722d0f2ae))
* **cz:** add fallback 3rd party completion loader ([b7ba70e](https://github.com/scop/bash-completion/commit/b7ba70e52e1e0f287c29b5d7362439dccd132e8b))
* **dot:** support filename extension .gv ([be0010e](https://github.com/scop/bash-completion/commit/be0010e8517de89bf06bda02bdccacc51968136e))
* **dprint:** add fallback 3rd party completion loader ([e201e0e](https://github.com/scop/bash-completion/commit/e201e0ee336e79c26bce2afc3f2b4f98d4fea12d))
* **eog:** add missing extension .heif ([9e4a48f](https://github.com/scop/bash-completion/commit/9e4a48fdaa372a41e6ad5c7d021164394054aa97))
* **eog:** associate with `*.avif` and `*.webp` ([#1005](https://github.com/scop/bash-completion/issues/1005)) ([f1c04b8](https://github.com/scop/bash-completion/commit/f1c04b816e1612acdac62605528d32b5d896916f))
* **eog:** associate with `*.heic` and `*.jxl` ([20c9cea](https://github.com/scop/bash-completion/commit/20c9cea129e1f1e92045098ce7555a3f2add30e8))
* **eog:** associate with `*.pbm` ([#1006](https://github.com/scop/bash-completion/issues/1006)) ([5472cc1](https://github.com/scop/bash-completion/commit/5472cc1fef9c60abf240bd8f4acf38dbdaabf1a1))
* **feh:** associate with y4m and heic/heif/avif ([e252c73](https://github.com/scop/bash-completion/commit/e252c73ff2b92320dfabc9daf0d3f4ac15171e96))
* **feh:** deassociate with avci/avcs ([af46f34](https://github.com/scop/bash-completion/commit/af46f3411acfbd28624f57ff6a7511148d1857f9))
* **flask:** add fallback 3rd party completion loader ([3e0d00d](https://github.com/scop/bash-completion/commit/3e0d00d4ea55a200ea67196a6de841e8633fcad3))
* **hash:** new completion ([#1013](https://github.com/scop/bash-completion/issues/1013)) ([4d0bffb](https://github.com/scop/bash-completion/commit/4d0bffb791c34c96114aeb2e4f6726b80aa8698e))
* **httpx:** add fallback 3rd party completion loader ([3f4861c](https://github.com/scop/bash-completion/commit/3f4861cc41ea05982b2c4a98cc1f56f21895419b))
* **ip:** Add completion for monitor subcommand ([fa696e6](https://github.com/scop/bash-completion/commit/fa696e60f3fe525a14bfbb409effb4e51b2e4450))
* **jungle:** add fallback 3rd-party completion loader ([c69845a](https://github.com/scop/bash-completion/commit/c69845abf7efe9c226cef46ebf8b9129058db43d))
* **keyring:** add fallback 3rd party completion loader ([8082602](https://github.com/scop/bash-completion/commit/80826028b7760409f20624a6fb4f4c5a4eaec8e3))
* **kontena:** add fallback 3rd-party completion loader ([5eef0ce](https://github.com/scop/bash-completion/commit/5eef0ce3e5bd29f32d5830abfaf60da1295c4562))
* **lefthook:** add fallback 3rd party completion loader ([dc9650e](https://github.com/scop/bash-completion/commit/dc9650e917d2018fa9eeb251e2c3db2b0f9d230a))
* **mailman:** prefer `list_lists` in same dir as command ([a46ccf1](https://github.com/scop/bash-completion/commit/a46ccf132d53f4f5adf5ad3b0e51ec1dde931298))
* **mysql:** prefer `mysqlshow` from same dir ([643886c](https://github.com/scop/bash-completion/commit/643886cf3c34eef47a86fbc6c2a64712c455dad3))
* no empty command completion if `no_empty_cmd_completion` is on ([faab292](https://github.com/scop/bash-completion/commit/faab29286074ec3c952dfe50f0cb22af65dadbeb))
* **npm:** add fallback 3rd-party completion loader ([f1c085c](https://github.com/scop/bash-completion/commit/f1c085cc56cf15d2bd0c97148535e36e81dfc5d4))
* **nvm:** add fallback 3rd-party completion loader ([dea7e1d](https://github.com/scop/bash-completion/commit/dea7e1d431f1700c2515ece47b07ba472e1f999c))
* **oc:** add 3rd-party completion loader (cobra) ([26b5f09](https://github.com/scop/bash-completion/commit/26b5f09490159fd8fc6b7c592cb70c40e8b214dc))
* **pip{,3}:** add fallback 3rd-party completion loader ([e3cbfba](https://github.com/scop/bash-completion/commit/e3cbfba64e6f52ee49c59052ef40eb4a11a48849))
* **pipenv:** add fallback 3rd party completion loader ([#1020](https://github.com/scop/bash-completion/issues/1020)) ([6ecf5bd](https://github.com/scop/bash-completion/commit/6ecf5bdb3c534db894da60e336f58235547de80d))
* **pytest:** complete new --import-mode value ([#1021](https://github.com/scop/bash-completion/issues/1021)) ([2d636a3](https://github.com/scop/bash-completion/commit/2d636a3cb4ee66b4bc3355fadff1a1e4246199d4))
* **rtx:** add fallback 3rd party completion loader ([0628e22](https://github.com/scop/bash-completion/commit/0628e223689b7bad0b9dc43e56d935a66b73b8a2))
* **scp,sftp:** prefer `ssh` from same dir to resolve options etc ([d55f5e6](https://github.com/scop/bash-completion/commit/d55f5e6a9e9fb53b02bb86a8fc6278e671099bb0))
* **ssh-copy-id,ssh-keygen:** prefer `ssh` from same dir ([5c1d270](https://github.com/scop/bash-completion/commit/5c1d2701456e14fbe946ef7ccfecc7e10829b806))
* **ssh-inscribe:** add fallback 3rd party completion loader ([7f2c197](https://github.com/scop/bash-completion/commit/7f2c197e4e59b313d0f47cde902f57145106660e))
* **ssh:** complete RequiredRSASize ([#1064](https://github.com/scop/bash-completion/issues/1064)) ([de15205](https://github.com/scop/bash-completion/commit/de15205d6c7997e8596dce4f9d2a2bfc476be716))
* **tkn-pac:** add 3rd-party completion loader (cobra) ([d0f2604](https://github.com/scop/bash-completion/commit/d0f26045937276427ebb9844e84f40cff1b928f4))
* **tkn:** add 3rd-party completion loader (cobra) ([161fc5d](https://github.com/scop/bash-completion/commit/161fc5d42cd7dd814a9197f788045ca052062c22))
* **xrandr:** comma separated `--setmonitor` third argument ([8a76f3d](https://github.com/scop/bash-completion/commit/8a76f3d75db290302f94ea732e01a9afd25c2c24))


### Bug Fixes

* **__load_completion:** quoted compspec for variants ([#1008](https://github.com/scop/bash-completion/issues/1008)) ([0a2443e](https://github.com/scop/bash-completion/commit/0a2443e3eec8557273dd905df9a28dc177861023))
* **_cd_devices:** `/dev/cdc-*` CDC device false positives ([5250728](https://github.com/scop/bash-completion/commit/52507286a6ea8c576414d7cca0f6a2d30dd78c84))
* **_comp__init_set_up_service_completions:** work around failglob ([2529d40](https://github.com/scop/bash-completion/commit/2529d40f22039b344fd0bb5280a8469b8783f2a9))
* **_comp_{first_arg,count_args}:** count - as argument ([e23a79e](https://github.com/scop/bash-completion/commit/e23a79e0fd715b0ae564082d9f1e4e7a907a195d))
* **_comp_{first_arg,count_args}:** count any arguments after -- ([9bfd760](https://github.com/scop/bash-completion/commit/9bfd760c1192da7b3d0d8f0f9c8bea8ed16f4e47))
* **_comp_command_offset:** Support complete -C ([80450ca](https://github.com/scop/bash-completion/commit/80450ca06973535671dc76ffe5abd6e377418f76))
* **_comp_compgen_fstypes:** avoid unexpected expansions ([a856d81](https://github.com/scop/bash-completion/commit/a856d811b73500550d317eff5fec487e41581add))
* **_comp_compgen_help:** allow dots to connect names in longopt ([79dadfc](https://github.com/scop/bash-completion/commit/79dadfc7fa37be26c7b263b809687db0907ae71f))
* **_comp_compgen_known_hosts:** work around bash-4.2 nounset ([d2860cb](https://github.com/scop/bash-completion/commit/d2860cb481be8f3cf97172cb1bf063da8f238e53))
* **_comp_compgen_split:** work around nounset ([f488f96](https://github.com/scop/bash-completion/commit/f488f9615a58140dc610bf2d406f3fcf6b8d82da))
* **_comp_compgen_term:** replace completions by default ([d3696a3](https://github.com/scop/bash-completion/commit/d3696a3f793b2e0b9a0940287b3ab3dd23ef247d))
* **_comp_compgen_usergroup:** avoid directly overwriting COMPREPLY ([d380498](https://github.com/scop/bash-completion/commit/d3804982650ec4fc7fbb7f7cab5522709b43f52d))
* **_comp_compgen:** do not inherit -a for explicitly specified var ([3c20766](https://github.com/scop/bash-completion/commit/3c20766a6248aec000eb55731dd6b159ecf45dc2))
* **_comp_compgen:** explicitly exclude `cur` from the target variable ([5fe98f3](https://github.com/scop/bash-completion/commit/5fe98f3c30fa69d0921703c7c90522a28bb00737))
* **_comp_count_args:** check optarg correctly ([874c503](https://github.com/scop/bash-completion/commit/874c5031986e23a3ca7843d216d7dde94b0f1d4b))
* **_comp_count_args:** exclude &lt;&gt;& from wordbreaks as _comp_initialize ([521d2bb](https://github.com/scop/bash-completion/commit/521d2bb5330de4a721ae20d37d330190aa4beef0))
* **_comp_count_args:** ignore empty $3 ([76eea74](https://github.com/scop/bash-completion/commit/76eea74581d4f404e817f686ea86236c8c75a9fc))
* **_comp_count_args:** perform optarg check also on $3 ([21d3122](https://github.com/scop/bash-completion/commit/21d3122fa933169849e9743212248c80b7843abb))
* **_comp_count_args:** skip reassembling cword and words ([3127703](https://github.com/scop/bash-completion/commit/3127703f12f3e82ddf3f950a801ab430024aca22))
* **_comp_delimited:** treat delimiter as a fixed string ([571a0f7](https://github.com/scop/bash-completion/commit/571a0f70f39fed70b2db51e1624b2a0ee29f00c4))
* **_comp_deprecate_func:** argument order in usage error message ([597f62f](https://github.com/scop/bash-completion/commit/597f62fe38627452f5330d40bc9004f2b3d4dda5))
* **_comp_get_words:** empty prev if unavailable (localvar_inherit) ([d8b8eef](https://github.com/scop/bash-completion/commit/d8b8eef63602cb0e34837320938b0819363d7d87))
* **_comp_initialize:** fix completions of redirections without space ([da16bf6](https://github.com/scop/bash-completion/commit/da16bf61c2604ea4efa9b2d125aedd2dd4acd4e5))
* **_comp_initialize:** protect against "localvar_inherit" ([0cc8d83](https://github.com/scop/bash-completion/commit/0cc8d833011c0763eb00939ad9a94d525d87bcea))
* **_comp_split,_comp_compgen:** strip periods from error message ([b3b6a7c](https://github.com/scop/bash-completion/commit/b3b6a7cf2865b3591ab571dc2563d1980a822ac5))
* **_comp_split:** update error message for the correct options ([3c4a89c](https://github.com/scop/bash-completion/commit/3c4a89ce48ca29c0c6461d12519bbf78bcc1ca02))
* **_comp_sysvdirs:** work around nounset ([da26178](https://github.com/scop/bash-completion/commit/da26178b9c7eaf11711b451ae292a01bd423a80c))
* **_filedir_xspec:** clean up unused variable `tmp` ([67f1189](https://github.com/scop/bash-completion/commit/67f11892351b710b9b3609e138a0a2c9248c4422))
* **_get_cword_at_cursor,cvs:** quote array length ([201239c](https://github.com/scop/bash-completion/commit/201239cb597a3d7d0165a5e37a093f063fe700d1))
* **_get_first_arg:** remove invalid doccomment ([eb40f56](https://github.com/scop/bash-completion/commit/eb40f566cc9e3c5a845a12caf0f4344dab2a0b2c))
* **_known_hosts:** use array for `options` (work around SC2178,SC2179) ([743d0a9](https://github.com/scop/bash-completion/commit/743d0a9be714c516dce23415e3a5c5f81d5d7bea))
* **_mock,rpm:** avoid icase flag `s/reg/rep/i` of GNU sed ([33c18ce](https://github.com/scop/bash-completion/commit/33c18ce90df153dabb89813d472dacf5a6259b92))
* **_service:** quote word ([c2d7fb7](https://github.com/scop/bash-completion/commit/c2d7fb73844531ccb10576ddd905fb058e00c88e))
* **_slackpkg:** do not scan after cword ([ecd1384](https://github.com/scop/bash-completion/commit/ecd13849f76966d9e15607bdd604972279bb9cd8))
* **7z:** remove redundant `printf '%s\n' "..."` ([20c2e32](https://github.com/scop/bash-completion/commit/20c2e322f8eb93240de73b62647e7ca2f7b8fa4f))
* **ant:** work around custom IFS ([584e567](https://github.com/scop/bash-completion/commit/584e56748da32e8f17a713b14c23ea2b7c92a4dc))
* **apt-cache:** check all the words ([84cfeda](https://github.com/scop/bash-completion/commit/84cfedabe958ad6f7cfc52525d79e9b801de4233))
* **aptitude:** do not use non-POSIX BRE \w ([cd8c870](https://github.com/scop/bash-completion/commit/cd8c8701b60f17f9aecc27482f64f121821085ee))
* **aspell:** use `_comp_expand_glob` against unexpected splitting ([58d5176](https://github.com/scop/bash-completion/commit/58d5176cba8bb25fe5348aadef100c083afa992f))
* **bash_completion:** remove some unnecessary `-a` to `_comp_compgen` ([636bb7c](https://github.com/scop/bash-completion/commit/636bb7c4062c3e410904769f1fde4f5b14a12afc))
* **bts:** update functions to generators and fix variable conflicts ([7d2867c](https://github.com/scop/bash-completion/commit/7d2867c01527c3037579fe24da62cccfdfd1883e))
* **carton:** remove a branch never happening ([cd063f9](https://github.com/scop/bash-completion/commit/cd063f9a40e4dc85e0a9de7ceeac019cdac57449))
* **chgrp:** fix typo `_comp_compgen{d  =&gt; _}filedir` ([#1063](https://github.com/scop/bash-completion/issues/1063)) ([4c98ce5](https://github.com/scop/bash-completion/commit/4c98ce58b7686b9f23f14bf05e809d9cd1a5669c))
* **compat:** find compat dir for prefix-install ([b6d06bc](https://github.com/scop/bash-completion/commit/b6d06bc47119fcfc47f10eed328ff54e95ddc14d))
* **completions/*:** quote array assignments properly ([848aa41](https://github.com/scop/bash-completion/commit/848aa413273516529ccc163966f138ad3a6c53a0))
* **completions/*:** scan until cword instead of ${#words[@]}-1 ([17e0cc3](https://github.com/scop/bash-completion/commit/17e0cc36fdb4c6543c7729419d6f736ee779db1f))
* **completions/*:** work around localvar_inherit ([e3a871d](https://github.com/scop/bash-completion/commit/e3a871d2517f688d7fe7132f33e96669a71b6aa1))
* **curl,koji:** use _comp_compgen_help ([f37f221](https://github.com/scop/bash-completion/commit/f37f221f3f191fec0d301815d276d830f6119186))
* **curl,qemu:** avoid using POSIX character classes for mawk ([d60c530](https://github.com/scop/bash-completion/commit/d60c5306f918b370c74f5ba6d3100720be139ad6))
* **curl:** colon handling for IPv6 address completions ([8508d1d](https://github.com/scop/bash-completion/commit/8508d1d8f6f3d50ac6fb45ec2eb0f05dcef8d817))
* **cvs,mutt,pkg_delete:** quote prefix to remove for literal matching ([533903e](https://github.com/scop/bash-completion/commit/533903edd5d0752cb4b910c3bcc6d86a0e55d779))
* **cvs:** avoid variable conflicts ([0d145c9](https://github.com/scop/bash-completion/commit/0d145c9f2852955848ca1855d9fae216ebbd78c3))
* **dict:** nounset error when host/port not set ([57191e2](https://github.com/scop/bash-completion/commit/57191e2731eb307434c2f11813d7dea303458dce))
* **dict:** protect splitting from custom IFS ([b31fe1e](https://github.com/scop/bash-completion/commit/b31fe1e3e1b3480d9dc6a469cae4e83ed204b382))
* **dict:** work around bash-4.2 array nounset ([d76dd9e](https://github.com/scop/bash-completion/commit/d76dd9ea448b71e111763e8048df49dde797d85e))
* **dict:** work around bash-4.3 `${v+"$@"}` with custom IFS ([cc21298](https://github.com/scop/bash-completion/commit/cc212983a2b2523e5b62e9735c6dff5f011bb80a))
* **dmypy:** protect against "localvar_inherit" ([768ce65](https://github.com/scop/bash-completion/commit/768ce656099879ba1b81de04b75db11679791c47))
* **dpkg-source:** filter completions by `cur` ([dbcef6e](https://github.com/scop/bash-completion/commit/dbcef6e37313b00f00ca65397be6a061b1af3bb8))
* **filedir:** work around bash-4.2 `compgen -f -- "''"` ([c8bb123](https://github.com/scop/bash-completion/commit/c8bb123abc0c148bfa078cf8bfe01bafc5cf50ff))
* fix problems with `set -o nounset` ([9c3e196](https://github.com/scop/bash-completion/commit/9c3e1961928248da8a6dd8e327c3860f71eb7dff))
* **gdb:** fix regression that fails to generate command names ([ca8e240](https://github.com/scop/bash-completion/commit/ca8e2405ae49c927f9c9d312aa0efdde3d231bf2))
* **gdb:** remove duplicate candidates ([532fc05](https://github.com/scop/bash-completion/commit/532fc05a73b415369ce1987463d82d791ee72dc0))
* **gdb:** use POSIX-compatible find expression ([73938cd](https://github.com/scop/bash-completion/commit/73938cd857c3a7fa884e24be7216d1c5b12d5a1c))
* **gdb:** work around bash 4.4 `${v+"$@"}` ([41236da](https://github.com/scop/bash-completion/commit/41236da411794133d08792df28207e2a77271c57))
* **get_words:** work around bash-4.3 connected `${v+"$@"}` ([81affbd](https://github.com/scop/bash-completion/commit/81affbd999a24f796f79fb5c1eec80e88b3a98d8))
* **getent:** check noargopts ([6143f25](https://github.com/scop/bash-completion/commit/6143f257f1593c319504c2627fe01b4c94e90a48))
* **getent:** work around localvar_inherit ([a12f438](https://github.com/scop/bash-completion/commit/a12f438ebf93d7427d1b5df7056ace8ec67e890c))
* **gzip,isort:** fix typo `_comp_com{gp =&gt; pg}en` ([462feb3](https://github.com/scop/bash-completion/commit/462feb34d3b49f70a2451cbf724d14e59bfd8e0a))
* **iperf,puppet:** use \{m,n\} instead of \? and \+ for POSIX sed ([3f0322b](https://github.com/scop/bash-completion/commit/3f0322b73d98c177ede1973ad1b84e66ee593ca9))
* **iperf:** work around failglob with backslash in bash-5.0 ([979f04f](https://github.com/scop/bash-completion/commit/979f04f67c76395e21f77b7945639f2eeaa3b37f))
* **ip:** Remove non-existent route completion ([3b9ce4d](https://github.com/scop/bash-completion/commit/3b9ce4dc0ba0a14d1d390f0e83c7b7b53a26b5a5))
* **isql:** work around nounset ([cb7007a](https://github.com/scop/bash-completion/commit/cb7007af225369e5872280411bb6871b55f274a5))
* **java,kldload:** use `_comp_compgen` for filename-based mod names ([81adc6c](https://github.com/scop/bash-completion/commit/81adc6ce741f69e936dfb0e1e891c372341814f2))
* **java:** localize `cur` ([ec0068d](https://github.com/scop/bash-completion/commit/ec0068d13f3c98551771e4ada4335f131c2f0cb4))
* **kcov,povray:** quote array expansions to avoid extra processing ([215edab](https://github.com/scop/bash-completion/commit/215edabcceece2b53becc78cf88a24fb120db299))
* **kldload,mplayer,mutt:** work around nounset ([5330fbe](https://github.com/scop/bash-completion/commit/5330fbeecdd490aa79f1cf7ce320e1b27a776d69))
* **kldunload:** remove unused -X option ([35017d2](https://github.com/scop/bash-completion/commit/35017d25acee6830d4c30485a1b2cba2f12e00ed))
* **known_hosts_real:** match `*.pub` instead of `*pub` ([470a644](https://github.com/scop/bash-completion/commit/470a644be3144d9fbd7d58d99d1c3fd73c773b9f))
* **known_hosts_real:** use `_comp_expand_glob` to avoid failglob ([672215e](https://github.com/scop/bash-completion/commit/672215e010d5f78085caf61cecda620b4c67f4ae))
* **known_hosts_real:** work around nounset ([07c619a](https://github.com/scop/bash-completion/commit/07c619a56950de1960312ae24411c27688e5aa1a))
* **lilo,rpcdebug,slapt-{get,src}:** work around nounset ([cfc3eda](https://github.com/scop/bash-completion/commit/cfc3eda9575a8b0b48a531652875206db36957d5))
* **lvm:** replace array slices with existing utility ([ea49840](https://github.com/scop/bash-completion/commit/ea498402ccf07eb1e4acef0ad40a7c188e567145))
* **lvm:** update call to sub-command ([30494f7](https://github.com/scop/bash-completion/commit/30494f7b426c73b1a6a616f63d3510027c6fb02e))
* **make:** handle multiple short options in skipping arguments ([24c80c9](https://github.com/scop/bash-completion/commit/24c80c91b7183ea5fcc9f2e7bc7ded303ea57d0a))
* **make:** typo fix in a code comment ([e72cc82](https://github.com/scop/bash-completion/commit/e72cc826534a0ea9dc840546134c19962697c3a0))
* **mdadm,update-alternatives:** fix leak variable ([14cd53b](https://github.com/scop/bash-completion/commit/14cd53bb38d0d0ef91bf82731e8946551f15023a))
* **medusa:** filter completions by the current word `cur` ([f3ae706](https://github.com/scop/bash-completion/commit/f3ae706e114c4924416c50cca90473e6ce98ae20))
* **mr:** avoid `${var/pat/$'...'}` for compat42 in bash &gt;= 4.3 ([b5ae5fa](https://github.com/scop/bash-completion/commit/b5ae5fada8b788885b0fd65cca34b85672f08189))
* **mr:** exactly match command names ([800b103](https://github.com/scop/bash-completion/commit/800b10372e383124856c37b505d5dc9232eb1e45))
* **mutt:** avoid unexpected word splitting ([3183e67](https://github.com/scop/bash-completion/commit/3183e67c72fcf5ba3d713de59a5d6898a9a64952))
* **mutt:** fix use of regex in the glob context ([63924e6](https://github.com/scop/bash-completion/commit/63924e67527e5fa7698ac0610c7fd90c7ba36ecf))
* **mutt:** rewrite ${cur/#!/X} to X${cur:1} against patsub_replacement ([6b8f82b](https://github.com/scop/bash-completion/commit/6b8f82b44e2308db66bc10e4f3529fe0ca5137e5))
* **mutt:** use _comp_dequote for safe eval ([f98379d](https://github.com/scop/bash-completion/commit/f98379d33ee376df7a7f7de6fb85af8fffb469ee))
* **openssl:** avoid variable conflicts ([a31906c](https://github.com/scop/bash-completion/commit/a31906cd2886b51e74d5d1bcbae3528a0f9f135b))
* **openssl:** fix variable `i` leak ([83844b7](https://github.com/scop/bash-completion/commit/83844b7df11ad4ca02bb5da473fa4c5e8e3df80d))
* **perl:** fix ineffective condition in an awk script ([302dc52](https://github.com/scop/bash-completion/commit/302dc52b9b02dc9a8fa9b1833012b3c4f96b6d7b))
* **perl:** helper call regression from 01611dd3d ([b85263a](https://github.com/scop/bash-completion/commit/b85263a6de7f8e60f09489caac3f9a0d613e804f))
* **pgrep:** do not rely on uncontrolled IFS ([99e38b2](https://github.com/scop/bash-completion/commit/99e38b27424a53f901ec274269f5646bb4b02850))
* **portinstall:** use awk and _comp_compgen to filter completions ([791f56c](https://github.com/scop/bash-completion/commit/791f56c62656ba19c4db804b7ccf8c2e8ba1d55a))
* **povray,sbopkg:** check filetype ([434eb08](https://github.com/scop/bash-completion/commit/434eb0853ed25c8730a38d5c3afbac170b397153))
* **povray:** process each element against patsub_replacement ([0afc2e4](https://github.com/scop/bash-completion/commit/0afc2e4a5db45b569827ca3bbef90f70bc7e74f5))
* **povray:** quote an argument properly ([0a240bd](https://github.com/scop/bash-completion/commit/0a240bd958a8aed9ef4251d0b223c42c60a77125))
* **ps,ss:** avoid extra expansions by -W "$(...)" in _comp_delimited ([cb347cb](https://github.com/scop/bash-completion/commit/cb347cb208c5708e30aa3d847849aa6ce71ba814))
* **pydoc:** specify -a to _comp_compgen_filedir ([34a91e9](https://github.com/scop/bash-completion/commit/34a91e9425992262913cbacf36706618a6f99bd4))
* **pytest:** do not filter underscores from parsed option arg choices ([ea13241](https://github.com/scop/bash-completion/commit/ea13241b2aaa9361ba6d701aaadd8f4ce39dc28a))
* **python:** avoid variable conflicts ([bef94c3](https://github.com/scop/bash-completion/commit/bef94c39b9fac23655c8b50181d7834869d72702))
* **python:** complete filenames for script arguments ([#1018](https://github.com/scop/bash-completion/issues/1018)) ([397a49d](https://github.com/scop/bash-completion/commit/397a49d1c3a4faa5e15639d2852828f99b659bcc))
* **python:** complete micropython and pyston script args consistently ([5b60ccc](https://github.com/scop/bash-completion/commit/5b60ccc1ee573637263bdaac070065af1406de4d))
* **qdbus:** remove cur and all the rest arguments ([531b751](https://github.com/scop/bash-completion/commit/531b75142dc812a37b638de5c272c2356506fcc2))
* replace \s with [[:space:]] for POSIX sed/awk ([1989ba9](https://github.com/scop/bash-completion/commit/1989ba9ad3de5cf33da3c9623fa87be5194fac08))
* **ri:** avoid interference on existing COMPREPLY ([0a9d931](https://github.com/scop/bash-completion/commit/0a9d9319d50db21e0ca0051c19b9b3375790d353))
* **ri:** fix wrongly quoted options to compgen ([5248bbf](https://github.com/scop/bash-completion/commit/5248bbf475d275bd6fc743d62f6f7f3ab0c30a39))
* **ri:** properly split methods ([910a5a0](https://github.com/scop/bash-completion/commit/910a5a07c0f43095134c97c6b10db9d05505c3ef))
* **ri:** split classes using `_comp_split` ([c95baa1](https://github.com/scop/bash-completion/commit/c95baa12d7879c81967b6dc9df896255d3253781))
* **ri:** work around localvar_inherit ([9d6ff68](https://github.com/scop/bash-completion/commit/9d6ff689f44a2e560cbb643607f87551e7a88190))
* **sbopkg:** add `;` mandatory before `}` in POSIX sed ([b286d53](https://github.com/scop/bash-completion/commit/b286d53dec9e7fb5edb513afa8bd60f8c0e9b68c))
* **sbopkg:** work around nounset ([9709bdf](https://github.com/scop/bash-completion/commit/9709bdf226996878760ad87759f0011ff8587a3b))
* **scp:** `nounset` mode error on unknown suboption, bash 4.2 ([82ca8d9](https://github.com/scop/bash-completion/commit/82ca8d924c4514f6570861d97b1ed953cbf9fe11))
* **ssh-copy-id:** call ssh, not -copy-id for suboption completion check ([473278f](https://github.com/scop/bash-completion/commit/473278faed2f6d7d894dc674e71d1cef41022b6c))
* **ssh-keygen:** call ssh, not -keygen to resolve supported protocols ([d569ea2](https://github.com/scop/bash-completion/commit/d569ea26389824b73732157947193efaea5f4ba8))
* **ssh-keygen:** first arg to ssh query for protocol versions ([8a0a71e](https://github.com/scop/bash-completion/commit/8a0a71e449b5458f89fbc55b0ab7d6f6bf6656e9))
* **ssh:** avoid variable conflicts ([f2df91d](https://github.com/scop/bash-completion/commit/f2df91d378004780d5237ab89faea2d6789169c0))
* **ssh:** fix wrong quoting inside compgen -W ([febb3b3](https://github.com/scop/bash-completion/commit/febb3b3257fca6402fc4c33c4de293bbf962580c))
* **ssh:** remove -a of generators ([55c5c45](https://github.com/scop/bash-completion/commit/55c5c459387e54ecdc869121fa97242e92ab87a8))
* **ssh:** remove unnecessary -a to _comp_compgen ([992f28e](https://github.com/scop/bash-completion/commit/992f28e73c82f3157e346845ba9e440b7f3d8f22))
* **ssh:** resolve relative ssh_config Include correctly ([4ac86e8](https://github.com/scop/bash-completion/commit/4ac86e84e55e793ba468c8cb6111510c9831377b))
* **ssh:** use `_comp_compgen` for `_comp_xfunc_ssh_identityfile` ([05e70ed](https://github.com/scop/bash-completion/commit/05e70ede40dc4b50cdeb67bfa8484af53d24f995))
* **ssh:** work around bash-4.2 nounset ([4447f83](https://github.com/scop/bash-completion/commit/4447f83c5d72823f336e3eefab2e8e10e080711b))
* **ssh:** work around bash-4.2 nounset ([d0695d0](https://github.com/scop/bash-completion/commit/d0695d0bc9d44d851d41b5c7e123ab6b95f29e81))
* **sudo:** use $1 (sudo) as $root_command ([329ca0e](https://github.com/scop/bash-completion/commit/329ca0eefd540b78e5fc287c2a4a2e2afdbfb45d))
* **svcadm:** fix ineffective IFS for splitting ([1fd456e](https://github.com/scop/bash-completion/commit/1fd456e7435b88811b82809dc5845ee53ff7aafb))
* **svcadm:** use the first matching name as command ([1870d5e](https://github.com/scop/bash-completion/commit/1870d5e0a67650a67c19895aefaae79eb2d6d76e))
* **sysbench:** fix extraction of test-name option ([cc246a1](https://github.com/scop/bash-completion/commit/cc246a1b7d0433996c99534f64e0363170ac00cd))
* **timeout:** always treat word after duration as command ([0d0531b](https://github.com/scop/bash-completion/commit/0d0531b254b03510f5dfa422a4198c33b5becd54))
* **tipc:** remove filtered-out elements ([75b36b2](https://github.com/scop/bash-completion/commit/75b36b20b57e67a253d0c2622e88ba84ccc3bc25))
* **update-alternatives:** quote dirname ([1205ed1](https://github.com/scop/bash-completion/commit/1205ed1958fa914faa8ccec778079e3f0ea2945b))
* **update-alternatives:** suppress error for non-existent directory ([adbdab8](https://github.com/scop/bash-completion/commit/adbdab8fb5620e208826256e66851a1343f09e75))
* **update-alternatives:** work around localvar_inherit ([1d9e19c](https://github.com/scop/bash-completion/commit/1d9e19cd2ae3650aa81512dc2ab811a80c16ca33))
* **upgradepkg:** use `_comp_compgen` for `_comp_cmd_upgradepkg` ([a0973d7](https://github.com/scop/bash-completion/commit/a0973d77e8a3706490e33ba0f807727250b95c0e))
* use _comp_compgen_split to avoid extra expansions by -W "$(...)" ([49997d0](https://github.com/scop/bash-completion/commit/49997d0b2ff4fbbea37558570632e096d777ee54))
* use `_comp_compgen_split` to split and filter words ([158b8ba](https://github.com/scop/bash-completion/commit/158b8ba1de91913108507ae163611a44ae4871b0))
* use `_comp_compgen_split` while keeping filtering by $cur ([a00ee19](https://github.com/scop/bash-completion/commit/a00ee19962e78f51a12b83bb1b11b89dc7512b22))
* use `_comp_compgen` for word list ([83f033f](https://github.com/scop/bash-completion/commit/83f033f798c0fefbc9f37149d3bd40b0f238eabc))
* use `_comp_split` while keeping filtering by $cur ([509e642](https://github.com/scop/bash-completion/commit/509e642d42a696a5737c94dc296c69ed60a4dd3b))
* use compgen -P prefix -W '"${arr[@]}"' against patsub_replacement ([45d036a](https://github.com/scop/bash-completion/commit/45d036ad804973a4b1916e818a73421435db444d))
* use XPG4 awk in Solaris ([cdd6da9](https://github.com/scop/bash-completion/commit/cdd6da9c77c7c99cab0aed7b9d7a5e4c0c5e0c78))
* **wol:** filter IP addresses by `cur` ([c3505c5](https://github.com/scop/bash-completion/commit/c3505c518f9d2695f678d82a1efcf35118af08ec))
* **wtf:** quote array assignment ([44c1d12](https://github.com/scop/bash-completion/commit/44c1d1292fdb840640a848aab6b2827002477ee9))
* **xfreerdp:** support the new form of the option /list:kbd ([d8cadfe](https://github.com/scop/bash-completion/commit/d8cadfea1036cb558f5c6f663843a19eaa3b5025))
* **xfunc ssh identityfile:** localize change to cur ([76b5726](https://github.com/scop/bash-completion/commit/76b572650f224e2e3b85918944af0bc8dfb49952))


### Performance Improvements

* **_comp_count_args:** skip reinitializing cword and words ([29398ef](https://github.com/scop/bash-completion/commit/29398efab4dd06fceb7c94c5329c5002b14cb8b4))
* **mutt:** return earlier on empty `cur` ([a244587](https://github.com/scop/bash-completion/commit/a244587e3c2d83bbef090438b329478da1896a4c))
* **portinstall:** return earlier for -lLo and non-existent file ([395e463](https://github.com/scop/bash-completion/commit/395e463d7e22bfb8b8b9f8ce4884ff7a979b2e35))
* **vncviewer:** set nocasematch inside subshell ([5ed0020](https://github.com/scop/bash-completion/commit/5ed0020f4ec39c5ff9994882cae9f04eb45e4ed7))

## 2.11 (2020-07-25)

* lilo: add -B and -E completions ([7dd16ad](https://www.github.com/scop/bash-completion/commit/7dd16adf6ae409ee76256ac8b73977e9a88e341e))
* lilo: honor -C when completing labels ([5b12f1d](https://www.github.com/scop/bash-completion/commit/5b12f1d49bf036f99ccd97df2c3effee63fb04d8))
* lilo: don't complete on commented out labels ([ad6a349](https://www.github.com/scop/bash-completion/commit/ad6a3496c3517534e5791334094cc23ec7f09437))
* test/dnssec-keygen: allow more alternatives in algorithm completion ([0f80613](https://www.github.com/scop/bash-completion/commit/0f80613bd8a554c6aece24cf694a4ba9a11e57ba))
* test/wol: don't fail MAC test if test system has /etc/ethers entries ([8130f87](https://www.github.com/scop/bash-completion/commit/8130f87b4e035e3b439ce2f06bdb6bb495288e15))
* : complete commands when prefixed with a backslash ([1e029e8](https://www.github.com/scop/bash-completion/commit/1e029e81dc0a121f575d35c925cc878448feccc4))
* ipcalc: new completion ([1307b7a](https://www.github.com/scop/bash-completion/commit/1307b7ae9f809aec4d1cda74a13878c1947eb095))
* README.md: add introduction ([3e941a6](https://www.github.com/scop/bash-completion/commit/3e941a643097d080277af00924e34ef925c4c41f))
* chromium-browser, firefox: complete on *.txt (#379) ([d705123](https://www.github.com/scop/bash-completion/commit/d7051234afee6cc164b59660dade18f34fb0d52f))
* test/ipcalc: fix tests with busybox ipcalc ([ba3b2ea](https://www.github.com/scop/bash-completion/commit/ba3b2ea05632e07920c4670d0297af557ba717c5))
* lilo: work around shellcheck false positive ([42dbeef](https://www.github.com/scop/bash-completion/commit/42dbeefb8a681e52f381218c2b69e586537ab6e4))
* test: port remaining finger, sftp, ssh, and xhost cases to pytest+pexpect ([aab32df](https://www.github.com/scop/bash-completion/commit/aab32df7ec7a9bff6059560d58c0c7088a983bd4))
* test: port some scp test cases to pytest+pexpect ([f4f365d](https://www.github.com/scop/bash-completion/commit/f4f365dd6c619b41f9947eb3e53a120fd5173cbe))
* test: fix spurious hosts fixture failure without avahi-browse installed ([b2c12be](https://www.github.com/scop/bash-completion/commit/b2c12be995eb4cb9aea931abb5f8c90a9f96907c))
* test: remove some no longer needed tcl/expect code ([f066c67](https://www.github.com/scop/bash-completion/commit/f066c676f2145acb4df84461ba75847af35d2e9f))
* test: port some _known_hosts_real unit tests to pytest+pexpect ([30aefd3](https://www.github.com/scop/bash-completion/commit/30aefd3ee918d2a2c70d24eeb37e7c36004d3ea8))
* test: host helper lint and usage fixes ([bd3d509](https://www.github.com/scop/bash-completion/commit/bd3d509f04fe6ca0ef9477293006fada716aebca))
* cryptsetup: add luksChangeKey arg completion (#380) ([5bb526a](https://www.github.com/scop/bash-completion/commit/5bb526a0b6f1af02b32464879c71a1fd5a5e4104))
* tsig-keygen: new completion ([399771d](https://www.github.com/scop/bash-completion/commit/399771d6f19d839069f0eb45001887a04231885e))
* test/upgradepkg: port remaining test case to pytest+pexpect ([3a0e9c1](https://www.github.com/scop/bash-completion/commit/3a0e9c1e78d33571f272d327c7f4fbb8dca250d8))
* test/tsig-keygen: require command for test_options ([9f0ae30](https://www.github.com/scop/bash-completion/commit/9f0ae306200cbf5cc6e62f72f77ea8a774ea41e3))
* test/chown,sudo: parametrize special case test, improve xfail targeting ([2f34e86](https://www.github.com/scop/bash-completion/commit/2f34e86d988733515e21b3050d4f4ab603b9adc4))
* test: remove some no longer needed old test suite code ([8b3007f](https://www.github.com/scop/bash-completion/commit/8b3007f7315e7313add3518599ce44c288c48aa6))
* test/cd: convert remaining test case to pytest+pexpect ([c23c807](https://www.github.com/scop/bash-completion/commit/c23c8079fb1d78f03acb5c183b85264f6c9e06db))
* test/cd: make dir_at_point produce better debuggable failures ([a6cfbcd](https://www.github.com/scop/bash-completion/commit/a6cfbcdc729a640b1b89af4cd4c8f3d713d51a47))
* nmap: parse options from -h output ([e99577a](https://www.github.com/scop/bash-completion/commit/e99577a3e7f7c571a61074242ab21e0245bfe3dd))
* nmap: handle options split on equals sign ([c51ccb7](https://www.github.com/scop/bash-completion/commit/c51ccb76d1f4fa08f1260e8d204d3622dce76f4e))
* openssl: support getting digest list from more recent openssl versions ([fe90e2a](https://www.github.com/scop/bash-completion/commit/fe90e2aef9364d2361bdc9ae57f06690de52a596))
* openssl: parse available options from $command -help ([9e03fc5](https://www.github.com/scop/bash-completion/commit/9e03fc5ca07ecc7cbb863b2affdd06f8bbf8b415))
* openssl: complete -writerand with filenames ([548feef](https://www.github.com/scop/bash-completion/commit/548feef55be93a99c488e31051f524a0e4c5549b))
* totem: reuse kaffeine completions (#372) ([e4d5eba](https://www.github.com/scop/bash-completion/commit/e4d5ebadf2a7ae61162defb7d6045fb8bff3f0da))
* test/cd: remove unused import ([9a9f09f](https://www.github.com/scop/bash-completion/commit/9a9f09fd175b859b53c014137b8c91722ef158f1))
* test/totem: add basic test case ([9c33f68](https://www.github.com/scop/bash-completion/commit/9c33f68a04e0ec6766cb224ab025efbd42ca54e7))
* extra/make-changelog: run through black ([c85e5b1](https://www.github.com/scop/bash-completion/commit/c85e5b16aed3e49b2a3445e67b5143c305c37883))
* : python type hint fixes and improvements ([d1dee07](https://www.github.com/scop/bash-completion/commit/d1dee0769275e68d39ef4c49983f4c7c89534b8f))
* test: drop redundant black args from docker runs ([eedaa1c](https://www.github.com/scop/bash-completion/commit/eedaa1c56c09d00d39347d4b4b12c5a9d2920b1f))
* pre-commit: add config with black, flake8, and mypy ([3b82089](https://www.github.com/scop/bash-completion/commit/3b8208985813bb424bc58a2d013eda08c50f7591))
* test: install black for Python 3.6 too ([3f1d1ee](https://www.github.com/scop/bash-completion/commit/3f1d1ee9e41f1976181fb9f8b7a8039ee42f0219))
* test: add flake8-bugbear ([1dddc81](https://www.github.com/scop/bash-completion/commit/1dddc81eb7363dfefde6f3b595efaa8d12e05691))
* test: add isort to pre-commit, run it ([092d20a](https://www.github.com/scop/bash-completion/commit/092d20a8c592cc801cd7604e7a2f6907a787fbf2))
* test: shellcheck tweaks ([e0c9c5e](https://www.github.com/scop/bash-completion/commit/e0c9c5e67ffb9d3f5c4076ffd667fb4a9b6dafbe))
* test: run pre-commit in tools container ([0a5d616](https://www.github.com/scop/bash-completion/commit/0a5d61699c25dc13fc7eec0a18a9e1ff87411dbe))
* jarsigner: complete on *.apk too (#386) ([64449ed](https://www.github.com/scop/bash-completion/commit/64449edf99377f66739c4ce51eeaec077687b52b))
* python: support executables with minor version (#389) ([bd3eba8](https://www.github.com/scop/bash-completion/commit/bd3eba8182e9cc72d838ea655a8c2aa25a3272fc))
* _known_hosts_real: check that ruptime is present before calling (#390) ([6217345](https://www.github.com/scop/bash-completion/commit/62173450aa686bed5431342410eb6edd21c33e6d))
* ip: improve completion of route subcommands (#326) ([9982cc3](https://www.github.com/scop/bash-completion/commit/9982cc34d6ea684720f171dcdfa1a20a0b5a81e3))
* test: fix CompletionResult.__eq__ UnboundLocalError ([0e1fe15](https://www.github.com/scop/bash-completion/commit/0e1fe15aa71cd4fafe571613f9a77b17b71fbcc6))
* copyright: add 2020 ([0055238](https://www.github.com/scop/bash-completion/commit/00552386675b8589943dc50748ab00e272979796))
* printenv: new completion ([014b3f2](https://www.github.com/scop/bash-completion/commit/014b3f2a0e13e87f25f31fd0ccacbf09ba3bcec2))
* _xinetd_services: look up from $BASHCOMP_XINETDDIR, add some unit tests ([ea1cfa1](https://www.github.com/scop/bash-completion/commit/ea1cfa1da4cd793f66e298dadc9b5b5a916d5a63))
* test: ignore flake8 messages that are in black's domain ([6dae998](https://www.github.com/scop/bash-completion/commit/6dae998a9e8870fff5f9511383c5becc8005e7b1))
* test: move shellcheck to pre-commit ([e908d02](https://www.github.com/scop/bash-completion/commit/e908d02129cf641784fcd14f0e3105bf573aaed3))
* test: move perltidy to pre-commit, run with --converge ([15aacc9](https://www.github.com/scop/bash-completion/commit/15aacc96cb1c767f162836b121d18d7ecc744a55))
* test: require openssl command for option argument tests ([4c64a1b](https://www.github.com/scop/bash-completion/commit/4c64a1bd293b6fe1b5ed182ac8306ed2036b2603))
* git: trigger docker rebuild on pre-commit config change ([ffc5adf](https://www.github.com/scop/bash-completion/commit/ffc5adfb8f1d312bfc77664dc3144289e978d0ae))
* test: split dependencies requiring Python 3.6.1+ to requirements-dev.txt ([009bf22](https://www.github.com/scop/bash-completion/commit/009bf2228c68894629eb6fd17b3dc0f1f6d67615))
* test: upgrade mypy to 0.770 ([383cbad](https://www.github.com/scop/bash-completion/commit/383cbad64934208df355cb5c1d6f29026b3f3ebe))
* printenv: indentation fixes ([3ac9f0a](https://www.github.com/scop/bash-completion/commit/3ac9f0a24331d0768ce16bdce4977694f037ed1e))
* test/printenv: require command for arg completion test ([afc9052](https://www.github.com/scop/bash-completion/commit/afc9052be4990f41675766935933e95a2f8f9a20))
* test/ldd: xfail if --help is not implemented ([71b465a](https://www.github.com/scop/bash-completion/commit/71b465a89801e60fc9c5172004e0d92353999adc))
* test/python: add testcase for submodule completion ([900824d](https://www.github.com/scop/bash-completion/commit/900824d9162b42399c17331cbbcfe6bce56665b4))
* pre-commit: run most python checks on helpers/python too ([5ed40e0](https://www.github.com/scop/bash-completion/commit/5ed40e089f5e5f33a125dc145f6d678fef0584ca))
* lintian: complete paths for Ubuntu's .ddeb and Debian's buildinfo files (#397) ([0988831](https://www.github.com/scop/bash-completion/commit/0988831a7a4fea73e5d61e64dd657d69779cf8e3))
* ip: add support for netns (#391) ([a2ccfa6](https://www.github.com/scop/bash-completion/commit/a2ccfa6bb0438cdb88ef1e551448e839bf70f99f))
* apt-get etc: use _apt_cache_packages from apt-cache ([d90fbe3](https://www.github.com/scop/bash-completion/commit/d90fbe3bfd7e53c365897fa584edc9d2d0a2e9bf))
* : whitespace tweaks ([3c87811](https://www.github.com/scop/bash-completion/commit/3c87811d8917852a132771e5c92b1e76886e493e))
* : argument interaction improvements ([ec6995c](https://www.github.com/scop/bash-completion/commit/ec6995cadc3ab24246e363b4cbc2452091cd4d28))
* test/aptitude: add some test cases ([762ee12](https://www.github.com/scop/bash-completion/commit/762ee12d24db419f02fc026e063962dde82e7275))
* aptitude: parse options list from --help, hardcode less ([27219f8](https://www.github.com/scop/bash-completion/commit/27219f8e76970e7780062d6746ab609792bb7e3d))
* aptitude: add some option arg (non)completions ([826b333](https://www.github.com/scop/bash-completion/commit/826b3336cfa25696b0886908609c29f87a37f8e4))
* crontab, wodim: silence shellcheck SC2191 and SC2192 ([e81f690](https://www.github.com/scop/bash-completion/commit/e81f6908ffbbe8477f636e5ca68ffda0748c12de))
* bash_completion: address shellcheck SC2220 ([a167497](https://www.github.com/scop/bash-completion/commit/a167497e3ea2136b51e62cb04851cb3178f9c332))
* : address shellcheck SC2221 and SC2222 ([365fa76](https://www.github.com/scop/bash-completion/commit/365fa76e2b587bd6c9c39154433b9ced38fe091d))
* smartctl: hush shellcheck SC2054 false positives ([0ad46d6](https://www.github.com/scop/bash-completion/commit/0ad46d6042d6dec1b28f3212551d668a1febe8d4))
* mplayer: address shellcheck SC1078 false positive ([a6e5e99](https://www.github.com/scop/bash-completion/commit/a6e5e9953ee275b67aae8a1f34e3fdc3fa340e38))
* java, pkgadd, sysbench: address shellcheck SC2124 ([5757fd4](https://www.github.com/scop/bash-completion/commit/5757fd4a3693da792f61ff600a0072218b4162dd))
* chronyc, wvdial: address shellcheck SC2178 ([cddf99a](https://www.github.com/scop/bash-completion/commit/cddf99a63b9bd4d561691299a3eb04aa56e60fa1))
* pkgadd: indentation fix ([523387e](https://www.github.com/scop/bash-completion/commit/523387e2891cf17dbf79767d0ee521c6946af46c))
* sysbench: add --test= deprecation TODO ([9caf5c9](https://www.github.com/scop/bash-completion/commit/9caf5c99b64803be50d15421460978bfe33a85be))
* protoc: complete all --*_out without more specific handling with dirs ([a10adf2](https://www.github.com/scop/bash-completion/commit/a10adf25a75bab2d9cd1eb5805e73d12149cb287))
* test: don't run shellcheck on completions/.gitignore ([3f0d77f](https://www.github.com/scop/bash-completion/commit/3f0d77f079901c080d10cc2331c6def42648278e))
* _known_hosts_real, op: address shellcheck SC2184 ([7fdd0a5](https://www.github.com/scop/bash-completion/commit/7fdd0a5582a0830d02575e290133b26f089c24e2))
* test/aptitude: require command where necessary ([72a59a6](https://www.github.com/scop/bash-completion/commit/72a59a6a604a3fba297484732bc3eac66a702969))
* test/printenv: xfail if --help doesn't contain options (e.g. busybox) ([2eccac4](https://www.github.com/scop/bash-completion/commit/2eccac4327e835f4ea553fe0fadf2f6103c24055))
* insmod, modinfo, modprobe: support xz compressed modules (#401) ([2c6a522](https://www.github.com/scop/bash-completion/commit/2c6a52236026a8e3307faca7b897ae368489391d))
* test: upgrade shellcheck to 0.7.1 ([6af1710](https://www.github.com/scop/bash-completion/commit/6af1710fcbf8ec1108a394a7d611394590957911))
* quote_readline: fix $ret leak ([3f340bf](https://www.github.com/scop/bash-completion/commit/3f340bf8d7f63f86987a3c947db272ccd296487e))
* info, java: address shellcheck SC2153 ([460f1b1](https://www.github.com/scop/bash-completion/commit/460f1b105f7ea3e6b26016cf61d6449c6b504c12))
* man, perl, route, tipc: address shellcheck SC2053 ([0fcfa65](https://www.github.com/scop/bash-completion/commit/0fcfa6592bf6d4085c1e37f6d0dd00510c805a1f))
* tipc: comment grammar and spelling fixes ([22b015e](https://www.github.com/scop/bash-completion/commit/22b015ec167cfe9cb8da1fc399067dc2d0a55f71))
* renice: address shellcheck SC2254 ([5f25641](https://www.github.com/scop/bash-completion/commit/5f2564100ed436d4eb2dc7a664a2706304e80b66))
* test/run: address shellcheck SC2164 ([49ac2d9](https://www.github.com/scop/bash-completion/commit/49ac2d9ffcc8cc2c58984ae1e38f360478ea80ed))
* _upvar, _upvars, _variables, rpm: address shellcheck SC1083 ([e043fac](https://www.github.com/scop/bash-completion/commit/e043fac649b50c7c0e5d0a8d49ce8bd93a139322))
* mutt: address shellcheck SC2088 ([2404e97](https://www.github.com/scop/bash-completion/commit/2404e9755aaba02bd11471f9a6bd3f11ce3a4386))
* ssh: add new -Q completions in OpenSSH 8.2p1 (#400) ([a5890aa](https://www.github.com/scop/bash-completion/commit/a5890aa5e29e0f1cd1500df4758d7883dd55f2f5))
* cvs, modprobe, sh: address shellcheck SC2209 ([f894bc1](https://www.github.com/scop/bash-completion/commit/f894bc19b770d83b91612219016674aed6546604))
* rpm, ssh, umount.linux: address shellcheck SC2120 ([d7381be](https://www.github.com/scop/bash-completion/commit/d7381be935d96244838b0a3a603639d95a0e6564))
* _filedir_xspec: address shellcheck SC2140 ([198d3f7](https://www.github.com/scop/bash-completion/commit/198d3f71fc09f11519321b9561c31b1ef796e7dd))
* scp: address shellcheck SC2089 and SC2090 ([c906aeb](https://www.github.com/scop/bash-completion/commit/c906aeb76cae276b04d8cbe25f80a986fec42018))
* java, pkgadd, sysbench: address shellchec SC2124 ([34e5e6a](https://www.github.com/scop/bash-completion/commit/34e5e6a0ad9d29b4f91e93a6952050e88c5ad2a6))
* test/lib/library.sh: address shellcheck SC2125 ([bef80ef](https://www.github.com/scop/bash-completion/commit/bef80ef624c3d7101b3661895958365f71b890a8))
* : address shellcheck SC2046 ([1d3add4](https://www.github.com/scop/bash-completion/commit/1d3add44563e48be7a6ec6cbd4cc8bb9e27d44f0))
* test: bump shellcheck severity to warning + some disables ([a6cd66c](https://www.github.com/scop/bash-completion/commit/a6cd66cc4b0dcf8f36e788b0ecdb637c72f0118e))
* test: tolerate duplicates from compgen actions ([bbd6814](https://www.github.com/scop/bash-completion/commit/bbd68147a9eb829ddbae4ffed6523b69a076b6ed))
* test/xfreerdp: skip xfreerdp kbd test if kbd-list returns empty ([9746a39](https://www.github.com/scop/bash-completion/commit/9746a39cbc2a1102c3bc9f7fff36ce36e8d05881))
* test: make it possible to not care whether command did output or not ([1ef3ad4](https://www.github.com/scop/bash-completion/commit/1ef3ad487fb9f02b8cc641439b2049529fbdfb9e))
* test: run skipif and xfail commands without caring if they output or not ([005ab9f](https://www.github.com/scop/bash-completion/commit/005ab9f0957f33f092d415f2ab2e1b3a3f501dfd))
* pgrep, pkill: add --ns and --nslist arg completions ([e649ea1](https://www.github.com/scop/bash-completion/commit/e649ea16374bbacfdb84c3d9803211754f62bfc1))
* pytest: complete test classes ([f4ac160](https://www.github.com/scop/bash-completion/commit/f4ac16016d005d91652226272e3d496ede8e5ca1))
* pytest: add some option arg (non-)completions ([61e5d8d](https://www.github.com/scop/bash-completion/commit/61e5d8de7fcbc426c5a1e3f7f5dcda9510051eed))
* test/cd: fix test_dir_at_point for setups that repeat "trailer" ([25b4f1d](https://www.github.com/scop/bash-completion/commit/25b4f1db77f2373f7a700998327e9d9d837da89a))
* test: generalize complete at point test ([8314c75](https://www.github.com/scop/bash-completion/commit/8314c7576134d2a1ae948bc6f3e008e15d837886))
* test/alias: port remaining test case to pytest+pexpect ([f81d313](https://www.github.com/scop/bash-completion/commit/f81d313138cb6ab0fd155089b3c7499d733c118b))
* nmap: fix option parsing with BSD sed ([e712b2f](https://www.github.com/scop/bash-completion/commit/e712b2f932a4f000c75a417ab62f19532147ed52))
* carton: fix command parsing with BSD sed ([1968b3a](https://www.github.com/scop/bash-completion/commit/1968b3ae59b20ea16fe0004ecff53831a1728467))
* _filedir*: update link to bug-bash discussion on -X (#404) ([bfd65f3](https://www.github.com/scop/bash-completion/commit/bfd65f3be7af378d944a269e77cdf5d93aaac022))
* openssl: update -starttls completions (#403) ([28df326](https://www.github.com/scop/bash-completion/commit/28df3269a994a33ccb8a7946a960ba26a97b6790))
* : replace various conditional expressions with arithmetic evaluation ([2ad992a](https://www.github.com/scop/bash-completion/commit/2ad992a1bca79e0a9353afc32cbb59749f2f07c0))
* bash_completion, invoke-rc.d, svcadm: trivial cleanups ([69a835a](https://www.github.com/scop/bash-completion/commit/69a835a335c8ecea7c8f0453097ab909838eef08))
* : enable and address shellcheck SC2053 ([333d590](https://www.github.com/scop/bash-completion/commit/333d5908887e5133916b5987ce67ba65b6a5b343))
* ssh-keygen: -s and -n completion improvements ([6666ebb](https://www.github.com/scop/bash-completion/commit/6666ebbb0d9934fd999937c10028f51295be5d28))
* : array subscript cleanups ([13b5a4b](https://www.github.com/scop/bash-completion/commit/13b5a4bea8bae0d67d9286ef1d732c5dad1de685))
* doc: recommend arithmetic evaluation ([5e5c83b](https://www.github.com/scop/bash-completion/commit/5e5c83b13808b2c29c0264284042998e4cc0816e))
* test: remove shellcheck severity filter, add explicit disables instead ([fd19ea0](https://www.github.com/scop/bash-completion/commit/fd19ea00db362d9a33a6d4edd0d626b3da26a69c))
* test: enable parallel pre-commit shellcheck ([48ab77e](https://www.github.com/scop/bash-completion/commit/48ab77e2ac5518ffbf8c6a2ce5b205da4b072c97))
* __reassemble_comp_words_by_ref, java: address and work around shellcheck SC2102 ([dcf9f39](https://www.github.com/scop/bash-completion/commit/dcf9f3991d94804591fac1941587f04d3f19b4c0))
* : more arithmetic evaluation cleanups, thanks to shellcheck SC2004 ([b147516](https://www.github.com/scop/bash-completion/commit/b147516b6081f451ace1e8172e6041029177536e))
* bash_completion, java, tipc: for loop whitespace consistency tweaks ([3abbd23](https://www.github.com/scop/bash-completion/commit/3abbd23be045138a1d63ecdd27b5c0de9f5c1cd1))
* pytest: address shellcheck SC2002 ([fe53ef2](https://www.github.com/scop/bash-completion/commit/fe53ef2ae8e3615c5a1186bfbc980f918b1d0c99))
* wget: address shellcheck SC2116 ([091a7cd](https://www.github.com/scop/bash-completion/commit/091a7cde3deaeb7650e6972f5dda8c943cd1c120))
* mutt: address shellchec SC2236 ([566c5f5](https://www.github.com/scop/bash-completion/commit/566c5f521d1d4cc3bdcba4b7555a4460d78aeebe))
* scp: work around shellcheck SC1003 ([e1cbe8f](https://www.github.com/scop/bash-completion/commit/e1cbe8f6f0d097b51f9815dcf6729ac3abca526c))
* ssh, xsltproc: address shellcheck SC2006 ([32421d1](https://www.github.com/scop/bash-completion/commit/32421d1d74a2915c227caa5c08457c0d7a9f18e6))
* test: make at-point completion tests easier ([e9450b8](https://www.github.com/scop/bash-completion/commit/e9450b88c38921aa9dd297f56c99c055857c1952))
* doc: add loop variable naming guideline ([a6a59e3](https://www.github.com/scop/bash-completion/commit/a6a59e32b8dd165f04409897641ba72d11d44e1d))
* apt-cache: fix command mode handling ([c41b772](https://www.github.com/scop/bash-completion/commit/c41b772dcb4e31c8a6ee8d3052f3d2742d7a889d))
* crontab: fix loop over already given args ([568c658](https://www.github.com/scop/bash-completion/commit/568c658c2c0ac6097f5332d4bfe23a9cf7b4de37))
* : various loop iteration improvements ([911a432](https://www.github.com/scop/bash-completion/commit/911a4322cdc8eb393921b2a52efbab4e394e67e2))
* : remove some unused variables, thanks to shellcheck SC2034 ([95f3bec](https://www.github.com/scop/bash-completion/commit/95f3bec3ba948fdb4f26673e008e4bfa990b96a0))
* test: add perlcritic to pre-commit, run on all perl ([dd890cd](https://www.github.com/scop/bash-completion/commit/dd890cd30ddc617a10c7ff114bef13ff6485536b))
* test: run pre-commit on host instead of docker ([b8c2a95](https://www.github.com/scop/bash-completion/commit/b8c2a95c5ed41088cd94d6ece044f37b941d8a2f))
* make: add bmake alias ([6b42551](https://www.github.com/scop/bash-completion/commit/6b42551fa0166508b1ef6c28751ccdf5c7c49f32))
* test/make: mark more cases as requiring command ([6354520](https://www.github.com/scop/bash-completion/commit/63545202952c970146eced87ae156f023f7306e4))
* test: run lint tests on Travis in a quickish separate first stage ([e493f6a](https://www.github.com/scop/bash-completion/commit/e493f6a2f2ea596f373c9f31e91f44633f6f2d4a))
* test: add script to maintain list of executables for full test coverage ([e0b9397](https://www.github.com/scop/bash-completion/commit/e0b93973acdc6f3938ecc1103971a8a0d18bf5ad))
* extra: trigger docker builds only on test-cmd-list.txt changes ([85f3c73](https://www.github.com/scop/bash-completion/commit/85f3c737d97e5f420885d9b875de73d56035c78a))
* find: fix -exec etc argument and its completion ([3e849c2](https://www.github.com/scop/bash-completion/commit/3e849c25ef9a2a2dc95c713085898a9547a70e18))
* reportbug, scp, sftp, svn: use compgen -c instead of _command ([e73535f](https://www.github.com/scop/bash-completion/commit/e73535f72e0190c953211625dd71d73cfb2fa7e6))
* _command: improve commentary ([2cf6b67](https://www.github.com/scop/bash-completion/commit/2cf6b670323ad0e914c5bed13122d2ee0cf07821))
* ssh-keygen: option and arg completion updates for new versions ([edc4f9b](https://www.github.com/scop/bash-completion/commit/edc4f9b0390ae07c572a3186bdb9741a5be321de))
* ssh-keygen: add -b arg completions according to specified -t ([69b477b](https://www.github.com/scop/bash-completion/commit/69b477bada9672fa5ec9079f42758a866e874180))
* ssh-keygen: -O arg updates and improvements ([63e04ee](https://www.github.com/scop/bash-completion/commit/63e04ee86aeee5dc33a0e5d80c5e4e28692ac937))
* README: clarify loading automatically on demand ([f5e831b](https://www.github.com/scop/bash-completion/commit/f5e831bb789094dc0293e61727b0319bb5714909))
* pre-commit, *.md: add markdownlint, address findings ([43e958a](https://www.github.com/scop/bash-completion/commit/43e958a6db294367451190624a73e5f532fb6174))
* : doc and comment link updates ([a234784](https://www.github.com/scop/bash-completion/commit/a234784b39e657be671f292c384395ed2fd64a75))
* pre-commit: use local perlcritic hook ([a277345](https://www.github.com/scop/bash-completion/commit/a277345460a38206837e5e98380b30c39be89734))
* editorconfig: apply yaml settings to .yaml too ([7af92f5](https://www.github.com/scop/bash-completion/commit/7af92f506c60c32eb95d4625e542e4e67d77eee9))
* test: add note about unescaped assert_complete single return values ([663ac51](https://www.github.com/scop/bash-completion/commit/663ac5175fd46b790beff1ac5952551b0e211f90))
* test: port most umount test cases to pytest+pexpect ([b621591](https://www.github.com/scop/bash-completion/commit/b6215914784096189db0ef4fd9d580a249a19675))
* Source user completion only if it's a file (#409) ([78bf738](https://www.github.com/scop/bash-completion/commit/78bf738eb082f67080ec26b64444fd9116907743))
* test: prefix fake test commands with underscore ([5351161](https://www.github.com/scop/bash-completion/commit/53511617f0ab972d0b6912990ebf0adc3eeffe9d))
* nmap: simplify help scraping a bit, don't try to emit unnecessary newlines ([8db05b4](https://www.github.com/scop/bash-completion/commit/8db05b4d1c6de665a76c330b484279dc279dff27))
* test: upgrade markdownlint to 0.23.0 ([5520dd4](https://www.github.com/scop/bash-completion/commit/5520dd4a20a7e849d24cf239a63a876e48dabc1b))
* test: fix incorrect fixtures/shared/default xfails/expectations ([f0fe52c](https://www.github.com/scop/bash-completion/commit/f0fe52c923a5becc0556798ea892d2710aa9500c))
* pre-commit etc: add shfmt ([08db11f](https://www.github.com/scop/bash-completion/commit/08db11f12a04516622948bb6c1ff8cc50001fb3a))
* : run all shell code through shfmt -s ([dbe0ec8](https://www.github.com/scop/bash-completion/commit/dbe0ec8c1a8a36752d3b63b685add075f36ba113))
* travis: use golang 1.14 for shfmt ([d34719a](https://www.github.com/scop/bash-completion/commit/d34719aa79f80578fe4be11ac859f348ae62ed02))
* apt-get: complete build-dep with dirs ([e7ea033](https://www.github.com/scop/bash-completion/commit/e7ea033f1075a99898b4ef8c3a364bb7ba23ef62))
* secret-tool: new completion ([e9a556f](https://www.github.com/scop/bash-completion/commit/e9a556f4a233250c6f05dfd0c5e9ae17a0ffd747))
* test/umount: convert remaining test case to pytest+pexpect ([4514d3e](https://www.github.com/scop/bash-completion/commit/4514d3e7ef774c4bdfc4fe3f9acdf66def16c664))
* test/scp: port remaining test case to pytest+pexpect ([12de472](https://www.github.com/scop/bash-completion/commit/12de47263b5a49ae8adab7fb1c85d3a2f42852e5))
* test/secret-tool: add to test command list ([25c1c76](https://www.github.com/scop/bash-completion/commit/25c1c76030107cf918015856299f899bfa251e80))
* test/slapt-get: convert remaining test case to pytest+pexpect ([033f548](https://www.github.com/scop/bash-completion/commit/033f5488bdec49f635b6e0ef13784698cdf8d190))
* apt-cache: avoid nonzero exit code from _apt_cache_packages ([ea97386](https://www.github.com/scop/bash-completion/commit/ea973866fdbb4f8f72d4e87d090950795ef68a49))
* _xfunc: simplify ([c0f6a6d](https://www.github.com/scop/bash-completion/commit/c0f6a6d09c88a23441a46c351eff25f23e64b528))
* test/slapt-src: convert remaining test case to pytest+pexpect ([62b3dc6](https://www.github.com/scop/bash-completion/commit/62b3dc6dc2024eb2101bddcd468552d485c60a8c))
* test: drop some no longer needed old test suite code ([2612750](https://www.github.com/scop/bash-completion/commit/2612750d2527865f432218770ccaa0e3dd398f71))
* lftp: use "bookmark list" command to list bookmarks ([c198a94](https://www.github.com/scop/bash-completion/commit/c198a94029aa33cfbd4bd0586e1674859337c35c))
* test: run pytest --verbose in docker ([ea0236e](https://www.github.com/scop/bash-completion/commit/ea0236e1e6b6ad2f0ffc402e4a1d4da6365014a3))
* test/_get_comp_words_by_ref: convert remaining test cases to pytest+pexpect ([322420d](https://www.github.com/scop/bash-completion/commit/322420d50b894e786226987c40c6d559313058f9))
* test/__expand_tilde_by_ref: port remaining test cases to pytest+pexpect ([d258b3c](https://www.github.com/scop/bash-completion/commit/d258b3ca8ab65fec4dfc6a14c4697212e09ad69c))
* test/_filedir: port more test cases to pytest+pexpect ([54c732a](https://www.github.com/scop/bash-completion/commit/54c732a82066e1595d8dd153e992987ea87b54b6))
* test: drop not needed sudo on Travis ([a9822bc](https://www.github.com/scop/bash-completion/commit/a9822bc79b997cfb985b1d8c91f9c3402adce232))
* test: run all Travis jobs on dist: bionic ([9c0a307](https://www.github.com/scop/bash-completion/commit/9c0a30729ce5dd9954ad32dd226021b733b94abd))
* test/_filedir: port remaining test cases to pytest+pexpect ([fb7fb46](https://www.github.com/scop/bash-completion/commit/fb7fb461362fd99cf21de40821793c8a38bf6c56))
* test: drop some no longer needed old test suite code ([4543212](https://www.github.com/scop/bash-completion/commit/4543212a52e9d569017013e1365e9d969683fb49))
* test/_expand: port remaining test cases to pytest+pexpect ([db481c6](https://www.github.com/scop/bash-completion/commit/db481c67fd8ea17036bd432c7e41dbe578a274e4))
* test/_filedir: fix shutil.rmtree on Python < 3.6 ([febf177](https://www.github.com/scop/bash-completion/commit/febf177e84162199394f71704cbcf58ce1dbefd3))
* test: replace some echos with printfs ([bcdf00c](https://www.github.com/scop/bash-completion/commit/bcdf00ce3cec4e6f43521d6d24d75e4574f59b66))
* test/_get_cword: port remaining test case to pytest+pexpect ([80ac63e](https://www.github.com/scop/bash-completion/commit/80ac63efbb3b4677514b900ba936d7af2d2c8eb9))
* test/_known_hosts_real: port more test cases to pytest+pexpect ([5c7bb2d](https://www.github.com/scop/bash-completion/commit/5c7bb2d72c03ad28f700e82b8ca1aeaa4528fedd))
* test: remove more no longer needed old test suite code ([b1a4de9](https://www.github.com/scop/bash-completion/commit/b1a4de9aee916b622889c28a61812092c4d76b9b))
* test/_known_hosts_real: port remaining test cases to pytest+pexpect ([21d4fba](https://www.github.com/scop/bash-completion/commit/21d4fba9d75860454bc0c869f63d66c6cd66bca6))
* test: remove old test suite code no longer used \o/ ([5afc3b5](https://www.github.com/scop/bash-completion/commit/5afc3b558f7651a4a0854fdd9ac0a4580876fd33))
* test: remove unused run-shellcheck, shellcheck is in pre-commit now ([76aa130](https://www.github.com/scop/bash-completion/commit/76aa130483d6da9761715b89ce750eebed3a95b0))
* test: shfmt bashrc ([ead40fa](https://www.github.com/scop/bash-completion/commit/ead40fab3e8e09833b632a6c1c0b828b92ae4ecb))
* test: sync shfmt and shellcheck configs ([c21e0e1](https://www.github.com/scop/bash-completion/commit/c21e0e11025fa2117417d5ddcbe65a7edd116fe3))
* test: upgrade pre-commit to 2.4.0+, drop shfmt kludge ([8af206f](https://www.github.com/scop/bash-completion/commit/8af206f45ad801c47e9e79060a0fbad033cd1d89))
* test: pre-commit config cleanups, ordering ([b4eadfd](https://www.github.com/scop/bash-completion/commit/b4eadfd4b401f15acaab714c363cb0a7ee7bcf8c))
* test: upgrade flake8 to 3.8.1 ([1f27374](https://www.github.com/scop/bash-completion/commit/1f27374d45e7062e6a74a0046bba289eda02f75a))
* test/irb: xfail options test if --help is not available ([f5ea515](https://www.github.com/scop/bash-completion/commit/f5ea515f07ec2cbbbc63262d1f053327a9d466ae))
* : use $ifs for storing/restoring $IFS ([acc43ed](https://www.github.com/scop/bash-completion/commit/acc43edf81c7eaf8b3556453a7cfc4b0104975cd))
* test: try harder to restore environment and cwd on failures ([f0bf00a](https://www.github.com/scop/bash-completion/commit/f0bf00a53677285600cdf8d82e754abb7f0e0644))
* : use more arithmetic evaluation ([328ce5b](https://www.github.com/scop/bash-completion/commit/328ce5be5b4b8822b3b9421dab4460c56990df0b))
* test: upgrade markdownlint-cli to 0.23.1 ([2255f2a](https://www.github.com/scop/bash-completion/commit/2255f2a8577cf7d48b5572d84a6900d9ffdef75e))
* dpkg-deb: fix --show/-W completion ([2eee0b5](https://www.github.com/scop/bash-completion/commit/2eee0b5063ae3fd8d48594bc03d2f5a9e4678ce5))
* test: add some dpkg-query test cases ([034bdee](https://www.github.com/scop/bash-completion/commit/034bdeec06f36c12d3cbf927ca293eee321817f0))
* dpkg-deb: add --raw-extract and -X arg completions ([69198c0](https://www.github.com/scop/bash-completion/commit/69198c0c4ef9830de1ea25d0b5c713adef8b5987))
* test: mark known non-ASCII issues with test suite as xfail ([106e825](https://www.github.com/scop/bash-completion/commit/106e825a7f3b07c6c4d59f0b9c4e66bfa48a870f))
* test/mr: handle missing "clean" with skipif ([eb93dde](https://www.github.com/scop/bash-completion/commit/eb93dde7304ee62e20d54cba003639b46bd18119))
* mr: avoid herestrings, simplify command parsing ([cf64492](https://www.github.com/scop/bash-completion/commit/cf6449226350987f8d0d3ff3d329ca31c19cbcac))
* _bashcomp_try_faketty: new function to try running command with a fake tty ([3829fe9](https://www.github.com/scop/bash-completion/commit/3829fe95d6583cc9eebc1abae1e553dedacd8cd5))
* postfix: try to arrange a fake tty so we can tickle the usage message out ([7aea619](https://www.github.com/scop/bash-completion/commit/7aea619b1b4563f6a5a55877aeeea009e71cfd87))
* 7z: fix -o/-w attached arg completion ([54774d6](https://www.github.com/scop/bash-completion/commit/54774d610323daff29e4cd00d5bcce23b714513f))
* __reassemble_comp_words_by_ref: avoid triggering nounset on indirect references ([0b0323c](https://www.github.com/scop/bash-completion/commit/0b0323c514276c0143568b9e03646b65ee57e788))
* bash_completion: line wrapping tweaks, NFC ([1d1d308](https://www.github.com/scop/bash-completion/commit/1d1d308d1f15e3ab2794c14ed54206cc56b96655))
* test/unit: include test_unit_known_hosts_real.py in dist ([fdc9fc9](https://www.github.com/scop/bash-completion/commit/fdc9fc9b231c084c6e8de32587ec6a746898ce39))
* test/unit: sort files included in dist ([d8f309f](https://www.github.com/scop/bash-completion/commit/d8f309f9fa8cb57067c6ef559269ea1eb3f9d98a))
* : avoid more errors in nounset mode ([3687d27](https://www.github.com/scop/bash-completion/commit/3687d27f1ac266fa784dacc4492f0c554ed3091c))
* test/inputrc: comment and whitespace tweaks ([1293d7c](https://www.github.com/scop/bash-completion/commit/1293d7c7258ef93c131e0e0d546064593610e731))
* : avoid more errors in nounset mode ([b807460](https://www.github.com/scop/bash-completion/commit/b807460140aa6dda09eb2af2ecf3afa1971c84c4))
* : avoid more errors in nounset mode ([d0a0eb5](https://www.github.com/scop/bash-completion/commit/d0a0eb5979d2831174b50e541d97559ac30d7d73))
* : avoid more errors in nounset mode ([505fb2c](https://www.github.com/scop/bash-completion/commit/505fb2c68e38c9071542ff30fbafdab7ddc6f363))
* : mark nounset mode as supported, issues with it are bugs now ([e85a361](https://www.github.com/scop/bash-completion/commit/e85a361199f02f3818312ba149dc64bf2c920d7a))
* test: skip various tests if we don't get a useful usage message ([6f12de6](https://www.github.com/scop/bash-completion/commit/6f12de60230b85fbdd004c69be23be1bc9798767))
* test/runLint: warn about [ ] instead of [[ ]] use ([f6f49db](https://www.github.com/scop/bash-completion/commit/f6f49db9175eb1ee5521bcb59ac6747bebaf1f28))
* modprobe, tshark, _included_ssh_config_files: use [[ ]] instead of [ ] ([50ad3f8](https://www.github.com/scop/bash-completion/commit/50ad3f8d91a2782b5d10dc177e98f2ec06171e20))
* scp, sftp, ssh: fix completion on options bundled with -4/-6 ([b4490f2](https://www.github.com/scop/bash-completion/commit/b4490f238ed534ce9084614a3580656e4cf759f6))
* _pids, _pgids, _pnames: improve shfmt formatting ([2bfb0a7](https://www.github.com/scop/bash-completion/commit/2bfb0a7e22ca94624e13df5876fe80acc7017f1b))
* _filedir: avoid unbound variable error on Ubuntu 14 and 16 ([ac37d77](https://www.github.com/scop/bash-completion/commit/ac37d77db99425aaa9d71fe9215e98623378a4b1))
* ip: add more completions for ip-rule ([426d07e](https://www.github.com/scop/bash-completion/commit/426d07e33ce5379ce4fc7726c140ca3b8c7c7575))
* ip: style fixes similar to ip-netns ([75093c0](https://www.github.com/scop/bash-completion/commit/75093c018a5dee8db1d7a97f42b0cc2c0902ef3e))
* ip: route shfmt, arithmetic evaluation ([a1fe69a](https://www.github.com/scop/bash-completion/commit/a1fe69adf7d476984a3498e8c1616d84e9b694f1))
* ip: complete route add table arg ([e4ad602](https://www.github.com/scop/bash-completion/commit/e4ad602f483f3574fd8630bfcd82bd88ef6def28))
* _init_completion: fix unassigned redirect completion in nounset mode ([2e52b40](https://www.github.com/scop/bash-completion/commit/2e52b40d5087c55798c7bf006654aafd98e5deab))
* : drop support for bash 4.1 ([e2d47ae](https://www.github.com/scop/bash-completion/commit/e2d47aea537ed18892ba485b2acba470ad79301a))
* test: enable shellcheck SC2035 ([77a3505](https://www.github.com/scop/bash-completion/commit/77a35054a335323014f8d63879c0017ff200f9c9))
* qemu, sbopkg: avoid unintentional globbing on option arg completions ([7dc4f61](https://www.github.com/scop/bash-completion/commit/7dc4f61dc0ebdbf45d64195ab9a8cb97768e455a))
* qemu: add -machine arg completion ([11cd174](https://www.github.com/scop/bash-completion/commit/11cd1746d94a98fe12bd989a168204e03583bf78))
* : avoid more errors in nounset mode ([5103c5d](https://www.github.com/scop/bash-completion/commit/5103c5dc3e236e3fa4e0e5a9048ada6ea99a9c76))
* _command_offset, route: cleanups ([9994be6](https://www.github.com/scop/bash-completion/commit/9994be6476247261cb8d3e1befcce4af72a136b0))
* cfrun: fix $hostfile leak ([68b5719](https://www.github.com/scop/bash-completion/commit/68b5719bbc2db1e71f76d843b4b6df41e671952b))
* : avoid more errors in nounset mode ([f4fa0b6](https://www.github.com/scop/bash-completion/commit/f4fa0b6d0ca079e350c8a98ddaeaa4d932fb21ef))
* bash_completion: fix array set checks with empty elements in them ([efaa991](https://www.github.com/scop/bash-completion/commit/efaa991d385e28eeb21490b3a91fd903e4f90b30))
* _known_hosts: avoid errors in nounset mode and no arguments ([36475fc](https://www.github.com/scop/bash-completion/commit/36475fc6762edca0d52a4817aba53f7c2431b9a7))
* pytest: add test class method completion ([ba4c941](https://www.github.com/scop/bash-completion/commit/ba4c941faa1ff5eb63e7984a6a52a7b703e2887f))
* README: document GNU make build requirement ([3423e14](https://www.github.com/scop/bash-completion/commit/3423e14fb0e891a7d2764233eccd3cbecc983e22))
* java, make: avoid errors in nounset mode on Ubuntu 14 and 16 ([1a633f3](https://www.github.com/scop/bash-completion/commit/1a633f3a551154ac3c70bfdc5263d73d8b3d9f3f))
* man, mutt: avoid errors in nounset mode on Ubuntu 14 and 16 ([e5fa022](https://www.github.com/scop/bash-completion/commit/e5fa0221578dce638a84107f0ee813be89bbe3bd))
* pytest: fix test class method completion with BSD awk ([3374911](https://www.github.com/scop/bash-completion/commit/337491161b1278a30d3f0aaf4a7cd2508ea809f5))
* gcc: avoid errors in nounset mode ([221a6aa](https://www.github.com/scop/bash-completion/commit/221a6aa9b18bb8afe11435ee2244589bbaac0ff4))
* bash_completion: trivial cleanups ([817d832](https://www.github.com/scop/bash-completion/commit/817d832fb7ac49eceda6ed3c21428dcc4659fa48))
* test/_known_hosts_real: tolerate duplicates ([0b2866a](https://www.github.com/scop/bash-completion/commit/0b2866a9fe32ee24df242e200896125c7ecf65ad))
* test/_known_hosts_real: reset COMP_KNOWN_HOSTS_WITH_HOSTFILE between tests ([5e63557](https://www.github.com/scop/bash-completion/commit/5e63557aac12251538091dc40bf587842327ced5))
* test/_known_hosts_real: don't modify class scoped base expected list ([133790c](https://www.github.com/scop/bash-completion/commit/133790c464c68fa728b2957a66ee1fa012181a88))
* test: upgrade mypy to 0.780 ([2e7271b](https://www.github.com/scop/bash-completion/commit/2e7271b3aed635a7fead1b97517673092eba5923))
* test: regex escape our magic mark for completeness ([588b05c](https://www.github.com/scop/bash-completion/commit/588b05c1bf74feadad61bd1d1b39e752e639c201))
* test/lspci: skip -A arg test if lspci fails -A help, e.g. busybox lspci ([e2cb804](https://www.github.com/scop/bash-completion/commit/e2cb80403d32af436a93782de4f33aee8352dd2b))
* .gitignore: clean up some no longer needed ignores ([df7b81e](https://www.github.com/scop/bash-completion/commit/df7b81ea93d799affa858f26749f6aba8f0c1a38))
* test/dpkg-query: mark as xfail on non-Debian based systems ([d176aaf](https://www.github.com/scop/bash-completion/commit/d176aafc18cb15a90c6c1d89918884d03382b20d))
* test: simplify completion parsing ([726e4c0](https://www.github.com/scop/bash-completion/commit/726e4c08a96b1f0be1c931179f1b3edb31913917))
* test: partial hostname completion fixes ([ba39695](https://www.github.com/scop/bash-completion/commit/ba39695792410c88f9d8e2303288274e2959cef7))
* test/slapt-src: single expected result handling fixes ([1b9c9bc](https://www.github.com/scop/bash-completion/commit/1b9c9bcc285c296aa2c6df3e75ad75537080947e))
* test/xhost: multiple expected result handling fixes ([888f39a](https://www.github.com/scop/bash-completion/commit/888f39a84a1847333c9e8bc06806886cd95eb103))
* test: upgrade flake8 to 3.8.3 ([4730f3f](https://www.github.com/scop/bash-completion/commit/4730f3fcb0c0cefafc29cad7b0aed81ecd1decca))
* _known_hosts_real: fix # handling in ssh configs ([ebabed1](https://www.github.com/scop/bash-completion/commit/ebabed1a4ca28f6e56a02831fee0e33f15047119))
* _known_hosts_real: fix completion of Host entries after a wildcard etc ([0c69125](https://www.github.com/scop/bash-completion/commit/0c69125d65faaa60b0829db58c88dc33366bb67c))
* test: upgrade perltidy to 20200619 ([87b169d](https://www.github.com/scop/bash-completion/commit/87b169dcf3b95d071280d70b3fc2fb0d8306c9e1))
* test: upgrade mypy to 0.781 ([d46e8a5](https://www.github.com/scop/bash-completion/commit/d46e8a58e7840c0a8a5e942eb987479c69a0272a))
* CONTRIBUTING.md: add posix and nounset mode item ([25887a3](https://www.github.com/scop/bash-completion/commit/25887a38ba3b81e9a6791ade89f5fa1a7c7ea5d3))
* test: upgrade mypy to 0.782 ([f7c14b5](https://www.github.com/scop/bash-completion/commit/f7c14b5579b7bde9b57ae9bb175be7cba916edd9))
* test/_known_hosts_real: add explicit no globbing test case ([8200b9a](https://www.github.com/scop/bash-completion/commit/8200b9a9529316f1f467aea18aaed2cbf4776bf0))
* test/shfmt: upgrade to 3.1.2, reformat with it ([de35c91](https://www.github.com/scop/bash-completion/commit/de35c91dd3fe6c6c1adaeeb7e1a96f6a8360b0eb))
* _known_hosts_real: prevent unwanted pathname expansion on host entries ([29be9d2](https://www.github.com/scop/bash-completion/commit/29be9d2e6bea356f16f12bfa7ad647b3ed67839e))
* _included_ssh_config_files: support globs ([8bd47d4](https://www.github.com/scop/bash-completion/commit/8bd47d4ee25642b5fcddefb825d5025721840673))
* _longopt: exclude too many dashes, allow underscores, require ends with alnum ([e2bcbd5](https://www.github.com/scop/bash-completion/commit/e2bcbd5115868bcfc048da1ce1b6b10f76762bf1))
* _known_hosts_real: avoid errors in nounset mode on Ubuntu 14 and 16 ([54fd092](https://www.github.com/scop/bash-completion/commit/54fd092139e05c8fa01b3256b6c717ef3cdb9fb6))
* test: upgrade markdownlint-cli to 0.23.2 ([95992bf](https://www.github.com/scop/bash-completion/commit/95992bfeee3c0409991fe45e89c1d08cd2eb4401))
* __get_cword_at_cursor_by_ref: fix regression on bash 4.2 ([2ed8d90](https://www.github.com/scop/bash-completion/commit/2ed8d90b51e3ca6a337a15086f47c7dc982891a4))
* pytest: complete async test class methods ([5c4f7a4](https://www.github.com/scop/bash-completion/commit/5c4f7a42d9864dcf91de845ea8167aad9cd3bdfc))
* test/inputrc: comment typo fix ([0d2367b](https://www.github.com/scop/bash-completion/commit/0d2367bd46b722a23e7b83e8bc828342ba857fa5))
* test/inputrc: do not set print-completions-horizontally ([d050205](https://www.github.com/scop/bash-completion/commit/d05020538be21f4b41a2f129c8f4eab83c077f40))
* unzip, zipinfo: complete *.aar (#428) ([8273ce3](https://www.github.com/scop/bash-completion/commit/8273ce3138708ad1db851b74eafba38d6683ca0a))
* pre-commit: update shellcheck-py URL ([08451ec](https://www.github.com/scop/bash-completion/commit/08451ec8383bb309d945589cab7d8c74a347a8b7))
* pre-commit: upgrade isort to 5.0.7 ([d128d70](https://www.github.com/scop/bash-completion/commit/d128d70b1baef5a93f376fd44f18c7824d4643f3))
* _known_hosts_real: exclude Host negations ([812e2ac](https://www.github.com/scop/bash-completion/commit/812e2acec01689b7c20488e2e11d335efd25ce9a))
* test/ant: gitignore all target cache files ([6703c90](https://www.github.com/scop/bash-completion/commit/6703c9035b7a1639a1eded9989ede80848539078))
* pre-commit: add pyupgrade, run it ([54b1e6f](https://www.github.com/scop/bash-completion/commit/54b1e6f70decfb8b8a5b15a1d14279e52551edf2))
* pre-commit: upgrade pyupgrade to 2.7.2 ([5ac90cc](https://www.github.com/scop/bash-completion/commit/5ac90cc4eb6330d0a1999e598e6873d7dc545b77))
* pre-commit: upgrade isort to 5.1.4 ([c2f0056](https://www.github.com/scop/bash-completion/commit/c2f0056c8edc35fc53f40a9b4d53d48e40f6676f))
* _xinetd_services: avoid nounset error on bash 4.2 ([0df93b0](https://www.github.com/scop/bash-completion/commit/0df93b02d355b2faaad0d25d20d45967b82edd63))
* test/ant: avoid complete-ant-cmd.pl interference with ANT_ARGS ([2ad91ec](https://www.github.com/scop/bash-completion/commit/2ad91ec985dc1fbc57d27f7b9815c7d0797426e8))
* test/tshark: fix multiple -O completion with no http2 support ([53f624f](https://www.github.com/scop/bash-completion/commit/53f624f8212497c116afd4a365768237ac4069bb))
* test/xfreerdp: skip --help failure cases ([b41b97c](https://www.github.com/scop/bash-completion/commit/b41b97cd2c646e813317429edbb8285c09cfd0b7))
* tshark: complete -r arg with all filenames (#422) ([436b0cb](https://www.github.com/scop/bash-completion/commit/436b0cbe9e0839a51edcc7948e4ca11f2444a238))
* pytest: rewrite in bash, support toplevel funcs, avoid nondef ones and classes ([bf3f720](https://www.github.com/scop/bash-completion/commit/bf3f72046f62ea86f43b65389d6d706a28544adb))
* pre-commit: anchor exclude patterns ([cc697a6](https://www.github.com/scop/bash-completion/commit/cc697a646c1bd1100890abd367bb8d0274a42275))
* extra/make-changelog: check and output usage message ([cf90b29](https://www.github.com/scop/bash-completion/commit/cf90b297696cee57650f4fb6c29cebe872f773a7))

## 2.10 (2019-12-05)

* README: link to cygwin package ([8d60fd3](https://www.github.com/scop/bash-completion/commit/8d60fd32be777e4c6e734c1dac431b39b53fb216))
* README: use light gray badges for unknown versions ([483968c](https://www.github.com/scop/bash-completion/commit/483968cd513142b71d1e6e9f199d5eaf958eb616))
* test_rpm2tgz: Fix expected output ([ac51b00](https://www.github.com/scop/bash-completion/commit/ac51b00358ebe510f4899db9678480a6c6a4eda1))
* test_chromium_browser: Skip test_2 if 'chromium-browser --help' fails ([0ee3982](https://www.github.com/scop/bash-completion/commit/0ee39821c67735fd16b5372cd4044775af1d1243))
* tar: add missing bsdtar, gtar, and star symlinks ([af0b3d0](https://www.github.com/scop/bash-completion/commit/af0b3d0130f61a7d7960dd8eee99529fea6e39a2))
* build: simplify symlink setup ([3bc0225](https://www.github.com/scop/bash-completion/commit/3bc0225ee28abc81800042c168b0721f5c68c49f))
* build: really reset return value before completions check ([fce6732](https://www.github.com/scop/bash-completion/commit/fce673275bf2a024f91bb455e5353e02f3690cf8))
* build: makefile whitespace tweaks ([a0949de](https://www.github.com/scop/bash-completion/commit/a0949de0f9988d3460b11820b97d6e4baab48cd3))
* test: bashrc comment and whitespace tweaks ([27daf01](https://www.github.com/scop/bash-completion/commit/27daf018539500cad68e488cc81caf064e65075c))
* test: more thorough system location interference avoidance ([7700896](https://www.github.com/scop/bash-completion/commit/77008960c7a402e96c24a5c9eab7d88ebc735896))
* test: set up BASH_COMPLETION_COMPAT_DIR in bashrc (only) ([f7e2a41](https://www.github.com/scop/bash-completion/commit/f7e2a4192e6e2980859da1a64816fab75aa11b09))
* test: reformat test_chromium_browser.py source ([7bf6281](https://www.github.com/scop/bash-completion/commit/7bf6281a47ce2fda45f8b7fec48e53e19e0f640d))
* pkg_delete: don't limit to FreeBSD ([5bc9b8e](https://www.github.com/scop/bash-completion/commit/5bc9b8eb51ccc41f78af0e108503de5edc87f101))
* tar: simplify locating tarball from command line ([26d9662](https://www.github.com/scop/bash-completion/commit/26d966207a2e4cef02a70d790f44812c1284d160))
* test_arp: Skip if ARP tables are empty ([90ede98](https://www.github.com/scop/bash-completion/commit/90ede989622143dc93c9a05a18bc23767c4bff9c))
* test: generalize check whether we're being run in a container ([1e3d3b4](https://www.github.com/scop/bash-completion/commit/1e3d3b4d40e3f6c150b9d31965f8b007ef823fc7))
* test_feh, test_makepkg: invoke grep as "command grep" ([5e706a4](https://www.github.com/scop/bash-completion/commit/5e706a433440af4fad630d1b362f7e75578cbcdb))
* test_getconf: skip if -a doesn't output any POSIX_V* ([70afc1e](https://www.github.com/scop/bash-completion/commit/70afc1ed3697c3171a004b7db2f19220117d2862))
* test_iconv: skip option completion if --help fails ([2cdac1b](https://www.github.com/scop/bash-completion/commit/2cdac1b9f24df62a1fa80c1824ee8524c9b02393))
* test_iconv: add basic file completion test ([0ba7af2](https://www.github.com/scop/bash-completion/commit/0ba7af221b3ec3ec7b1efecd3c8458068f1934b3))
* iconv: weed out ... from encoding completions ([40be1f4](https://www.github.com/scop/bash-completion/commit/40be1f491bfbeec50787cbe6bd2c6a794b19ef46))
* test: add Alpine Linux container, allow failures for now ([316289f](https://www.github.com/scop/bash-completion/commit/316289fb0837676f310925748070e1e1e87de750))
* test: support xfail in our markers like skipif, use it a lot ([b3fecab](https://www.github.com/scop/bash-completion/commit/b3fecab48c22c07015a6fef7a00d4cb0ea1b74f3))
* test: expect failures for various completions without useful --help ([0a777ff](https://www.github.com/scop/bash-completion/commit/0a777ff73bf5ddbd5de92f2bf1e3f8429e471f72))
* test_lsusb: xfail with unparseable --help ([e717ce4](https://www.github.com/scop/bash-completion/commit/e717ce4503ab646055cd6b88d4087c162a529137))
* test_wget: test --s instead of --h ([ddd4b39](https://www.github.com/scop/bash-completion/commit/ddd4b396b9a29c25715f6b2b768737b0b015c6c9))
* timeout: fallback to _parse_usage from _parse_help ([7683eef](https://www.github.com/scop/bash-completion/commit/7683eefe974d7c508c7a7c3e2aa8f318f30c2699))
* test_ifup: accept short option completions too ([071dc19](https://www.github.com/scop/bash-completion/commit/071dc199c1413146d485ee28bfa6c9a189c3681b))
* test: use one Dockerfile for all dists ([495dab2](https://www.github.com/scop/bash-completion/commit/495dab2d80001aeabfe6136bd046a0231ff5d115))
* test: run our docker script in test containers by default ([a59f00a](https://www.github.com/scop/bash-completion/commit/a59f00aeb2efa0decb3767e99c2445890a340d35))
* _pnames: adapt for busybox ps, rewrite in pure bash ([5443c81](https://www.github.com/scop/bash-completion/commit/5443c819622495fcdc759d5dd4e5c31633eab389))
* test: disallow Alpine failure on Travis ([2748b79](https://www.github.com/scop/bash-completion/commit/2748b79d678db1d06dac733b0ddc5a51a77cef1e))
* iconv, lz4, tipc, xsltproc: replace some seds with compgen -X ([32e8b93](https://www.github.com/scop/bash-completion/commit/32e8b934c4bb3089b9a2b6e1677e7c014509f734))
* test: port compgen and quote tests to pytest+pexpect ([3752208](https://www.github.com/scop/bash-completion/commit/37522086868ae6da2e3630b24056b443ba9706e0))
* test: port _variables unit tests to pytest+pexpect ([b670968](https://www.github.com/scop/bash-completion/commit/b670968232cbc91e494658b2b06330693ee42939))
* README: add some badges, tweak existing ([cd9f061](https://www.github.com/scop/bash-completion/commit/cd9f0616567eee91eb971575baabcb8d97f3780e))
* test: convert finger partial test case to pytest+pexpect ([f3537dd](https://www.github.com/scop/bash-completion/commit/f3537dd02acb7e8cc8d6150f9e38785ee75f958d))
* test: convert bunch of _filedir unit tests to pytest+pexpect ([2da46c3](https://www.github.com/scop/bash-completion/commit/2da46c3299403d93d1754b5ee2d8903cc874d267))
* test: flake8 fix ([65aa0db](https://www.github.com/scop/bash-completion/commit/65aa0db5142f29ebd8a7e5d1bae91ffe8b2db516))
* test: convert more _filedir unit tests to pytest+pexpect ([102e9a4](https://www.github.com/scop/bash-completion/commit/102e9a413101c702c1f458e55d79a861e80950a9))
* test: add basic autossh test ([06cea18](https://www.github.com/scop/bash-completion/commit/06cea18e2ff03e90d1c663614e4d90422cfce246))
* chsh, pwck: try _parse_help before _parse_usage ([696f90d](https://www.github.com/scop/bash-completion/commit/696f90d30a5cd6769be0affc166b2003de8a44e7))
* test: add bunch of basic _parse_usage use test cases ([dcef445](https://www.github.com/scop/bash-completion/commit/dcef445f19d6c879144c09c5e6e96108fbe7c933))
* cal: try _parse_help before _parse_usage ([2500b50](https://www.github.com/scop/bash-completion/commit/2500b504a16cfe8fd73caa5d1d53f377d1a90f11))
* postfix: option completion is expected to fail at the moment ([2deda5b](https://www.github.com/scop/bash-completion/commit/2deda5b49d1fe722008c3d676a4e95a551237586))
* badblocks: fix $i leak ([e8ac021](https://www.github.com/scop/bash-completion/commit/e8ac021ed13e5b110b9e0701b29d6c9704d33461))
* .gitignore: add configure.lineno ([44ed05a](https://www.github.com/scop/bash-completion/commit/44ed05ac88888bbc0d156e89b62e63d630e1c2fc))
* test: add bunch of basic _parse_help use test cases ([6b4fd9b](https://www.github.com/scop/bash-completion/commit/6b4fd9b783b1fe623f7fc1e55538a626076e5a8f))
* test: add more basic _parse_help use test cases ([c64a4ac](https://www.github.com/scop/bash-completion/commit/c64a4acd6637f0718b8dabfb53f40e8a6d8b71d7))
* test: xfail getent and pwdx option completions with unparseable --help ([ef215a6](https://www.github.com/scop/bash-completion/commit/ef215a624a3f83a0aadd02fe9ddd9da775a6aa91))
* test: zopflipng flake8 fix ([026e52a](https://www.github.com/scop/bash-completion/commit/026e52a9b419cfb251f943be892b589b8520ff55))
* test: enforce minimum pytest version ([69c94b9](https://www.github.com/scop/bash-completion/commit/69c94b9bf81811aba2b1b7f7bb7ade587880e95b))
* build: make pytest executable configurable, look for pytest-3 too ([35188d9](https://www.github.com/scop/bash-completion/commit/35188d93848de125dc82c798dbcac0d6e93fb255))
* test_pwdx: xfail more unparseable help cases ([98a7aa6](https://www.github.com/scop/bash-completion/commit/98a7aa6cfd694149a9ca3857d224fe85a4acf538))
* test: xfail unparseable mock and munin-node-configure --help cases ([139acc9](https://www.github.com/scop/bash-completion/commit/139acc9fe3b35b000d899f54e06a7b638cc3e2e8))
* pgrep: fix fallback to _parse_usage ([5cb4be4](https://www.github.com/scop/bash-completion/commit/5cb4be4220d649a78e85a6e772dcbe85622f9840))
* test: don't try to install black on Python < 3.6 ([bfe14e7](https://www.github.com/scop/bash-completion/commit/bfe14e7b8e02f97d490a3b3c8087588e5318b5ee))
* test_wsimport: xfail options test on unparseable -help ([2baab4f](https://www.github.com/scop/bash-completion/commit/2baab4f1a8ad70500cea543758aadc13cc219878))
* man: fall back to _parse_usage for _parse_help ([2e1cb45](https://www.github.com/scop/bash-completion/commit/2e1cb45991af0e52a6be93b46264ffd45097d980))
* test: add basic tox fixture ([a81a1e8](https://www.github.com/scop/bash-completion/commit/a81a1e80163147c1aabd9476507338dfa255b880))
* tox: do simple parse on tox.ini if --listenvs* yields nothing ([d7c92e6](https://www.github.com/scop/bash-completion/commit/d7c92e602fc776272724c3c37a14181a4b01edf5))
* README: badge title tweaks ([35411bf](https://www.github.com/scop/bash-completion/commit/35411bf821b66146d7dfcfc3abf4c7d7ba4a30bb))
* influx: new completion ([378865d](https://www.github.com/scop/bash-completion/commit/378865db6e61d9a3ecfd0281b80b5d75167e792b))
* test: source our profile.d test env script in docker ([3702ae0](https://www.github.com/scop/bash-completion/commit/3702ae08454974e8c847ef565a383384094a92b4))
* chromium-browser: add --proxy-server arg completion ([fef4c9f](https://www.github.com/scop/bash-completion/commit/fef4c9f784f26d50af93d0b9c4f6628a91626a5b))
* README: drop distro badges, link to Repology instead ([d2574f5](https://www.github.com/scop/bash-completion/commit/d2574f51c3cadde840a0601a561009d7a53a2ebf))
* ip: invoke the tool as $1 ([bbe88bb](https://www.github.com/scop/bash-completion/commit/bbe88bb6b1b028adb103b9cbfafc7c7168ff0667))
* test: fix required pytest version ([9cd7c03](https://www.github.com/scop/bash-completion/commit/9cd7c03f898559601e81e2a933d205d1ea1a51f6))
* test: register our pytest markers to hush warnings from 4.5+ ([c187879](https://www.github.com/scop/bash-completion/commit/c18787923bc40b4ee599946deb579eab6d3fa5b2))
* gssdp-discover: new completion ([f8fac3a](https://www.github.com/scop/bash-completion/commit/f8fac3aefd4067d039d1e088f227808259602676))
* tox: complete defaults after a -- ([c06cfb0](https://www.github.com/scop/bash-completion/commit/c06cfb053eea2d5cbe478f5179ad5e3d824d760a))
* tox: include -- in option completions ([0ee3a3b](https://www.github.com/scop/bash-completion/commit/0ee3a3b4db0a65a4ed6a2a07d5c259c1c398de59))
* test: drop sourcing our no longer existing profile.d script ([ed14353](https://www.github.com/scop/bash-completion/commit/ed143530a2811d605de198a11bc699a90da645fd))
* test: don't expect a .tox dir in fixture ([6dd937d](https://www.github.com/scop/bash-completion/commit/6dd937d3c86aa098b273002c9589f9e5ba2c5891))
* test: expect failures for bc without --help useful with _longopt ([5fd2701](https://www.github.com/scop/bash-completion/commit/5fd2701371a491176b25fa9683481d6d143675f2))
* test: skip gssdp-discover --message-type when option not available ([b0fb710](https://www.github.com/scop/bash-completion/commit/b0fb7101aaad984e21ce520b39e6416db261a354))
* xvfb-run: new completion ([5c0e988](https://www.github.com/scop/bash-completion/commit/5c0e98832a1ee4e2829d47e2fb22f495ac09b641))
* test: avoid gnome-mplayer core dump on Ubuntu 14 ([07eada3](https://www.github.com/scop/bash-completion/commit/07eada37598e19c282081102ad29d62a6e87e9f4))
* test: remove unnecessary returns after pytest.skip ([d5fa7e3](https://www.github.com/scop/bash-completion/commit/d5fa7e31e2f88a438d224adde89e15c14e173ada))
* test: fix acroread fixture dir ([1f7fdc6](https://www.github.com/scop/bash-completion/commit/1f7fdc67d3e015156d4622e6904a728f3717dadb))
* test: installpkg test fixes ([322ec19](https://www.github.com/scop/bash-completion/commit/322ec19f06d4ec2ea32c861c023fb9644985150a))
* java: make jar/zip listing work with unzip ([8b8783c](https://www.github.com/scop/bash-completion/commit/8b8783c48d58f0320fd72fa7805643a0f7615311))
* test: use sh +* as ccache command test case ([87ba114](https://www.github.com/scop/bash-completion/commit/87ba114a2da100e4002240060a41b89fb9bbfdb5))
* test: avoid some sed -r/-E runLint false positives ([970eab9](https://www.github.com/scop/bash-completion/commit/970eab95140451c04a11a515d9ee860154fce9d1))
* ipv6calc: parse help instead of hardcoding option list ([151ac18](https://www.github.com/scop/bash-completion/commit/151ac18040eb27533ebca4815c6286e2d758c0f5))
* lvm pv*, vg*: parse help instead of hardcoding option list ([a8d5489](https://www.github.com/scop/bash-completion/commit/a8d548950f6a79e14c131251091026730c2ddb49))
* test: portinstall/upgrade test case and setup fixes ([669c3fe](https://www.github.com/scop/bash-completion/commit/669c3fe142f50d1f72c57c9639169691598622a0))
* pkgutil: fix $i leak ([d375b63](https://www.github.com/scop/bash-completion/commit/d375b638160d9dea43528dc99054db0a0d5d4497))
* pkg-get: fix $i leak ([07e8dc6](https://www.github.com/scop/bash-completion/commit/07e8dc6c89c81f72093ef1ff7dcd29b87016c2ee))
* chromium-browser: Add support for .mhtml files ([a951666](https://www.github.com/scop/bash-completion/commit/a95166674def153222d46346863a456f73803877))
* _terms: combine and simplify somewhat ([f6d4614](https://www.github.com/scop/bash-completion/commit/f6d461483ea7c6b3cc6710401cca6ba50c687ed4))
* _terms: search directly from various terminfo dirs ([88ed3c7](https://www.github.com/scop/bash-completion/commit/88ed3c712df1b72d730496e736faba493488e892))
* test: mark sbcl-mt xfail due to whitespace split issues ([9838cbf](https://www.github.com/scop/bash-completion/commit/9838cbfaf9e508b1ec1ad05959d89f3cf9e5d719))
* test: explodepkg and upgradepkg test fixes ([f40a1ca](https://www.github.com/scop/bash-completion/commit/f40a1ca85658c9d7cc8366ac6766098f78b69412))
* test: always run tests which don't require tested command ([674dd80](https://www.github.com/scop/bash-completion/commit/674dd805ba3639c7b507fd997fdf6e97708d84be))
* test: add bunch of basic option parsing test cases ([faacf36](https://www.github.com/scop/bash-completion/commit/faacf36e3432fd740293788910ee6304e0b5a031))
* test: don't sort expected completion lists under the hood ([1e3d504](https://www.github.com/scop/bash-completion/commit/1e3d50429d45e44d4fe65b3c7b2f37eb6e8c7b48))
* test: hush flake8-bugbear B010 ([b6d74cc](https://www.github.com/scop/bash-completion/commit/b6d74cc99881e51e7bd9504ce55273a3f1b95def))
* test: ignore _makepkg_bootstrap in makepkg test env ([b044a4f](https://www.github.com/scop/bash-completion/commit/b044a4f9d750410e4882e5badfa3b2f8a1145e28))
* test: xfail MAC address completion without networking ([36db77a](https://www.github.com/scop/bash-completion/commit/36db77a14e516c4ab452ce57f83e4406852b83e3))
* travis: pass NETWORK as env var, so we can actually use it ([0f8187d](https://www.github.com/scop/bash-completion/commit/0f8187ddc01dabc6129f7f1673e950d03d57cbfd))
* test: fix retrieving command to test from request ([29ad382](https://www.github.com/scop/bash-completion/commit/29ad38254fa52ea159d2bc1959c80599c6b52128))
* gprof: _parse_usage, drop hardcoded option list ([bebe43d](https://www.github.com/scop/bash-completion/commit/bebe43d6e7881d39e9c03faadf2927fa08042241))
* lintian-info: _parse_help, add more option arg (non)completions ([29157d0](https://www.github.com/scop/bash-completion/commit/29157d07aafac3938fb26e93c542288d027eca9a))
* screen, smartctl, update-alternatives: _parse_help, drop hardcoded option list ([bf207da](https://www.github.com/scop/bash-completion/commit/bf207da5b3879ca5c94fd382b609b5b41f6802b1))
* sysctl: invoke completed sysctl instead of one from path to get variables ([e32ca04](https://www.github.com/scop/bash-completion/commit/e32ca04b96f4e7c78232d6d75bc3782847f8aa97))
* test: mark more tests that parse command output as requiring command ([503143b](https://www.github.com/scop/bash-completion/commit/503143bf4dd7c43088b646f81eafa4494c476b26))
* test: add require_longopt xfail helper, use it ([6d79948](https://www.github.com/scop/bash-completion/commit/6d79948d9fe5923a3ec9fcaf881fb182cb59569d))
* dmypy: new completion ([3135bc3](https://www.github.com/scop/bash-completion/commit/3135bc3780fda004a2c04e2ae007ae9ae1672f22))
* travis: generate dist tarball on alpine ([be2e8f3](https://www.github.com/scop/bash-completion/commit/be2e8f3fe6be17051a9aa6fe081b66897e0d8b43))
* wine: install for wine-development and wine-stable too ([afc5a30](https://www.github.com/scop/bash-completion/commit/afc5a303ae25afdb109464e4f63fc17783383ed5))
* perltidy: associate *.t (#338) ([96b5e07](https://www.github.com/scop/bash-completion/commit/96b5e07b585b3122d51c5c25bc4289daa96524a7))
* travis: test with Debian 10 ([c0a3c55](https://www.github.com/scop/bash-completion/commit/c0a3c5592dadcee37cf41dcf5fac2a357d1b5f67))
* ssh: option and argument completion updates (#332) ([29a14f0](https://www.github.com/scop/bash-completion/commit/29a14f0f33ea8f3e8a77070eb30d14d547f8ac2b))
* java: don't assume jar is installed ([120bf77](https://www.github.com/scop/bash-completion/commit/120bf77ad2d644eba6fb506caed08fa475a426e7))
* _sysvdirs: always return 0 ([debbff9](https://www.github.com/scop/bash-completion/commit/debbff95fe35082877ccda05153d6d87562088ea))
* test: xfail locale-gen option completion if --help is not available ([4cf7e4f](https://www.github.com/scop/bash-completion/commit/4cf7e4f52d7da127198146cf57161f9bcee546da))
* valgrind: look tool names from lib/*-linux-gnu dirs too ([6c8380d](https://www.github.com/scop/bash-completion/commit/6c8380df27f6af2704d15c2de7efbb87fe0d1f48))
* test: adjust java expectations based on whether jars can be listed ([9e351e4](https://www.github.com/scop/bash-completion/commit/9e351e41eb4bbadd94f9df48915708f78b8246b9))
* op: direct command parsing stderr to /dev/null ([d10dcdf](https://www.github.com/scop/bash-completion/commit/d10dcdf798890afcb361e6286dfbff1404d43916))
* ri: hush some warnings ([f5d99f2](https://www.github.com/scop/bash-completion/commit/f5d99f22f6da6deb4fb037a2966d8b2f8532a4f3))
* carton: new completion ([6722aaa](https://www.github.com/scop/bash-completion/commit/6722aaa018ff426953496577db2daf3980443ffd))
* CONTRIBUTING: disable e-mail bug gateway due to spam ([8ef547a](https://www.github.com/scop/bash-completion/commit/8ef547aad3566cdf83e659bda1113a0de55a4738))
* apt-get: fix pkg version completion if it contains a colon (#351) ([8f0595c](https://www.github.com/scop/bash-completion/commit/8f0595c6e2396e875aeffac03dbd8b9d5b3d10cf))
* _variables: add TERM and LC_* completion (#353) ([d1756f0](https://www.github.com/scop/bash-completion/commit/d1756f06ef9bffb1b4621c4e63e47e181ddf1086))
* cppcheck: Add new standards to --std option. (#356) ([d8df8b5](https://www.github.com/scop/bash-completion/commit/d8df8b5e2d56a5f2fa5930b7278cc2cc6b6c90b8))
* test: mark dcop and mr testcases requiring the cmd as such ([73e8faf](https://www.github.com/scop/bash-completion/commit/73e8faf4d987aef26d52c2354d4336596a7cee92))
* makepkg: fix option completion ([3566352](https://www.github.com/scop/bash-completion/commit/356635225de0fb1efecb6e17bb1284fb2c6c5a80))
* .gitignore: mypy cache ([0dd1e65](https://www.github.com/scop/bash-completion/commit/0dd1e652c6045a51a4d5c6eb2952248d43fdc88e))
* test: add minimal mypy config ([860766e](https://www.github.com/scop/bash-completion/commit/860766ea4f44ebb98497eb8990634b705dd3d4f9))
* test: python typing fixes ([af136b5](https://www.github.com/scop/bash-completion/commit/af136b50b222dcbfb9d881a8c33280b5051dc266))
* test: fix cpio users test in presence of usernames with whitespace ([20cacd2](https://www.github.com/scop/bash-completion/commit/20cacd2ebabfd1cb9178f0bccb2faf859e5469bc))
* shellcheck: add some option arg (non)completions ([000fa10](https://www.github.com/scop/bash-completion/commit/000fa10469d4704803ed48563cf656449d77232a))
* test: shellcheck config cleanups ([0f3922b](https://www.github.com/scop/bash-completion/commit/0f3922be10b83361505fffa76d7bb2931415d25e))
* bash_completion.sh: shellcheck SC2086 fixes ([ded48bb](https://www.github.com/scop/bash-completion/commit/ded48bb7ab53c1836c6ec436cb304b6823a180f4))
* _filedir: remove unused $x ([2701887](https://www.github.com/scop/bash-completion/commit/2701887f4862b29dafa0d9ecef329a766713a3fe))
* _filedir: avoid duplicate dirs internally, and a compgen -d call for files ([da99bc5](https://www.github.com/scop/bash-completion/commit/da99bc55954e9f60b9c3a9e9071ff6301d7015cb))
* curl: make @filename completion do the right thing with dirs ([aa3652b](https://www.github.com/scop/bash-completion/commit/aa3652b6b7de9aabfafa79f52c70825f23f967ab))
* pkg-config: generate Name from autotools PACKAGE ([32369a0](https://www.github.com/scop/bash-completion/commit/32369a009d14f129e000cba4a0a2d1b8fc65ccea))
* unzip, zipinfo: complete *.aab (#340) ([31b5cbc](https://www.github.com/scop/bash-completion/commit/31b5cbc8016b181675e10dd068c92008782d4196))
* pkg-config: Relative paths ([8c53025](https://www.github.com/scop/bash-completion/commit/8c5302581ad20954cfa164dcae26f1ed6277dd90))
* autotools: Replace pkgdatadir with datadir ([0cc34e8](https://www.github.com/scop/bash-completion/commit/0cc34e8de658bd11fa7f728eb9852c7e29d8d6d4))
* cppcheck: Remove deprecated option 'posix'  for '--std=' ([d1ae094](https://www.github.com/scop/bash-completion/commit/d1ae094807532e42b71c18e648b182c5c44a0daf))
* perl: fix completion with space between option and argument ([8eba6d0](https://www.github.com/scop/bash-completion/commit/8eba6d0c6b88dacd80762ada3a3bc5fe14673fb2))
* perl: indentation fixes ([1360ba9](https://www.github.com/scop/bash-completion/commit/1360ba95149d0880486d1abfdcfcf6f77355c44d))
* test: add some trivial perl -E/-e cases ([188ca8a](https://www.github.com/scop/bash-completion/commit/188ca8af208f35ebdc9895d5fd41d6cdd83d90a6))
* screen: complete first arg with serial devices ([9cde25b](https://www.github.com/scop/bash-completion/commit/9cde25bfd74bdd1cb51e46a4b1958ebd444cdbd6))
* screen: add //telnet completion ([7a8e408](https://www.github.com/scop/bash-completion/commit/7a8e408267ca21530d6d6de4ae92b9a0362e14fd))
* screen: add serial device basic arg (non)completion ([fb46fed](https://www.github.com/scop/bash-completion/commit/fb46fed657d6b6575974b2fd5a9b6529ed2472b7))
* update-rc.d: remove dead code ([845be11](https://www.github.com/scop/bash-completion/commit/845be116278719c2a4ab4d4edaee81f38cc17b9f))
* update-rc.d: indentation fix ([645cc41](https://www.github.com/scop/bash-completion/commit/645cc41deab3dce6a38120be05ab02ee1ca6bc89))
* ssh, scp, sftp, ssh-copy-id, curl: improve identity file completion ([44ce113](https://www.github.com/scop/bash-completion/commit/44ce11334803f924f60e2e2c00c8b7d3dc81c33f))
* unrar: complete on *.exe (#337) ([2b81989](https://www.github.com/scop/bash-completion/commit/2b8198919e4bc65efd8c68ade769e65f6df473f1))
* gcc: support new --completion option (#222) ([87a9e9c](https://www.github.com/scop/bash-completion/commit/87a9e9c47f028efb6178c69ced2df74b62a73639))
* test: bump black to >=19.10b0 ([540e5d1](https://www.github.com/scop/bash-completion/commit/540e5d1a7ec9599f404ec88d35aa37bf25557755))

## 2.9 (2019-04-27)

* completions/Makefile: Fix check-local in VPATH builds ([57b2c93](https://www.github.com/scop/bash-completion/commit/57b2c937c1fa8409a4416702cf5b8844233a5566))
* ssh: Order various switch cases closer to alphabetical ([cde26f0](https://www.github.com/scop/bash-completion/commit/cde26f07207d3256ec56f519256cb7a7d38d993e))
* ssh: Sync query type list with OpenSSH 7.5p1 ([655ede3](https://www.github.com/scop/bash-completion/commit/655ede3db3f56ae7669c296110d8dbef9c329b3a))
* ssh: Sync config option lists with OpenSSH 7.5p1, add some value completions ([15abc03](https://www.github.com/scop/bash-completion/commit/15abc03c71d49c1bfc328e5c860c1bd30b15feb0))
* apt-get: Complete install package=versions ([c664b07](https://www.github.com/scop/bash-completion/commit/c664b07ac98bf46c0ea416161cef0a0dbe11ab50))
* apt-get: Add indextargets to list of suggested commands ([d623786](https://www.github.com/scop/bash-completion/commit/d6237861144c897866630e2d3fb81da5d5413e81))
* apt-get: Simplify -t and friends completion, support Ubuntu ([9b531a0](https://www.github.com/scop/bash-completion/commit/9b531a08cc825ec723ef8ff78f2072ace6f85a31))
* apt-get: Sync option list with apt 1.5.1 ([f4e54d9](https://www.github.com/scop/bash-completion/commit/f4e54d918b5a367a7e665d563e2f8336553ea950))
* apt-get: Add -h/-v/-o non-completions ([c80c82c](https://www.github.com/scop/bash-completion/commit/c80c82c9dcffeb2834f1bcb3edd38a228c20624a))
* dpkg-source: Add --before-build --after-build --commit, and --print-format ([1404d3f](https://www.github.com/scop/bash-completion/commit/1404d3f995649e1f70ffbb8239acd585e8246df4))
* README: Link to various distro packages ([c8f4f87](https://www.github.com/scop/bash-completion/commit/c8f4f87070150719249fc1c585d0ec414d120ce7))
* README: Point Debian and openSUSE badges towards unstable and Tumbleweed ([2dc5865](https://www.github.com/scop/bash-completion/commit/2dc5865bb78d2d9ef131da53b15710528716054b))
* ssh: Complete all *File option args with _filedir ([7cd0090](https://www.github.com/scop/bash-completion/commit/7cd00901477878d135a844624eff795995199761))
* test: Add comment line to fixtures/_known_hosts_real/known_hosts ([a0e2ce3](https://www.github.com/scop/bash-completion/commit/a0e2ce369a28c5eb41dd8d96495457ddb9254b28))
* _known_hosts_real: Reimplement known hosts file parsing in pure bash ([71ac42b](https://www.github.com/scop/bash-completion/commit/71ac42b98c0eba39819fb8478d6443032c6ef6f1))
* mount, umount: Deprecate on Linux in favor of util-linux >= 2.28 ones ([861be75](https://www.github.com/scop/bash-completion/commit/861be7590ed96fb1aacc8b5161f10a00ae57eebe))
* .dir-locals.el: Set -O extglob for flycheck bash checks ([b49a182](https://www.github.com/scop/bash-completion/commit/b49a182cc68a9d69d702754c3adc0b17d50e52f6))
* reportbug: Add bunch of option arg (non-)completions ([81e2971](https://www.github.com/scop/bash-completion/commit/81e29716093a13dff3bde8b9f486e44f99662ac8))
* a*,ccze,curl,wget: Support completing arg of last bundled short option ([b358aa1](https://www.github.com/scop/bash-completion/commit/b358aa1ebd88e353788f7c5655e9c489fe4eba40))
* b*: Support completing arg of last bundled short option ([d664aeb](https://www.github.com/scop/bash-completion/commit/d664aebf6bd6ef5ca0602f6440887273bbe000e3))
* cvs: Add completion for the log command ([7c5a752](https://www.github.com/scop/bash-completion/commit/7c5a75277842b0ac71d1154d87534fccd86ab22b))
* chage, chpasswd: Add -R/--root arg completion ([5634cfe](https://www.github.com/scop/bash-completion/commit/5634cfe5923a0f9b83b5dc7275185578c9cac68e))
* dhclient: Parse options with _parse_usage ([5789780](https://www.github.com/scop/bash-completion/commit/57897803dbce6cf90c24bfd59a08fb39cfdfd3c0))
* dhclient: Add some option arg (non-)completions ([2a7d2e3](https://www.github.com/scop/bash-completion/commit/2a7d2e3eb540188cafda3bf0c103f59ef2b7991f))
* dselect: Parse options with _parse_help ([1b15dc6](https://www.github.com/scop/bash-completion/commit/1b15dc6884a39928cd57f48f1d900f3d9fef86d7))
* c*, d*: Support completing arg of last bundled short option ([ed24bbc](https://www.github.com/scop/bash-completion/commit/ed24bbc8e77e3fb40ef7bf730eebfaa3b06cac24))
* ether-wake: Install for etherwake as well ([b6903bc](https://www.github.com/scop/bash-completion/commit/b6903bc346f6b97d12f02007676dfc280f051fb6))
* e*: Support completing arg of last bundled short option ([008a407](https://www.github.com/scop/bash-completion/commit/008a4074c72152187195c7fd4a1e47e5ae348528))
* f*: Support completing arg of last bundled short option ([c1dfa0f](https://www.github.com/scop/bash-completion/commit/c1dfa0f5ab43516e1c31978d05548ca2c8c6008a))
* g*: Support completing arg of last bundled short option ([e00c119](https://www.github.com/scop/bash-completion/commit/e00c1195427bb092473ec7f753262e5751642d0f))
* scrub: New completion ([6b5c8d4](https://www.github.com/scop/bash-completion/commit/6b5c8d4dc5ff49e84839279ca581202acc37fce5))
* ecryptfs-migrate-home: New completion ([3f90f09](https://www.github.com/scop/bash-completion/commit/3f90f09f1b8dc32f9ffb55566bfe8ed88418c52b))
* h*: Support completing arg of last bundled short option ([1c126ff](https://www.github.com/scop/bash-completion/commit/1c126ff5c64006edc58e33d8bee4546bafb49405))
* i*: Support completing arg of last bundled short option ([665088a](https://www.github.com/scop/bash-completion/commit/665088ae3925e9d60352b898f35c449420702834))
* test: Skip scrub -p test when its --help doesn't list available patterns ([9039d77](https://www.github.com/scop/bash-completion/commit/9039d771b63b562276151239c38f760418a9e5f4))
* lftp: Support completing arg of last bundled short option, handle -s ([ff07e56](https://www.github.com/scop/bash-completion/commit/ff07e569964809400c652e4949c663e80a66fab1))
* xgamma: Comment spelling fix ([6119b2c](https://www.github.com/scop/bash-completion/commit/6119b2cf0bd96b432482c3361634140a04a77f81))
* links: Install completion for links2 too ([b1fbe75](https://www.github.com/scop/bash-completion/commit/b1fbe75475c140a7047cf8208650562332ef57ac))
* iconv: Split charset completion to _iconv_charsets, add test case ([cefd0da](https://www.github.com/scop/bash-completion/commit/cefd0daf30e7022bdbce98de88dca5ba6244c1a5))
* _filedir: Refactor to remove heredoc-dependent loop ([6ffde95](https://www.github.com/scop/bash-completion/commit/6ffde95c7ea257ff60cb51a263a8d33f4bb0898b))
* _filedir: Drop unnecessary evals ([7be0dd6](https://www.github.com/scop/bash-completion/commit/7be0dd6c9556c264dfa0fe1b6a8f4d19a2216473))
* wget: Remove nonexistent arg to _ip_addresses ([9214270](https://www.github.com/scop/bash-completion/commit/92142707893641834e177f6a928160878d6b0506))
* _ip_addresses: Add option to complete all/v4/v6 addresses, add unit test ([dc72400](https://www.github.com/scop/bash-completion/commit/dc724002cb0291880a2daacc51f2e7ad4cf2950d))
* links: Major rework, parse options from --help, add option arg completion ([173e104](https://www.github.com/scop/bash-completion/commit/173e1045361404cd9a765438885652cdcb5cef3b))
* iperf, nc: Include IPv6 addresses in bind address completions ([209b16b](https://www.github.com/scop/bash-completion/commit/209b16bf0888ce6367c06d256e43afffa33a8d90))
* chmod: New completion ([9501226](https://www.github.com/scop/bash-completion/commit/9501226cb139ac828c5079cb7c079d00277468c8))
* json_xs: New completion ([1433694](https://www.github.com/scop/bash-completion/commit/143369404d844111a7f4cbd6f19cda16d3738d16))
* README: Add Q/A on overriding a completion, modernize local install answer ([22e9a02](https://www.github.com/scop/bash-completion/commit/22e9a020adf1ab73896411ca2f3712a445062727))
* build: Do cmake, pc, and profile variable replacements in Makefile ([81ba2c7](https://www.github.com/scop/bash-completion/commit/81ba2c7e7dfbaefbafa1e8615727c9612e5fb314))
* build: Use AC_PROG_SED to locate sed ([3031df3](https://www.github.com/scop/bash-completion/commit/3031df31aba36be8fef11186a7bff5163433bae4))
* pkg-config: Complete on *.pc files ([d8667ae](https://www.github.com/scop/bash-completion/commit/d8667ae379fa325ecd43626fe9cfd5e955450e85))
* build: Improve cleanup of test/log and test/tmp dirs ([103db12](https://www.github.com/scop/bash-completion/commit/103db12fd29746ec88f69849842fa3d2f9d251d0))
* test: Run perlcritic and flake8 on perl and python helpers in Travis ([da18668](https://www.github.com/scop/bash-completion/commit/da18668c64bcaa061db00ce17c86fb4dc39ac7dc))
* lzma: Use _parse_help instead of hardcoded option list ([bca4c60](https://www.github.com/scop/bash-completion/commit/bca4c6002e9381310b3563551bf09b3865c15238))
* [jkl]*: Support completing arg of last bundled short option ([9ab622e](https://www.github.com/scop/bash-completion/commit/9ab622e76be185380f627c68cd090e69d7b8466a))
* __load_completion: Avoid bad array subscript on "commands" ending with slash ([583562b](https://www.github.com/scop/bash-completion/commit/583562b9e56207bd428497ceb96df4e1f1f53158))
* aclocal, automake: Support versioned 1.16 executables ([485d6de](https://www.github.com/scop/bash-completion/commit/485d6de3909f9d8c83eb38c0a1797ba448a3a754))
* test: Mark psql etc test cases untested if --help doesn't work ([e8e7a15](https://www.github.com/scop/bash-completion/commit/e8e7a154b2dbcad08b02396f114017d3caf02ac4))
* _xspecs: Declare as global on bash >= 4.2 ([a47bd37](https://www.github.com/scop/bash-completion/commit/a47bd375bb0f95dc6d388d4097c420bddb72ae33))
* README: Note $BASH_COMPLETION_USER_DIR ([8c67dad](https://www.github.com/scop/bash-completion/commit/8c67dadc402a370e082b372b2df7a9702ab72623))
* README: Add instructions for overriding completions system wide ([7cbbf35](https://www.github.com/scop/bash-completion/commit/7cbbf353403292395dd4d3c04e506162a457c336))
* pytest: Rename from py.test to follow upstream recommended name ([fbf2981](https://www.github.com/scop/bash-completion/commit/fbf298186fa94f001ae2ef01b4716e14fe8a544d))
* rpm: Complete --licensefiles with -q ([fb13b2e](https://www.github.com/scop/bash-completion/commit/fb13b2ef4e7a10bf29ec30301bb8b2fc8654caf3))
* ngrep, tshark: Complete on *.pcapng too ([550728e](https://www.github.com/scop/bash-completion/commit/550728eb93224da5442f6e9339cbb879a2af7300))
* ifquery: New ifup alias completion ([932d0be](https://www.github.com/scop/bash-completion/commit/932d0be8904c3a65eb32d784a4b517ad68bf93da))
* _count_args: Add support for not counting specified option args ([daf010a](https://www.github.com/scop/bash-completion/commit/daf010ad945d84f48ef672ab4905d56e34ad1886))
* ifup etc: Add option and option argument completion ([f499aa3](https://www.github.com/scop/bash-completion/commit/f499aa3d3dc985d62e99b46198300ce19e0f9d2a))
* ssh, scp, sftp: Support completing arg of last bundled short option ([645d901](https://www.github.com/scop/bash-completion/commit/645d90167c9d7cd737f49961a0730d9cb892f95b))
* completions/Makefile.am: Use install-data-hook, not install-data-local ([ee6b37a](https://www.github.com/scop/bash-completion/commit/ee6b37ad7ff5b309cbb9b886a871252abd9398fa))
* mplayer: Add common supported module music formats ([c2d4221](https://www.github.com/scop/bash-completion/commit/c2d4221655bc2267b8d992489316006a73cee498))
* mplayer: Associate with *.S[3T]M, *.med, *.MED ([264fb85](https://www.github.com/scop/bash-completion/commit/264fb85647b4b70362cf5a34a69612e241e7fdf3))
* minicom: Use _parse_help instead of hardcoded option list ([62578a3](https://www.github.com/scop/bash-completion/commit/62578a356387465ecdc878a6e3bb66395bf2b5b1))
* modinfo: Use _parse_help instead of hardcoded option list ([1397fa7](https://www.github.com/scop/bash-completion/commit/1397fa72a918f5344798faf79273067ffa324345))
* m*: Support completing arg of last bundled short option ([8f82fd9](https://www.github.com/scop/bash-completion/commit/8f82fd98cc56ac6973ff01407784c3c76b0e462a))
* mysqladmin: Reuse --default-character-set completion from mysql ([b984e6a](https://www.github.com/scop/bash-completion/commit/b984e6a04a70088ac9c0d6bdb59279274b8a7137))
* mysql, mysqladmin: Complete --ssl-{ca,cert,key} option arg ([d5ee055](https://www.github.com/scop/bash-completion/commit/d5ee055d6c30b6bc27a28547533912eb7e6b7bd2))
* modinfo: Fall back to _parse_usage if _parse_help yields no results ([d2f55d0](https://www.github.com/scop/bash-completion/commit/d2f55d0d04eafc24d4a74a15c87d029b43942007))
* cryptsetup, nc, sh: Skip option args when counting arguments ([11dee0e](https://www.github.com/scop/bash-completion/commit/11dee0e0497210443cf7a32c16ce913a62e449db))
* cryptsetup: Add some option arg (non-)completions ([68295cc](https://www.github.com/scop/bash-completion/commit/68295cc7f40063e75ddaab0f08b90dcc5a653998))
* n*: Support completing arg of last bundled short option ([02a13f7](https://www.github.com/scop/bash-completion/commit/02a13f7f0eda6db3e0017a625863c5c43af43829))
* profile.d: Avoid tested variable values being confused as [ ] operators ([3f1c884](https://www.github.com/scop/bash-completion/commit/3f1c884bef639af2cae959b04b87fb3a41eeeb7d))
* passwd: Try _parse_help before _parse_usage to parse options ([16e82b8](https://www.github.com/scop/bash-completion/commit/16e82b8c27a4ccce0cc0c4f203617f747d427b5f))
* [op]*: Support completing arg of last bundled short option ([5a654a8](https://www.github.com/scop/bash-completion/commit/5a654a834a44c9569b527b543b39fbe969efbe74))
* pyvenv: Support versioned 3.6-3.8 executables ([b0de719](https://www.github.com/scop/bash-completion/commit/b0de7196c1e211f0f832c8d7af3ce38f85f1ab94))
* querybts: Use _parse_help, not hardcoded option list, misc improvements ([00de26b](https://www.github.com/scop/bash-completion/commit/00de26bf42b9d389dae36787386e4a2ab0cf6c1b))
* reportbug: Run _parse_help and apt-cache more selectively ([ecdcdb5](https://www.github.com/scop/bash-completion/commit/ecdcdb5f240a2a63accaa1c5da35eb74fdbacef4))
* [qr]*: Support completing arg of last bundled short option ([13579f2](https://www.github.com/scop/bash-completion/commit/13579f2426ca88b1ae966bea3abc6a6326d99e83))
* jq: New completion ([8d57c41](https://www.github.com/scop/bash-completion/commit/8d57c41995f17eef59506d79d44ec4276d2086cc))
* tune2fs: Update -o/-O argument lists ([27efd8c](https://www.github.com/scop/bash-completion/commit/27efd8c1d66a47a5313c3467ce6b67b6b150ef90))
* sqlite3: Add some option arg (non-)completions ([1c2b0d7](https://www.github.com/scop/bash-completion/commit/1c2b0d778bcaf19dc13844100af3627c6367cbad))
* sshow: Add -p arg completion ([c2acf69](https://www.github.com/scop/bash-completion/commit/c2acf697c57ff931d1c943f69c2745b7cf5eea20))
* strace: Use _parse_help instead of hardcoded option list ([77539a5](https://www.github.com/scop/bash-completion/commit/77539a50d6073be3b4972ed14069002e69b3d41b))
* sudo: Parse options from help/usage output, add some long option support ([46d5d2d](https://www.github.com/scop/bash-completion/commit/46d5d2d4ea9be932a48f7e7de5a9e4ad2231a74b))
* test: Add sysctl option parsing test case ([5b8b66a](https://www.github.com/scop/bash-completion/commit/5b8b66a3feaf73bfa9780e69d0308e1d2d367f1d))
* sysctl: Recognize --pattern/-r and --load options ([0f63ecb](https://www.github.com/scop/bash-completion/commit/0f63ecb8234f97a6669a7ee0bb4a57cbe3e5cb54))
* sudo: Improve long option arg handling ([ca3b1f6](https://www.github.com/scop/bash-completion/commit/ca3b1f664f0bfa92cc688909dee68937fd638286))
* s*: Support completing arg of last bundled short option ([bd1d93c](https://www.github.com/scop/bash-completion/commit/bd1d93cede385001667f0ad205f4e900711b42c8))
* jq, sqlite3: Protect against negative array subscripts ([bd3e2f2](https://www.github.com/scop/bash-completion/commit/bd3e2f297018bdbcd81b719f47c433d8e0aa1d17))
* test: Support running with local BSD binaries, do it w/ ubuntu14 in CI ([2fe53fe](https://www.github.com/scop/bash-completion/commit/2fe53fef669a9dc077784bf3ab73db650d879ab2))
* xdg-settings: Make help parsing work on BSD ([40f1c24](https://www.github.com/scop/bash-completion/commit/40f1c24fb3109c074efdea1f897b062afccb725e))
* test: Skip jq option completion test if its --help doesn't list them ([e407508](https://www.github.com/scop/bash-completion/commit/e40750884e25700aa02f3e1ffb9a7662b05e9a47))
* tracepath: Actually use our separate completion instead of _known_hosts ([b0c0d5e](https://www.github.com/scop/bash-completion/commit/b0c0d5e201a15857c065ccea204ddaf67accfef0))
* tracepath: Add -m and -p arg non-completions ([a7ee04e](https://www.github.com/scop/bash-completion/commit/a7ee04e98f0c30cab5e53f852e6373e75872a30c))
* urlsnarf: Add -p arg completion ([8cf0ce3](https://www.github.com/scop/bash-completion/commit/8cf0ce3dc6f73aa67cee59846d1ec828814fe222))
* uscan: Use _parse_help instead of hardcoded option list ([d13306d](https://www.github.com/scop/bash-completion/commit/d13306d74fbe879398bdc86a7b0dc4fc9bd5534b))
* [tu]*: Support completing arg of last bundled short option ([26cb739](https://www.github.com/scop/bash-completion/commit/26cb739d3cd1a1935f1b5e186090ea9dfd4847fd))
* test: Fix iwspy test case ([886f4a1](https://www.github.com/scop/bash-completion/commit/886f4a1e3168b6e413e5a9cb60c2b3ed714c3734))
* [vwx]*: Support completing arg of last bundled short option ([bbd6296](https://www.github.com/scop/bash-completion/commit/bbd629670ca291e10d1d9a7a68941025d94959e0))
* fio: New completion ([c92a426](https://www.github.com/scop/bash-completion/commit/c92a4264fe8522de6040a14e8e6f9a3ed1ddf792))
* ngrep: Add "any" to -d arg completions ([507d593](https://www.github.com/scop/bash-completion/commit/507d593962ed8a305e17ac7807704ee5fc4f9ae1))
* tshark: Get available interfaces from -D output ([8d7d24d](https://www.github.com/scop/bash-completion/commit/8d7d24d949ba77cd26ae904614fffdbae05e5246))
* pylint: Option arg completion improvements ([fe69cb5](https://www.github.com/scop/bash-completion/commit/fe69cb582b42a02aebd6ea59ce6449de43da7f4c))
* mplayer: Associate with *.w64 ([6526596](https://www.github.com/scop/bash-completion/commit/6526596964b311a830507c5342818253212ec405))
* test: Mark flake8 untested if it seems broken ([a5d490a](https://www.github.com/scop/bash-completion/commit/a5d490a330be979c47f3a2b61c608c83a1e3db45))
* test: Limit number of wget option completions to avoid unresolved result ([4f242f2](https://www.github.com/scop/bash-completion/commit/4f242f2bd70bd0706061c81600402f2887d032f9))
* travis: Split long lines in script ([d765f75](https://www.github.com/scop/bash-completion/commit/d765f756036907574bcf9ecdb2be6f8ddb7bc056))
* travis: Run ubuntu14/bsd with no network ([64c1817](https://www.github.com/scop/bash-completion/commit/64c18170a44df5fe3a0c384f9b95cebe80405997))
* pydoc, pylint: Skip module completion if current looks like a path ([bdd6458](https://www.github.com/scop/bash-completion/commit/bdd64589f2466f1bdab9708aa530536c66a13bc7))
* gpgv: New completion ([ff65882](https://www.github.com/scop/bash-completion/commit/ff6588216dc1e789b091a27430e6d512f5bbfcb0))
* extra/git-post-commit.sh: Add git post-commit Docker Hub trigger hook ([03442fa](https://www.github.com/scop/bash-completion/commit/03442face6795c56ba0b89eadcc8f7e1ba1b0f14))
* _services: Try systemctl list-unit-files if systemctl list-units fails ([9f52c69](https://www.github.com/scop/bash-completion/commit/9f52c693d9eecd66f6a92343121a8230c32215b3))
* test: Run docker tests with --verbose ([d45f902](https://www.github.com/scop/bash-completion/commit/d45f9020ae30b5c70e0ddf449a6e7877ea637f44))
* test: Bump expect's match_max to 20000 by default ([bda6e45](https://www.github.com/scop/bash-completion/commit/bda6e453e79123b1adbc7b979f93437ad2e4bcf0))
* test: Fix buffer size option listing in run --help ([eb21e66](https://www.github.com/scop/bash-completion/commit/eb21e663bd82e1a6e13fef2fe272fcf7ebde6850))
* pylint: Implement comma separated --confidence arg completion ([5aee5d7](https://www.github.com/scop/bash-completion/commit/5aee5d77ebb7c1a1e242558f72b0a1dd178b9945))
* pylint: Bring -f/--format arg completion up to date with pylint 1.9.2 ([7e6a220](https://www.github.com/scop/bash-completion/commit/7e6a22079bfab2f9eb38d4743e43cadb89880a59))
* pydoc, pylint: Determine python2/3 based on command basename only ([fa6ec59](https://www.github.com/scop/bash-completion/commit/fa6ec59dfacdb7067575ad0c2d13b8494fbd4397))
* test: Limit number of pylint option completions ([9f75f4b](https://www.github.com/scop/bash-completion/commit/9f75f4bcd2da48dc24d9e5a0f8cce5658c164d94))
* test: Add pylint-3 test case ([c401847](https://www.github.com/scop/bash-completion/commit/c40184702676816ea79cd1746867ffc1e5311e8d))
* git-post-commit: Avoid some error trash when HEAD is not a symbolic ref ([579b733](https://www.github.com/scop/bash-completion/commit/579b73367d9437c1c5b33653d26be11c4a2a19b4))
* tox: Fall back to --listenvs for env list if --listenvs-all fails ([6009de8](https://www.github.com/scop/bash-completion/commit/6009de8534cd24b71d0bfda479823abcc640e4b1))
* test: Start converting test suite to pytest+pexpect ([641173e](https://www.github.com/scop/bash-completion/commit/641173e04e9819134a9ad1b02a4217f61bf7882c))
* test: Update generate for pytest+pexpect ([8b1d916](https://www.github.com/scop/bash-completion/commit/8b1d916faf9841a6bbc09a36f2ba84049ce5be40))
* test: Convert first bunch of completions to pytest+pexpect ([ff4289f](https://www.github.com/scop/bash-completion/commit/ff4289f9f2994eb807f4c0a9ec996fd78542e8c3))
* test: Don't include our magic mark in completions ([d8712ad](https://www.github.com/scop/bash-completion/commit/d8712ad6a89827fef871e10c082a7fd2b533a337))
* test: Fix completion list when there's one completion ([4fc8f71](https://www.github.com/scop/bash-completion/commit/4fc8f718d5bb0550c9e88452c43ce69437bfba57))
* test: Convert remaining simple x* to pytest+pexpect ([ae9b3a9](https://www.github.com/scop/bash-completion/commit/ae9b3a9e8313e8403acfbb1d453dd2691f0c60c1))
* test: Improve generate to handle basic args and problematic chars ([6fc2de1](https://www.github.com/scop/bash-completion/commit/6fc2de1b362dfbffa3d9156c68490d47259ba82e))
* test: Convert trivial [u-z]* test cases to pytest+pexpect ([ac47b5c](https://www.github.com/scop/bash-completion/commit/ac47b5c49a9e8e2434bb33a86d687f347d6d03cb))
* test: Add some additional simple completion test cases ([b2e0c51](https://www.github.com/scop/bash-completion/commit/b2e0c51ed5198bae5d9e1218f16967450eff93eb))
* test: Use make pytest docker executable env-configurable, default pytest-3 ([2473d1f](https://www.github.com/scop/bash-completion/commit/2473d1f9490dd3cca631a8fb5e9722e0812e4cd0))
* test: Use /root/.local/bin/pytest on centos6 by default ([6164b45](https://www.github.com/scop/bash-completion/commit/6164b45a48a5a058dcdfa7f5562ade4a126fc431))
* test: Convert trivial t* test cases to pytest+pexpect ([4205d79](https://www.github.com/scop/bash-completion/commit/4205d7921caa09edae4a183133591fa2817e727c))
* test: Convert trivial s* test cases to pytest+pexpect ([cd11331](https://www.github.com/scop/bash-completion/commit/cd113319ba73d14f6ed73782365902e6d938f10c))
* test: Add new test files to EXTRA_DIST ([6130867](https://www.github.com/scop/bash-completion/commit/6130867362edd027183303aa174d2005e405a59c))
* test: Add generated test files to t/Makefile.am automatically ([a672f55](https://www.github.com/scop/bash-completion/commit/a672f550aa0216b21ce8275a93cddb592e639481))
* test: Convert trivial [qr]* test cases to pytest+pexpect ([2b79b27](https://www.github.com/scop/bash-completion/commit/2b79b273ce80bdd076335af22249929b7d3c09af))
* test: Use /root/.local/bin/pytest on ubuntu14 by default ([36f73ad](https://www.github.com/scop/bash-completion/commit/36f73ad8a9a6cd7f29483823025c8ed06af2966c))
* test: Convert trivial o* test cases to pytest+pexpect ([9ba87c3](https://www.github.com/scop/bash-completion/commit/9ba87c39a75b17b46894ad47217d83df2b5fe051))
* test: Bulk convert lots of trivial test cases to pytest+pexpect ([cdb9a22](https://www.github.com/scop/bash-completion/commit/cdb9a224868449fefbe6447c5475b749d5a3851c))
* test: Bulk convert some more trivial test cases to pytest+pexpect ([e546577](https://www.github.com/scop/bash-completion/commit/e5465771b901ac59fbbd67d121d26654e076c79a))
* test: Make "generate" do the right thing with more partial args ([45d5ad9](https://www.github.com/scop/bash-completion/commit/45d5ad98adca66e20a6384aa825f492939d600c9))
* test: Convert some more trivial test cases to pytest+pexpect ([de97e77](https://www.github.com/scop/bash-completion/commit/de97e775e08b4c60f93908927d85b45860e91dc2))
* test: Convert some more trivial test cases to pytest+pexpect ([8c9c4f1](https://www.github.com/scop/bash-completion/commit/8c9c4f12d72c2b275e8d6d34edb2cf560d1aa93a))
* test: Convert some more trivial test cases to pytest+pexpect ([5cab3b3](https://www.github.com/scop/bash-completion/commit/5cab3b32dcb22f585f16b4a34007b053bad90cc6))
* test: Convert some more trivial test cases to pytest+pexpect ([cb17e4b](https://www.github.com/scop/bash-completion/commit/cb17e4be72e2ccf0c2704e97faa13ada8d22acac))
* test: Treat non-dash args better in generate ([ecfa09d](https://www.github.com/scop/bash-completion/commit/ecfa09d4a5629d3adbc502940156db5498922049))
* iperf: Add g/G to --format completions ([cfef384](https://www.github.com/scop/bash-completion/commit/cfef3841d1f38b724d1209a9e43885cde5b89b97))
* iperf: Install for iperf3 too ([a520baa](https://www.github.com/scop/bash-completion/commit/a520baa0c68fabf283129521c2a59bc2e9597805))
* iperf: Improve client/server specific option parsing ([51863c4](https://www.github.com/scop/bash-completion/commit/51863c4604771e04f7dca2f6f1a960fc7558b723))
* xmodmap: Use _parse_help instead of hardcoded option list ([fa278dc](https://www.github.com/scop/bash-completion/commit/fa278dcf99bfae48ced96740712b231f069c1447))
* iperf: Add g/G to --format completions ([a2277c0](https://www.github.com/scop/bash-completion/commit/a2277c0857ce13c2b4ada55206af72482c6c7d71))
* iperf: Install for iperf3 too ([e4425bd](https://www.github.com/scop/bash-completion/commit/e4425bd5d36d94b2682c8ffb7cb60d91e8814be6))
* iperf: Improve client/server specific option parsing ([c187063](https://www.github.com/scop/bash-completion/commit/c187063b955f837f9bbd22ec52ca4133aeac7d39))
* xmodmap: Use _parse_help instead of hardcoded option list ([1b60881](https://www.github.com/scop/bash-completion/commit/1b608812e47a091933b685d01bcbed03dd2107f1))
* test: Add some iperf, iperf3 and xmodmap test cases ([b3acc94](https://www.github.com/scop/bash-completion/commit/b3acc9412937abd92a9ff4d7a34515520df56b5c))
* test: Make test base work with Python 3.3+ ([83e91da](https://www.github.com/scop/bash-completion/commit/83e91da21d33be89f0aa2a07a0706b4910f4ba2c))
* test: Add support for pytest pre-test commands ([ccba78d](https://www.github.com/scop/bash-completion/commit/ccba78d32a82c2081d5d08beed2887f944f96335))
* test: Convert some more test cases to pytest+pexpect ([987aa3d](https://www.github.com/scop/bash-completion/commit/987aa3d50cc3a5c6b1dd8620cd5c5c7ed7eb1d2e))
* test: Add support for skipping individual tests based on shell command status ([6a4d824](https://www.github.com/scop/bash-completion/commit/6a4d8245f6919550f18c4534a25fd59f61d11437))
* test: Convert some more test cases to pytest+pexpect ([8145625](https://www.github.com/scop/bash-completion/commit/81456254bb4e09a09447a4429c923e478088327b))
* test: .gitignore pytestdebug.log ([24c66c1](https://www.github.com/scop/bash-completion/commit/24c66c1184c7f38d3c56fe6d4fcf8b8e38fc2514))
* test: Convert some more test cases to pytest+pexpect ([27053d1](https://www.github.com/scop/bash-completion/commit/27053d104ce140291e9d2b024c313167facaf2e3))
* test: Add support for running test case in a specified dir ([339874f](https://www.github.com/scop/bash-completion/commit/339874fdc5a5f9805fec6aaee0c4dd0f24febb6e))
* test: Convert some more test cases to pytest+pexpect ([ed64828](https://www.github.com/scop/bash-completion/commit/ed64828e9f5c883946e93771b7d9a4f1bdaa0323))
* test: Fix some regressions introduced in recent test conversions ([aaaa60d](https://www.github.com/scop/bash-completion/commit/aaaa60d67fea6322f54e2a79cb83b02f4f254e32))
* test: Convert some more test cases to pytest+pexpect ([038fd4e](https://www.github.com/scop/bash-completion/commit/038fd4e2c92ebf487ca936edff2bd3cc6512706c))
* test: Convert some more test cases to pytest+pexpect ([c32a405](https://www.github.com/scop/bash-completion/commit/c32a405000539483a4c5ad3527e297b640dd76b3))
* test: Convert some more test cases to pytest+pexpect ([e0be296](https://www.github.com/scop/bash-completion/commit/e0be296af493d3998dca9acb44f82dce636af106))
* test: Include command name in test class name, use numbered test method names ([649cd43](https://www.github.com/scop/bash-completion/commit/649cd43616fdba56648fbccaf5194c2a6022587d))
* test: Convert some more test cases to pytest+pexpect ([aece7ad](https://www.github.com/scop/bash-completion/commit/aece7ad1bbc1870ba1016f00e9047094734a480a))
* test: Add class level skipif based on bash exec result ([ae723ed](https://www.github.com/scop/bash-completion/commit/ae723ed0504c45afc1bb34fdc65d5329094ce083))
* test: Add python3 test case ([9a0fddc](https://www.github.com/scop/bash-completion/commit/9a0fddc85b8f4d7c85446ea8e0e976187a4a4658))
* test: Convert some more test cases to pytest+pexpect ([c5300cd](https://www.github.com/scop/bash-completion/commit/c5300cd5571cc432e277a8d9aff35f6f2d23eb8f))
* test: Rename BASHCOMP_* test env variables to BASHCOMP_TEST_* ([2fa7566](https://www.github.com/scop/bash-completion/commit/2fa75666ee48edc744db322621398a4ffcc07d7d))
* test: Log pexpect interaction to $BASHCOMP_TEST_LOGFILE if set ([5198bf5](https://www.github.com/scop/bash-completion/commit/5198bf5328fb5f6875b3b7c010e89efd6c92417a))
* test: Pass through $HOME and $DISPLAY to test bash ([2c32433](https://www.github.com/scop/bash-completion/commit/2c3243300316d3ebff18045cb4e0f162a41a5fce))
* test: Fixture reorganization ([c677b8e](https://www.github.com/scop/bash-completion/commit/c677b8e09bee213b6392dc4cd7364989ebb4f9a4))
* test: Add ability to selectively ignore diffs in environment ([e575c8e](https://www.github.com/scop/bash-completion/commit/e575c8ed1e3e2f84c92c845790d50d5b31426e60))
* test: Implement load_completion_for using assert_bash_exec ([dd9a6eb](https://www.github.com/scop/bash-completion/commit/dd9a6ebd14bec202ddf7809e2a18075e7e5bdcb4))
* test: Convert some more test cases to pytest+pexpect ([73f05ba](https://www.github.com/scop/bash-completion/commit/73f05bad0c3db9dcee3572b8143ff13678951ad3))
* test: Replace + with Plus in test class names ([d1c2c72](https://www.github.com/scop/bash-completion/commit/d1c2c72b9a79b97cc53b186d9c681a62f0a721c7))
* test: Mark xfreerdp as expected failure for now ([489dc30](https://www.github.com/scop/bash-completion/commit/489dc30682529d2c2cce54f02bce8da1ec54e742))
* test: Convert some more test cases to pytest+pexpect ([fff2fa3](https://www.github.com/scop/bash-completion/commit/fff2fa3098423cb1e2cfe52762c8fcf4a48fe5ee))
* test: chdir to fixtures dir in Python as well ([08a89c0](https://www.github.com/scop/bash-completion/commit/08a89c08d531333966f54a70942d0c44a703856a))
* test: Allow __load_completion to fail ([4abf2ba](https://www.github.com/scop/bash-completion/commit/4abf2bae1b5fcf58c6b981fcebf907ac83fe7107))
* test: Sort completion results in Python for ease of use in Python tests ([f40cfe0](https://www.github.com/scop/bash-completion/commit/f40cfe0dfa40f2e7d6db68d370aa98ec2cbbd7b3))
* test: Convert some more test cases to pytest+pexpect ([9e6ed13](https://www.github.com/scop/bash-completion/commit/9e6ed13f4936058e1042ad34558b95c1f46472c4))
* test: Use more conventional Python file names for tests ([9c278da](https://www.github.com/scop/bash-completion/commit/9c278dada33667ff7edcfda2c21115fb8662909f))
* test: Add support for per-test env modifications ([be3d073](https://www.github.com/scop/bash-completion/commit/be3d0735e78f1d3bc0e8eff17fcff86921bed6d4))
* test: Don't require complete marker on test methods ([9d15992](https://www.github.com/scop/bash-completion/commit/9d1599228b07da22b6ec3ae876797ce3af1bbda8))
* test: Fix jq and scrub skipif commands ([b7840bf](https://www.github.com/scop/bash-completion/commit/b7840bfccefe79189b782135aa85076723d72e24))
* test: Convert some more test cases to pytest+pexpect ([ba4c972](https://www.github.com/scop/bash-completion/commit/ba4c972333bfbf86dbc94ccc9ed406b1883eb424))
* test: Misc test suite fixes ([d1f983a](https://www.github.com/scop/bash-completion/commit/d1f983a9f5cdccaeb148b64f6887001c9182a8b0))
* test: Convert some more test cases to pytest+pexpect ([ff1e0af](https://www.github.com/scop/bash-completion/commit/ff1e0afb01a8d4a655865cf40c0a46a25405454c))
* test: Convert some more test cases to pytest+pexpect ([044e5da](https://www.github.com/scop/bash-completion/commit/044e5da3169137c8d777ab87cde5424430f65c66))
* test: Misc test suite fixes ([6de89eb](https://www.github.com/scop/bash-completion/commit/6de89eb37d459d9a455ab7bb42c5fcbd97c5077c))
* test: Convert some more test cases to pytest+pexpect ([75628f7](https://www.github.com/scop/bash-completion/commit/75628f77b481ed73120f9101e31e983057a16756))
* test: Support setting cmd=None to require no command, for unit tests ([8884ceb](https://www.github.com/scop/bash-completion/commit/8884cebab72ae8d8ac3873d595ff501c3b54e27a))
* test: Convert some more test cases to pytest+pexpect ([416fc05](https://www.github.com/scop/bash-completion/commit/416fc05ac24105a70f719419241fcff248827204))
* test: Convert some more test cases to pytest+pexpect ([c85516b](https://www.github.com/scop/bash-completion/commit/c85516be79397c24ade9c12c89fd77f364f1ad46))
* test: Use test_unit_* prefix for unit tests, to avoid name clashes ([c7b3427](https://www.github.com/scop/bash-completion/commit/c7b3427cb085d02b7559604489ded675752f58d1))
* test: Convert some more test cases to pytest+pexpect ([d5b0d1b](https://www.github.com/scop/bash-completion/commit/d5b0d1b0a98fcea8de578a31e943d0187e4f1995))
* test: Misc test suite fixes ([fddc36f](https://www.github.com/scop/bash-completion/commit/fddc36f36312eee100283d93baacf22a73952823))
* test: Convert some more test cases to pytest+pexpect ([35e94b7](https://www.github.com/scop/bash-completion/commit/35e94b736a1304344a28430127bc5cae059dac63))
* test: Misc test suite fixes ([e50dfec](https://www.github.com/scop/bash-completion/commit/e50dfeca93957f9b043538df6f7aa779d1f7f7e6))
* test: Convert some more test cases to pytest+pexpect ([3ca2039](https://www.github.com/scop/bash-completion/commit/3ca20396fdb398c6c52956efe682c9d468da7266))
* test: Convert some more test cases to pytest+pexpect ([451b3d1](https://www.github.com/scop/bash-completion/commit/451b3d1187547550cc382b2ee94c234dcf52d32b))
* gccgo: Add as a GCC completion target (#227) ([ec9aefa](https://www.github.com/scop/bash-completion/commit/ec9aefa37fc9b0b53c004481402221e517a3a3ae))
* gcc: Add g++, gcc, gccgo, and gfortran *-7 aliases ([bea1d6a](https://www.github.com/scop/bash-completion/commit/bea1d6a2419412513f11918f20fa74fd1f229403))
* __load_completion: Avoid unnecessary lookups from nonexistent dirs ([f2b76a1](https://www.github.com/scop/bash-completion/commit/f2b76a1eb254bb6399c5d7a10bec659b691704e4))
* unzip, zipinfo: Associate with *.whl ([df8bc32](https://www.github.com/scop/bash-completion/commit/df8bc32be0989cef6c4baaa30230848f9d1ea4ad))
* test: Misc test suite fixes ([d1b0a75](https://www.github.com/scop/bash-completion/commit/d1b0a756a68e234b78861abf51b40f89123548d0))
* test: Make case specific env entries shell code, not escaped ([703dcb8](https://www.github.com/scop/bash-completion/commit/703dcb860e2523f684479f73bb5caf14517d86d2))
* test: Convert some more test cases to pytest+pexpect ([0518f62](https://www.github.com/scop/bash-completion/commit/0518f62c0c17fa48c574fe2cb915f8a75f87ab59))
* test: Pylint fixes ([bbf6ff1](https://www.github.com/scop/bash-completion/commit/bbf6ff116f4be7236af996b014c24524513fe3e8))
* test: Remove kill, killall remnants ([8e21442](https://www.github.com/scop/bash-completion/commit/8e214425bd53f6a7338e96b34748653abda781c2))
* test: Mark MANPATH without leading/trailing colons test an xfail on CI CentOS 6 ([896aac9](https://www.github.com/scop/bash-completion/commit/896aac9b68cab927abf2351f4ea1041d30277156))
* nc: Add some more option (non-)completions ([4d88339](https://www.github.com/scop/bash-completion/commit/4d88339928aa064a567d5feb40694dfd24a274c5))
* test: Convert some more test cases to pytest+pexpect ([3f84586](https://www.github.com/scop/bash-completion/commit/3f8458652a65ad761b9b7f771385295c0cb43f73))
* test: Pylint fixes ([1389110](https://www.github.com/scop/bash-completion/commit/13891101fc4e11b5fb9eab10b215f1caf501dc65))
* test: Convert some more test cases to pytest+pexpect ([0db4768](https://www.github.com/scop/bash-completion/commit/0db4768883afbe01ec5f7333ee25289dee0eebd4))
* test: Convert some more test cases to pytest+pexpect ([b6f4e50](https://www.github.com/scop/bash-completion/commit/b6f4e500b2389e92b69014cf316e521ac98c567a))
* test: Convert some more test cases to pytest+pexpect ([23da5e6](https://www.github.com/scop/bash-completion/commit/23da5e63b7cee02e7f405f96cf8957b007abaf94))
* firefox etc: New non-xspec completion ([ab572bb](https://www.github.com/scop/bash-completion/commit/ab572bbf1bd33785cf0c53b7c4ba87144413ea34))
* chromium-browser, google-chrome*: New non-xspec completion ([9db48eb](https://www.github.com/scop/bash-completion/commit/9db48eb67e9a9b4479913f06b2384a6087051e81))
* test: Convert some more test cases to pytest+pexpect ([f0f0976](https://www.github.com/scop/bash-completion/commit/f0f097642fb3aa8d91c403646367b65bddd2820f))
* doc: Start documenting new pytest+pexpect tests ([58499ba](https://www.github.com/scop/bash-completion/commit/58499ba3c9d57ac2a08d9a8de29240e58d19de33))
* mplayer etc: Complete on *.crdownload partial downloads in addition to *.part ([2837a48](https://www.github.com/scop/bash-completion/commit/2837a48c7954116b4e6d4e1c911798b6e54f6c68))
* freeciv-gtk2: Install for freeciv and freeciv-gtk3, rename to freeciv ([f253e1e](https://www.github.com/scop/bash-completion/commit/f253e1e8edb4f7bd307afc5e7171e7995a4cb998))
* freeciv: Option and arg completion updates ([fcaff16](https://www.github.com/scop/bash-completion/commit/fcaff164be94317b7cafbd05a5f5826105382df5))
* isort: New completion ([c2dd674](https://www.github.com/scop/bash-completion/commit/c2dd6745661d886327bb79591a6c6911f6f5c3f6))
* gnome-screenshot: New completion ([4581c6f](https://www.github.com/scop/bash-completion/commit/4581c6fe9715a57337af06648daad78d5d326d0e))
* perlcritic: New completion ([2f8da81](https://www.github.com/scop/bash-completion/commit/2f8da814f715327db0f509c8e409e237fc9d5ff8))
* gcc: Add g++, gcc, gccgo, and gfortran *-[568] aliases ([c9c14da](https://www.github.com/scop/bash-completion/commit/c9c14da4d32f3713b3a28e5c706f92a6f0903d35))
* test: Remove leftover completion/ls.exp ([46ff7b3](https://www.github.com/scop/bash-completion/commit/46ff7b378430c5a3f456d2b096f9d16925f3f231))
* tshark: update -T and -t completions ([5869351](https://www.github.com/scop/bash-completion/commit/58693518ecb561e7f7e9a0d4b6767cbf18f4238d))
* tshark: prevent a single-character file from breaking -G completion ([8618ea5](https://www.github.com/scop/bash-completion/commit/8618ea52e48e55f04f001f0b90955b2a4a157e33))
* tshark: support .gz and .cap files for -r expansion ([a250248](https://www.github.com/scop/bash-completion/commit/a25024815f43b99e9b77a2991ecc9ab16bfe7f41))
* xmllint: Improve --encode, --pretty, and --xpath arg (non-)completions ([cf5fe10](https://www.github.com/scop/bash-completion/commit/cf5fe10fe03f67f6d477aef04585277fda3d3ee4))
* chromium-browser: consider chrome and chromium as aliases ([58da331](https://www.github.com/scop/bash-completion/commit/58da33199ff84bf12ae561071a15fa825fda8487))
* conftest: fix RemovedInPytest4Warning due to use of node.get_marker ([9249a2a](https://www.github.com/scop/bash-completion/commit/9249a2a9005a3b9c91ff6e432dd96337688254f6))
* doc: Some pytest install clarifications ([e463b89](https://www.github.com/scop/bash-completion/commit/e463b89d600cc6a2d040556e24090a083f5c98cd))
* test: fix flake8 complaints about unused imports ([27a9856](https://www.github.com/scop/bash-completion/commit/27a98567e3027fa006b3e4ee2702b14e5077d10d))
* test: fix misinterpretation of completion output in tests ([85275cf](https://www.github.com/scop/bash-completion/commit/85275cf550433a90f4875845142ad93132b16820))
* okular: Added support for xz-compressed files. ([0381d27](https://www.github.com/scop/bash-completion/commit/0381d275e085b63adf4842d0c6b3d41eeea02141))
* hunspell: New completion ([eaecbf4](https://www.github.com/scop/bash-completion/commit/eaecbf4df704d43b27672a235e14cc3bf9d309b9))
* op: New completion ([03ddd0b](https://www.github.com/scop/bash-completion/commit/03ddd0b4103d12b975a5a3be9c4b4627deae1e3a))
* mypy: New completion ([12db483](https://www.github.com/scop/bash-completion/commit/12db483cdb08b4d602050f1ae63692e8d768fee2))
* test: Convert remaining perl test cases to pytest+pexpect ([8aa8c84](https://www.github.com/scop/bash-completion/commit/8aa8c840ad636d4b31bdeaba63fb9d9a9e6c8fc1))
* 7z: add .msi support ([d6b678c](https://www.github.com/scop/bash-completion/commit/d6b678cab235fa5b27fa4961bd4a92f19081b13d))
* test: Convert more tar test cases to pytest+pexpect ([eaba3aa](https://www.github.com/scop/bash-completion/commit/eaba3aadf36751229b91215a24cb91bf3779ddb8))
* dpkg: List held packages (#250) ([95042d0](https://www.github.com/scop/bash-completion/commit/95042d01826dd01340e09e948a2525a905e336d3))
* makepkg: Use _parse_help instead of hardcoding option list ([bdbe5ef](https://www.github.com/scop/bash-completion/commit/bdbe5ef109d4942412e4f795bd29a7c79483832c))
* test: Allow unknowns options in makepkg option completion ([9227e92](https://www.github.com/scop/bash-completion/commit/9227e920dbb35f47dcdf827e4561722ef0e8426f))
* makepkg: Don't apply to other than Slackware makepkg ([d44e854](https://www.github.com/scop/bash-completion/commit/d44e8548cc8b8e4ee2656af7228248125de829eb))
* locale-gen: New completion ([6176fd6](https://www.github.com/scop/bash-completion/commit/6176fd65af83cd7737d481d195030bdd0f92d1db))
* test: Convert cancel test case to pytest+pexpect ([efd68b2](https://www.github.com/scop/bash-completion/commit/efd68b2b8b059328a7b39cc96bf4c236a0a55340))
* cancel: Add some option arg (non-)completions ([9fcc52f](https://www.github.com/scop/bash-completion/commit/9fcc52fede350af591a9dad3295299afee5705aa))
* cancel: Split long line ([896adae](https://www.github.com/scop/bash-completion/commit/896adaea7dbb703bb2b93879e5f293f2cf17063f))
* test: Convert portinstall test case to pytest+pexpect (untested) ([da37fa3](https://www.github.com/scop/bash-completion/commit/da37fa341903565379523b6e85fb7626997e1879))
* tar: Support completions for zstd compression extensions (#255) ([1d911cb](https://www.github.com/scop/bash-completion/commit/1d911cb0fe7ed2797d7ab16cd3858970b7dbae5c))
* tar: Clean up some redundant code ([a143560](https://www.github.com/scop/bash-completion/commit/a143560f315ddfbe7c4662cba8a27b93137f425b))
* ssh-copy-id: Add -i and -o arg (non-)completions ([f8d1280](https://www.github.com/scop/bash-completion/commit/f8d1280df57acfc1d0d49a9eb117f78cfb563ad3))
* test suite: Ignore _scp_path_esc in env for ssh-copy-id ([84f40ab](https://www.github.com/scop/bash-completion/commit/84f40ab5dca1e70284e3bc56773b1de6a2974b68))
* grpck: Add --root/-R arg completion ([5bd30be](https://www.github.com/scop/bash-completion/commit/5bd30be3990094f5ee6fc0468b6d24bc90465ebc))
* grpck: Parse options with _parse_help, falling back to _parse_usage ([4f64fb0](https://www.github.com/scop/bash-completion/commit/4f64fb0d7ed7a83cd888e0f25d4050e26c20a060))
* test: Add requirements.txt for installing dependencies ([b1192e2](https://www.github.com/scop/bash-completion/commit/b1192e26876ee01ae81b9dce2736a37beb9aeeba))
* doc: Some test dependency doc updates ([9aaa0b8](https://www.github.com/scop/bash-completion/commit/9aaa0b80eb6798613781d8aa133975e5267eddcd))
* _filedir_xspec: Fallback to suggesting all files if requested (#260) ([464c822](https://www.github.com/scop/bash-completion/commit/464c822ee44abb2f3d5a9f559099a303505668cf))
* doc: Update docs on generating simple tests ([840f342](https://www.github.com/scop/bash-completion/commit/840f342d31c47debc9fdf69d6bb1733d24166608))
* test: Expect failure in gkrellm if there's no X display ([eeb63ea](https://www.github.com/scop/bash-completion/commit/eeb63ead44b0eb28fe4c6f817a292f9a11e29436))
* arp: New completion, somewhat incomplete ([a0d40e5](https://www.github.com/scop/bash-completion/commit/a0d40e53918a639a1550e4592871c86289badb78))
* test: Be more consistent with "CI" env var examination and xfails ([0bfa552](https://www.github.com/scop/bash-completion/commit/0bfa5524f9ed318c5fcb44a4c73cdfb055bddc98))
* unzip, zipinfo: Associate with *.xar (eXist-db application package) (#257) ([c263a3f](https://www.github.com/scop/bash-completion/commit/c263a3fc8ca098cb42eab88026e1ffa2fa809ec0))
* test: Fix arp CI (non)expectations, remove redundant test case ([a7ece55](https://www.github.com/scop/bash-completion/commit/a7ece5532328f4901fa366f4ee23d9e3f7488674))
* tcpdump: Various option and their arg completion updates ([e453c40](https://www.github.com/scop/bash-completion/commit/e453c401a9af815ea0c112f854fbca5a4e180d39))
* doc: Note email issues gateway ([872164a](https://www.github.com/scop/bash-completion/commit/872164adb46a5c106442274d1cb9a56ecf88d723))
* Travis: Remove unused PYTEST env var ([ff7bba7](https://www.github.com/scop/bash-completion/commit/ff7bba7eab63e32a456a97146145c08449b95ceb))
* pydocstyle: New completion ([ebee700](https://www.github.com/scop/bash-completion/commit/ebee7008d2119c271f0ff216294b36fe9b2923c3))
* test: Fix _count_args test_7 to test intended case ([7093e83](https://www.github.com/scop/bash-completion/commit/7093e8301852c0924dd94861e0d7ac47665bc761))
* _count_args: Add 3rd arg for treating option-like things as args ([3809d95](https://www.github.com/scop/bash-completion/commit/3809d9558f2af6c63b675a1385f008f0ad3af388))
* chmod: Fix file completion after modes starting with a dash ([75ec298](https://www.github.com/scop/bash-completion/commit/75ec298eaae391258da9418a55d3d47d855e5e9e))
* .gitignore: Add pytestdebug.log ([3e9011a](https://www.github.com/scop/bash-completion/commit/3e9011aa012ef349d499f8ab96b019fe6b21e09a))
* sysctl: Treat -f as alias for -p/--load ([76549bb](https://www.github.com/scop/bash-completion/commit/76549bb4e56bb4f968d686da52ca8b1ea46754a1))
* xrandr: match the output name exactly for --mode ([3556fcb](https://www.github.com/scop/bash-completion/commit/3556fcbf478cbfe1ea631a98267056bb8a460240))
* chmod: Fix "-" completion ([a269a60](https://www.github.com/scop/bash-completion/commit/a269a603207f5bf7b3a9eb17b8abeeb2159f159e))
* _xspecs: Simplify bash version check ([ec630f5](https://www.github.com/scop/bash-completion/commit/ec630f50d62eb5599dd5c882da75681b69bf5608))
* test: Remove some no longer used old test suite code ([a492414](https://www.github.com/scop/bash-completion/commit/a4924148f0c846cb8640fbc45e1a466058a16744))
* tshark: Support preferences (-o) completion with memoization ([9b4098f](https://www.github.com/scop/bash-completion/commit/9b4098f748302568ff8c014f634fa822e962f029))
* tshark: fix completion of -Xlua_script option ([50f52f7](https://www.github.com/scop/bash-completion/commit/50f52f77cf5c5339dfe7e1c3ff2991ea4a912c59))
* test/t: Avoid trailing backslash in Makefile.am's to appease automake ([5c73b79](https://www.github.com/scop/bash-completion/commit/5c73b7948d924d48e84ea67000fdb7b07970f06b))
* build: Include test/t in dist tarball ([698ef1d](https://www.github.com/scop/bash-completion/commit/698ef1dae56f366c729dd5c46cafc49e9fedb965))
* test: Clean up and docker-ignore __pycache__ dirs ([a6916d6](https://www.github.com/scop/bash-completion/commit/a6916d682f5ded8e99c7918e27d6105e82cc7591))
* test: Skip ifup options test if it doesn't grok --help, not in CI ([3780c80](https://www.github.com/scop/bash-completion/commit/3780c802fa6bdc915c4e667c003189c22d0f68bb))
* test: Mark some xfails based on if in docker instead of in CI ([5af8f75](https://www.github.com/scop/bash-completion/commit/5af8f759b0ab7625e275873321db322c594bd750))
* inotifywait: New completion ([8bea1ec](https://www.github.com/scop/bash-completion/commit/8bea1ec8f0e8a708bd22be5d7eddcdf37a19fcae))
* _ip_addresses: Avoid completing ipv4 ones with -6 ([ebeecf8](https://www.github.com/scop/bash-completion/commit/ebeecf872dc0925e34177f862c371ad6e4010f08))
* test: Convert _ip_addresses unit tests to pytest+pexpect, extend some ([186d47d](https://www.github.com/scop/bash-completion/commit/186d47dff33c8c2deea2c903dcc82f251a3d9bd0))
* inotifywait: Avoid some false positive event names ([82a50b0](https://www.github.com/scop/bash-completion/commit/82a50b07719860a293c0dd12b53892ed3a86612d))
* inotifywait: Fix -e completion with BSD sed ([cfa6432](https://www.github.com/scop/bash-completion/commit/cfa6432743b2107e4b7bba0ebe17a1dd8eac7ba7))
* inotifywatch: New completion, common with inotifywait ([2aa57d1](https://www.github.com/scop/bash-completion/commit/2aa57d13202f15bf057ff5134ee6a56686032211))
* man: Fix completion when failglob option is enabled (#225) ([b3a25cf](https://www.github.com/scop/bash-completion/commit/b3a25cfe429b8c87d9194c2d9042349ba71979c9))
* test: Add pre_cmds support for completion fixture ([e56da98](https://www.github.com/scop/bash-completion/commit/e56da98dac7188b72c4df420c82452c438119636))
* test: Add man failglob test case ([c1dd7f2](https://www.github.com/scop/bash-completion/commit/c1dd7f22995ba8c7c6064e47e2c557abdc2ddf78))
* test: Rename completion.line to .output ([7d7032b](https://www.github.com/scop/bash-completion/commit/7d7032bfcfcd47f7ed3372c9c7ac95851d8402c0))
* test: Match Python's default locale unaware sort in bash setup ([c46536a](https://www.github.com/scop/bash-completion/commit/c46536aa6e6e79764075800b5b747fea7e20bc2c))
* test: Refactor/improve completion results checking ([0d9d375](https://www.github.com/scop/bash-completion/commit/0d9d375fed79651c8cd179df8bef032730506a5d))
* __parse_options: Avoid non-zero exit status ([5ecb9d7](https://www.github.com/scop/bash-completion/commit/5ecb9d7e1372961f4f983b1fd0adaa273ac06bc4))
* test: Convert remaining _parse_help unit test to pytest+pexpect ([a2c9da2](https://www.github.com/scop/bash-completion/commit/a2c9da2355e984eea8c9296e707bc77187d12b0d))
* ifstat: New completion ([50251dc](https://www.github.com/scop/bash-completion/commit/50251dcaa79308c3926e331db944734d9aafa76e))
* test: Fix test generation wrt results checking improvements ([39ab16a](https://www.github.com/scop/bash-completion/commit/39ab16a9be35c8942b2fa5371ea98ac1acb2a6ca))
* iperf, iperf3: Add some option arg (non-)completions ([cd2b18a](https://www.github.com/scop/bash-completion/commit/cd2b18a53145f9f4dcca05ee583cc9b221d86f4f))
* test: Convert remaining man test case to pytest+pexpect ([c10ece5](https://www.github.com/scop/bash-completion/commit/c10ece5f7646bbc7ffeb85943da1b45de85b8772))
* ifstat: Make work with iproute2 version ([0b44673](https://www.github.com/scop/bash-completion/commit/0b4467343aeaab4d8760943bf26955505814b908))
* test: Remove unnecessary autouse=True from fixtures ([cfd66c2](https://www.github.com/scop/bash-completion/commit/cfd66c236a19ec40dba7bfbf31cafff9cb537da9))
* .gitignore: Add .python-version (for pyenv) ([2527706](https://www.github.com/scop/bash-completion/commit/2527706c0dbc65f125529101690087ad128d33ab))
* test: Clean up man tmp dir ([e13640e](https://www.github.com/scop/bash-completion/commit/e13640eac170580211fed402473effc7f410670d))
* test: Remove unnecessary ri xfail ([a4dd187](https://www.github.com/scop/bash-completion/commit/a4dd187c52e6270ae3dcc09c48a9f613b234f886))
* jsonschema: New completion ([15fe639](https://www.github.com/scop/bash-completion/commit/15fe6395eb447a7e66a7aec07be2963c5f6c2131))
* xfreerdp: Update for more modern xfreerdp ([7499fef](https://www.github.com/scop/bash-completion/commit/7499fefbd487196f78300e58f65138901862c128))
* adb: Deprecate in favor of one shipped with the Android SDK ([479cc27](https://www.github.com/scop/bash-completion/commit/479cc270c594c3c1655519eba8745933b50d120c))
* test: Convert remaining tar test cases to pytest+pexpect ([509878d](https://www.github.com/scop/bash-completion/commit/509878dcbc86b3528aac7b15e83203a3f7bfec38))
* test: Fix declare test case with bash 5.0 ([89a0ed7](https://www.github.com/scop/bash-completion/commit/89a0ed758de066dec353687b6506b512f2901cf8))
* test: Expect failure for chown all users test as non-root ([78bb3c0](https://www.github.com/scop/bash-completion/commit/78bb3c040f40bd903f7898812b3d2e95c4e7f47e))
* xm: Deprecate completion for obsolete command (#284) ([c306a41](https://www.github.com/scop/bash-completion/commit/c306a418288d2e6cc94dac0f3f1443385405f3d1))
* ssh: support RemoteCommand and SyslogFacility options ([8b2f90e](https://www.github.com/scop/bash-completion/commit/8b2f90e2174f8babb139097228d7923ef2ca4e90))
* test: sort t/Makefile.am EXTRA_DIST in C locale ([17984a2](https://www.github.com/scop/bash-completion/commit/17984a2baa943b2166bc7217212dfa6d57a852d7))
* test: rewrite "generate" in Python, fix trailing backslash in EXTRA_DIST ([7a9913f](https://www.github.com/scop/bash-completion/commit/7a9913ffa8c1508823ba05f832895d20b6a178a2))
* xfreerdp: reinstate support for old versions with dash option syntax ([5f4f529](https://www.github.com/scop/bash-completion/commit/5f4f529ed3755a682e999d93f87dc5ac518ab51f))
* test: remove no longer needed completion/*.exp ([2515f72](https://www.github.com/scop/bash-completion/commit/2515f726bd7195d8c80fdc8442fbd48bffe7398a))
* test: avoid interrupting magic mark output ([1ea87c9](https://www.github.com/scop/bash-completion/commit/1ea87c99707fdc40c956560220e5a49d9e376383))
* test: Increase expect pty to 160 columns ([b01cfe1](https://www.github.com/scop/bash-completion/commit/b01cfe1a4fed490ef1705f5c52e3c93e5187da49))
* pytest: complete pytest-xdist --dist, --numprocesses, and --rsyncdir ([141e5e6](https://www.github.com/scop/bash-completion/commit/141e5e663789aa2243a0aa23c1bbe0b8c620e991))
* post-commit: trigger on test/requirements.txt too ([51aa5c3](https://www.github.com/scop/bash-completion/commit/51aa5c3588fc22a3fef1313f5ed51cdce1c6a7f9))
* test: add dependency on pytest-xdist ([eabce5b](https://www.github.com/scop/bash-completion/commit/eabce5b598b98489a831d44ec3e94b29e57926a6))
* extra: add git pre-push hook for triggering Docker Hub builds ([7ab7a9b](https://www.github.com/scop/bash-completion/commit/7ab7a9bd418134b6c83d57f538dc5674b930e9a3))
* test: use pytest-xdist ([0087574](https://www.github.com/scop/bash-completion/commit/0087574d5d130529c46d6c6a0b274439df652204))
* python: make warning action list reusable ([fb0bb60](https://www.github.com/scop/bash-completion/commit/fb0bb6086864835d5bc71dfb1b43636767120267))
* pytest: complete --pythonwarnings/-W arg ([6fad7d8](https://www.github.com/scop/bash-completion/commit/6fad7d8f411e734004a4f9b893dd32c266ed156f))
* copyright: add 2019 ([95ae4bb](https://www.github.com/scop/bash-completion/commit/95ae4bbb17bb1ace29c4222fc7841ab69df23237))
* _longopt: don't complete --no-* with file/dirname arg ([dd80f35](https://www.github.com/scop/bash-completion/commit/dd80f35279afd4f056dc191767b9869c9649d476))
* nsupdate: new completion ([80ba367](https://www.github.com/scop/bash-completion/commit/80ba367c4e62c63927b2ffe6884ed9756ace5195))
* _longopt: pick first long option on a line, not last ([54abb2e](https://www.github.com/scop/bash-completion/commit/54abb2e3c495e6732af6546b0fe41e0a5cd6922f))
* _longopt: simplify regex, use printf instead of echo, drop unnecessary sort ([c1ec2e1](https://www.github.com/scop/bash-completion/commit/c1ec2e1b00913d9dc38e5dcf52bfbea4a2a9eff8))
* test: add some _longopt unit tests ([6fdb0e2](https://www.github.com/scop/bash-completion/commit/6fdb0e2c3e47125ce29c2de0929d893452c2f595))
* test: include test_unit_longopt.py in dist ([4a73043](https://www.github.com/scop/bash-completion/commit/4a73043f46be3f939c857f04673a659d8d10b0ac))
* modprobe: append = to module parameter completions ([12d589e](https://www.github.com/scop/bash-completion/commit/12d589e705ccf8a44d4ce158c1c1c9885dce71ee))
* dnssec-keygen: new completion ([6e008e2](https://www.github.com/scop/bash-completion/commit/6e008e200b700a303059a8c9658d3bcc79c11f98))
* shellcheck: new completion ([fd37d6f](https://www.github.com/scop/bash-completion/commit/fd37d6f506013a42d35841b88d19851f192cf4ad))
* ulimit: new completion ([2bf12d7](https://www.github.com/scop/bash-completion/commit/2bf12d7efb2ab73f5948d3c7868ff4c0273a71de))
* ulimit: improvements when -a is specified ([6bc7bba](https://www.github.com/scop/bash-completion/commit/6bc7bba0315d64b1285861ad16a3f708d5437926))
* ping, tracepath: parse options primarily with _parse_help ([09fa92d](https://www.github.com/scop/bash-completion/commit/09fa92d75e7b542c5ae601ba1fbd9c0155dbb66e))
* modprobe: module parameter boolean values ([83b4b21](https://www.github.com/scop/bash-completion/commit/83b4b21d4075a88bd8e615b7e2da91520bd0a028))
* bzip2: recognize *.tbz2 as bzipped ([c3e5447](https://www.github.com/scop/bash-completion/commit/c3e54476693dabecc1c8d52150001739d1404412))
* : remove spaces immediately within $() ([90e380b](https://www.github.com/scop/bash-completion/commit/90e380b935afefbe63e4b68e4448a1bbb701696a))
* : remove whitespace after redirections ([689beea](https://www.github.com/scop/bash-completion/commit/689beea52bd43bb45d96645f965e211351fbe579))
* msynctool: code cleanups ([0fddd55](https://www.github.com/scop/bash-completion/commit/0fddd5512587cf717a9eb39ba422a80f8f7000e8))
* : spelling fixes ([2a93236](https://www.github.com/scop/bash-completion/commit/2a932367917a9d7349bf049257e10840caa9c4da))
* svn, svk, wget: use _iconv_charsets ([63257ba](https://www.github.com/scop/bash-completion/commit/63257ba4f8466fad3e4e219259554a0e8627def8))
* : make _parse_usage fallbacks more concise ([3c10d4d](https://www.github.com/scop/bash-completion/commit/3c10d4d88015e096a0ee840d6a008508607cb81b))
* valgrind: look up tools from libexec dirs too ([662cb72](https://www.github.com/scop/bash-completion/commit/662cb7267515ee6b26de9bea833a570a2df80be2))
* .dir-locals.el: use flycheck-sh-bash-args ([77f2855](https://www.github.com/scop/bash-completion/commit/77f2855e10f08e9789908d7a91d42c407b67ea32))
* : format Python code with black ([2c1a531](https://www.github.com/scop/bash-completion/commit/2c1a53114896d2f7b8bff075fd16fb811f17b311))
* : format Perl code with perltidy ([ee73bf7](https://www.github.com/scop/bash-completion/commit/ee73bf7dce346d2ab20bd76d060c2906553c7792))
* test: check for perltidy errors and warnings ([9be5de1](https://www.github.com/scop/bash-completion/commit/9be5de1e2ef3c9186c9cb317c12198073b50ed44))
* test/tools: run all tools, don't stop at first failure ([4ebda77](https://www.github.com/scop/bash-completion/commit/4ebda77e88fce26e7e88abe035a6daae33a6c72a))
* : arithmetic expression related cleanups ([834339f](https://www.github.com/scop/bash-completion/commit/834339fafabf9cab22c7708ca3c964e3fa8b769b))
* test/tools: fix exit status incrementation ([9f4a18f](https://www.github.com/scop/bash-completion/commit/9f4a18fa20944d2cd2f30e196ddc9495c28566ac))
* gdb: relax core filename pattern ([c59a2d0](https://www.github.com/scop/bash-completion/commit/c59a2d0950cb5b20da46598889f31bc17ac3658c))
* _parse_help: look for long options somewhat more eagerly ([3f88944](https://www.github.com/scop/bash-completion/commit/3f88944e550b082984c71175e7a09a5f7801285e))
* test: add some gdb non-core files ([6fa7458](https://www.github.com/scop/bash-completion/commit/6fa745848937b9714ca58cd0675d44c0f0145ea8))
* test: port some more ssh test case to pytest+pexpect ([d2bec62](https://www.github.com/scop/bash-completion/commit/d2bec62f609e4ce4117ba74d5a81a45ad8e23369))
* ssh: don't offer protocol v1 specific options if it's not supported ([aba5560](https://www.github.com/scop/bash-completion/commit/aba556019b7707e24093cc1c1ae082bec743ba3e))
* : whitespace tweaks ([2f71b27](https://www.github.com/scop/bash-completion/commit/2f71b27557b196888971f2ada06f71f238939f00))
* various: apply file vs dir special cases also when invoked with full path ([82ffbb2](https://www.github.com/scop/bash-completion/commit/82ffbb2d157bc401c0838fe98146fa89ecf8c56b))
* phing: don't complete -l with files ([a8c4417](https://www.github.com/scop/bash-completion/commit/a8c4417ae5931fb9b172e3ff85d18e81061da8a3))
* phing: fix getting just a tab for options on CentOS 6 ([e41f07e](https://www.github.com/scop/bash-completion/commit/e41f07e0be7aa22d19ad9f877ffe7ea5d1c99ce6))
* : add missing "ex: filetype=sh" ([f237c25](https://www.github.com/scop/bash-completion/commit/f237c25fdf55ee1ce7076e2265ab5f80255efaaf))
* _upvar: delete, unused ([b1a8a7d](https://www.github.com/scop/bash-completion/commit/b1a8a7d1c318d6fb2ffc5f050c887defa47ff77e))
* Revert "_upvar: delete, unused" ([6970e6d](https://www.github.com/scop/bash-completion/commit/6970e6d62cf6ec1f6c79119843ad495b55f0d6de))
* _upvar: deprecate in favor of _upvars ([4f35b0c](https://www.github.com/scop/bash-completion/commit/4f35b0cd7f6240addb68aa3127abe2a6dcc31411))
* : error output consistency, use bash_completion prefix ([2a04042](https://www.github.com/scop/bash-completion/commit/2a04042d530ed5dd1b746073f8ffc059c42f3fa2))
* : more arithmetic expression consistency ([c8c1b4d](https://www.github.com/scop/bash-completion/commit/c8c1b4da2f11f03db9665ca974fbe3d616b471b4))
* tshark: ignore stderr when parsing -G, -L, and -h output ([7abc83e](https://www.github.com/scop/bash-completion/commit/7abc83ee03fbe61d82b568479a5c50ad543a09c5))
* test: improve tshark -O arg completion test ([4548e71](https://www.github.com/scop/bash-completion/commit/4548e71e90bf591e537eaed2e7890fb85650cb2b))
* tshark: speed up tshark -O completion ([b930bfc](https://www.github.com/scop/bash-completion/commit/b930bfc75af6229d456c58282e8bf80e04cfca5a))
* apt-cache: protect showsrc against regex specials ([6e2ddea](https://www.github.com/scop/bash-completion/commit/6e2ddea986a3462070e6eafb098fc3d3f16bd835))
* test: adjust _get_comp_words_by_ref test to changed error output ([56703df](https://www.github.com/scop/bash-completion/commit/56703df67ddfaeccb0cc2fd7f529c63527f031a0))
* synclient: remove unused local variable "split" ([366488f](https://www.github.com/scop/bash-completion/commit/366488f2e3a806490eb1d7d6bcf674a8ea771f90))
* mypy, mysql, xmms: don't complete unknown split long option args ([1a698d5](https://www.github.com/scop/bash-completion/commit/1a698d526c5b6747c9b40f23c1d91db421c9846b))
* apt-get: protect source against regex specials ([22c5c3c](https://www.github.com/scop/bash-completion/commit/22c5c3c286575bd782f956e16abe4522474ae75a))
* AUTHORS: remove unrelated project association from my entry ([8a74451](https://www.github.com/scop/bash-completion/commit/8a74451ec83af0741fff9d17003a13012fd1a00d))
* test: shorten long gdb test core file name so tar doesn't croak on it ([e83bf9b](https://www.github.com/scop/bash-completion/commit/e83bf9b0a4e76d2ad361884a3c65eefbd685403f))
* iptables: improve existing table arg parsing ([97a91f8](https://www.github.com/scop/bash-completion/commit/97a91f8993f45823c21fbc309c00915a0c1572e8))
* test: add script to run shellcheck, run it in Travis, allowing failure for now ([f21c67f](https://www.github.com/scop/bash-completion/commit/f21c67f17fcc909745d59e3647398c50b2cd8eb9))
* ebtables: improve existing table arg parsing ([996a203](https://www.github.com/scop/bash-completion/commit/996a203f07a075633baed3960763f34534ea2447))
* test: add invoke-rc.d test case for not repeating already given options ([e0bb03a](https://www.github.com/scop/bash-completion/commit/e0bb03a588ba57a77424a19f690c1413b3de8086))
* _included_ssh_config_files: doc grammar fixes ([bae4a3d](https://www.github.com/scop/bash-completion/commit/bae4a3d11ecb86767573c9b03aed85287a8c415a))
* _included_ssh_config_files: store found included files in an array ([6d01267](https://www.github.com/scop/bash-completion/commit/6d012674ee28fd25f9bfa64b6409d7eb99844b61))
* : shellcheck error fixes ([f1c32f4](https://www.github.com/scop/bash-completion/commit/f1c32f4b85227a4da5dbb6fc137a7703a4efe20a))
* test: add make -C test case ([ea3c900](https://www.github.com/scop/bash-completion/commit/ea3c9001f42f31d49a13962586b71469aa416a4d))
* make: quote eval array definitions to work around shellcheck SC1036 bug ([abe266c](https://www.github.com/scop/bash-completion/commit/abe266ce4d26c37cf8ad681136cc8fd7edd28d4e))
* ri: shellcheck error fixes ([d45f9fa](https://www.github.com/scop/bash-completion/commit/d45f9faf432947ecf0e178d8be50f4c3163c2b4c))
* travis: fail on shellcheck errors ([e146ef5](https://www.github.com/scop/bash-completion/commit/e146ef59c53ce311fb1f30f06499aa3c91ffb3f1))
* travis: don't fail DIST != tools ([a3129ac](https://www.github.com/scop/bash-completion/commit/a3129ac624adf390b072f63f98c6f8be552b0415))
* travis: run shellcheck on bash_completion.sh.in too ([0eebaef](https://www.github.com/scop/bash-completion/commit/0eebaef0a4296bf0c37c8fcdd0931701b25abb88))
* tar, valgrind: avoid some herestrings ([3d3905c](https://www.github.com/scop/bash-completion/commit/3d3905cbb8232091c3b656567a843b97b8bc6e11))
* test: make runLint search for herestrings ([8510da8](https://www.github.com/scop/bash-completion/commit/8510da8fb20658fbb18f3f0744214ddfc473f542))
* test: move default shell option from run-shellcheck to .shellcheckrc ([4dc8da5](https://www.github.com/scop/bash-completion/commit/4dc8da5db79dd7d79312cdd139ab0422eb6fb532))
* shellcheck: disable bunch of warnings when in "-S warning" mode ([a36f57f](https://www.github.com/scop/bash-completion/commit/a36f57f922d461558064be39f8cfb64288f64cbf))
* arp, ccze, ifstat, inotifywait, makepkg: invoke sed with "command" ([5ff11ce](https://www.github.com/scop/bash-completion/commit/5ff11ced5729f9258b75b633fc366360b277cf4c))
* __parse_options, 7z: avoid herestrings ([ff5fb58](https://www.github.com/scop/bash-completion/commit/ff5fb5864dc41629f216dbea0a1fbf892ded4e66))
* CONTRIBUTING: note runLint and run-shellcheck ([07408a6](https://www.github.com/scop/bash-completion/commit/07408a6b3974c442f35dedf46ef49292b8664166))
* CONTRIBUTING: add upstream vs bash-completion considerations ([34fb914](https://www.github.com/scop/bash-completion/commit/34fb91447ff9a92e8976732f042ba20cfa615326))
* doc/testing: remove lots of legacy info, add some new ([d31f2c9](https://www.github.com/scop/bash-completion/commit/d31f2c9adbcab984754ecd93b44778e1e7a673d8))
* xvnc4viewer: code cleanups ([3e193ed](https://www.github.com/scop/bash-completion/commit/3e193edea316461c2bf5d799ca90cce9c056e301))
* ssh: fix suboption completion with combined -*o ([8d2c976](https://www.github.com/scop/bash-completion/commit/8d2c976c11e379f28430f1ae15be3764755dcffb))
* ssh: make option completion case insensitive ([0984db6](https://www.github.com/scop/bash-completion/commit/0984db6747e3f3ef1e73cdadc4447b6cb091cc83))
* ssh: make -o protocol completion less hardcoded ([73d8304](https://www.github.com/scop/bash-completion/commit/73d830445339df51eba37129c0151a250d290d84))
* 7z: add some TODO notes on parsing "i" output for extensions ([7748282](https://www.github.com/scop/bash-completion/commit/7748282e1bff081b0507b09146cbbafab3c8cb1a))
* : avoid shellcheck SC1010 ([a3aa845](https://www.github.com/scop/bash-completion/commit/a3aa84519e338d246925bf816b12f96224ba47ad))
* : avoid shellcheck SC1007 ([659805b](https://www.github.com/scop/bash-completion/commit/659805bd2a834d2e58075b1807083eef54aa73fb))
* dist: include various *.in explicitly ([f2f8c7b](https://www.github.com/scop/bash-completion/commit/f2f8c7b3c69039c0f1fe09e33c85c1075bdd409c))
* test: use parallel make in docker test ([8efece6](https://www.github.com/scop/bash-completion/commit/8efece6123393c07c127375c9f754407d7702a20))
* test: use $(RM) ([72b47aa](https://www.github.com/scop/bash-completion/commit/72b47aacefae825b670a1892665b74a6b690194e))
* test: group dejagnu related stuff in one place in Makefile ([ce95fe4](https://www.github.com/scop/bash-completion/commit/ce95fe40fd88a2c425aece0b69544cc989da0b3e))
* test: run pytest tests with make check ([5a53bc1](https://www.github.com/scop/bash-completion/commit/5a53bc1c24c672f774fde210c8bdd2f26992a58b))
* test: run distcheck in docker, ditto test suite driven by it ([bdf40aa](https://www.github.com/scop/bash-completion/commit/bdf40aab2528d0c32a7fad97d3a1156b042ff81c))
* test: add fixtures/make/extra_makefile to CLEANFILES ([47fc6d7](https://www.github.com/scop/bash-completion/commit/47fc6d7c217326aed607e21c8525084f3f0f6118))
* travis: reindent with 2 spaces by usual convention ([54030ad](https://www.github.com/scop/bash-completion/commit/54030ad7738689d0709648e48d0177a1c9d00014))
* travis: deploy release dist tarball to github ([8d91ee2](https://www.github.com/scop/bash-completion/commit/8d91ee269e4086cc1d3550865b3f9533796717b3))

## 2.8 (2018-03-17)

* test suite: Limit number of screen -T completion matches ([1c5d13e](https://www.github.com/scop/bash-completion/commit/1c5d13ee8c60cc2ef22751d9e876296be731d749))
* test suite: Support overriding default match buffer size (#141) ([e6a56b9](https://www.github.com/scop/bash-completion/commit/e6a56b97e05d5df29f61b0e23650a65f1492f1fb))
* lftp: Support ~/.local/... bookmark location (#144) ([1e347ee](https://www.github.com/scop/bash-completion/commit/1e347eef6a9de89855aba8a8b92f25be1fe18c17))
* test suite: Limit amount of output from process name completion ([f206607](https://www.github.com/scop/bash-completion/commit/f206607cbde36b46473a11cce9b3ebfd75ec36f4))
* ktutil: Don't leak i and command environment variables ([fb8e5aa](https://www.github.com/scop/bash-completion/commit/fb8e5aa106463cf0ef3a5920010d4805ae22e985))
* test suite: Add bunch of missing basic test cases ([ca61c5e](https://www.github.com/scop/bash-completion/commit/ca61c5e60eb422fd90ef940fec3aceba4a6a5dff))
* uscan: Don't leak cword and words environment variables ([0ab85ec](https://www.github.com/scop/bash-completion/commit/0ab85ec0a4177bee07253eacac1faa072ff64ff9))
* xm: Don't leak args and commands environment variables ([29fd200](https://www.github.com/scop/bash-completion/commit/29fd2001928af49898184b9ada988f63f924a097))
* test suite: Enable wine in ubuntu14 ([9ccc3db](https://www.github.com/scop/bash-completion/commit/9ccc3db8834168b7af65e76ef320dda01b38046e))
* test suite: Install aptitude in ubuntu14 container ([1fc5d30](https://www.github.com/scop/bash-completion/commit/1fc5d3022333fa08d3fbb5bc4995cf5acbaf7e7b))
* aptitude-curses: Use aptitude completion ([c8d40ea](https://www.github.com/scop/bash-completion/commit/c8d40eab48735c0ef601b2a227d42594b7511e24))
* groupdel: Parse and handle long options ([e987744](https://www.github.com/scop/bash-completion/commit/e987744d342812185d675ea428a197de7394051b))
* curl: Fix -x etc option argument hostname completion ([3708590](https://www.github.com/scop/bash-completion/commit/3708590603920a6e67be0df3f22924e4ea236fc9))
* : Protect _known_hosts_real from user input treated as options ([8706bc1](https://www.github.com/scop/bash-completion/commit/8706bc1d70873698d0d456254f415322343f6b03))
* aptitude: Add keep to commands list (Debian: #867587) ([5ed2fc4](https://www.github.com/scop/bash-completion/commit/5ed2fc4a9a604c780da48e87964d4ac21fc7a85d))
* test suite: Add basic hid2hci and munin-node-configure test cases ([c498655](https://www.github.com/scop/bash-completion/commit/c4986556555f5a6f45761ad2fdc3a1a2623f5b76))
* test suite: Add info and pinfo option test cases ([deba602](https://www.github.com/scop/bash-completion/commit/deba602bec10e4dda56ebe2908404e13ebd1286d))
* test suite: Limit amount of info and pinfo test output ([bb8645b](https://www.github.com/scop/bash-completion/commit/bb8645b5ff71e532de2bc9678f6e77d7009b0b37))
* : Protect shopt reset from non-default $IFS ([5240618](https://www.github.com/scop/bash-completion/commit/5240618033fecc3032a421d39c2eaeb2e4c20d57))
* alias: Fix completion followed by = (#146) ([2f21940](https://www.github.com/scop/bash-completion/commit/2f2194011db46beaa4e9eb44705bc6a8a08c56ab))
* oggdec: New completion ([0dc16d3](https://www.github.com/scop/bash-completion/commit/0dc16d38076cdea90a00d7e346ab545e88abf1d5))
* Add support for .lzo extension (--lzop) to tar (#155) ([49a8dd8](https://www.github.com/scop/bash-completion/commit/49a8dd857628807460468db92a38233c50c83196))
* Add support for .lz4 extension to file-roller (#158) ([5268e43](https://www.github.com/scop/bash-completion/commit/5268e43824773ccaf6c3cd48d1d4a5185a6a11d0))
* lsusb: New completion ([4e323d9](https://www.github.com/scop/bash-completion/commit/4e323d9a71e9bbd1c4432c3ade3794073a88d3d7))
* lspci: New completion ([ea3d2b2](https://www.github.com/scop/bash-completion/commit/ea3d2b249413aeda28b51e7eaf545be243c0684a))
* test suite: Skip fedoradev GPG checks at least for now ([2ef9387](https://www.github.com/scop/bash-completion/commit/2ef9387bc4628e4c5203a63f14b66d5765763589))
* test suite: Drop no longer needed fedoradev /usr/bin/which workaround ([53feba3](https://www.github.com/scop/bash-completion/commit/53feba31e9b49340605304258e18ff06baacaa22))
* vpnc: Improve config completions ([c695083](https://www.github.com/scop/bash-completion/commit/c695083125fb6ce4f944039fc94745a1a33d30cf))
* vpnc: Add some option argument (non)completions ([0086850](https://www.github.com/scop/bash-completion/commit/0086850c28c41cb383249ba47d44edb8b3ec3b53))
* iptables: Parse options from --help output ([0bdc11d](https://www.github.com/scop/bash-completion/commit/0bdc11d8c9838abf055fc7403fa9739eb5d29820))
* iptables: Avoid stderr trashing when invoked as non-root ([c8c61f7](https://www.github.com/scop/bash-completion/commit/c8c61f70f9f2cee5b9f7f6276a52af1424dbc380))
* iptables: Use invoked command instead of hardcoded "iptables" ([1b5c6e8](https://www.github.com/scop/bash-completion/commit/1b5c6e87e4517326c778dd67ba174828f2f40697))
* README.md: Whitespace cleanup ([a6e3655](https://www.github.com/scop/bash-completion/commit/a6e3655747120aa1782a17b414ffc2e6f80c8b64))
* ebtables: new completion (#150) ([334b47c](https://www.github.com/scop/bash-completion/commit/334b47cf663c5d7c8f197970a340e745f47cd55f))
* test: Use prebuilt docker hub bash-completion images ([ecaec9a](https://www.github.com/scop/bash-completion/commit/ecaec9a86470c6d6299a6522c0fddde44ff0b5f6))
* rfkill: Rename to _rfkill to avoid conflict with util-linux >= 2.31 ([d962f3a](https://www.github.com/scop/bash-completion/commit/d962f3a3be2b5c3811e546890c99b19694c2a08e))
* test suite: man cleanup ([c8ff683](https://www.github.com/scop/bash-completion/commit/c8ff6833a9f113d9feb29f750ae638436f64cbf3))
* test suite: Make man test subject names less generic ([78f4cec](https://www.github.com/scop/bash-completion/commit/78f4cec642381e7bfe25c22c8c7fde15775431e7))
* test suite: Add bunch of man and MANPATH test cases ([3b2fd04](https://www.github.com/scop/bash-completion/commit/3b2fd04caa12b720d941d27b751e76a32af8290e))
* man: Don't use $MANPATH directly (#161) ([e6a4715](https://www.github.com/scop/bash-completion/commit/e6a471511dfdc230ff3eed65ccba09b6d7d30262))
* unzip, zipinfo: Associate *.gar (#165) ([e5ee408](https://www.github.com/scop/bash-completion/commit/e5ee408a5aee2f1c9d898150e256540d020fc5ee))
* pylint: Install for pylint-2 and pylint-3 too ([fee4f3d](https://www.github.com/scop/bash-completion/commit/fee4f3d6c97c4cc48650533d96c16faa71171b39))
* pylint: Invoke python3 to search for modules if command contains 3 ([d63b33d](https://www.github.com/scop/bash-completion/commit/d63b33d415bf3103daa2aaeead9860084da0d8f3))
* tox: New completion (#163) ([89fb872](https://www.github.com/scop/bash-completion/commit/89fb872bcf9b6cc508a8079a7d203d31a2694620))
* tox: Avoid stderr spewage when -e invoked without tox.ini ([1ac8895](https://www.github.com/scop/bash-completion/commit/1ac88953a8a81d7509648ebe9f8751319bca3609))
* tox: Include ALL in -e completions ([9678f55](https://www.github.com/scop/bash-completion/commit/9678f55dabe921b62dd848bfa38b4d1ffffc21ca))
* tox: Remove spurious executable bits ([609ed0d](https://www.github.com/scop/bash-completion/commit/609ed0de65732f9442a9c17d12ba88c0ba855cef))
* xdg-settings: New completion ([6cf792b](https://www.github.com/scop/bash-completion/commit/6cf792bf3dffcafdfb5765bd0b300e3c10ace9e2))
* test: run bash with --norc to avoid system bashrc ([77ea9fc](https://www.github.com/scop/bash-completion/commit/77ea9fc444db7d240cffc2cbf1b38f0b12075aed))
* test: Add some comments regarding bash init in library.exp ([215301d](https://www.github.com/scop/bash-completion/commit/215301dfb869b803b1dbada879d8e37ca6641e41))
* test: Remove things moved to library.exp from bashrc ([6495d88](https://www.github.com/scop/bash-completion/commit/6495d886d8e86ef8f54cc0aacbd08838482e6d58))
* Whitespace ([3fa189e](https://www.github.com/scop/bash-completion/commit/3fa189ee0b4be4da9aa8c9ac34ddead27e74f861))
* test: Add files to test older ri with ([a85f96c](https://www.github.com/scop/bash-completion/commit/a85f96c75dc81ee1e5a24433ac7741d414a77c0b))
* ri: Fix integrated ri 1.8 class completion ([9035568](https://www.github.com/scop/bash-completion/commit/903556864dc366c55ce2904821ef31a4920babb4))
* apt-get: Complete *.deb on install if argument contains a slash ([a36231e](https://www.github.com/scop/bash-completion/commit/a36231e81eaeb272777abe581dce06e988f219a3))
* reportbug: Add -A/--attach arg completion ([339abbb](https://www.github.com/scop/bash-completion/commit/339abbbaa07c824a278fc1e836e25b335b314676))
* reportbug: Don't hardcode option lists, split option args at = ([1d44276](https://www.github.com/scop/bash-completion/commit/1d4427621185a6f005232c6d769109dadd46f795))
* ssh-keygen: Add -E arg completion ([3807a99](https://www.github.com/scop/bash-completion/commit/3807a996dbf8f4a94c4cb1be5861c0f228584225))
* xdg-mime: New completion ([24e7c26](https://www.github.com/scop/bash-completion/commit/24e7c266748b575358f978a73b0b5ff02a5868ed))
* test: dpkg,ls,_tilde: Skip gracefully if no uniq user for completion is found ([95a04fe](https://www.github.com/scop/bash-completion/commit/95a04feab494e55c14de866e02a8a8bfbae398ec))
* test: Ignore duplicates in find_unique_completion_pair list ([d2f14a7](https://www.github.com/scop/bash-completion/commit/d2f14a7b7504d05d9cbdd048cf546fb55fbbfbd8))
* ssh: Declare $prefix closer to use ([2516a25](https://www.github.com/scop/bash-completion/commit/2516a257081be11b49a3755358d1ca39463b7162))
* ssh: Add -J/ProxyJump completion ([cc6667e](https://www.github.com/scop/bash-completion/commit/cc6667e293c670b04b0d827d16935681b9de7083))
* _known_hosts_real: Document -a better ([693282c](https://www.github.com/scop/bash-completion/commit/693282caaa206676fc4d1d17713877d630b07d65))
* nproc: New completion ([cb7877c](https://www.github.com/scop/bash-completion/commit/cb7877caa89184e28ae7b6b58d1d1c42abdeb9d1))
* getconf: New completion ([55ed68d](https://www.github.com/scop/bash-completion/commit/55ed68dab30d66dce7be89e6e4a8fc846cbe4044))
* pv: New completion ([f6f5f4f](https://www.github.com/scop/bash-completion/commit/f6f5f4f1fe8e225e06ca5174e6e36899a7baf81c))
* cryptsetup: Update option lists ([89d1a3f](https://www.github.com/scop/bash-completion/commit/89d1a3fe4a0e0e977244c43be399cb13890bff45))
* dpkg: Add -V/--verify arg completion ([bb0a82f](https://www.github.com/scop/bash-completion/commit/bb0a82f3dc5d60561362c8259eb9a4bbdac701bd))
* lowriter,localc etc: Use corresponding oo* completions ([fbd52a5](https://www.github.com/scop/bash-completion/commit/fbd52a5e31747beb4974da97b9d3ed4f6ceb7a61))
* perltidy: New completion ([9cddfdf](https://www.github.com/scop/bash-completion/commit/9cddfdf1b777e028a9dbf32b442a3ce2629d20cf))
* flake8: Various option arg completion improvements ([15389e6](https://www.github.com/scop/bash-completion/commit/15389e66c92d61a4a1de593dd4017d76d7e2a0ef))
* pycodestyle: New completion ([0de48aa](https://www.github.com/scop/bash-completion/commit/0de48aa77f2b84ed409d983279f8b1334242de2f))
* travis: Don't build local docker images, use vskytta/bash-completion ones ([f1a1e14](https://www.github.com/scop/bash-completion/commit/f1a1e14092db8613d0274a37073f078c1689a94e))
* test: Work around broken centos/fedora postfix config in non-IPv6 setup ([5db0365](https://www.github.com/scop/bash-completion/commit/5db036505cb9d50651b4e5035e040435256dfffc))
* test: Add "postconf -" test case ([ac73726](https://www.github.com/scop/bash-completion/commit/ac73726de7421a4a61ba46a2c30795c9080dc24a))
* Revert "travis: Don't build local docker images, use vskytta/bash-completion ones" ([ac9c468](https://www.github.com/scop/bash-completion/commit/ac9c468c9375289effa39741c048c6aa154164e1))
* test: Try to skip postconf variable test on broken postfix configs altogether ([4ba2e73](https://www.github.com/scop/bash-completion/commit/4ba2e73104a323143a8060ef4e1e5098a183e445))
* test/docker: Tweak work dir, add bash as default cmd ([e0a4385](https://www.github.com/scop/bash-completion/commit/e0a438549221ed4e8be0b6e5487bc5f62c2479ca))
* python: Support completing dotted module hierarchies ([db9f81b](https://www.github.com/scop/bash-completion/commit/db9f81be1673d432afeb85fce8a35e93d9e48643))
* lsscsi: New completion ([079b7ac](https://www.github.com/scop/bash-completion/commit/079b7ac19124e1d59db176465d7b7ca65884f7d1))
* radvdump: New completion ([5b3e79e](https://www.github.com/scop/bash-completion/commit/5b3e79e25df9c6d50eb23d465008a71bbac7dbfd))
* java: Complete *.war ([c1912bf](https://www.github.com/scop/bash-completion/commit/c1912bfd080d4eadba8bc81754ba7b2723115d78))
* ssh,ssh-add,ssh-keygen: Complete pkcs11 options with *.so ([0fb4ae2](https://www.github.com/scop/bash-completion/commit/0fb4ae2fea44b857f854af8b02c838fbbb1700aa))
* kldunload: Show modules with digits ([6f17a85](https://www.github.com/scop/bash-completion/commit/6f17a85fad28c0770bb4fe704c2ed5e92e242029))
* kldunload: Increase robustness of compgen filters (#185) ([e2193c7](https://www.github.com/scop/bash-completion/commit/e2193c771a4b3284fc0e50593902d9edbf60d8c1))
* _known_hosts_real: Add option to filter IPv4 and IPv6 addresses ([0c40185](https://www.github.com/scop/bash-completion/commit/0c40185fcf70ba6f7dfddbc3933bcab22a339c46))
* ping*,ssh,scp,sftp,tracepath6: Filter IPv4/IPv6 literal addresses ([b547f22](https://www.github.com/scop/bash-completion/commit/b547f2231a1d6c588bf425e5cd1f0bb6012fc562))
* geoiplookup: New completion ([7c7dfe8](https://www.github.com/scop/bash-completion/commit/7c7dfe8b1c4b98cea0b7b4fc254207f16651eb87))
* xdg-mime,xdg-settings: Fix inclusion in tarball ([3e9e03b](https://www.github.com/scop/bash-completion/commit/3e9e03ba3494668cbe663f36231445e15c2f005f))
* tox: Complete comma separated -e arguments ([eb8a7e7](https://www.github.com/scop/bash-completion/commit/eb8a7e760088cc0fc15f9f1310092e6ac85738f2))
* mplayer: Disable user config when parsing options ([df36cae](https://www.github.com/scop/bash-completion/commit/df36cae3f85fbf2c386560f4e2da62bb7a67196b))
* test suite: Some more mplayer and mencoder coverage ([c71f5fc](https://www.github.com/scop/bash-completion/commit/c71f5fcf25ef321074a9f0b215784b0e28c37058))
* : Comma separated opt arg completion improvements ([021058b](https://www.github.com/scop/bash-completion/commit/021058b38ad7279c33ffbaa36d73041d607385ba))
* xine etc, ogg123, mplayer -audiofile: Associate with *.oga ([b102367](https://www.github.com/scop/bash-completion/commit/b10236717bae7e1a072031fa9cbf0006b66b4057))
* dpkg: Fix man page section in comment ([e16b320](https://www.github.com/scop/bash-completion/commit/e16b320ae1eb898f45f7eb891d036c059a2c0040))
* dpkg: Complete --vextract on deb files ([facefd6](https://www.github.com/scop/bash-completion/commit/facefd6dd0ea0aab8005c8a42ef125e594ed637d))
* dpkg-query: Fix -W/--show completion ([78cdbe1](https://www.github.com/scop/bash-completion/commit/78cdbe1bb8f7ab597a020a1c606154ccd4e39eea))
* ccze: New completion ([e858751](https://www.github.com/scop/bash-completion/commit/e858751feb53ca76b48bc9cbd1e4021044c0c34d))
* make: Pass script to sed as parameter instead of using process substitution ([153d6d4](https://www.github.com/scop/bash-completion/commit/153d6d4fab1faafbd33fccb0ba8a99964f05a95b))
* openssl: Add completion for the genpkey, pkey, pkeyparam, and pkeyutl commands ([19759ad](https://www.github.com/scop/bash-completion/commit/19759ad14d825896997f70fb37a9f34b98a28903))
* _avaiable_interfaces: Get rid of eval ([edb7d59](https://www.github.com/scop/bash-completion/commit/edb7d599242a184769a3aed2a7167c354cab6033))
* __load_completion: Code cleanup ([5e69954](https://www.github.com/scop/bash-completion/commit/5e69954bd1dd6e346d1cb1e44a6d5b03d2c106fb))
* __load_completion: Load "xspec" completions dynamically too ([e6781be](https://www.github.com/scop/bash-completion/commit/e6781bebfff6da5e3b5ce244c103e35d06dee9c6))
* mkdir: Complete on files in addition to dirs ([dbb3870](https://www.github.com/scop/bash-completion/commit/dbb38708e39bc8cb463ccaab294b5d1ae1e173f6))
* mkdir: Complete files without appending space ([b0babf8](https://www.github.com/scop/bash-completion/commit/b0babf873b21eb2f1d936e6e2288b451c1a58778))
* Bump copyright years to -2018 ([e306ffd](https://www.github.com/scop/bash-completion/commit/e306ffda0e8e9eb38ea676f7d0916d68d0cb87f3))
* __expand_tilde_by_ref: Eval tilde expansion only, simplify ([21f410a](https://www.github.com/scop/bash-completion/commit/21f410a860c20ab79a9afebcf8a0ca8f7fba9d08))
* dd: Omit space only when offering one completion ending with = ([cea7cce](https://www.github.com/scop/bash-completion/commit/cea7cce326235531fa36c2e10a2b907b266186a2))
* dd, find, gcc: Remove unnecessary tilde expansion ([6927248](https://www.github.com/scop/bash-completion/commit/692724843c93fdbf4f253f26c62ad1a9334f73f1))
* test: Add assert_complete_homedir, use in dpkg and ls ([ec85ac4](https://www.github.com/scop/bash-completion/commit/ec85ac48e3a38360c86d89abb8fe6ed7ea2568fc))
* bzip2, gzip, and other compressors: Use _tilde instead of _expand ([3c4fc36](https://www.github.com/scop/bash-completion/commit/3c4fc36fd65f414960b5ad1c17bb88f720bca20b))
* test: Add some _expand unit tests ([3b8dfd3](https://www.github.com/scop/bash-completion/commit/3b8dfd34709d621d928a61fb04a811a96fb15c03))
* _expand: Reuse __expand_tilde_by_ref and _tilde logic, clean up ([6365265](https://www.github.com/scop/bash-completion/commit/63652650a4f43938e2b7195084ad03cf4e93ff36))
* info, man, rsync: Defer _expand invocation ([add4e3c](https://www.github.com/scop/bash-completion/commit/add4e3cd443da011e8302b5d1fcfb5673d850f7a))
* test/unit: Whitespace tweaks ([69c318a](https://www.github.com/scop/bash-completion/commit/69c318a339901699b430af699145a8ff62394894))
* test: Fix getting username in non-login shells ([6ae330f](https://www.github.com/scop/bash-completion/commit/6ae330f1ab90f15d97477a8c254dd29f166ecdb6))
* make-changelog.py: Use python3 ([5f1d238](https://www.github.com/scop/bash-completion/commit/5f1d2381a54dc236b0419e78f1a733f2d033ed93))

## 2.7 (2017-07-01)

* pdfunite: New *.pdf completion ([65239a9](https://www.github.com/scop/bash-completion/commit/65239a91a4fface3f1b183b17c79548916461077))
* test suite: Fix __expand_tilde_by_ref test expectation output ([ff2afaf](https://www.github.com/scop/bash-completion/commit/ff2afaf4f8a89411b65e0f035509db1b6372e292))
* Makefile: update default compatdir (#132) ([00a7b9f](https://www.github.com/scop/bash-completion/commit/00a7b9fb79ece368064cce41a74be5dd56f6b266))
* pyflakes: Remove redundant xspec completion ([0a32296](https://www.github.com/scop/bash-completion/commit/0a32296840e928d25729a8661cd4d7196db3479e))
* test suite: Generalize xspec completion install check ([1a7137f](https://www.github.com/scop/bash-completion/commit/1a7137fdf61a50a1ada5fe8cd3ae5c9dafddedc1))
* Make user completion file configurable, disable in tests ([d231255](https://www.github.com/scop/bash-completion/commit/d231255201468e0b5a79c28d9a0e4b246dfeec90))

## 2.6 (2017-06-27)

* xine etc: Associate *.webm and *.weba (#112) ([eee71d2](https://www.github.com/scop/bash-completion/commit/eee71d240feacbce8fe3dcde4fe9d8f730849d7f))
* mplayer: Associate *.weba (#112) ([82dbb85](https://www.github.com/scop/bash-completion/commit/82dbb853bb2bfb720ece29f2fe806ee8f3695b52))
* xine etc: Associate uppercase *.WM[AV] ([7250d5b](https://www.github.com/scop/bash-completion/commit/7250d5b721c37b507127285b94a1083de379dfdc))
* Add missing sidedoor to .gitignore (#114) ([440f65c](https://www.github.com/scop/bash-completion/commit/440f65ce444c00519bc9fa6b5a87fbd4f10a2e67))
* Bump copyright years ([922bc58](https://www.github.com/scop/bash-completion/commit/922bc58693ad5254142b7aed9a160f2291ddf087))
* eog: Associate with *.j2c and *.jpg2 ([c91b36d](https://www.github.com/scop/bash-completion/commit/c91b36dd8cd31ea95a952c1fd858d1f3738f3998))
* xv: Associate with *.j2c, *.j2k, *.jp2, *.jpf, and *.jpg2 (Debian: #859774) ([0de9b7b](https://www.github.com/scop/bash-completion/commit/0de9b7b086574213d21b28c25d1bd5d517880ba0))
* Travis: Switch tests to docker, update to Ubuntu 14 ([82d85c1](https://www.github.com/scop/bash-completion/commit/82d85c1380707461ad8511b95349d8a56b9961ea))
* test suite: Add WIP Fedora dev config ([6ed2857](https://www.github.com/scop/bash-completion/commit/6ed285773db08b1200a5063a58a9c0cce9f94c53))
* synclient, udevadm: Avoid use of posix char classes for awk ([2abecdc](https://www.github.com/scop/bash-completion/commit/2abecdc60e06aebc397a7eb62ae053d34fc68315))
* ssh-keygen: Make option parsing work with OpenSSH < 7 ([375bd17](https://www.github.com/scop/bash-completion/commit/375bd174aa5750b98c2a01fd3a42b669a7fefb9d))
* (docker): Run completion tests with xvfb-run, e.g. for gkrellm ([c8f5dac](https://www.github.com/scop/bash-completion/commit/c8f5dac01ee1cd26452bc7e67c75a86924a272e4))
* tshark -G: Avoid stderr noise when running as superuser ([ea6377d](https://www.github.com/scop/bash-completion/commit/ea6377d2b03be91d6af2fc11c6581eb7aafa1ec4))
* (test suite): Fix mmsitepass completion test ([596d961](https://www.github.com/scop/bash-completion/commit/596d961152b94fa45470e565d9adf2c3997011c1))
* mr: Avoid stderr trash and test suite failure if man is N/A ([be12bb2](https://www.github.com/scop/bash-completion/commit/be12bb2514730576ce170a1e7c1f136e20f0f385))
* (docker): Pull in missing fedoradev xvfb-run which dependency ([e468e44](https://www.github.com/scop/bash-completion/commit/e468e44b32092531dfd3f3c764dccaa5cad1ccb2))
* (test suite): Don't assume lists set up in newlist test cases ([a9494fe](https://www.github.com/scop/bash-completion/commit/a9494fe4501067388783a6dc454ebe5c2413d3b6))
* (test suite): Delete trailing whitespace ([ca64e75](https://www.github.com/scop/bash-completion/commit/ca64e755f13b64b1eaec0f4233ff6286e11f68b2))
* newlist: Parse options from --help, add some arg non-completions ([472db27](https://www.github.com/scop/bash-completion/commit/472db27ae1c5869f7a2b84fb8a1597229d4807a2))
* (test suite): Don't assume mounted filesystems in quota* tests ([a9fc3dc](https://www.github.com/scop/bash-completion/commit/a9fc3dc7766f0415acb4aba3d14776c97e5ea9fc))
* (test suite): Make chkconfig test behave better in container ([fcaac9a](https://www.github.com/scop/bash-completion/commit/fcaac9ac97dc963d87023fe4fda32c722677bfee))
* (test suite): Unsupport various kill, renice cases if ps is N/A ([b5d81bf](https://www.github.com/scop/bash-completion/commit/b5d81bf4479c6454822f21537ec7aa47d592ed39))
* (test suite): Fix tar test case for ones having --owner-map ([b719ee2](https://www.github.com/scop/bash-completion/commit/b719ee2f27393008f2f6dc5722241f118e822906))
* (test suite): curl has lots of options, add more test prefix ([4698dbe](https://www.github.com/scop/bash-completion/commit/4698dbe5ccc807333b4128aa1463a90062167ee5))
* (test suite): Fix perl -d* test cases with no Devel::* installed ([9941b3d](https://www.github.com/scop/bash-completion/commit/9941b3d63d35913e44e41a7b1a05c449543f7fda))
* (test suite): Ensure /usr/(local/)games is in $PATH ([fda9c43](https://www.github.com/scop/bash-completion/commit/fda9c43c233e16380704ae0ed6500fa91c38b167))
* (test suite): Add bunch of packages to ubuntu14 container ([ffeb91a](https://www.github.com/scop/bash-completion/commit/ffeb91a28bee72038215d968a1f9237a8ad0599e))
* (test suite): Add our ./configure to PATH to test it, test opts ([07cbd0d](https://www.github.com/scop/bash-completion/commit/07cbd0df40629a4b0da1eb1ccaf201e7aba1463a))
* (test suite): Add basic hping3 test case ([9f2efc5](https://www.github.com/scop/bash-completion/commit/9f2efc525d76db97b8bbf702c7b8f243451e5bf7))
* (test suite): Test lsof on ubuntu14 ([db40d50](https://www.github.com/scop/bash-completion/commit/db40d5084e781399a5b1276caf8a5a43315ab7b2))
* unshunt: Parse options from --help ([a7caf57](https://www.github.com/scop/bash-completion/commit/a7caf57cc49feae235cc15daa4bdf5b3d7e4a491))
* (test suite): Install jshint to ubuntu14 container with npm ([4813904](https://www.github.com/scop/bash-completion/commit/48139045355dd9ddee5e67938f6e61123542d7ee))
* (test suite): Add mailman bin dir to PATH for some mailman tools ([5eee6c5](https://www.github.com/scop/bash-completion/commit/5eee6c571df8156fec45c9dfbd0b733b73014346))
* (test suite): Install jshint globally in ubuntu14 ([987f251](https://www.github.com/scop/bash-completion/commit/987f251c02b0d367ef0a7ababf18eccc6be64458))
* alpine: Parse opts from -h output, add some opt arg completions ([7c5bc49](https://www.github.com/scop/bash-completion/commit/7c5bc49e29cf20ed5a338bad0da178dcecc63b6f))
* (test suite): Add basic alpine test case ([282cdba](https://www.github.com/scop/bash-completion/commit/282cdbae6272e966e9fb0f91b06cf415ef929dda))
* (test suite): Fix alias and cd cursor position tests ([35f1cd8](https://www.github.com/scop/bash-completion/commit/35f1cd844227c9ddcc36f316d87c4de9fe9db426))
* (test suite): Add unrar to ubuntu14 container ([77fa46d](https://www.github.com/scop/bash-completion/commit/77fa46de688341bf229d27d05b51af434b7b5e0c))
* (test suite): Install some things N/A in ubuntu14 to fedoradev ([f6c0d3a](https://www.github.com/scop/bash-completion/commit/f6c0d3a207c59caac74f4943126ea147d8c6896b))
* (test suite): Avoid interference from user and system dirs (#87) ([1d3b6da](https://www.github.com/scop/bash-completion/commit/1d3b6da02a731e43077dc7dbef51036381b7889c))
* (test suite): Accept non-whitespace single word in assert_complete_any ([476b260](https://www.github.com/scop/bash-completion/commit/476b26017e4168ad4be0b8d66ee9db03315fbdac))
* (test suite): Load tested completions dynamically ([7fa35af](https://www.github.com/scop/bash-completion/commit/7fa35afb89e0a6614c173fef56191f01fa2e3ee7))
* arch: Parse options from --help ([ca31dfa](https://www.github.com/scop/bash-completion/commit/ca31dfaf37ba0302da8b75427e36d3248e1c51ef))
* (test suite): Add mailman bin dir to PATH for arch test ([222fbb8](https://www.github.com/scop/bash-completion/commit/222fbb80c19f1fa229a554a01726c2bffe6ab5c1))
* (test suite): Ignore runtime loaded env function changes ([3dd8112](https://www.github.com/scop/bash-completion/commit/3dd81121c41767afa46cc5ef2911b33fad9de8ad))
* (test suite): Add basic CentOS 6 container for bash 4.1 coverage ([537c9a0](https://www.github.com/scop/bash-completion/commit/537c9a0e9d59c699927a2eeb04d87c05a3638490))
* Don't offer * as configured interface when there are none ([c287bb2](https://www.github.com/scop/bash-completion/commit/c287bb2af817cf12e065ea9c7c500532612bcd6d))
* (test suite): Don't assume configured interfaces in CI setups ([dbaafbd](https://www.github.com/scop/bash-completion/commit/dbaafbdc79cd2abbe3ed27acad7293e507c67116))
* (test suite): Simplify renice test, fix with only one completion ([8936b10](https://www.github.com/scop/bash-completion/commit/8936b10767144c4e5f84c04660f86c0828f7cc99))
* (test suite): Fix CentOS 6 tcllib setup ([b253814](https://www.github.com/scop/bash-completion/commit/b253814289bcbe606a9bda2b961898585f814d80))
* (test suite): Info test needs docs, don't exclude from CentOS ([f4b79ae](https://www.github.com/scop/bash-completion/commit/f4b79ae1c4ec89e1539f4bc026fc21ee8c0a25e2))
* (test suite): Add our own dummy ri test fixture ([1a61fc8](https://www.github.com/scop/bash-completion/commit/1a61fc8b41504cea6372c9c9ceb118bea3133d7b))
* ri: Add option and arg completions ([bf838e9](https://www.github.com/scop/bash-completion/commit/bf838e938342d4ebb8e3a45b837a60c80be661a9))
* Don't define BASH_COMPLETION_COMPAT_DIR ([c41a762](https://www.github.com/scop/bash-completion/commit/c41a76237bc9dcbfa326eeddd026b66d7646d91d))
* oowriter: Associate with *.pdf ([0cea46f](https://www.github.com/scop/bash-completion/commit/0cea46f1c88c1805ef3e9d11802d48c0a8013b4b))
* py.test: New completion ([eae26d9](https://www.github.com/scop/bash-completion/commit/eae26d9bfac3df98eb047fc45d9b8a7c4bc5d7b9))
* _configured_interfaces: Parse from /etc/network/interfaces.d/* on Debian ([d0c4410](https://www.github.com/scop/bash-completion/commit/d0c44102a8ed2e0a40d4e5456fc27bc21973a40e))
* (test suite): Don't insist on loading all completions dynamically ([f845109](https://www.github.com/scop/bash-completion/commit/f845109a83bb7efbab89df9016f23595fea0bb7a))
* (test suite): Set dcop result to untested if dcop server is not running ([25fa2f7](https://www.github.com/scop/bash-completion/commit/25fa2f72dce597a30e1e4a6088f175f159e9d159))
* man: Don't check OSTYPE or GNU userland, just enable on all systems ([d0ff1a9](https://www.github.com/scop/bash-completion/commit/d0ff1a946a3bea2122b5adb77260faf06627a2de))
* _user_at_host: Append @ suffix to username part completions ([f9f10a3](https://www.github.com/scop/bash-completion/commit/f9f10a396da44dddcd3f965cfe55fb3e16f85cf8))
* _user_at_host: Set nospace only when completing username part ([a4c6763](https://www.github.com/scop/bash-completion/commit/a4c67638eaea89503c1623a78e1680ac1eb2331f))
* bind: Add option and argument (non-)completions ([67e082c](https://www.github.com/scop/bash-completion/commit/67e082cf9dd0a9d9a0c7fd187e66db8fc8a374ee))
* xine etc: Fix *.m4a association ([279c3cd](https://www.github.com/scop/bash-completion/commit/279c3cd6c66f0b18a74dbc96fde178998a7283d8))
* mplayer, xine, etc: Associate *.mp4a and *.mp4v ([c868d7b](https://www.github.com/scop/bash-completion/commit/c868d7becbcb7ac16617a4858f40dd123821c32a))
* mplayer: Remove duplicate *.m4a and *.m4v associations ([0845c21](https://www.github.com/scop/bash-completion/commit/0845c2183c91054013055ca1966ec1e547fa5b13))
* Revert "(test suite): Fix alias and cd cursor position tests" ([c30a613](https://www.github.com/scop/bash-completion/commit/c30a6135f3f56931ed8e92b54733be3494805c14))
* (test suite): Set TERM to dumb, not dummy ([81c72d4](https://www.github.com/scop/bash-completion/commit/81c72d444ab8aa8393371effd2334b2b83cb8503))
* (test suite): Test screen -T completions ([c936d99](https://www.github.com/scop/bash-completion/commit/c936d99610d7a47fd75f7df38b9611faadd2167e))
* gm: New completion, commands only for now ([1ac39db](https://www.github.com/scop/bash-completion/commit/1ac39db3f9906d23357661093360747b6b45f59d))
* openssl: Add sha2 commands ([ec02542](https://www.github.com/scop/bash-completion/commit/ec02542c4293f3013dac3ad57a6775c4da66260d))
* openssl: Parse available digests from dgst -h ([b6ddef3](https://www.github.com/scop/bash-completion/commit/b6ddef3d1f80c17b81c254ede4b362624a73b2a4))
* python: Fix traceback avoidance on module completion error ([5faa19c](https://www.github.com/scop/bash-completion/commit/5faa19cb380fa31aa569401b30014dde3598a485))
* test suite: Add Python module completion test case ([6abf9d6](https://www.github.com/scop/bash-completion/commit/6abf9d6c9ad848040f50a7a8732950789f831f97))
* micropython: New completion, aliased from python ([9e5522f](https://www.github.com/scop/bash-completion/commit/9e5522fc29bda255caf9dd796c75c4c02662c756))
* python: Split module completion to separate helper ([abb3016](https://www.github.com/scop/bash-completion/commit/abb3016cac990ffacf66a05ecd7a8d657589d817))
* test suite: Improve ls home dir test descriptions ([fc8c976](https://www.github.com/scop/bash-completion/commit/fc8c9760368d4da2d97d78bae1f89d35fa0e4d42))
* dpkg: Fix dpkg -i home dir completion ([dd8c53b](https://www.github.com/scop/bash-completion/commit/dd8c53b05f4954d0401d18b108d771e0d79d88a2))
* test suite: Cosmetic tweaks ([a01c66b](https://www.github.com/scop/bash-completion/commit/a01c66b6e1e7b288fb96aefed777e4e3afb367bc))
* test suite: Mark dpkg -L test case untested if no packages installed ([d3276a8](https://www.github.com/scop/bash-completion/commit/d3276a8c2c8e3b17549c21fc4daec1159495edac))
* tar: Comment spelling fixes ([8c5283f](https://www.github.com/scop/bash-completion/commit/8c5283f89840fcb81811eae8fc19d4905f640c52))
* test suite: If colon trim doesn't do anything, trim as usual ([f4ffafe](https://www.github.com/scop/bash-completion/commit/f4ffafe0e9d361cf4494962d70e5d6b76ed91407))
* test suite: Ignore env var pulled in by use of scp in tests ([7f1f33b](https://www.github.com/scop/bash-completion/commit/7f1f33bdcb6e5eded2295ffeaec27199335ce65f))
* test suite: Fix ifdown and ifup CI test skipping ([089288d](https://www.github.com/scop/bash-completion/commit/089288dd8b7a3c1abbcb0b230014ae03c92ca663))
* test suite: Skip an expected make test case failure in centos6 CI ([4d5785f](https://www.github.com/scop/bash-completion/commit/4d5785f1b3bc320266addfecc8fab6d0b07d6904))
* Expose version in BASH_COMPLETION_VERSINFO, use it in profile.d script ([eb6f40c](https://www.github.com/scop/bash-completion/commit/eb6f40c5a7e715d8a9104bf90c67018428ca2eb7))
* test suite: Mark expected centos6 CI _filedir failures as such ([a1a4eb8](https://www.github.com/scop/bash-completion/commit/a1a4eb82050084a8447c4d2e9e248da9131d55b0))

## 2.5 (2017-02-04)

* : Whitespace fixes ([2ed36b8](https://www.github.com/scop/bash-completion/commit/2ed36b808cee5416139a034477a223f4910b53d8))
* Travis: zopfli is AWOL? ([96141cd](https://www.github.com/scop/bash-completion/commit/96141cd29817c38b30a6ede294d91913548e475e))
* make: Declare _make_target_extract_script like other functions ([7be3f33](https://www.github.com/scop/bash-completion/commit/7be3f33541f6c18d36008b49d25d018d50a8fa28))
* : Move indentation settings to .editorconfig ([619fb1f](https://www.github.com/scop/bash-completion/commit/619fb1f86ece68ea56e0e5c30c030376c1f8a07f))
* CONTRIBUTING: Reorder sections ([fefcf16](https://www.github.com/scop/bash-completion/commit/fefcf162ffd00b34e24aae55ec92293c2a6590f3))
* .ipa is just a zip file and we should let unzip handle it (#71) ([7b9dc46](https://www.github.com/scop/bash-completion/commit/7b9dc46c6a26a1a8e99409bb7752dfe153f2de0f))
* deja-dup: New completion ([d3c483c](https://www.github.com/scop/bash-completion/commit/d3c483c6d13596062aafbe2e8359affb2c2a8a9e))
* perl: Remove full path to cat in PERLDOC_PAGER call ([06e4c48](https://www.github.com/scop/bash-completion/commit/06e4c4859c270e0999f3c8c9ba02fc16adf8aa6b))
* mr: New completion ([624a452](https://www.github.com/scop/bash-completion/commit/624a452f57c2a0a777984493a8756ef7388a3f72))
* travis: Add mr ([78b0543](https://www.github.com/scop/bash-completion/commit/78b054319cc8f4dd39076fb1ffdf898c3aaf498f))
* dd: Sync completions with coreutils 8.24 ([8b35dd3](https://www.github.com/scop/bash-completion/commit/8b35dd34233761c2b238858eae7b458a6d017672))
* mplayer: Associate with *.mjpg, *.mjpeg (Debian: #837779) ([c16826e](https://www.github.com/scop/bash-completion/commit/c16826ee35ecb405fe87007404d0fb846ad61a15))
* ssh-keygen: support ed25519 keys (#79) ([85b6951](https://www.github.com/scop/bash-completion/commit/85b6951358fc6d120f9bf73960694439050264a1))
* ssh-keygen: Parse switches with _parse_usage, not _parse_help ([4dc0016](https://www.github.com/scop/bash-completion/commit/4dc0016625993487ed28ff0914ab8ebca47bd4e8))
* Add support for Include in ssh config (#70) (#80) ([1016e43](https://www.github.com/scop/bash-completion/commit/1016e4336c2eaa95888275df6ea248b4e913fb76))
* ant: parse targets in imported buildfiles (#84) ([62233c8](https://www.github.com/scop/bash-completion/commit/62233c87c030dfe96f4c55575c4ebb93bb984638))
* mr: Disable "clean" test case, command N/A before mr 1.20141023 ([98146c9](https://www.github.com/scop/bash-completion/commit/98146c9275524e4db33d05c52147b103550be4a5))
* Support for python gui source files (#91) ([84881eb](https://www.github.com/scop/bash-completion/commit/84881eb314f45a42e11f81e574777e3c05e86e72))
* ip: Recognize address in addition to addr ([b53b287](https://www.github.com/scop/bash-completion/commit/b53b287c0c0166a0c66fea8aebe694086d71ba03))
* ip: Recognize a as alias for address and l for link ([7029dd8](https://www.github.com/scop/bash-completion/commit/7029dd825201a1d75e052408102dd8a3f744ace1))
* Add sidedoor to _ssh() completion (#106) ([7ecca5c](https://www.github.com/scop/bash-completion/commit/7ecca5c89aede4ddd39e516969f9bfc8aba219ee))
* (test suite): Avoid loading user's ~/.bash_completion, fixes #87 ([c96f432](https://www.github.com/scop/bash-completion/commit/c96f4323c429b793cb03366faac59fbb78472dca))
* test/config/*: Delete trailing whitespace ([d8cb128](https://www.github.com/scop/bash-completion/commit/d8cb1284245e23351ccc6d96346aea13ec1bcaf4))
* travis: Skip bluez and nis for now due to post-install script issues ([58aaad3](https://www.github.com/scop/bash-completion/commit/58aaad3ee112333cdb1984d9838c6a7f747fdfc5))
* Fix bug in 'make' completion when using BSD sed (#108) ([33f3ef3](https://www.github.com/scop/bash-completion/commit/33f3ef366e594ea8bf88d934031b63b5cd89285a))
* ccache: fix completing compiler's flags ([ae84d53](https://www.github.com/scop/bash-completion/commit/ae84d53c44a3d4cb0bc38e7df53b69242dbd7cc7))
* Add more tests for ccache ([b0ec56d](https://www.github.com/scop/bash-completion/commit/b0ec56d24caee277aaf35ee86beb939add648165))
* javac: Complete -cp like -classpath ([c068fca](https://www.github.com/scop/bash-completion/commit/c068fcaa83c8e1b183df62df8f85be6012a56df0))
* test suite: Add java/javac non-completion fixture ([b35c4c9](https://www.github.com/scop/bash-completion/commit/b35c4c9cbbf45b59206f1a4ed1260a554a0f920e))
* Minor edits to README.md (mostly formatting) (#110) ([23cc43b](https://www.github.com/scop/bash-completion/commit/23cc43b4484c8d683d9c56400cc57698c2c2d984))

## 2.4 (2016-08-12)

* tipc: add tipc completions ([a0b5863](https://www.github.com/scop/bash-completion/commit/a0b586332f32f5c4ba4735424f387915bd975946))
* CONTRIBUTING: Highlight request for test cases ([0eb9600](https://www.github.com/scop/bash-completion/commit/0eb9600990b0197b2982a7f79bcfa9aa2401cb6f))
* test suite: Add function and declare test cases ([81f2708](https://www.github.com/scop/bash-completion/commit/81f2708198dd87604c4257b94b18c0e96a2852fb))
* mpv: Don't install symlink for it, fixes #24 ([0382773](https://www.github.com/scop/bash-completion/commit/0382773bbfc21dc1fb5467c1c0426ea3c984b6ec))
* aclocal: Install completion for 1.14 and 1.15, fixes #25 ([a8f4357](https://www.github.com/scop/bash-completion/commit/a8f4357f154f142ef30347b87a378e07b22608e9))
* Better handling of typeset/declare ([af3934a](https://www.github.com/scop/bash-completion/commit/af3934ad3474e94faa08f94523ed029e95c98767))
* Add tests for declare/typeset ([71b9147](https://www.github.com/scop/bash-completion/commit/71b9147d375393c60e3628bd7cd5ab41fac8504d))
* Support completing array variables and expansions ([dde1d31](https://www.github.com/scop/bash-completion/commit/dde1d314c4e8934693e7090ef4467dfac7d72a92))
* tipc: add test framework ([f6d00ad](https://www.github.com/scop/bash-completion/commit/f6d00ad8af985c8790eb9151aa3840e0cf4e76a0))
* tipc: merge functions into main ([935a98d](https://www.github.com/scop/bash-completion/commit/935a98d1550372d082abe4ac6965cf9ea1fe3989))
* tipc: use bash here string instead of echo ([d03425e](https://www.github.com/scop/bash-completion/commit/d03425e23ff8e2957bc639c61f9d9bfbc2fac3a9))
* tipc: remove unnecessary function _tipc_get_val() ([1969438](https://www.github.com/scop/bash-completion/commit/1969438c818c0829b78f87d34968357f3478b94a))
* tipc: add command prefix to link sed ([cdbc193](https://www.github.com/scop/bash-completion/commit/cdbc193826cabe74f89650236ae1bda8f7e9aa9f))
* The BitKeeper completion used the wrong set of commands ([be62355](https://www.github.com/scop/bash-completion/commit/be623550daf22492d58dfa359eea71cf36b723f3))
* man: Prioritize MANPATH, simplify, add fallback e.g. for busybox, fixes #28 ([c9f54e7](https://www.github.com/scop/bash-completion/commit/c9f54e7a411613cbfba2b200d35059f5bcb87113))
* cppcheck: Complete filenames too for --platform ([7634fe1](https://www.github.com/scop/bash-completion/commit/7634fe1b88948cbb724b6f3e8e3d8a6786973fd3))
* tipc: use cur variable for flag completion ([696eb89](https://www.github.com/scop/bash-completion/commit/696eb8961e2038c88eaff85b3bea20fe938ccd87))
* tipc: readd call to complete command ([831abc7](https://www.github.com/scop/bash-completion/commit/831abc716288b636ce4aa66a4e0b1e5b3100a469))
* Use shell globbing instead of ls to source files in compat dir ([760bd6f](https://www.github.com/scop/bash-completion/commit/760bd6f2758437eac08c4133884434cfd1ca54b3))
* aspell, minicom, mysql: Replace use of ls with printf ([751dbbb](https://www.github.com/scop/bash-completion/commit/751dbbb156aaa5171ba5e9136662ee5fbc6cf7d6))
* rpm: Fix --whatenhances arg completion ([e236020](https://www.github.com/scop/bash-completion/commit/e236020f858fc8929eee2fc4f1f4b7ce0ac5b3b0))
* make: Avoid a grep ([47c5c97](https://www.github.com/scop/bash-completion/commit/47c5c9773b00504a174326e356a5455892efe616))
* vncviewer: Cleanup shopt use, drop an eval ([cfc6212](https://www.github.com/scop/bash-completion/commit/cfc62125a8fc9e3d72e9154a504f9548b0b2ba38))
* aptitude: List packages using _apt_cache_packages, fixes #33 ([49265f0](https://www.github.com/scop/bash-completion/commit/49265f0094690bb9443de1c46a54d87d58876310))
* tar: silence --version's stderr output ([e1b1fd2](https://www.github.com/scop/bash-completion/commit/e1b1fd29a781d6f760ba2735e183d03a3e0ea130))
* test suite: Add bashcomp_bash env var for better control on tested bash ([93940c0](https://www.github.com/scop/bash-completion/commit/93940c099aa629d1990ba4fc995d9e44453253ab))
* Travis: First steps toward testing with OS X ([60a5b47](https://www.github.com/scop/bash-completion/commit/60a5b47760710836338faf4867a08985fcbb10d5))
* chroot: New (generic long options) completion, see #38 ([ce49e8d](https://www.github.com/scop/bash-completion/commit/ce49e8d87430bd640fb2ed1b5e03b05e02093b75))
* pyvenv: New completion ([fd9515f](https://www.github.com/scop/bash-completion/commit/fd9515fbe33bd5fa40a86251bb9e8a2616012633))
* abook: Parse long options from command including full path ([5ad751f](https://www.github.com/scop/bash-completion/commit/5ad751f89726a34ad0a22720a686e80a877c76b3))
* test suite: Add basic pushd test case ([864cb34](https://www.github.com/scop/bash-completion/commit/864cb342b37110df17903e6bae94c6bf2b785cd3))
* pushd: Use _cd completion for CDPATH support, closes #38 ([e35c504](https://www.github.com/scop/bash-completion/commit/e35c5046676b12d43bc19b5d2727ec03a6086dc6))
* Support pod document files for perldoc (#39) ([40e7b4e](https://www.github.com/scop/bash-completion/commit/40e7b4e8730e515a075c1e4852aa98ffa7ac78e0))
* perl: Remove some duplicated code ([cf559ae](https://www.github.com/scop/bash-completion/commit/cf559aeb1e67ee40a2b8c38e5581e02cd346ee22))
* test suite: Add perldoc module+pod completion test case ([c6defa3](https://www.github.com/scop/bash-completion/commit/c6defa39cede252c9b3b39aaa35a6608b6310ad2))
* mysql: Avoid --default-character-set error with failglob, fixes #46 ([fa10367](https://www.github.com/scop/bash-completion/commit/fa10367f319b91bda56b8cf972d60dfb5ae65c5b))
* tipc: remove unnecessary return values ([6c3942c](https://www.github.com/scop/bash-completion/commit/6c3942c8844dd95613d178dd835e6253004ce14e))
* tipc: make loop variables local ([e1b535e](https://www.github.com/scop/bash-completion/commit/e1b535e31f28097a6f90da1e0e5b11d5280dcde0))
* tipc: use double brackets in if conditions ([d815b46](https://www.github.com/scop/bash-completion/commit/d815b46102e6a0d92ca7c0e3a8c9d8881f606a3a))
* apache2ctl, aspell, make: Don't hardcode completion generator command ([3426649](https://www.github.com/scop/bash-completion/commit/342664947b241902d352dba2aede4cdcda5585f1))
* python: Support -Q and -W arg completion without space ([a9d0250](https://www.github.com/scop/bash-completion/commit/a9d0250abf879468272c56068cda5a94e1e850b3))
* xetex, xelatex, luatex, lualatex: Associate with tex files ([1e567a5](https://www.github.com/scop/bash-completion/commit/1e567a5eaf861804b6b8d636f976e947b4d2dc1c))
* CONTRIBUTING: Note patch preferences if not using GitHub pull requests ([5017ee6](https://www.github.com/scop/bash-completion/commit/5017ee6a3be315bf66eb5ee642a0450aa93741e6))
* (test suite): Remove test/fixtures/_filedir/a"b from git, create on the fly ([fec077d](https://www.github.com/scop/bash-completion/commit/fec077d555f112b9f455c45860f90a3b47392fcf))
* (test suite): Remove Bash::Completion.3pm.gz from git, create on the fly ([52d05a6](https://www.github.com/scop/bash-completion/commit/52d05a68e4583c8ee4dcd2c1f0da5db6629e5ff1))
* (test suite): Fix fallout from fec077d555f112b9f455c45860f90a3b47392fcf ([6170f07](https://www.github.com/scop/bash-completion/commit/6170f073a8765dabcd74f98c3362a10201f5322e))
* tipc: suppress tipc error messages ([83f78d2](https://www.github.com/scop/bash-completion/commit/83f78d2b4c2942a01f16697963a5ff24540d9ca7))
* tipc: handle complete words without trailing space ([1e778fc](https://www.github.com/scop/bash-completion/commit/1e778fcf780abe0f6d4044fe891b8385bf9bbd0b))
* tipc: fix missing last char in link name completion ([502b718](https://www.github.com/scop/bash-completion/commit/502b7189350632cd074f86361b498294b745ee61))
* tipc: Indentation fix ([48ab557](https://www.github.com/scop/bash-completion/commit/48ab5571b5a9c21db269cbf981cf7f9dae78bd44))
* tipc: Invoke ls with "command" ([103dbe5](https://www.github.com/scop/bash-completion/commit/103dbe5cf25087da4e5804374debcf8e9bb289a1))
* (test suite): Pass assert_complete_any for exact/only given arg completed ([847c2bf](https://www.github.com/scop/bash-completion/commit/847c2bf1aa30ca9f2718f583ad148ba558ca6cc2))
* Travis: Install more packages for more test coverage ([6388b8f](https://www.github.com/scop/bash-completion/commit/6388b8f5e77e1357a2d99109a46df7b813fdfd52))
* hcitool,svcadm,tar: Spelling fixes ([9a03b80](https://www.github.com/scop/bash-completion/commit/9a03b8068e20d4384786d9e5259b0d6d25575438))
* pypy3: Alias to python ([35767bf](https://www.github.com/scop/bash-completion/commit/35767bfdf4c8751f2bb453aa65bf359b313b79de))
* pypy*: Add basic --jit arg completion ([72beabe](https://www.github.com/scop/bash-completion/commit/72beabe4fcae1025a18e689b551b9c5c2d327c30))
* : Remove redundant return 0's ([8611927](https://www.github.com/scop/bash-completion/commit/8611927cfd3f95b88adcd8eff34cb17250870291))
* : Trivial cleanups ([b31c0f9](https://www.github.com/scop/bash-completion/commit/b31c0f95c7151beb8f97ebed042d555264aa6fc1))
* pkg-get,pkgrm: Drop unnecessary _have calls ([2d2d15b](https://www.github.com/scop/bash-completion/commit/2d2d15b8f4eab836c39f7d31c4ef0c9586103ad8))
* jarsigner: Add some option arg (non)completions ([5da97ee](https://www.github.com/scop/bash-completion/commit/5da97ee3da2582b988df7d9ecab788dc8b63485e))
* pkg-get: Don't use hyphens in function names ([e194c31](https://www.github.com/scop/bash-completion/commit/e194c312c765cd87541ab46178b17521e098c64c))
* lrzip: Add -m arg noncompletion ([fd6412e](https://www.github.com/scop/bash-completion/commit/fd6412e159143360972549233f28bb84e6cdc3f9))
* javadoc: Add bunch of option arg (non)completions ([30598c8](https://www.github.com/scop/bash-completion/commit/30598c864c71e6a5184a7f4aef02cfed2df38e32))
* rpm: Offer --filetriggers with -q ([8212aca](https://www.github.com/scop/bash-completion/commit/8212acae3588ecec0afcb0ac94a799b62a475737))

## 2.3 (2016-03-28)

* lvm: fix all commands that should get all PVs ([caa2222](https://www.github.com/scop/bash-completion/commit/caa22225f86034f72e7020e0919b4a618c580509))
* lvm: pvcreate should be able to use all block devcices ([2212dc5](https://www.github.com/scop/bash-completion/commit/2212dc584160977cfbca1eb43dd745b8056baf3b))
* CONTRIBUTING.md: Ask for test cases ([78e9493](https://www.github.com/scop/bash-completion/commit/78e949363c0f1162d324d7d6c788f74e5b10cc57))
* make check: Test syntax of all completion files ([b5d8a84](https://www.github.com/scop/bash-completion/commit/b5d8a84852c0ef9984f56f047ed296d1081ce668))
* travis: configure and run completions syntax check ([75c537c](https://www.github.com/scop/bash-completion/commit/75c537c5a72c078dcaa21dad6c19bf90e2d444a3))
* Completion for python zip archives ([f205719](https://www.github.com/scop/bash-completion/commit/f20571949641f44eb8a0e6e88a1aeffd1bd2a42d))
* unzip, zipinfo: Complete on *.pyz ([a576160](https://www.github.com/scop/bash-completion/commit/a5761607bd37a201a2512f10bbc7be646e51b2fb))
* test suite: Add some python test cases ([16d52f9](https://www.github.com/scop/bash-completion/commit/16d52f9a6c6be22d421ba2de07aec8413803c112))
* python: Complete all files only if -c is before current word ([6eae809](https://www.github.com/scop/bash-completion/commit/6eae809beacc7c6ab3db5f5ccfa57dce48d00e3b))
* python: Don't offer options after -c ([5f016fc](https://www.github.com/scop/bash-completion/commit/5f016fcd3991c389da27edd37668b0fd7db37cf4))
* python: Complete all files also after -m ([f17407b](https://www.github.com/scop/bash-completion/commit/f17407bc3282f2abd5fb9ea47e07b18cf4a9e898))
* python: Simplify code ([235f726](https://www.github.com/scop/bash-completion/commit/235f7269773b80fed4b41722de28df93ee0fd817))
* tar: Don't write to /tmp/jetel ([6bdd922](https://www.github.com/scop/bash-completion/commit/6bdd92202f55d7c530dcbeb2a243604dac546cf1))
* test suite: Add tar xvf filename test case ([807f903](https://www.github.com/scop/bash-completion/commit/807f903f86ad647425c83f7ad6fc499014c6734e))
* tar: Fix GNU tar help output parsing regex, fixes #15 ([0b7189d](https://www.github.com/scop/bash-completion/commit/0b7189d4eee4597e11cab02e6b4dcae488db5ca8))
* tar: Remove unused variable ([7ab05bf](https://www.github.com/scop/bash-completion/commit/7ab05bf9d97ce5a6ced7acdfc21235e06542c3f2))
* tar: Detect GNU/other from --version only once per session ([88c671a](https://www.github.com/scop/bash-completion/commit/88c671a2c74bfde3bc2ec7c6f74133ac613c61da))
* test suite: Fix tar failure caused by previous tar change ([198ab64](https://www.github.com/scop/bash-completion/commit/198ab64862777763f9750216131938300bf3a79c))
* test suite: Tolerate "See 'man feh'" feh --help output ([b4b8916](https://www.github.com/scop/bash-completion/commit/b4b89162e376574980292eaab761d393a6fbd5f9))
* test suite: Don't insist on property completions if synclient -l fails ([9716a54](https://www.github.com/scop/bash-completion/commit/9716a54837d43cbd8d79c04ac66251df8d3fcf1f))
* test suite: Fix abook test case ([eae0183](https://www.github.com/scop/bash-completion/commit/eae0183322854ee51500482e2223f5c3091d39b7))
* : Use [:blank:] instead of $'\t ' tricks where appropriate, fixes #19 ([657fcf6](https://www.github.com/scop/bash-completion/commit/657fcf62c77343ede3a21430a6c8e4faf4cd1dbe))
* gnokii: Use <<< instead of echo + pipe ([b7ededc](https://www.github.com/scop/bash-completion/commit/b7ededc40fdf02f3ee110e057ebe103338ccbf0f))
* make: Use <<< instead of printf + pipe ([1a122fb](https://www.github.com/scop/bash-completion/commit/1a122fb5827588d57339fc88f0692dd92a0c14b2))

## 2.2 (2016-03-03)

* feh: Add new sort type ([0354981](https://www.github.com/scop/bash-completion/commit/0354981b1f348263edb7c2e6f1f8dc619a48c476))
* kcov: Add new sort types, complete --replace-src-path arguments ([6cb7f96](https://www.github.com/scop/bash-completion/commit/6cb7f969745dd6b2075dd61244f8e60445a6a3d5))
* Add config for cmake to bash-completion. ([94b7e63](https://www.github.com/scop/bash-completion/commit/94b7e63f6d525a029efac07e20f4102fe8563a71))
* _mac_addresses: Use explicit C locale for ifconfig (Debian: #704832). ([a9db458](https://www.github.com/scop/bash-completion/commit/a9db458339fb518178b1b6cc234930bb869e95ea))
* aclocal, automake: Install for *-1.10, *-1.12, and *-1.13 too. ([e772425](https://www.github.com/scop/bash-completion/commit/e772425cdd660d4d59914d2e067be07d7f4b3504))
* cvs rm: Don't filter existing files with -f (RedHat: #949479). ([9852597](https://www.github.com/scop/bash-completion/commit/9852597d8c19567ee1e01c03a297692d9c1cd764))
* Use == instead of =. ([497c209](https://www.github.com/scop/bash-completion/commit/497c20902a978098512674bbaa37a839e355c782))
* nmcli completion was integrated upstream ([580a4cf](https://www.github.com/scop/bash-completion/commit/580a4cf6e55ab1afd6b1f8f7efc54436e7046e44))
* Revert "nmcli completion was integrated upstream" ([cdd2c3d](https://www.github.com/scop/bash-completion/commit/cdd2c3d71d9eec2860ad99ba3f81ef65dbd1e723))
* nmcli: Deprecate our completion, upstream has one in *******. ([9780b0a](https://www.github.com/scop/bash-completion/commit/9780b0a04472187cb4ac4557e21038caac31fd86))
* sh: Complete script arguments with all filenames (Alioth: #314226). ([daaa541](https://www.github.com/scop/bash-completion/commit/daaa54100ed061a0c8e7ce89a8bda009530246a3))
* cvs: Fix checkout -j non-completion. ([bb0739f](https://www.github.com/scop/bash-completion/commit/bb0739fafd87bfca2e5d43e8b448ec1293a43ada))
* Clean up/compact various globs. ([d99c220](https://www.github.com/scop/bash-completion/commit/d99c2203b9ff6e35ce12fbe0572773a9f3abeadb))
* wget: Stop completing after --help/--version. ([5786568](https://www.github.com/scop/bash-completion/commit/57865686de2d2ff0c455704428a49d5a3b4f98c8))
* wget: Drop incorrect -nv arg completion. ([1969d12](https://www.github.com/scop/bash-completion/commit/1969d125bacde3da22c06e5ca729c7e766ce7d65))
* wget: Add --accept-regex/--reject-regex/--regex-type arg (non)completions. ([5c6b1bb](https://www.github.com/scop/bash-completion/commit/5c6b1bb4a47ca522c111f110f44eff771d9aea5f))
* perl*: Fix handling of relative paths in @INC. ([18c28bb](https://www.github.com/scop/bash-completion/commit/18c28bb9a2147e662755e393d1ec39f270948d30))
* perl: Fix -dt: completion. ([97efc7c](https://www.github.com/scop/bash-completion/commit/97efc7ca08f22043ce5c55f41ac093de9c7ca082))
* hcitool, rfcomm, ciptool, hciconfig: Don't leak $args. ([3a3290d](https://www.github.com/scop/bash-completion/commit/3a3290ddcf81181bbd9f22a739536b65df0c5647))
* 7z: New completion. ([5a9e8f2](https://www.github.com/scop/bash-completion/commit/5a9e8f219b847a6c99f294b4cc492e77a63a22f0))
* file-roller: Reuse unzip's xspec. ([42196ef](https://www.github.com/scop/bash-completion/commit/42196ef7fc1f91c51a471a1b50d2c1a48d77560f))
* 2to3: New completion. ([a4b69e7](https://www.github.com/scop/bash-completion/commit/a4b69e75873b9b24e1da0c6bda22d42365cc97ac))
* perl: -d/-dt option argument is optional (Alioth: #314242) ([ad455df](https://www.github.com/scop/bash-completion/commit/ad455dfa9de9253d52d6352888777f6a9328eade))
* dpkg: Suppress unwanted error messages (Debian: #706502) ([9ca8d93](https://www.github.com/scop/bash-completion/commit/9ca8d933c69e5a52e7866227dfc823657c180f40))
* cppcheck: Add new --enable option argument and --library argument completion ([77ebc93](https://www.github.com/scop/bash-completion/commit/77ebc93a023e6b1bba87b9572c17e824d248c540))
* export, _variables: Do TZ= completion (Redhat: #994646). ([3f144be](https://www.github.com/scop/bash-completion/commit/3f144beb721525821136f76f53a3147b4688d331))
* Cosmetics. ([62e0c3b](https://www.github.com/scop/bash-completion/commit/62e0c3ba7245741c9ef2a664a84a8f6c61a6f875))
* make: Use only posix basic regexps with sed (Alioth: #314345) ([f230cfd](https://www.github.com/scop/bash-completion/commit/f230cfddbd12b8c777040e33bac1174c0e2898af))
* _longopt: Run commands in C locale. ([a282d02](https://www.github.com/scop/bash-completion/commit/a282d0254cca2e9e1e851e251dc77f3b2aa653ce))
* aptitude: safe-upgrade accepts package name as parameters (Alioth: #313638, Debian: 673235) ([e91a458](https://www.github.com/scop/bash-completion/commit/e91a45889f3fb8b4007949797fc3f07af24bb52f))
* bzip2, gzip, lzma: Cleanups. ([61d1d7d](https://www.github.com/scop/bash-completion/commit/61d1d7df42cace84c5d706b43f44ea2083c54723))
* zopfli: New completion. ([2da4ee9](https://www.github.com/scop/bash-completion/commit/2da4ee9ad5a9016e4e4a89e4822bf6e1688c6175))
* make: Fix basic regex for portability (Alioth: #314345) ([3ac523f](https://www.github.com/scop/bash-completion/commit/3ac523f57e8d26e0943dfb2fd22f4a8879741c60))
* _known_hosts_real: Pre-expand \t instead of relying on sed supporting it (Alioth: #314393). ([50ae579](https://www.github.com/scop/bash-completion/commit/50ae57927365a16c830899cc1714be73237bdcb2))
* dict: Trivial regex cleanup. ([705be00](https://www.github.com/scop/bash-completion/commit/705be005027577ba540212be6b0bbb25e95f78f3))
* abook, kldunload: Pre-expand \t instead of relying on sed supporting it. ([24f0c58](https://www.github.com/scop/bash-completion/commit/24f0c58c29885f89ef2a3a16db17e73b6cd6f64e))
* cc, c++: Install minimal completion for non-gcc ones (Alioth: #314417). ([d6600e6](https://www.github.com/scop/bash-completion/commit/d6600e6a10a370961b4bd4e1e060ba79e080a8d7))
* cc, c++: Check path to binary when finding out if it's gcc (Alioth: #314417). ([ffabc6f](https://www.github.com/scop/bash-completion/commit/ffabc6f2829201815c1a9c69cfdf8bdbe77ff648))
* f77, f95: Use the same completion as for g77, g95 if they are links to gfortran ([df8782d](https://www.github.com/scop/bash-completion/commit/df8782d76928bd39b3de138305f9b0af36628a9f))
* 7z: Improve completion ([d048a14](https://www.github.com/scop/bash-completion/commit/d048a14b2ae18c70ddcf54558cdc79eaf7e8d751))
* mplayer: -dvd-devices takes dvd devices, dirs and .iso files as argument ([d482e74](https://www.github.com/scop/bash-completion/commit/d482e746821f102205475dfb835ca75a7fd02d63))
* _known_hosts_real: Exclude %h HostName entries (RedHat: #1015935). ([fcc9545](https://www.github.com/scop/bash-completion/commit/fcc9545b44f32b627f75e1589fb93a5c7ad86bff))
* timeout: New completion. ([63b4995](https://www.github.com/scop/bash-completion/commit/63b499593a595cffd09403852555577e3f590cb5))
* complete on freerdp-specific known hosts list ([1cfbbdd](https://www.github.com/scop/bash-completion/commit/1cfbbdd52cafd6b995f7241e2b87a41ed0f7a87a))
* bts: New completion, thanks to Federico Ceratto. ([b2e7951](https://www.github.com/scop/bash-completion/commit/b2e7951cd714bd31e6efd0b6e16c1f916fd94486))
* appdata-validate: New completion. ([e93cc98](https://www.github.com/scop/bash-completion/commit/e93cc98523e722bc0557ae623b4bd9d16ee01486))
* uscan: New completion, thanks to Federico Ceratto ([61fa261](https://www.github.com/scop/bash-completion/commit/61fa261502f904c3afa5f053a68cccd243cbfcd4))
* Refactor bts and uscan, since they use common functions ([780d97c](https://www.github.com/scop/bash-completion/commit/780d97c5ac41a1d170fa294811e8cadffa550248))
* wtf: Hush stderr when db file doesn't exist. ([f5df66f](https://www.github.com/scop/bash-completion/commit/f5df66f47653ebe781090411a8e03fa052053273))
* wtf: Don't offer -f if it was already specified. ([e694978](https://www.github.com/scop/bash-completion/commit/e694978346b017d92dfeb7968fc57d018c8721a4))
* wtf: Look for acronym db from /usr/share/games/bsdgames/acronyms too. ([920bbb8](https://www.github.com/scop/bash-completion/commit/920bbb8582bcd3039ff4e4388e795e3144eb30b6))
* (testsuite): Limit wtf completions to A* to keep expect happier. ([c527b54](https://www.github.com/scop/bash-completion/commit/c527b543a84363449902e2be5e80786aceaa7931))
* cppcheck: Include - in --file-list completions. ([4df6d3f](https://www.github.com/scop/bash-completion/commit/4df6d3ffb86cf2cd9f583d5e927f2661a8a83a84))
* optipng: New completion. ([34a74df](https://www.github.com/scop/bash-completion/commit/34a74df61973d1f49805135e79045e2c7b1589e0))
* lz4: New completion. ([a2e2f19](https://www.github.com/scop/bash-completion/commit/a2e2f198d820c55857e372f6477c6fe07a23df92))
* (testsuite) Check for grep and ls invoked without "command", see README ([d98e56f](https://www.github.com/scop/bash-completion/commit/d98e56fb0725f96a5cb39362c2d9a60a39e69154))
* lintian: Replace some grep+cuts with awk ([93ee009](https://www.github.com/scop/bash-completion/commit/93ee00947ae112f195ed368f3414f3622d113630))
* gcc, lintian, make, pkgadd, slackpkg: grep -> "command grep" (Debian: #734095) ([e3edf7a](https://www.github.com/scop/bash-completion/commit/e3edf7ac423179f70acbd77d9ffbbf5cfa91ce58))
* aptitude, dpkg: Replace some grep+cuts with awk ([e777395](https://www.github.com/scop/bash-completion/commit/e777395ac3ce25527e58e04bfc406ae03bdb3b12))
* ip: Add some addr, addrlabel, and route arg completions ([b5d0cdd](https://www.github.com/scop/bash-completion/commit/b5d0cdd802817708bb1fdb03880eb95e9be9e67b))
* jpegoptim: New completion ([8617e22](https://www.github.com/scop/bash-completion/commit/8617e227e541ffc3d34c69d861c52f188bd5ae15))
* Bump copyright years to 2014. ([01fd3b4](https://www.github.com/scop/bash-completion/commit/01fd3b4f55407623dbef58b6b6a756ad623c334f))
* ssh-keygen: New completion ([0b7d92c](https://www.github.com/scop/bash-completion/commit/0b7d92cf2bb3eb04668c73b6d39726caca617bf9))
* portsnap: New completion. ([c3770c1](https://www.github.com/scop/bash-completion/commit/c3770c17984bb61255db6adb48886b466c335e03))
* freebsd-update: New completion. ([5ff5a4e](https://www.github.com/scop/bash-completion/commit/5ff5a4e0f875e8d1a49848408cfe72778889cbe8))
* testsuite: Add basic tests for portsnap and freebsd-update ([602d188](https://www.github.com/scop/bash-completion/commit/602d18853ccb71baa233c33d93503b0ce27070a0))
* hwclock,ionice,rtcwake: Deprecate in favor of util-linux ones (Debian: #737672) ([e4b1740](https://www.github.com/scop/bash-completion/commit/e4b17402bbb5d53797a601de93771d8bd0df5213))
* _*: Install our deprecated completions too, try loading them secondarily ([e201d1b](https://www.github.com/scop/bash-completion/commit/e201d1be7ed92bf35c249d6d01c4001378e0ed77))
* testsuite: Add basic test cases for deprecated completions ([3e06371](https://www.github.com/scop/bash-completion/commit/3e0637128f67807500f3af042a6f536d1404b968))
* testsuite: Add basic newgrp test case ([bb76897](https://www.github.com/scop/bash-completion/commit/bb768978ba2eee1ab5d0f4e3d6158d9df1643630))
* cal,chfn,chsh,dmesg,eject,hexdump,look,newgrp,renice,runuser,su,write: Deprecate on Linux in favor of util-linux ones (Debian: #737672) ([e452f2e](https://www.github.com/scop/bash-completion/commit/e452f2e8d6788458830bf2afe55d641e6adfa940))
* pyflakes: New completion ([e14617e](https://www.github.com/scop/bash-completion/commit/e14617e498f0e2e2faceea9205f96d72c3e9ef8d))
* flake8: New completion ([5152356](https://www.github.com/scop/bash-completion/commit/5152356ad1b0c8384d52ea9648529c448bbb4af2))
* ri: Fix colon handling in class completion. ([24ea53f](https://www.github.com/scop/bash-completion/commit/24ea53f01de9c218a133d8cf7b3c77d6ad07bc26))
* ri: Fix class completion with ri >= 3. ([3cdcfde](https://www.github.com/scop/bash-completion/commit/3cdcfdeda65a281a4f175a6b2f330c983a4bd217))
* (testsuite) Avoid complete-ant-cmd.pl errors with our build.xml ([14be62a](https://www.github.com/scop/bash-completion/commit/14be62aae4197a7f98e913c0ada910c5c2ea7f04))
* FAQ: Clarify that we mean the bash man page for M-/ ([293bbaa](https://www.github.com/scop/bash-completion/commit/293bbaaa90238511b12ffb0efd3d5de0cbd5abfc))
* profile.d: Don't return from a sourced script (Debian: #741657) ([867282a](https://www.github.com/scop/bash-completion/commit/867282a7341ccfff9a0e8a8ef6bba6e781b66afb))
* man: Use -w instead of --path ([4927730](https://www.github.com/scop/bash-completion/commit/492773098004700d670b4e1c12eebac506471d90))
* xrandr: Add (some) --setprovider* arg completion support ([c50313c](https://www.github.com/scop/bash-completion/commit/c50313c30df5eb3a7956b0420f882508e79da0a5))
* xrandr: Use the invoked command internally, not hardcoded "xrandr" ([b758afc](https://www.github.com/scop/bash-completion/commit/b758afc105fd0656c13f88a5bd2f73bac81399db))
* qemu: Apply completion to qemu-kvm/-system-i386/-system-x86_64 too ([61ad655](https://www.github.com/scop/bash-completion/commit/61ad655fc4a6c18947ce6f17d4ea8f87abd6c945))
* qemu: Fix -balloon arg completion ([4a4afd5](https://www.github.com/scop/bash-completion/commit/4a4afd5eab0fcaf19ac1f2ce55c1bb305f89903f))
* pngfix: New completion ([13ad1f1](https://www.github.com/scop/bash-completion/commit/13ad1f1966f5baa14d9785c4b732dda3a29f4f14))
* gdb: support --args style completion (Alioth: #314664) ([b1f7803](https://www.github.com/scop/bash-completion/commit/b1f78035565dcca343841cc4fa7ff54b09cc81b9))
* eog: Complete on *.pgm (RedHat: #1090481) ([77e11c4](https://www.github.com/scop/bash-completion/commit/77e11c41ea376e364ac83ddc54428b18052b8ffe))
* nslookup: complete on hosts (Alioth: #314673) ([b74d537](https://www.github.com/scop/bash-completion/commit/b74d53761afb48e5e0948b70e7202a1c1cda4897))
* hostname: New completion ([c924d32](https://www.github.com/scop/bash-completion/commit/c924d32b4c127bc9cc46cb71d3148c8bdbfdd6cf))
* pypy: New completion identical to python (Alioth: #314501) ([38a013e](https://www.github.com/scop/bash-completion/commit/38a013e22cd30c0cce84caaaff2763d0c39ce131))
* Use more straightforward way to check bash version ([768a958](https://www.github.com/scop/bash-completion/commit/768a95854616e25c8da3f9d79048f9bbb5ecb2a6))
* dpkg: Add support in dpkg completion for .ddeb (LP: #568404) ([eafde37](https://www.github.com/scop/bash-completion/commit/eafde3784836391741c2264ad1b350fff1aa276a))
* make: completion shouldn't be confused by the output of $(info confuse: make) ([54d53c6](https://www.github.com/scop/bash-completion/commit/54d53c622745e907d288d67d832a0b09aaa1d3df))
* Puppet: use puppet terminology ([ac98c31](https://www.github.com/scop/bash-completion/commit/ac98c31f30b74b32ec0eea8df6c71b866b16b58e))
* Puppet: puppet -* doesn't imply 'puppet apply' ([20e9bef](https://www.github.com/scop/bash-completion/commit/20e9befbff2b4cac09f7beec58e5ad6eac1425b4))
* Puppet: puppet parser support ([b56df70](https://www.github.com/scop/bash-completion/commit/b56df708a1bbaf27bf76d06b18448bba6b2d59f6))
* Puppet: agent: update options list, accordind to 'puppet help agent' ([af5ba56](https://www.github.com/scop/bash-completion/commit/af5ba56089088669e31bb49d855bcc42c98f0951))
* Puppet: apply: update options list, accordind to 'puppet help apply' ([5a536ae](https://www.github.com/scop/bash-completion/commit/5a536aec4ca4e8bc6c179dfd2f98d71a3c541f1d))
* Puppet: cert: update options list, accordind to 'puppet help cert' ([b46636a](https://www.github.com/scop/bash-completion/commit/b46636ae16c98045caf9c4dbfa4020f3c08671a5))
* Puppet: describe: update options list, accordind to 'puppet help describe' ([0bbcc47](https://www.github.com/scop/bash-completion/commit/0bbcc476b2d8f54c8a6a3e633db97586ecb0e23c))
* puppet: Exit early on -h|-V|--version in addition to --help ([f94d1a6](https://www.github.com/scop/bash-completion/commit/f94d1a6803a488b24b3b7005ce86c19949a381e6))
* puppet: Parse most subcommand options from "help subcommand" output ([00a80a2](https://www.github.com/scop/bash-completion/commit/00a80a2a830cc5cfea262865e5d97d1f901ca177))
* puppet: Recognize some short options ([dbe7325](https://www.github.com/scop/bash-completion/commit/dbe732517ef2a2d8c628e3578db330b64d177948))
* (testsuite) Add puppet subcommand option test case ([d748a5a](https://www.github.com/scop/bash-completion/commit/d748a5a24914a9f5f0173a98bd6435aac3d39747))
* mpv: New completion alias + adjustments for mplayer (Debian: #749115) ([de78c16](https://www.github.com/scop/bash-completion/commit/de78c1653aad081daf28cd349e0f52801db3a180))
* _services: README in sysv init dir is not a service ([41cdfc6](https://www.github.com/scop/bash-completion/commit/41cdfc6510093ddb6766aabb1574d2064279620e))
* __reassemble_comp_words_by_ref: Make work with failglob on (Alioth: #312741) ([732906b](https://www.github.com/scop/bash-completion/commit/732906b25096508fbc5d15d684dea0312ed7fca0))
* psql: Tell psql to not load .psqlrc as it may change output format (Alioth: #314636) ([9186add](https://www.github.com/scop/bash-completion/commit/9186add6298cac3c684b1123ea3c8006f87fc24b))
* Quote unset array element to avoid globbing interference (Alioth: #314708) ([84135d7](https://www.github.com/scop/bash-completion/commit/84135d756bd395aa720c3bd08c660bc54d24f90c))
* Various mostly array element unsetting fixes under failglob ([1ed2377](https://www.github.com/scop/bash-completion/commit/1ed2377c895d3f1826df0820d992f746c10373a9))
* ssh-add, ssh-keygen: -? needs to be quoted under failglob (Alioth: #314709) ([24c8f1e](https://www.github.com/scop/bash-completion/commit/24c8f1e44e4e3a094745a45896963c4b3edff9ad))
* Quote _filedir arguments when appropriate to prevent failglob failures ([8566a5a](https://www.github.com/scop/bash-completion/commit/8566a5a89633a6d689192638410914910e2a1523))
* slapt-src: split options from their arguments ([3a65be4](https://www.github.com/scop/bash-completion/commit/3a65be4a18f67d1b5101ce1d03398728aebe0668))
* slapt-{get,src}: Fix issue with sed not being able to handle some characters ([70e52c8](https://www.github.com/scop/bash-completion/commit/70e52c8a1fc4ebc3ea5516879505ca5da504b32b))
* gendiff: Quoting suffix pattern to avoid triggering failglob ([154f388](https://www.github.com/scop/bash-completion/commit/154f3884139825a38e161c2e66837a35e9aa3841))
* sbopkg, slapt-{get,src}: expand tilde in config file name ([505481c](https://www.github.com/scop/bash-completion/commit/505481c8cc4bdb13b89de8505d5d84c31314245c))
* slapt-src: Handle --config=FILE option properly ([7220727](https://www.github.com/scop/bash-completion/commit/72207276a5d4a92c9fa0baef1f21941f9f1ad891))
* _filedir_xspec: Fix with failglob on ([b65232f](https://www.github.com/scop/bash-completion/commit/b65232fa34e310cf357e2ff4b8921e50e260d294))
* lvm: _lvm_count_args parameter must be quoted in order to failglob not to complain ([3717fe7](https://www.github.com/scop/bash-completion/commit/3717fe7b6d97ba8e8e8eb55ce10c05808dc4d4b8))
* (testsuite) Add vgcreate test case for _lvm_count_args with failglob on ([615fd18](https://www.github.com/scop/bash-completion/commit/615fd18195793cd867c0a365c25069cdbb89c565))
* _lvm: using a single-pattern case and invoking function according to words[1] ([01024f5](https://www.github.com/scop/bash-completion/commit/01024f595e9e6e3f243b2abc1a3474529ce1f083))
* umount: Fix mount points escaping/unescaping with Bash-4.3 ([292830b](https://www.github.com/scop/bash-completion/commit/292830be53f69456d284c10dc5fea28dd393d7bc))
* (testsuite): move testing of _linux_fstab() to umount.exp ([dcb0ea2](https://www.github.com/scop/bash-completion/commit/dcb0ea2ca29cf2c335f1b075f51290d023fcda2e))
* _parse_help: Fix failglob failures (Alioth: #314707) ([d238ab5](https://www.github.com/scop/bash-completion/commit/d238ab5445627b6fd9888507d1b7545bb20408a7))
* modprobe: fix params with multi-line descriptions ([454f67a](https://www.github.com/scop/bash-completion/commit/454f67a0478e2097e765169191151e9cf83fe978))
* modprobe: Try parsing help before using hardcoding option list ([c6ec8f9](https://www.github.com/scop/bash-completion/commit/c6ec8f979bb6a6c6bdfeae75749a5be4c5145fdb))
* ssh-add, ssh-keygen: -? needs to be quoted under failglob (take 2) (Alioth: #314709) ([60b8fab](https://www.github.com/scop/bash-completion/commit/60b8fabec499dbd56636548369ff3796e0d0d0fd))
* isql: Fix failglob failure ([9d250f9](https://www.github.com/scop/bash-completion/commit/9d250f9b53a58c70c7a388e5d2272d8a384d6eb1))
* Added test/site.{bak,exp} to .gitignore ([afe39fd](https://www.github.com/scop/bash-completion/commit/afe39fd1e171d0ea3d4fb8ec0d8c8c14fa120ed8))
* adb: New completion ([4bbab19](https://www.github.com/scop/bash-completion/commit/4bbab196cbce908dacbc225de71433272228a985))
* (testsuite) Use 'set' command posix behaviour when saving env (Alioth: #314720) ([16361c8](https://www.github.com/scop/bash-completion/commit/16361c873ba04d941c56c7e6a3fbe8d446f07ca7))
* (testsuite) Save shell variables when saving env (Alioth: #314720) ([dbb93ae](https://www.github.com/scop/bash-completion/commit/dbb93ae77ba6613d89081b9592fb3714b604476c))
* xz: Complete -T/--threads argument ([9e2db8a](https://www.github.com/scop/bash-completion/commit/9e2db8a03ea661eb7f4a4c41cd0558c063c04aca))
* xmllint, xmlwf, xsltproc: Complete on *.dbk and *.docbook (Alioth: #314770) ([ab8eeb3](https://www.github.com/scop/bash-completion/commit/ab8eeb3a713b1223752336fc4cb0d515c24579cc))
* xsltproc. TODO fix for previous commit ([7c5c622](https://www.github.com/scop/bash-completion/commit/7c5c622dda132a2c4a78028273f86fd51a9d680c))
* python(3): Add -X argument non-completion ([eb79f9d](https://www.github.com/scop/bash-completion/commit/eb79f9d97fc384b2cbfdb0c9a782d9ef935356ab))
* 7z, adb: Trivial cleanups ([d259e71](https://www.github.com/scop/bash-completion/commit/d259e7175851f08cda10ae6c279ab1d0e7bc8045))
* ant: Don't offer more completions after options that exit ([98d6b5e](https://www.github.com/scop/bash-completion/commit/98d6b5e440fc666256cfb3ee2fe2f07da45f31a7))
* (testsuite) Add ant -f <buildfile> test case ([40db483](https://www.github.com/scop/bash-completion/commit/40db4831747a0a4f80fcb383c0c7b5398b327538))
* ant: Support buildfile set in $ANT_ARGS (Alioth: #314735) ([86df56d](https://www.github.com/scop/bash-completion/commit/86df56d5ec986566a33cc5b1067c6dbc6078dab1))
* mplayer, *xine: Complete on *.mts (Debian: #759219) ([852e0f6](https://www.github.com/scop/bash-completion/commit/852e0f60f0df8264a84a3be543d09f1d13c6f04f))
* rpmbuild: Complete *.spec on --clean (RedHat: #1132959) ([e879eb0](https://www.github.com/scop/bash-completion/commit/e879eb0a1706cad92d6df8b3c665d1d5227b1f67))
* rpmbuild: Complete *.spec on --nobuild ([42e1f34](https://www.github.com/scop/bash-completion/commit/42e1f344b7fef71100e42ab97f63e35281116ced))
* Comment update ([fa064e8](https://www.github.com/scop/bash-completion/commit/fa064e85633cd56000f8bf2cd8eb3bc76eceeeed))
* _completion_loader: Set empty command to _EmptycmD_ for cross version compat ([7394d74](https://www.github.com/scop/bash-completion/commit/7394d744aee259929012cbcd53a9c268dfe9c025))
* _init_completion: Handle cword < 0 (LP: #1289597) ([a9c556c](https://www.github.com/scop/bash-completion/commit/a9c556ccad819869a6a5d932aac0a75a99372f08))
* pigz, unpigz: Handle *.zz ([4038c71](https://www.github.com/scop/bash-completion/commit/4038c71357dd85ee049363c6c48c513e1c1c263d))
* Protect various compgen invocations from -* leakage (Debian: #766163) ([882649b](https://www.github.com/scop/bash-completion/commit/882649b7123855a0b87fcee7e4bc043ca2cca711))
* (testsuite) Add cd in dir without subdirs or CDPATH test case ([9444a87](https://www.github.com/scop/bash-completion/commit/9444a8742e3aa8d1ef8eb35a377a860f9e59fa43))
* _pnames: Add -s for producing (possibly) truncated names (RedHat: #744406) ([52d8316](https://www.github.com/scop/bash-completion/commit/52d8316c5ce4060cf86154eea3ba6fa51447760a))
* Actually install the lz4 completion ([ed07b18](https://www.github.com/scop/bash-completion/commit/ed07b18e61bc4756c94d3230e6a0b32798087e20))
* _completion_loader: Protect compgen from -* leakage (Debian: #769399) ([32e6e49](https://www.github.com/scop/bash-completion/commit/32e6e4908885197ae3f48da0f4fe7d182245818b))
* gphoto2: Fix split argument handing, and colon treatment in --port args ([7d66285](https://www.github.com/scop/bash-completion/commit/7d66285b42f7847e1b5dedf5ed7c4536cb2bb288))
* : Invoke command to be completed, not its basename ([7d3de61](https://www.github.com/scop/bash-completion/commit/7d3de619d1d84cba29597a54b4f0c0b786e8abe0))
* gphoto2: Replace tail with awk ([89add74](https://www.github.com/scop/bash-completion/commit/89add74ae07d17eaaff811159e086663e4791d85))
* ccache: Add -o/--set-config arg name completion ([dfb2d01](https://www.github.com/scop/bash-completion/commit/dfb2d01babcf00ecbb57c97e648d97881004797d))
* chrome, firefox etc: Complete on *.pdf ([6a60025](https://www.github.com/scop/bash-completion/commit/6a60025e01165cddce61257902358badd738f1fa))
* strings: Fix -T/--target arg completion with non-English locale ([976ad96](https://www.github.com/scop/bash-completion/commit/976ad96007fbc8edac189737876e485e0cc38e08))
* upstart support for service completion ([3567d93](https://www.github.com/scop/bash-completion/commit/3567d9354fbc40f050831559df17d442e3177a8d))
* (testsuite): Add mcrypt -a and -m argument completion tests ([377e240](https://www.github.com/scop/bash-completion/commit/377e24024ee419a50fd21a1ae2e984f19ac512b3))
* mcrypt: Simplify -m arg completion ([a592a09](https://www.github.com/scop/bash-completion/commit/a592a09cc6fb1d43ef737baf5b31371f55ad19d0))
* tshark: Simplify cut usage ([23bf3bd](https://www.github.com/scop/bash-completion/commit/23bf3bd412adb3cfff774f019d8c376658d50152))
* createdb, dropdb: Drop -o default, it does not appear to do anything good here ([e71b452](https://www.github.com/scop/bash-completion/commit/e71b4522d18306dfa98b013441122910a1166ea9))
* createuser: New completion ([7999f28](https://www.github.com/scop/bash-completion/commit/7999f28f62f49caf2b736fd835152c2579c2646d))
* dropuser: New completion ([3cf50a1](https://www.github.com/scop/bash-completion/commit/3cf50a1437f9f0b8ef16bd6ea58db18e7a9204f6))
* profile.d: Avoid some warnings from shells in "nounset" mode (Debian: #776160) ([c725e6b](https://www.github.com/scop/bash-completion/commit/c725e6b195ea6ac2d25dfbb85b7e87bfbe42fe68))
* cppcheck: Option argument (non-)completion update ([e687c3a](https://www.github.com/scop/bash-completion/commit/e687c3a1085e06502b882f71b4deab79266b9b14))
* reptyr: Rename file to _reptyr to avoid conflict with upstreamed completion ([6a4ad49](https://www.github.com/scop/bash-completion/commit/6a4ad49fa53cc92d3bd23b5b4db0b3318f2ef136))
* tune2fs: Add missing return in -M arg completion ([edea6cb](https://www.github.com/scop/bash-completion/commit/edea6cb6da246930c1314384ba2f150efd0c07d5))
* synclient: New completion ([6a1bf8d](https://www.github.com/scop/bash-completion/commit/6a1bf8d11430fea80864bd0fef46210143d91407))
* Drop reference to no longer used sysconf_DATA ([ba79e9e](https://www.github.com/scop/bash-completion/commit/ba79e9e5798ac4f25036ddf15885cd3bbaeed4bc))
* README: Add autotools and cmake tips ([3a8e7bd](https://www.github.com/scop/bash-completion/commit/3a8e7bd0fe45b4ebf76b4cd6da9246034b2e0557))
* README: Add cmake usage example ([889718b](https://www.github.com/scop/bash-completion/commit/889718be68ccd442ee824f6405d706d8e2734a03))
* README: Don't hardcode /etc in cmake fallback dir ([2c5efee](https://www.github.com/scop/bash-completion/commit/2c5efeec3a3b8cf29dd2f161935e786988d9dc36))
* Revert "README: Don't hardcode /etc in cmake fallback dir" ([caaa474](https://www.github.com/scop/bash-completion/commit/caaa4745e40a5d1d9122b0774ba589ef5fafbcbf))
* Load user completions from $BASH_COMPLETION_USER_DIR/completions ([1d25d72](https://www.github.com/scop/bash-completion/commit/1d25d72ca8633c19cb089dff447d08c531379c59))
* 2015 ([d423969](https://www.github.com/scop/bash-completion/commit/d423969756d0043007d80a7b30e5b4c611e06883))
* _filedir: Fix overquoted argument to compgen -d (RedHat: #1171396) ([d2920b7](https://www.github.com/scop/bash-completion/commit/d2920b7e79e5f347fed064b2a5aa952ef200e615))
* _filedir: Remove unused variable ([80c2bb6](https://www.github.com/scop/bash-completion/commit/80c2bb696437134d6d8a3fb9c9f455fec75f470a))
* make: Add __BASH_MAKE_COMPLETION__ variable ([f9115ce](https://www.github.com/scop/bash-completion/commit/f9115ce0bb215e9089266d6f1ec20c3375aa970a))
* _filedir: Avoid some unnecessary work with -d ([40c764a](https://www.github.com/scop/bash-completion/commit/40c764af57ea0cd0c5c5cebeba6c838230d30c93))
* gnokii: New completion ([3eb1b0d](https://www.github.com/scop/bash-completion/commit/3eb1b0dda81f2bc15a6c4f104cb5f0d2169c3101))
* gnokii: Various minor and cosmetic fixes ([b07e355](https://www.github.com/scop/bash-completion/commit/b07e35511138adc2a375ea3fcd21aee6a175e4f3))
* (testsuite): Add basic gnokii test case ([90ebb7e](https://www.github.com/scop/bash-completion/commit/90ebb7eb54b5598a5780dab90c984bad160f4268))
* gnokii: Drop dead code ([ca138d0](https://www.github.com/scop/bash-completion/commit/ca138d05db3853ea89f09979be3c32eea554de1c))
* gnokii: Fix completions of options that are prefixes for others ([8930330](https://www.github.com/scop/bash-completion/commit/89303303bd28d2c0b9d1e7e69e607ca2cf650760))
* gnokii: Include and install it ([1950590](https://www.github.com/scop/bash-completion/commit/1950590367838993fba00586139137a50a1d4f80))
* make: Fix detection of intermediate targets where make has changed its database whitespace ([4b209b0](https://www.github.com/scop/bash-completion/commit/4b209b0b172ddecff1e9aaf5de9ea64267fb9053))
* jshint: New completion ([adff509](https://www.github.com/scop/bash-completion/commit/adff509ec54a45957ac3c0f1facbe6dec3c5a0aa))
* tar: rework the completion completely ([8b23c84](https://www.github.com/scop/bash-completion/commit/8b23c84cc2935b15b8cb2ac1a90d061b914229a6))
* bsdtar, tar: Remove symlinks from git, have make create them ([81cfa06](https://www.github.com/scop/bash-completion/commit/81cfa067cc1bcb9d508beb8f6689e14e436e2220))
* (testsuite) Add required "empty" dir for tar ([a8f4507](https://www.github.com/scop/bash-completion/commit/a8f450797b47c25670c87452392161609ea67c36))
* tar: Style tweaks ([055d1ae](https://www.github.com/scop/bash-completion/commit/055d1ae59f54bf20d22b068631698427f38ce4a0))
* tar: Plug $line var leak ([d049481](https://www.github.com/scop/bash-completion/commit/d0494819521c1a70e3d83e26f2a55620ea64e89d))
* eog: Complete on *.ppm (RedHat: #1090481) ([6c2ae9f](https://www.github.com/scop/bash-completion/commit/6c2ae9fb18cb56d89b190286a422b60f03d85ba4))
* Document how to avoid command_not_found_handler interference ([1d9e705](https://www.github.com/scop/bash-completion/commit/1d9e705639e40c28b3ae4ce2a99c9c7308e3c653))
* sysctl: Try parsing options from help before usage ([e9fe2d9](https://www.github.com/scop/bash-completion/commit/e9fe2d9f7b9c3401f990b140d58de9e6c6b42a5c))
* sysctl: Return early on --help, --version ([d01427d](https://www.github.com/scop/bash-completion/commit/d01427df599c8ef2a32c95e286a6c82045d4d26e))
* ssh: Add -Q argument completion ([b6ffe26](https://www.github.com/scop/bash-completion/commit/b6ffe261f8515d4fddb319424b87d4d38d10dd91))
* ssh: Query ciphers and macs from ssh before hardcoded fallback ([cf4c7eb](https://www.github.com/scop/bash-completion/commit/cf4c7ebf6c754028957fe0061d0ed247a47ecb1c))
* ssh: Complete HostbasedKeyTypes,HostKeyAlgorithms,KexAlgorithms values ([2779b66](https://www.github.com/scop/bash-completion/commit/2779b66e5dc8d0278dcde921ed29fc60ce89f181))
* checksec: New completion ([ffd9038](https://www.github.com/scop/bash-completion/commit/ffd9038923aed3e8be6fe1c746ea3d41407cacb6))
* pdftotext: New completion ([4289460](https://www.github.com/scop/bash-completion/commit/4289460691719255ca5d34c04d2a7cacc6ccd1ba))
* pgrep, pidof, pkill, pwdx, vmstat: Add support for procps-ng ([f68589f](https://www.github.com/scop/bash-completion/commit/f68589fde4a95c1b30c9cbb70dcfada133f0f09e))
* __get_cword: avoid $index < 0 (Alioth: #315107) ([fa1ad7d](https://www.github.com/scop/bash-completion/commit/fa1ad7dff9e7099b14552a8782181e9b00f89cc2))
* rpm: Add --whatenhances/recommends/suggests/supplements and --recommends/supplements completions ([81acda7](https://www.github.com/scop/bash-completion/commit/81acda727a9ca34eb156c69becf38eac68b50ea7))
* modplug*: Associate *.oct and *.okt ([69cfaed](https://www.github.com/scop/bash-completion/commit/69cfaed89c25171d40d9d268de7ab6b9412b159f))
* __load_completion: New function, use in _completion_loader and _xfunc ([cad3abf](https://www.github.com/scop/bash-completion/commit/cad3abfc7ec1cfc2ae17a2330bf9f23993c73f09))
* mpv: Remove mplayer-aliased completion ([00abd48](https://www.github.com/scop/bash-completion/commit/00abd48e5b1d5d79fff46b7f791b2b90d1d6953b))
* make: Offer hidden targets when it is clear that the user is trying to complete one of them ([e0c0832](https://www.github.com/scop/bash-completion/commit/e0c08321795c8174fa2275d2683c4ffc1b36db5b))
* ssh-copy-id: Offer only *.pub to -i ([f9f66c3](https://www.github.com/scop/bash-completion/commit/f9f66c39d35c18c5985bc2baca2242c2e17d539a))
* sftp: Add -l arg non-completion ([4d82190](https://www.github.com/scop/bash-completion/commit/4d82190da70621d6b0fc375c063e12f97b27edb4))
* scp, sftp: Fix querying ssh options ([50ea015](https://www.github.com/scop/bash-completion/commit/50ea015f8bc66919eb0ecc7ed351c1f89755f8c0))
* scp, sftp: Complete -S on commands ([35a0f97](https://www.github.com/scop/bash-completion/commit/35a0f97a5a9bb0aadf834a81761855ad65bb5e28))
* (testsuite) Ignore files generated by complete-ant-cmd.pl ([f661811](https://www.github.com/scop/bash-completion/commit/f6618113b55e49c4c81f3a2a9fd5dc95a36a683f))
* make: Don't pick up variables when makefile is reloaded ([c5451db](https://www.github.com/scop/bash-completion/commit/c5451dbd310074f8bceeada0e48e542713dada1e))
* Load completions also from $XDG_DATA_DIRS (RedHat: #1264094) ([c89dcbb](https://www.github.com/scop/bash-completion/commit/c89dcbbd5510876f6304ef10806b00cc9fda19dc))
* chronyc: Add -6 option ([7669f0c](https://www.github.com/scop/bash-completion/commit/7669f0c1bece8f4a344d8e22f7d4969f8f141c10))
* chronyc: Add missing subcommands ([ef26136](https://www.github.com/scop/bash-completion/commit/ef26136ea3f4fcf123c1a4af741be625fbac1a05))
* chronyc: Update help text parsing ([bc03de5](https://www.github.com/scop/bash-completion/commit/bc03de502c3d8391ee220a3a28ed964db8c5e73a))
* chronyc: Wrap long lines ([aa404ca](https://www.github.com/scop/bash-completion/commit/aa404ca17d3838b18669e006ab30446cb402988b))
* chronyc: Parse command args from help output ([5fd0077](https://www.github.com/scop/bash-completion/commit/5fd00776c49f1d4552630c986e70f21e4a6028ad))
* ssh: Avoid completing commands before hostname ([f8f6ffa](https://www.github.com/scop/bash-completion/commit/f8f6ffa72ed3db9c6628b50f1bd3ef9582b4f264))
* cppcheck: Add native to --platform completions ([9cbd68b](https://www.github.com/scop/bash-completion/commit/9cbd68becbd728d5fda88a6f456c4c72eac92ec8))
* minicom: Recognize user ~/.minirc.* as config files ([39acdb2](https://www.github.com/scop/bash-completion/commit/39acdb21e89f89c207089da4c061c193d5d8af84))
* test suite: Fix ssh-copy-id test on old setups with no identities ([e899139](https://www.github.com/scop/bash-completion/commit/e899139ee38b96ebbf2338d35b4f5e99dce514a4))
* test suite: Expect failure in modinfo/modprobe if there are no modules ([0a6877e](https://www.github.com/scop/bash-completion/commit/0a6877e8395ac6b46b4bdf3877b674c37aa4dec8))
* Set up Travis ([e3c8573](https://www.github.com/scop/bash-completion/commit/e3c8573d14866041516beb3a503e3bd752666ec2))
* test suite: Output tool log on failure in CI ([fa6b80f](https://www.github.com/scop/bash-completion/commit/fa6b80f89c53233fd00b9c33fde2161eef9d8e2a))
* test suite: Make apt-get test less sensitive to available commands ([7234914](https://www.github.com/scop/bash-completion/commit/7234914a9fb3a17bc116f18bd1a8b6e4beece147))
* travis: Avoid Travis default ri, use distro one instead ([dc6e8c2](https://www.github.com/scop/bash-completion/commit/dc6e8c257fb895142564eac871daeec78331d522))
* README: Update POSIX spec link ([ac9f41b](https://www.github.com/scop/bash-completion/commit/ac9f41b93d8d280c9069cfb06efffcef991404d0))
* Update URLs and various instructions to GitHub ([53215d4](https://www.github.com/scop/bash-completion/commit/53215d4a74ace3477e48dc70e5be19f8bb3c0866))
* aclocal, automake: Install for versioned 1.14 and 1.15 executables ([caf3b36](https://www.github.com/scop/bash-completion/commit/caf3b364e367bcb2bd518fd436ec134049c2c9e6))
* build system: Switch to xz compressed tarball ([b51eb9d](https://www.github.com/scop/bash-completion/commit/b51eb9d7f1bc9a7ad6c6f5feb56fca5b963572fa))
* Drop <NAME_EMAIL> ([aee91a9](https://www.github.com/scop/bash-completion/commit/aee91a91baa3c32dc315be39d5f95f8045217594))
* README: Convert to markdown ([0d3e597](https://www.github.com/scop/bash-completion/commit/0d3e59703fa016f2a8ed0fe6efdfe7fd00fa9250))
* README: Split contributing to separate CONTRIBUTING doc ([0345d02](https://www.github.com/scop/bash-completion/commit/0345d028ee4ad7793d6293a54bb69929e442041b))
* Use command built-in to run sed to avoid any custom aliases ([0b37725](https://www.github.com/scop/bash-completion/commit/0b377254203680cb483168f45eaac301db8ac1b3))
* test suite: Fix ssh partial hostname completion test ([4f13792](https://www.github.com/scop/bash-completion/commit/4f13792b4fe87ebfcd6d9bbe15c9813f2a1b2a94))
* travis: Run tests with --all to get some more useful output ([d04af62](https://www.github.com/scop/bash-completion/commit/d04af6260a7cfcb1c521298eae346bec9303bfc0))
* travis: Install more packages for [0-9][ab]* test coverage ([0cec990](https://www.github.com/scop/bash-completion/commit/0cec99072d36ffe68bec89e4e668ec94b4534929))
* test suite: Use unsupported instead of xfail for modinfo/modprobe cases ([501c106](https://www.github.com/scop/bash-completion/commit/501c10692dfcf744dbf333d78e0ed933b9dc51a8))
* test suite: Mark unsupported look test case as such, not unresolved ([3dedaa9](https://www.github.com/scop/bash-completion/commit/3dedaa9dd971ea1177561c3f4525ad044ca5dfc5))
* travis: Add note about (currently) N/A packages ([b5caa09](https://www.github.com/scop/bash-completion/commit/b5caa09b6d1a24d778e87cb65f31590e8ce8b8e6))
* travis: Install more packages for c* test coverage ([e41426a](https://www.github.com/scop/bash-completion/commit/e41426a57d9924a22af16e2d92b062568470ee61))
* travis: Install more packages for [de]* test coverage ([1cca6ed](https://www.github.com/scop/bash-completion/commit/1cca6ed104330e1541942204890063f8accf798d))
* Modify all usages of 'sed' to be run with command bash builtin ([1aad419](https://www.github.com/scop/bash-completion/commit/1aad419db4851fc7fa4b2e384645ecda2f000d88))
* lint: Check for sed without "command" ([417122b](https://www.github.com/scop/bash-completion/commit/417122bd0293d79921cde05d80ecc1b6654a3542))
* gnokii, minicom: Use grep through "command" ([536d79f](https://www.github.com/scop/bash-completion/commit/536d79ff42b38b38cc7b7453b0219e6dda888efc))
* mysql, puppet: sed portability fixes ([f8b0828](https://www.github.com/scop/bash-completion/commit/f8b0828e4fcb25f9628b268fb760aa23cc5db60f))
* mysql: Fix --default-character-set completion with mariadb ([695b28a](https://www.github.com/scop/bash-completion/commit/695b28aa860d867c49c2ea17e62f1d5b6ebd80a7))
* travis: Install more packages for [fg]* test coverage ([987af3f](https://www.github.com/scop/bash-completion/commit/987af3f463cbe48f7b31b1a7bed9f3262772b716))
* travis: Install more packages for [hi]* test coverage ([e0ff9ce](https://www.github.com/scop/bash-completion/commit/e0ff9cea15612e529c9d0702c0cf7ecc61860e67))
* Update copyright year ([68f3e4e](https://www.github.com/scop/bash-completion/commit/68f3e4eb144125ac3d5dfc57f9482f4815dc9c41))
* indent: Remove generic long option completion ([c4517d3](https://www.github.com/scop/bash-completion/commit/c4517d393250863afbb9ddc9b481ea6a61519720))
* Don't complete hostnames found after Hostname in ~/.ssh/config ([4621117](https://www.github.com/scop/bash-completion/commit/46211179bff660992417fccd68e598b4e612e56a))
* travis: Install more packages for [jkl]* test coverage ([6699288](https://www.github.com/scop/bash-completion/commit/6699288aee3ffd0530ad268fe06bc5c042eecec5))
* travis: Install more packages for m* test coverage ([3495a48](https://www.github.com/scop/bash-completion/commit/3495a48ecbef38ab5e12ff41ecdbd22cb54c933c))
* travis: Install more packages for [op]* test coverage ([26c2d23](https://www.github.com/scop/bash-completion/commit/26c2d231dd5e081c7e45d693eeb0f8a667850b0c))
* travis: Install more packages for [qr]* test coverage ([d491063](https://www.github.com/scop/bash-completion/commit/d4910632f213af7eb6ee537300b1f76ce2faa0b7))
* travis: Install more packages for [stuvw]* test coverage ([9d12e2c](https://www.github.com/scop/bash-completion/commit/9d12e2c554b7e2963773792f6936dc75df540951))
* travis: Install more packages for [xyz]* test coverage ([18ca938](https://www.github.com/scop/bash-completion/commit/18ca93839af37b1e494c832016d2a766833ea7f4))
* Remove various comments related to bash versions we don't support ([ffdc9da](https://www.github.com/scop/bash-completion/commit/ffdc9daecebf5ca4b45995de40469579fd36aac7))
* ssh: Extract duplicate code to _ssh_configfile ([09f56f8](https://www.github.com/scop/bash-completion/commit/09f56f80a68f2fddc52cf8c91637b80c6ae02164))
* xmllint, xmlwf, xsltproc: Complete on Mallard *.page files ([1c005b1](https://www.github.com/scop/bash-completion/commit/1c005b19e9b262fdd40c8fc5aae4e75779b1d78b))
* README: Expand troubleshooting section somewhat ([06996ea](https://www.github.com/scop/bash-completion/commit/06996ea0d4ac83a002613f1d7e50ce68a595cb31))
* README.md: Not need for autoreconf, fixes #11 ([20f9013](https://www.github.com/scop/bash-completion/commit/20f9013f38d5c54892900654ab037c173e5cc167))
* zopflipng: New completion ([56c3ea1](https://www.github.com/scop/bash-completion/commit/56c3ea163fd294a144a79d641881f34e625b77db))
* README.md: Markdown tweaks ([8a748d3](https://www.github.com/scop/bash-completion/commit/8a748d32fae35f2dcf5a7c7061f183a18adae320))
* README.md: More markdown tweaks ([76d8823](https://www.github.com/scop/bash-completion/commit/76d88231013b0027239a5523f9c70f0ad3f571ee))
* make-changelog.py: Make work with Python 3 ([8a9cb54](https://www.github.com/scop/bash-completion/commit/8a9cb54d9769fcfebc37d85d204a5893b7ae0eee))
* make-changelog.py: flake8 fixes ([47ca0cd](https://www.github.com/scop/bash-completion/commit/47ca0cdaba318f6419b20d86050c6ef04ce1ce66))
* make-changelog.py: Fix footer line output ([d178db6](https://www.github.com/scop/bash-completion/commit/d178db64b28bc5db39a33641930deefeab4baf10))
* make-changelog.py: Set myself in footer ([7c713e8](https://www.github.com/scop/bash-completion/commit/7c713e872606558fc7314425e3dbaf3d85fea81f))
* README.md: Note autoreconf need only in unprepared tarball ([3c3b696](https://www.github.com/scop/bash-completion/commit/3c3b696e8a9604f3a0dec159aa5da2c1c4d0f09e))

## 2.1 (2013-04-05)

* eog: New completion. ([3a1cdbd](https://www.github.com/scop/bash-completion/commit/3a1cdbdadc92e041b3b446f4d40d5ffcabe86d4e))
* wsimport: New completion. ([bffce42](https://www.github.com/scop/bash-completion/commit/bffce4218e33bc61c0551b87e4b5bfbb41ad4223))
* kcov: Add new sort types (introduced in kcov-9). ([36f1b83](https://www.github.com/scop/bash-completion/commit/36f1b832fd3544dd44dad7b6bce5c115f45cd50d))
* kcov: Complete arguments of --limits option. ([f604f6c](https://www.github.com/scop/bash-completion/commit/f604f6ce5b952e10ea6053445e9f70c2079943b8))
* Add .msi completion for Wine ([f3e3fc5](https://www.github.com/scop/bash-completion/commit/f3e3fc5a4d51367a3e8ce29255bb386c809d0947))
* ssh: Add -O argument completion (Debian: #680652). ([217e143](https://www.github.com/scop/bash-completion/commit/217e143fd69ad2b83ec8187af2e9e1c21dcb759a))
* Add more complete OpenDocument support to unzip completion. ([6a71ee5](https://www.github.com/scop/bash-completion/commit/6a71ee504c11b798125b9c85359d5b7367a27dd4))
* Add support for OOXML document format extensions to unzip completion. ([7c7b560](https://www.github.com/scop/bash-completion/commit/7c7b5608c8505633aa55e106f98d2a943937d41a))
* Fine tune previous commit. ([ca53345](https://www.github.com/scop/bash-completion/commit/ca533452195134de6ceb5c3f894e824fac53849a))
* wine: Fix extension glob to work on its own. ([e2e64a1](https://www.github.com/scop/bash-completion/commit/e2e64a16329cf0005006dffea590dce20200469d))
* man: Add support for .lz man pages (RedHat: #839310). ([c9ed166](https://www.github.com/scop/bash-completion/commit/c9ed16694da974ce9faa3f49a274c7c6cfb4abc9))
* man: Trivial cleanups. ([a624cc2](https://www.github.com/scop/bash-completion/commit/a624cc2c3026981835330f4d001b7df53f8d84ae))
* clzip, pdlzip, plzip: New lzip alias completions. ([fc107b4](https://www.github.com/scop/bash-completion/commit/fc107b4bdc5a42302b6316ab80755d6cca69d9ba))
* lzip: Do not append space after equal sign in long options. ([9f0a6e4](https://www.github.com/scop/bash-completion/commit/9f0a6e45f8238e3220db3ffaa058c39110ea7a59))
* mussh: New completion. ([882d527](https://www.github.com/scop/bash-completion/commit/882d527237c77cdc757672affa070bb1b150b778))
* mount.linux: Add some new mount options intoduced in Linux 3.5 ([3ea1597](https://www.github.com/scop/bash-completion/commit/3ea1597d3c6c926553b850caad75527a08412eb1))
* mount.linux: Clean up mount options, remove duplicates. ([3aa040d](https://www.github.com/scop/bash-completion/commit/3aa040d25f5556b6e9bf92c784072aba7620f729))
* Remove trailing whitespace ([3f9fe7a](https://www.github.com/scop/bash-completion/commit/3f9fe7a853c74d68f64e05ecaca474e7a6819d81))
* Remove more whitespace ([a9b253c](https://www.github.com/scop/bash-completion/commit/a9b253ca8b475dc2ffda6edc7451af28d23d4eed))
* Trim trailing whitespace. ([a6ff579](https://www.github.com/scop/bash-completion/commit/a6ff57986f22d19e7a638e398a73a1a3ac19ff13))
* slackpkg, slapt-get: Update the list of package sets. ([371fb91](https://www.github.com/scop/bash-completion/commit/371fb91b213c3bb4b86eb22e09701ec1be18b7dd))
* colormake: New make alias completion (LP: #743208, Debian: #682557). ([31e262b](https://www.github.com/scop/bash-completion/commit/31e262bcaf9bee249deb24a0e08cd85346d7a628))
* Ignore colormake symlink. ([6158bd2](https://www.github.com/scop/bash-completion/commit/6158bd2d86f6122cfc8d3df045e9f4c7c8f3293a))
* opera: Handle options. ([3c49af9](https://www.github.com/scop/bash-completion/commit/3c49af9ec97b66553c3d756de8f4b5a5f8a54708))
* mount.linux: Add options completion for davfs. ([f379e92](https://www.github.com/scop/bash-completion/commit/f379e9275c5a966dacd3b2bd45a5b651aae55641))
* evince: Evince supports opening .pdf.xz files (Alioth: #313739). ([30f9335](https://www.github.com/scop/bash-completion/commit/30f93357c81bf3fbb03ef8a6b3b0f5ce9addde13))
* Fix __reassemble_comp_words_by_ref for $COMP_CWORD == ${#COMP_WORDS[@]} ([dc15093](https://www.github.com/scop/bash-completion/commit/dc150937d126430f5cc20dfaf3239a71049fe0df))
* valgrind: Add --soname-synonyms option arguments completion. ([904faab](https://www.github.com/scop/bash-completion/commit/904faab0c57b4f6a65a4f62dc9f644d4771e59fe))
* eject: New completion. ([b9276c8](https://www.github.com/scop/bash-completion/commit/b9276c8a374ac5e8cbe7ac814fe3c989f903ddd0))
* fusermount: Complete curlftpfs-mounts for "fusermount -u" (Debian: #685377) ([2897e62](https://www.github.com/scop/bash-completion/commit/2897e62fe7e535eb048f7e08f03ac3fbc3a84fa5))
* gphoto2: new completion ([92ddcea](https://www.github.com/scop/bash-completion/commit/92ddcea20a473a8d59b1efaea94261b4b36fa35c))
* nmcli: new completion ([7f1721d](https://www.github.com/scop/bash-completion/commit/7f1721dd9064e5685b984975a7819bfe18b8d71b))
* cppcheck: Add new standards to --std option. ([1c362f4](https://www.github.com/scop/bash-completion/commit/1c362f4c99eea0ae32ffb6f006f71d1bfb18bc78))
* make: convert make completion to use smarter parser ([b28d710](https://www.github.com/scop/bash-completion/commit/b28d7108d3677c61bd01c51ccee8bb1cf9e3bfba))
* Fixed tilde expanding in _filedir_xspec ([fdb080f](https://www.github.com/scop/bash-completion/commit/fdb080ff89195f85e1b76d0752979dfefa777595))
* make: Do not append space if target is a filepath. ([2babb45](https://www.github.com/scop/bash-completion/commit/2babb45402ee338b888056f01222a602d07a44cd))
* tar: Fix detection if the $prev is a tar file. ([3622f2f](https://www.github.com/scop/bash-completion/commit/3622f2f9e531ddbb243b39774546cf11ee9ccf3e))
* _parse_help, _parse_usage: Run commands in C locale. ([8227351](https://www.github.com/scop/bash-completion/commit/822735146f5a5ef71c75f6f8494ad3969eaf2cc5))
* testsuite/_filedir: Remove the cruft from the a\$b->h unit test (Alioth: #313480) ([23ac383](https://www.github.com/scop/bash-completion/commit/23ac38333e469eac47d35dae7c640bff4a6b5203))
* make: incremental completion for make with compact display ([39f00f9](https://www.github.com/scop/bash-completion/commit/39f00f92e52b783e7e9e43aac4b4274cc9dee152))
* make: Convert internal variable names to lowercase, indentation fix. ([b93e399](https://www.github.com/scop/bash-completion/commit/b93e3999b0c485a9808c1d4cb0f825b8019b5a73))
* lvm volumes: Complete on /dev/mapper/* (RedHat: #851787). ([8e63eaf](https://www.github.com/scop/bash-completion/commit/8e63eafb83342dec5a04fe3c49330c6cbe00f96d))
* tar: Don't take -I to mean bzip2. ([f321357](https://www.github.com/scop/bash-completion/commit/f32135799155cbde9237567a5a3a811172adf341))
* feh: Add new options introduced in feh-2.7. ([52163a3](https://www.github.com/scop/bash-completion/commit/52163a337f34cf185fe55833bbd41080a0228baf))
* wget: New completion. ([e29c6bc](https://www.github.com/scop/bash-completion/commit/e29c6bc872ba437f48eae7c70347c843413de4cc))
* wget: Fix completion of multiple tags for --{follow,ignore}-tags. ([e0c70ab](https://www.github.com/scop/bash-completion/commit/e0c70abc8b63bed961b736b17e4757ca0a5f1875))
* useradd: Fix -k, -K, and --home-dir argument completions. ([09d24da](https://www.github.com/scop/bash-completion/commit/09d24da884fcf98a7a22a4487ab79f03c3f20026))
* useradd, usermod: Support comma separated -G/--groups arg completion. ([8ec4846](https://www.github.com/scop/bash-completion/commit/8ec484692225f2f1565975a763df856dfbb7f27b))
* wget: Use == instead of =. ([d803323](https://www.github.com/scop/bash-completion/commit/d803323974dd979a55f7f87e96e96b5bf9d9da4f))
* cppcheck: Add --language/-x argument completion. ([b9189ce](https://www.github.com/scop/bash-completion/commit/b9189ce170388d87c46a4a37f3e05dba0cd8878a))
* new completion: svcadm ([80356ca](https://www.github.com/scop/bash-completion/commit/80356ca816cfee198bad59359d7a641449183fec))
* new completion: pkg-get ([6ddec67](https://www.github.com/scop/bash-completion/commit/6ddec67c2f923ce54f3935bc23822c9eaf350430))
* new completion: pkgadd ([a54fa73](https://www.github.com/scop/bash-completion/commit/a54fa7340a617eabb2c3bfede9a24c9f142505be))
* new completion: pkgrm ([ff444b5](https://www.github.com/scop/bash-completion/commit/ff444b528397f273d46da7c52a445b924adccc00))
* new completion: pkgutil ([e5ac55d](https://www.github.com/scop/bash-completion/commit/e5ac55d5df03b04ff082cebd8245fc28c2ef59fd))
* better entry ([166ac77](https://www.github.com/scop/bash-completion/commit/166ac7754f31d95e7230cb80c521cda01e441faa))
* fix perms ([8002d32](https://www.github.com/scop/bash-completion/commit/8002d320afee9e38aa39c1acb553402f4f109f84))
* zathura: Add simple completion for zathura document viewer. ([97eb4da](https://www.github.com/scop/bash-completion/commit/97eb4daa62004ee882961ccd49e7af7418443c81))
* gpg, mplayer: Restore correct options. ([6be8628](https://www.github.com/scop/bash-completion/commit/6be86286d6db176b785dcea5fdd262b49e133934))
* lvm: Fix typo in option name: s/continguous/contiguous/. ([b2365b3](https://www.github.com/scop/bash-completion/commit/b2365b3d8743fb0fdca70c5138e66e6b389ba5bd))
* mplayer: Add -subcp argument completion. ([7f2eb83](https://www.github.com/scop/bash-completion/commit/7f2eb8327c9c59576cd181f813bd27e6e681b02b))
* mplayer: Add some new option argument completions. ([f3e1079](https://www.github.com/scop/bash-completion/commit/f3e10798e68d9b714a0f527179c48b22ab5c16ce))
* mplayer: Cleanup. ([2c28608](https://www.github.com/scop/bash-completion/commit/2c2860826fdc624b6aad6532dd46cef79389d872))
* mplayer: Add opus to the list of supported formats. ([b5dce1c](https://www.github.com/scop/bash-completion/commit/b5dce1c4b69ff2e9935941e54bf9f1ef3ceee542))
* completions/Makefile.am: symlinks depends on $(DATA) to avoid race conditions ([370b7a0](https://www.github.com/scop/bash-completion/commit/370b7a0d2fdf7f322b59f3c35c1cea19901ef2f2))
* _command_offset: Restore compopts properly (Alioth: #313890) ([2472fad](https://www.github.com/scop/bash-completion/commit/2472fad5de03ef91864926240a709b3f2b72a173))
* Update copyright year and release number. ([3b93c22](https://www.github.com/scop/bash-completion/commit/3b93c22a5bb9e47301fca89ec677a9805ea71518))
* _known_hosts_real: Filter ruptime stdout error spewage (Alioth: #313893). ([f917b75](https://www.github.com/scop/bash-completion/commit/f917b750dda9dc977579c264840878a97ed0fdfe))
* lv{create,resize,extend}, vg{create,reduce,extend,split}: Fix variable leaks. ([b2d985c](https://www.github.com/scop/bash-completion/commit/b2d985c0bf9c0013a86cb63247d67b0ed39c9e75))
* man: Fix -P/--pager full path arg completion. ([b8cbf1b](https://www.github.com/scop/bash-completion/commit/b8cbf1ba9401fc99375e01cfc122a7ef2aa620f2))
* tcpdump: Fix -z full path arg completion. ([f009a1b](https://www.github.com/scop/bash-completion/commit/f009a1bfca25e262a68bbe52520bf91531ae6773))
* valgrind: Fix full path <command> arg completion. ([c6f6080](https://www.github.com/scop/bash-completion/commit/c6f6080569e3572893a6ff6561b3b8182dd2d2c1))
* vgcreate: Add missing symlink. ([e3a1a49](https://www.github.com/scop/bash-completion/commit/e3a1a49b972d1ade7342cd53a0a47d2589466fa1))
* lvm: Take option args into account when counting args (RedHat: #860510). ([e96613e](https://www.github.com/scop/bash-completion/commit/e96613e32e21ea389dddba9756bf3d78070acc4b))
* lvm: Add _lvm prefix to helper functions. ([609034d](https://www.github.com/scop/bash-completion/commit/609034db74f60eab6ae4531696a44849647f7d42))
* mount: Fix parsing /etc/fstab on *BSD. ([7d59112](https://www.github.com/scop/bash-completion/commit/7d591128a8baf91dfe3a461ae53f7acded4c7473))
* feh: Fix list of background styles. ([9e04f3e](https://www.github.com/scop/bash-completion/commit/9e04f3edc8e70c5aeef98866e400f772c0395e10))
* mount.linux: Add options completion for nfs. ([4a76f5a](https://www.github.com/scop/bash-completion/commit/4a76f5a16646ef3c0407e092a66f9f35e273ff81))
* mount.linux: Add some new mount options intoduced in Linux 3.7 ([74a37e7](https://www.github.com/scop/bash-completion/commit/74a37e7507ff5c779a8cdd271b2aeb80d33bcc96))
* chronyc: New completion. ([ff11fed](https://www.github.com/scop/bash-completion/commit/ff11fed5f8f1d73b02d515da3af335644807ab4d))
* scp: Treat strings with slash before colon or starting with [.~] as local. ([41a37d7](https://www.github.com/scop/bash-completion/commit/41a37d767940af7928282874618e3dc60549de54))
* useradd,userdel,usermod: Add -R/--root arg completion. ([5c8279b](https://www.github.com/scop/bash-completion/commit/5c8279b818560146176372752c4a87c588207674))
* userdel: Add -h/--help non-completion. ([1d75b67](https://www.github.com/scop/bash-completion/commit/1d75b671a00337ae74a1295682c130649aef3bfe))
* luseradd,lusermod,luserdel: New completions. ([08203f7](https://www.github.com/scop/bash-completion/commit/08203f7743ea41e7f10a891c130a18be713bd494))
* man: Don't expand man page extensions too early. ([49ea121](https://www.github.com/scop/bash-completion/commit/49ea121e5d86eea76bfa18174fd8f70911217d09))
* ssh: Add some -o and related arg completions. ([45c9ff5](https://www.github.com/scop/bash-completion/commit/45c9ff5f691cfb6decdba1bf362708a698b3d595))
* fix interface completion ([b9b4c6b](https://www.github.com/scop/bash-completion/commit/b9b4c6bf2ce61d46a798f11a4d04bc55ba276b0a))
* add -p option completion ([89098f7](https://www.github.com/scop/bash-completion/commit/89098f79fd33216a46f7135a994f4c0a8057671f))
* nc: New completion. ([26991e1](https://www.github.com/scop/bash-completion/commit/26991e1bf4270a95dfea537b7ea514a1130b65bd))
* tar: Simplify bzip patterns. ([9c80d8b](https://www.github.com/scop/bash-completion/commit/9c80d8b5217df396efa44e0eccf94616bf585013))
* tar: Recognize taz and tb2 as compressed tarballs. ([e8daf2d](https://www.github.com/scop/bash-completion/commit/e8daf2d2790b90b2abebd63b7a5534f1ea7e446a))
* ncftp: Add option completion. ([2eeffee](https://www.github.com/scop/bash-completion/commit/2eeffeea7a5c067f4dc0c7645f664cd6232264f0))
* Avoid sourcing dirs in completion loader to avoid fd leaks (RedHat: #903540). ([fea1c17](https://www.github.com/scop/bash-completion/commit/fea1c178b47cf7ac95ab27c39a98e0464e19976c))
* Brown paper bag fix for the previous commit. ([c4cc3eb](https://www.github.com/scop/bash-completion/commit/c4cc3eb63bf120cf81e25cb97780fd3e4a91ebff))
* modprobe: Don't suggest installing already installed modules. ([d08b9f2](https://www.github.com/scop/bash-completion/commit/d08b9f233559b3dced20050ba312b08fe0de53b4))
* ngrep: New completion. ([8c57295](https://www.github.com/scop/bash-completion/commit/8c572951330bb0ed3a669fd2d8e4dd219430ff11))
* tshark: New completion. ([cee32c6](https://www.github.com/scop/bash-completion/commit/cee32c6424113a2149daa2830ecb3fa942781420))
* _mac_addresses: Fix with net-tools' ifconfig that outputs ether, not HWaddr. ([f6df76e](https://www.github.com/scop/bash-completion/commit/f6df76e8cac6ae47b93c87d594d70dab211b860a))
* _mac_addresses: Try ARP cache with "ip neigh" if arp is not available. ([87dede9](https://www.github.com/scop/bash-completion/commit/87dede96c0fe8961081310284bfe58972dd801c4))
* wol: Try "ip addr" before ifconfig for finding out broadcast addresses. ([19ce232](https://www.github.com/scop/bash-completion/commit/19ce23282ca6c00b482ac8044b64d91dbb3b62e6))
* _mac_addresses: Try local interfaces with "ip link" if ifconfig is N/A. ([b78ef32](https://www.github.com/scop/bash-completion/commit/b78ef321bedfdf8071627366de74451967846f98))
* _ip_addresses: Try with "ip addr" if ifconfig is not available. ([aa516ac](https://www.github.com/scop/bash-completion/commit/aa516acdc537b14541cb16424d51af6403321705))
* _available_interfaces: Without -a, try with "ip link" if ifconfig is N/A. ([3064e9d](https://www.github.com/scop/bash-completion/commit/3064e9d707404e5aa59bb8b643d02208fb0f9daa))
* dnsspoof,filesnarf,macof,sshow,tcpkill,tcpnice,urlsnarf: Fix -i completion. ([7543e0b](https://www.github.com/scop/bash-completion/commit/7543e0baf812e24066b863b5d13fdb1efffc3428))
* arpspoof,dsniff,ether-wake,nmap: Offer active interfaces only. ([9d15e25](https://www.github.com/scop/bash-completion/commit/9d15e25a9e527e2d310ba0e0501e26350998532a))
* modinfo: Use ,, for lowercasing instead of tr in a subshell. ([06002d0](https://www.github.com/scop/bash-completion/commit/06002d04c7cbfe9ac7c92508b6dcb7323627d07a))
* pydoc: New completion. ([5c8a002](https://www.github.com/scop/bash-completion/commit/5c8a002008bd2dfdb9196b57c368193a6e05b1e2))
* pyflakes: New completion. ([a77d3d5](https://www.github.com/scop/bash-completion/commit/a77d3d550564198c11e2b4823a53979868262a48))
* python, pydoc: Add module completion. ([0e8d34e](https://www.github.com/scop/bash-completion/commit/0e8d34e6bda72787b6b9833e042d3f55d73a4288))
* pydoc: Complete on keywords and topics. ([44f1065](https://www.github.com/scop/bash-completion/commit/44f1065ada14dda97d3c0417b120207d460e0be0))
* pylint: New completion. ([f1100ef](https://www.github.com/scop/bash-completion/commit/f1100ef25a69a9910882c5692926cab22c173496))
* xrandr: Don't leak $i when completing --mode. ([d66fc76](https://www.github.com/scop/bash-completion/commit/d66fc76be6058098d98e07e049db92079268dc0f))
* xrandr: Cleanups. ([a3d4266](https://www.github.com/scop/bash-completion/commit/a3d4266cab93e2dacf4fc058344f10a0e0860313))
* xrandr: Avoid --mode completion error when --output is not given. ([225b395](https://www.github.com/scop/bash-completion/commit/225b395b494d8c4f2167429cc58256e5898d1d14))
* xrandr --mode: Clean up one awk call. ([1e6a791](https://www.github.com/scop/bash-completion/commit/1e6a79196cc4942d24ff78d7955e4c295786e883))
* xrandr: Use _parse_help. ([ae42c96](https://www.github.com/scop/bash-completion/commit/ae42c9675838fc92a7b77f71c0a4988c8e724822))
* xrandr: Add bunch of option arg non-completions. ([ba50a54](https://www.github.com/scop/bash-completion/commit/ba50a546418d88cfc45653cb1e91b43764eb9a3b))
* vipw: Add -R/--root arg completion. ([23a049a](https://www.github.com/scop/bash-completion/commit/23a049a801a6ce2c86e343710cc0245e003df0cb))
* host: Complete with known hosts. ([eb0be65](https://www.github.com/scop/bash-completion/commit/eb0be65d630e348d8e644fb70d8e2a86b00e3bdc))
* groupmems: Add -R/--root arg completion. ([250d5eb](https://www.github.com/scop/bash-completion/commit/250d5eb93256bec97be3f5a01a1344939928b978))
* acpi,chpasswd,dmesg,gkrellm,groupmems,hwclock,lastlog,pwd,vipw: Complete options even without "-" given. ([452e938](https://www.github.com/scop/bash-completion/commit/452e938766c784ce6750f8a718cb90a84b2e9d7d))
* ip: Remove some stale TODOs. ([06fd510](https://www.github.com/scop/bash-completion/commit/06fd510c44c17db856678e0d572c14b0cebb2e00))
* ip: Improve addr show and link show completions. ([61fe8d1](https://www.github.com/scop/bash-completion/commit/61fe8d10a8e15285e2ad152017403e8bb609614b))
* _available_interfaces: Try with "ip link" if ifconfig is N/A also with -a. ([eef7941](https://www.github.com/scop/bash-completion/commit/eef7941842f309491d52a9fef457a8fbb6a4e6a2))
* make: Don't leak $mode. ([2758c4f](https://www.github.com/scop/bash-completion/commit/2758c4fd7eacafdfd27772e46ccd00731d36931d))
* (testsuite) Fix pwd unit test. ([23406dc](https://www.github.com/scop/bash-completion/commit/23406dcf20f4718775e8556766be7d55a879a4be))
* make: Make work in POSIX mode. ([c0818b0](https://www.github.com/scop/bash-completion/commit/c0818b005ab8056bd4bb0a1894bfcd7de148238e))
* (testsuite) Make pydoc test more likely to work with our limited expect buffer size. ([0837ad0](https://www.github.com/scop/bash-completion/commit/0837ad07d93c62e2b6b9cb917239183d9fbda95a))
* cpio: Cleanups. ([e1a0759](https://www.github.com/scop/bash-completion/commit/e1a075971d37ed563ad22cc5a1df548a677d67da))
* cpio: Recognize pass thru when -p is bundled w/other options (RedHat: #912113). ([eb396b5](https://www.github.com/scop/bash-completion/commit/eb396b58a709201e61daf2e381abecb411863b2a))
* vpnc: Use _parse_help instead of hardcoding options, add basic test case. ([e479610](https://www.github.com/scop/bash-completion/commit/e4796104bc81247bceb591164b227b3cd762c46f))
* vpnc: Add bunch of option arg (non)completions. ([e7cd7ba](https://www.github.com/scop/bash-completion/commit/e7cd7ba7df96a5a2bc74693fdede77ca06349af0))
* genisoimage: Use _parse_help instead of hardcoding options, add basic test case. ([e424ed3](https://www.github.com/scop/bash-completion/commit/e424ed3e52f90884377cb2384498b2f907aff1e9))
* : Line continuation, whitespace, and compgen -W ... -- "$cur" quoting cleanups. ([6185297](https://www.github.com/scop/bash-completion/commit/6185297fc90e82c2788a8f3ea0fd42d54267c499))
* file-roller: New completion. ([7a1aad7](https://www.github.com/scop/bash-completion/commit/7a1aad780e9f64ad213caed8aa71f45e12294e63))
* strings: New completion. ([ad8d1f1](https://www.github.com/scop/bash-completion/commit/ad8d1f1a8f879e696956ce0bd7733ef5f3365a6b))
* ss: New completion. ([12ae7eb](https://www.github.com/scop/bash-completion/commit/12ae7eb21451710492a8bc450e07f6c3bd79a4ec))
* hexdump: New completion. ([552a2f2](https://www.github.com/scop/bash-completion/commit/552a2f2a94cf03ef6ee8d53ba8bad1dbb52af640))
* xxd: New completion. ([e38e68f](https://www.github.com/scop/bash-completion/commit/e38e68f96cd7d9d1c973462368003e12661305f9))
* hexdump: Actually install for hd as well. ([73d1f0f](https://www.github.com/scop/bash-completion/commit/73d1f0f16482b3261291454f37bd9e663fbafaa8))
* udevadm: Deprecate ours, one is shipped in systemd >= 196 (RedHat: #919246). ([48158ee](https://www.github.com/scop/bash-completion/commit/48158ee3e454fe4f6baf30bc59884962aaf3ce8e))
* Fix __ltrim_colon_completions() fail on parameter (\$1) containing a glob. ([e191799](https://www.github.com/scop/bash-completion/commit/e191799dea5b38272dba7e5efe60b3adf86311e5))
* interdiff: New completion. ([796fbbd](https://www.github.com/scop/bash-completion/commit/796fbbdc86e10df67f4930597ce56f9fab5f9457))
* koji: Complete on build targets when --target is given to wait-repo. ([2f2f127](https://www.github.com/scop/bash-completion/commit/2f2f1278c7ae535d1c3763370e22e7f4083a50e7))
* lua: New completion. ([99153fb](https://www.github.com/scop/bash-completion/commit/99153fb1ef75b9beec2d85966883bf3e99d095ad))
* luac: New completion. ([29f5a4a](https://www.github.com/scop/bash-completion/commit/29f5a4a5f4403f246e22fb8c2133e8696d2f0e41))
* pkg-config: Try to complete --variable= if package name is already given. ([408cb08](https://www.github.com/scop/bash-completion/commit/408cb08051cf18404f89e3fb89c4924cc3fa04ea))
* tar: Fix completing files inside *.tlz when J is explicitly given. ([d02d940](https://www.github.com/scop/bash-completion/commit/d02d94080d950768bfeb8c830a678da55549f824))
* tar: Support *.tar.lz (Debian: #703599). ([beaba62](https://www.github.com/scop/bash-completion/commit/beaba62b346bc588d1f9466f338f64073ad2716f))
* patch: New full featured completion. ([7afc973](https://www.github.com/scop/bash-completion/commit/7afc97366f0a931cb98bd151c5971d4c4c926e59))
* unzip/zipinfo: Associate with more StarOffice extensions. ([f0a3147](https://www.github.com/scop/bash-completion/commit/f0a3147179ea06bdcd463e2d160df97255656e95))
* jar: Reuse unzip's xspec (RedHat: #928253). ([3cb64ac](https://www.github.com/scop/bash-completion/commit/3cb64accaf281f6315baa7bced2c72e1eff9d12f))
* cppcheck: Complete --include= with filenames. ([1a3967c](https://www.github.com/scop/bash-completion/commit/1a3967c8d4d8d9027040e00ecf0756dafaeb88ec))
* Fix helper script to create changelogs ([4d096e0](https://www.github.com/scop/bash-completion/commit/4d096e0b873478c7b01c5019038c646b7184da60))
* Releasing 2.1 ([3085c7e](https://www.github.com/scop/bash-completion/commit/3085c7e12179817a02a611016606391295c69942))

## 2.0 (2012-06-17)

* sudo: Handle options (Alioth: #311414). ([91a61af](https://www.github.com/scop/bash-completion/commit/91a61afe59eeca58736e55745ae76ad6641a6a12))
* sudo: Fix option list parsing ([6621f37](https://www.github.com/scop/bash-completion/commit/6621f37c5dc2cf59ba2c34ee038f5699418e643e))
* sudoedit: New completion. ([d0a1495](https://www.github.com/scop/bash-completion/commit/d0a14954ab45cb79aba9bff44d5ba910eac7925d))
* ssh-add: New completion. ([28f15fd](https://www.github.com/scop/bash-completion/commit/28f15fd05c28ee84e79ee2d5f17b5867e682efef))
* pwd: New completion. ([6943138](https://www.github.com/scop/bash-completion/commit/694313874a8ef025c5bbbf2a57de058fb684e9b9))
* _command_offset: Properly quote arguments of eval (Alioth: #313499). ([2e97527](https://www.github.com/scop/bash-completion/commit/2e975278c59de9ab6caa5ace65de4b49c6678e65))
* Fix completion loading when a symlink is sourced, thanks to Jonathan Nieder ([318759c](https://www.github.com/scop/bash-completion/commit/318759c8497cfce3704bc1b97c41459a5be08f7b))
* Revert "Fix completion loading when a symlink is sourced, thanks to Jonathan Nieder" ([2ad325d](https://www.github.com/scop/bash-completion/commit/2ad325d4afc4ef96aedc7b91c8e75502647c260d))
* mount.linux: Add some new mount options intoduced in Linux 3.0-3.2 ([1cb1e31](https://www.github.com/scop/bash-completion/commit/1cb1e31e182b0503ea82f9f74c9b43fe91799844))
* _modules: Ignore error messages. ([db53fc7](https://www.github.com/scop/bash-completion/commit/db53fc77a5349088b24830b490b16dfcc6bee540))
* modprobe, modinfo, insmod: Move modprobe and modinfo completions to their own files. ([f67818e](https://www.github.com/scop/bash-completion/commit/f67818e023f9afbeef8698fdd3c08eb0f90ad468))
* sbopkg: Use _parse_help. ([32e8f33](https://www.github.com/scop/bash-completion/commit/32e8f3301801c9ff86f9620ca135f83f71590e29))
* sbopkg, slackpkg, slapt-{get,src}: Use shorter form of the check if file exists. ([3388314](https://www.github.com/scop/bash-completion/commit/33883145af5e3290105e2f784f84c7468617d5ee))
* rmmod: Add option completions. ([47c49db](https://www.github.com/scop/bash-completion/commit/47c49dbfec80c43695e4c95c4cc1e5781c685d93))
* testsuite/generate: Generate less linefeeds. ([068e422](https://www.github.com/scop/bash-completion/commit/068e422222dd862c33137b0d88eab7fe08d9e71e))
* insmod: Install for insmod.static too. ([d02b4e1](https://www.github.com/scop/bash-completion/commit/d02b4e15e3d130d426bcee1a7b03cc72f6144c90))
* mplayer: Add -monitoraspect arg completion. ([a90d7d8](https://www.github.com/scop/bash-completion/commit/a90d7d861a53fa7aee6be505678cd43fb578cafc))
* mplayer: Add generic handling of options that take arguments. ([45c0886](https://www.github.com/scop/bash-completion/commit/45c0886accafd08272fa17c5daad85a1ee52cd56))
* Workaround bash bug that fails to complete <, > ([6f3d650](https://www.github.com/scop/bash-completion/commit/6f3d650e2309feff4f3e80717409ebccb2d38362))
* testsuite: Fix spurious modinfo and modprobe test failures on systems that have /lib and /lib64 dirs. ([d7a6fb1](https://www.github.com/scop/bash-completion/commit/d7a6fb1f47d5daafb555f09fb5d9bd544ae99ec6))
* _filedir: Properly quote paths to avoid unexpected expansion. ([98f90eb](https://www.github.com/scop/bash-completion/commit/98f90ebdf8b7ccf49e7854640712af1ff4a47871))
* Properly declare 'symlinks' dependencies ([ec7fe00](https://www.github.com/scop/bash-completion/commit/ec7fe0006632f2c008414bb3e8c84fa4862c962f))
* pigz: Add -p/--processes arg completion. ([cff897e](https://www.github.com/scop/bash-completion/commit/cff897eda8886c93b5b8c0e939588af36983a5f4))
* apt-get: add 'changelog' to completed commands ([ae98bfa](https://www.github.com/scop/bash-completion/commit/ae98bfa721ff9ff4cd9c64075d2eb7fe01d76828))
* Really complete 'changelog' ([071ba93](https://www.github.com/scop/bash-completion/commit/071ba93a0b9b5db37dc7c5b4b2423507d8f6b741))
* su: Add linux-specific completion ([d2aedc8](https://www.github.com/scop/bash-completion/commit/d2aedc83e143e7d506c8c5340dac4a820cc50076))
* testsuite: Add basic su test case. ([f41d7e2](https://www.github.com/scop/bash-completion/commit/f41d7e2ff660fdf9a95490d4fb438eefa565e9d1))
* su: Fix long option handling. ([e4fe946](https://www.github.com/scop/bash-completion/commit/e4fe946621d824fe04f5c8a8f4f774342b53df40))
* su: Add --session-command arg completion. ([e7c4035](https://www.github.com/scop/bash-completion/commit/e7c4035089548efdf9a15fb96a289585b8b322d1))
* su: Complete -s/--shell with shells instead of all files. ([91528b5](https://www.github.com/scop/bash-completion/commit/91528b527145bec711b4b3ea8c65335a6ed617b0))
* vmstat: New completion. ([bed5694](https://www.github.com/scop/bash-completion/commit/bed56941110985446aed52302f437c50b8182524))
* lyx: Remove simple completion, upstream has more complete one (Debian: #662203) ([a062777](https://www.github.com/scop/bash-completion/commit/a062777d4b9c7dc8a51d1e87c938b48fed810232))
* insmod, modprobe: Don't hardcode path to modinfo (Alioth: #313569) ([199a63b](https://www.github.com/scop/bash-completion/commit/199a63bd4db2020c8d6aaad6723cf881c023c0ff))
* man: --path option is supported on Darwin (Alioth: #313584) ([fb2d657](https://www.github.com/scop/bash-completion/commit/fb2d657fac6be93a1c4ffa76018d8042859e0a03))
* man: Move variable declaration to the right place. ([39ac464](https://www.github.com/scop/bash-completion/commit/39ac4642bff9cd9813b8d4b2758b17fe945584cc))
* testsuite/generate: Tweak linefeeds. ([1fc97c4](https://www.github.com/scop/bash-completion/commit/1fc97c47baf7453b6a27f5e0104c9d434e59acc1))
* acpi: New completion. ([0ab693c](https://www.github.com/scop/bash-completion/commit/0ab693ca50206e46f23fccba52eeb8516e2e6fc9))
* hwclock: New completion. ([3d7102b](https://www.github.com/scop/bash-completion/commit/3d7102b9a5b138091122bad601cc3c104df0ce8f))
* feh: Update option argument completions. ([d2a3db0](https://www.github.com/scop/bash-completion/commit/d2a3db0b7a0cfc7e0da037a003873c19b8fc22a2))
* fbi, feh: Complete more supported file formats. ([63574c8](https://www.github.com/scop/bash-completion/commit/63574c8f14fb813bea08e59e43a170ff0f4cf592))
* fbgs: Add new options introduced in fbida-2.09. ([4710d1b](https://www.github.com/scop/bash-completion/commit/4710d1bf48f9fde1becfcc902aa1c91312a58a91))
* cppcheck: Complete new --relative-paths option arguments ([587d268](https://www.github.com/scop/bash-completion/commit/587d26834ac7116afedfdad86f6cc7a931fbf57d))
* ri: Rename ri_get_methods helper to add leading underscore ([8b3f19a](https://www.github.com/scop/bash-completion/commit/8b3f19a82e60c725f8588a91b1679dde4b1b1967))
* _expand: Suppress unwanted bash error messages (Alioth: #313497) ([ccda61d](https://www.github.com/scop/bash-completion/commit/ccda61d928505abfa839a2c048e3b3d7f89d3a9e))
* make: Add generic variable completion. ([f7240b8](https://www.github.com/scop/bash-completion/commit/f7240b82a4d45f03b741f11b771fae17d3c1c713))
* man: Recognize 3gl as man page filename extension -- at least Mesa uses it. ([739c6d2](https://www.github.com/scop/bash-completion/commit/739c6d2833370e618fcdc37b5a72c15426313fae))
* _realcommand: Try greadlink before readlink (Alioth: #313659). ([32f2239](https://www.github.com/scop/bash-completion/commit/32f223963a013e4875c999109c0a2fa8f9a0ecdf))
* Comment spelling fix. ([f990d5e](https://www.github.com/scop/bash-completion/commit/f990d5e617954c57e73209dba8a8061f87d538a4))
* Spelling fix. ([7532eda](https://www.github.com/scop/bash-completion/commit/7532eda90ff631611e003e9b51dca8a0e558e95c))
* qiv: Add *.svg. ([e0dc594](https://www.github.com/scop/bash-completion/commit/e0dc594d2aabc5040ce0cf53bb73e14f60218025))
* xmllint: Add *.svgz. ([b54aded](https://www.github.com/scop/bash-completion/commit/b54adedd4f158ac41ecd6a8c6cc7d8faf51dc069))
* add xz compression extension for kernel modules ([67d30da](https://www.github.com/scop/bash-completion/commit/67d30da6b79cf7983058a1840bce215990094246))
* autotools: Use MKDIR_P instead of mkdir_p (Alioth: #313671). ([f8ac6a5](https://www.github.com/scop/bash-completion/commit/f8ac6a5aeb287763341e4db2dacd1eb2c3cdecf6))
* lbzip2: Add -n argument completion. ([d141f9c](https://www.github.com/scop/bash-completion/commit/d141f9c6ebd8659638de9d5de64e202ac1f450c0))
* _tilde*: Escape tilde in [[ $1 == \~* ]] tests (RedHat: #817902). ([709d6e0](https://www.github.com/scop/bash-completion/commit/709d6e06902df7205280d0626995fc5b6abe6e89))
* scp: Recognise symlinks to directories as directories (Debian: #666055). ([89acac9](https://www.github.com/scop/bash-completion/commit/89acac9910a27cc9428314e66c3bdbfc48c65f41))
