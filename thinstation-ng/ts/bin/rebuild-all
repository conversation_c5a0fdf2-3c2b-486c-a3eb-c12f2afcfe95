#!/bin/bash
# Description: Thinstation Utility to rebuild all Binary Packages
# URL: http://www.thinstation.org
# Maintainer: <PERSON> (don cupp jr at ya hoo dot com)

for port in `prt-get listinst`; do
	if [ "$port" == "open-vm-tools" ]; then
		prt-get update mesa3d
	fi
	prt-get update -fr $port -if -im
done
if [ -n "`ls -A /var/log/pkgbuild`" ]; then
	echo "The following packages failed to rebuild. Please check their logs in /var/log/pkgbuild"
	ls /var/log/pkgbuild |cut -d . -f 1
else
	echo "Everything rebuilt correctly! Yeah!"
fi
