machine qemu

#module usb-storage
#module isofs            	# ISO9960 file system support for CDRoms
#module udf			# CDRom UDF file system support
module vfat             	# Fat and VFat file system support
module exfat
#module ntfs             	# NTFS file system support
#module ext2             	# Ext2 file system support
#module ext4			# Ext4 file system support

#package ts-classic              # The classic ts logic for network/netfiles/hostname/mounting/telnetd/telnet/playcd
package automount
package autonet
#package alsa

#package xorg7-vmware
package xorg7

package locale-en_US  # English-US

package freerdp		# X RDP Client - fork of rdesktop
#package open-vm-tools
#package cups		# Common Unix Printing System

package gtk-2.0			# Full gtk-2.0 plus clearlooks theme
package gtk-3.0
package gtk-theme-adwaita
package icons-adwaita
package fonts-TTF-BH		# This one works nicely most of the time and is small.

#param fastboot       true				# Mangles the filesystem a special way as to improve boot speed and reduce
							# memory utilization. Cool/Dangerous . Harder to dubug other packages. (Finishing Touch)
							# Set to 'true' to enable or 'lotsofmem' for slightly slower booting but no squash lag on app launch.
#param tsuser         tsuser                            # Name of the user that thinstation will run as.
#param tsuserpasswd   pleasechangeme                    # Do Change! Console/telnet password for non-root

param rootpasswd     pleasechangeme			# Do Change!  Console/telnet password for root
                                        		# If this is enabled, Telnetd will be enabled.
param bootlogo       false				# Enable or Disable the use of the Boot splash.
param boottheme	     default           			# Backgound picture during boot
param splash	     silent
param fbmtrr         0                                  # MTRR value for uvesafb (default = 0, 4 is the best) grep your log to make sure you have not set it to high
param fbsm           ywrap                              # Window scrolling method (redraw, ypan, ywrap) ywrap is best, but may not work correctly for all people
param bootresolution 1280x768-32

param desktop file:./backgrounds/Hive_Lite.jpg		# Custom image to load as desktop background
param defaultconfig  thinstation.conf.buildtime  	# The file with default setup.  No other config file is found
                                                 	#  during boot.
param basename       thinstation     			# Used for all config/tftp/scp file names
param basepath       ts6.3               		# Used to determine path to tftp/scp files
param baseurl        http://www.doncuppjr.net		# Used to determine url to wget files
param haltonerror    false				# Will halt on error, default is true
param hardlinkfs     true
param sametimestmp   true                               # When enabled, the timestamps for all files and folders will be set to 00:00 of todays date in your timezone
param initrdcmd	     "xz -9e --check=crc32 --threads=0"				# Compression mode and level of initrd file. none, gzip -9, lzma -9 ,bzip2 -9

param downloads         /downloads
#param bootprefix "test/" # Add a prefix directory to /boot for PXE based launch, used to allow multiple builds on same PXE server
                          # Value will be inserted as follows: /${bootprefix}boot
                          # Note: This is incompatible with non-network grub based boot images
param syslinuxtheme     "default"
#param allres		true		# Includes a lot of resolution splash images for live-cd s
#param allfirmware	true		# Includes a lot of firmwares for live-cd s
param blacklist "snd-pcsp.ko pcspkr.ko"
param mesa_3d	disable
param blockpackage "alsa pcscd ccidreader acpi splash switchuser gnome-pulse gstreamer gst-libav gst-plugins-base"

