#!/bin/sh
trap "" INT QUIT TSTP
. /etc/thinstation.global
hide_cursor

if [ -n "$2" ] && [ "$2" == "lightdm" ]; then
	TSUSER=$LOGNAME
elif [ -n "$2" ]; then
	TSUSER=$2
fi

if [ "$TSUSER" != "root" ]; then
	HOME=/home/<USER>
else
	HOME=/root
fi

stale_tasks()
{
	tasklist=`ps x |grep -e '/usr/sbin/console-kit-daemon --no-daemon' -A 10000 |grep -E -v 'pam-foreground|ps x|/usr/sbin/console-kit-daemon --no-daemon|login|/sbin/session|grep|cut$|echo$'`
	tasklist=`echo "$tasklist" |cut -d ' ' -f2`
	if [ -n "$tasklist" ]; then
		return 0
	else
		return 1
	fi
}

if [ "$1" == "open" ]; then
#	/bin/used -post
	umask 027
	mkdir -p $HOME
	IFS=$'\n'
	if [ -d /etc/skel ]; then
		for file in `find /etc/skel |cut -d '/' -f4-`; do
			if ! [ -e $HOME/"$file" ]; then
				mkdir -p $HOME/"`dirname $file`"
				cp -a /etc/skel/"$file" $HOME/"$file"
			fi
		done
	fi
	unset IFS
	chown -R "$TSUSER:`id -gn $TSUSER`" $HOME
	chmod 2700 $HOME
fi

if [ "$1" == "close" ] && [ -z "`pidof shutdown`" ] && [ -z "`pidof reboot`" ]; then
	timeout=0
	while stale_tasks && [ "$timeout" -lt "30" ]; do
		for task in $tasklist; do
			kill -HUP $task > /dev/null 2>&1 &
		done
		wait
		let timeout=timeout+1
	done
	timeout=0
	while stale_tasks && [ "$timeout" -lt "30" ]; do
		for task in $tasklist; do
			kill -KILL $task > /dev/null 2>&1 &
		done
		wait
		let timeout=timeout+1
	done
	if is_enabled $DESTRUCTIVE_RELOAD; then
		fuser -k $HOME
		rm -rf $HOME
	fi
	IFS=$'\n'
	if [ -d /media ]; then
		for mount in `ls -1 /media`; do
			fuser -k /media/$mount
			umount /media/$mount
		done
	fi
	if [ -e /etc/lightdm/lightdm.conf ] && grep -q /etc/lightdm/lightdm.conf -e '^autologin-user='; then
		pkill -P1 -fx /bin/lightdm
	fi
fi
