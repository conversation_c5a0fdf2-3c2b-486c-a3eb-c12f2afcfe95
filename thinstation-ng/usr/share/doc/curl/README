                                  _   _ ____  _
                              ___| | | |  _ \| |
                             / __| | | | |_) | |
                            | (__| |_| |  _ <| |___
                             \___|\___/|_| \_\_____|

README

  Curl is a command line tool for transferring data specified with URL
  syntax. Find out how to use curl by reading the curl.1 man page or the
  MANUAL document. Find out how to install Cur<PERSON> by reading the INSTALL
  document.

  libcurl is the library curl is using to do its job. It is readily
  available to be used by your software. Read the libcurl.3 man page to
  learn how.

  You find answers to the most frequent questions we get in the FAQ document.

  Study the COPYING file for distribution terms.

  Those documents and more can be found in the docs/ directory.

CONTACT

  If you have problems, questions, ideas or suggestions, please contact us
  by posting to a suitable mailing list. See https://curl.se/mail/

  All contributors to the project are listed in the THANKS document.

WEBSITE

  Visit the curl website for the latest news and downloads:

        https://curl.se/

GIT

  To download the latest source code off the GIT server, do this:

    git clone https://github.com/curl/curl.git

  (you will get a directory named curl created, filled with the source code)

SECURITY PROBLEMS

  Report suspected security problems via our HackerOne page and not in public.

    https://hackerone.com/curl

NOTICE

  Curl contains pieces of source code that is Copyright (c) 1998, 1999
  Kungliga Tekniska Högskolan. This notice is included here to comply with the
  distribution terms.
