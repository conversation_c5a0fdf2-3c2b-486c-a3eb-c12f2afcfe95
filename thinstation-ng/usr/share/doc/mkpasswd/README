In 1999 I wrote this Whois client from scratch because the alternatives
were obsolete or bloated.

This client is intelligent and can automatically select the appropriate
whois server for most queries.

The internal database is often more accurate than IANA's published one,
but please send me any information you have regarding domains and network
resources which are not correctly handled by the program.

Because of historical reasons this package also contains the mkpasswd
program, which can be used to encrypt a password with crypt(3).


The canonical distribution point for releases of the program is
https://ftp.debian.org/debian/pool/main/w/whois/ .


Useful information sources:
- https://www.ripe.net/ripe/docs/current-ripe-documents/ripe-database-documents
- https://www.iana.org/domains/root/db/
- https://www.icann.org/en/resources/idn/fast-track/string-evaluation-completion
- https://www.aftld.org/

<PERSON> <<EMAIL>>
