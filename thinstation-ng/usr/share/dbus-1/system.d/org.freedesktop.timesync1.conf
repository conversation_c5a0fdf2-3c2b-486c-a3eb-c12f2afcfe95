<?xml version="1.0"?> <!--*-nxml-*-->
<!DOCTYPE busconfig PUBLIC "-//freedesktop//DTD D-BUS Bus Configuration 1.0//EN"
        "https://www.freedesktop.org/standards/dbus/1.0/busconfig.dtd">

<!--
  This file is part of systemd.

  systemd is free software; you can redistribute it and/or modify it
  under the terms of the GNU Lesser General Public License as published by
  the Free Software Foundation; either version 2.1 of the License, or
  (at your option) any later version.
-->

<busconfig>

        <policy user="systemd-timesync">
                <allow own="org.freedesktop.timesync1"/>
                <allow send_destination="org.freedesktop.timesync1"/>
                <allow receive_sender="org.freedesktop.timesync1"/>
        </policy>

        <policy context="default">
                <deny send_destination="org.freedesktop.timesync1"/>

                <allow send_destination="org.freedesktop.timesync1"
                       send_interface="org.freedesktop.DBus.Introspectable"/>

                <allow send_destination="org.freedesktop.timesync1"
                       send_interface="org.freedesktop.DBus.Peer"/>

                <allow send_destination="org.freedesktop.timesync1"
                       send_interface="org.freedesktop.DBus.Properties"
                       send_member="Get"/>

                <allow send_destination="org.freedesktop.timesync1"
                       send_interface="org.freedesktop.DBus.Properties"
                       send_member="GetAll"/>

                <allow send_destination="org.freedesktop.timesync1"
                       send_interface="org.freedesktop.timesync1.Manager"
                       send_member="SetRuntimeNTPServers"/>

                <allow receive_sender="org.freedesktop.timesync1"/>
        </policy>

</busconfig>
