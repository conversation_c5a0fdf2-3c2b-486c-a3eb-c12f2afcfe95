# The ptrace system call is used for interprocess services,
# communication and introspection (like synchronisation, signaling,
# debugging, tracing and profiling) of processes.
#
# Usage of ptrace is restricted by normal user permissions. Normal
# unprivileged processes cannot use ptrace on processes that they
# cannot send signals to or processes that are running set-uid or
# set-gid. Nevertheless, processes running under the same uid will
# usually be able to ptrace one another.
#
# Fedora enables the Yama security mechanism which restricts ptrace
# even further. Sysctl setting kernel.yama.ptrace_scope can have one
# of the following values:
#
# 0 - Normal ptrace security permissions.
# 1 - Restricted ptrace. Only child processes plus normal permissions.
# 2 - Admin-only attach. Only executables with CAP_SYS_PTRACE.
# 3 - No attach. No process may call ptrace at all. Irrevocable.
#
# For more information see Documentation/security/Yama.txt in the
# kernel sources.
#
# The default is 1., which allows tracing of child processes, but
# forbids tracing of arbitrary processes. This allows programs like
# gdb or strace to work when the most common way of having the
# debugger start the debuggee is used:
#    gdb /path/to/program ...
# Attaching to already running programs is NOT allowed:
#    gdb -p ...
# This default setting is suitable for the common case, because it
# reduces the risk that one hacked process can be used to attack other
# processes. (For example, a hacked firefox process in a user session
# will not be able to ptrace the keyring process and extract passwords
# stored only in memory.)
#
# Developers and administrators might want to disable those protections
# to be able to attach debuggers to existing processes. Use
#   sysctl kernel.yama.ptrace_scope=0
# for change the setting temporarily, or copy this file to
# /etc/sysctl.d/20-yama-ptrace.conf to set it for future boots.

kernel.yama.ptrace_scope = 0
