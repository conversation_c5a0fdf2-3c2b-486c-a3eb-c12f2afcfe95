#!/bin/bash
set -x
start=$PWD
self="$0"
self_path="`echo $0 |sed -e 's|ts/bin/bt||g'`"
cd "$self_path"
#set -e

setup_qemu()
{
	pipewirecontext="`find /run/user -name pipewire-0 -printf '%h\n'`"
	if [ -n "$pipewirecontext" ]; then
		export XDG_RUNTIME_DIR="$pipewirecontext"
	fi
        procs=`nproc`
        let procs=procs/2
        HD=thinstation.img
        SHARED_FOLDER="-virtfs local,path=/,mount_tag=host0,security_model=mapped,id=host0,multidevs=remap"
        HDD="-drive file=$HD,index=0,media=disk,format=raw"
        AGENT="-chardev spicevmc,id=ch1,name=vdagent,clipboard=on -device virtio-serial-pci -device virtserialport,chardev=ch1,id=ch1,name=com.redhat.spice.0"
        USBHD="-drive if=none,id=stick,file=/usbhd.img -device nec-usb-xhci,id=xhci -device usb-storage,bus=xhci.0,drive=stick"
        VGA="-vga qxl -global qxl-vga.vram_size=132710400"
#       VGA="-vga virtio"
#       VGA="-vga std"
#       VGA="-vga cirrus"
#       VGA="-vga vmware"
#       SOUND="-device AC97"
	SOUND="-audiodev pipewire,id=pw -device AC97,audiodev=pw"
        CPU="-cpu host -machine q35 -smp cores=$procs,threads=1,sockets=1"
        MEMORY="-m 8192"
#       RNG="-object rng-random,id=RNG"
        MOUSE="-usb -device usb-tablet"
        KVM="-enable-kvm"
#       EFI="-L . --bios usr/share/edk2.git/ovmf-x64/OVMF-pure-efi.fd"
        EFI="-L . --bios usr/share/edk2/ovmf/OVMF_CODE.fd"
#        NETWORK=`bridge $1`
#       QEMU=qemu-system-i386
        QEMU=qemu-system-x86_64

        STANDARD_MACHINE="$SOUND $RNG $VGA $CPU $MEMORY $MOUSE $KVM $NETWORK -serial stdio $AGENT $SHARED_FOLDER"
        NET_MACHINE="$SOUND $RNG $VGA $CPU $MEMORY $MOUSE $KVM -serial stdio"
}

confirm_iso()
{
	case $1 in
		grub) ISO=build/boot-images/grub/thinstation-efi.iso;;
	esac
	if [ ! = $ISO ]; then
		"$1 ISO $ISO"
		"Not Found!"
		exit 1
	fi
	CDROM="-drive file=$ISO,if=ide,media=cdrom,readonly=on"
}

confirm_hd()
{
	if [ ! -e $HD ]; then
		qemu-img create -f raw $HD 32G
	        if [ "$?" != "0" ]; then
			echo "Likely not enought space to create hd image."
			exit 3
		fi
	fi
}

confirm_usb_hd()
{
	if [ ! -e /usbhd.img ]; then
		truncate --size 2G /usbhd.img
	fi
}

bridge()
{
	bootroot=build/boot-images/grub/efi-source
	case $1 in
	net) echo "-netdev user,id=net0,tftp=$bootroot,bootfile=/boot/grub2/pxeboot.img -device virtio-net-pci,netdev=net0";;
	net-efi) echo "-netdev user,id=net0,tftp=$bootroot,bootfile=/EFI/BOOT/BOOTX64.EFI -device virtio-net-pci,netdev=net0";;
	*)	if [ -n "`ifconfig |grep -e br0`" ]; then
		#	echo "-netdev bridge,id=br0,br=br0 -device virtio-net,netdev=br0"
			echo "-netdev bridge,id=n1 -device virtio-net,netdev=n1"
		fi
	;;
	esac
}

wrap_bios()
{
        if [ ! -e build/boot-images/grub ]; then
                echo "$IMAGE not found?"
                echo "Did you build an image."
                exit 1
        fi
        export AGREE=true
        rm thinstation.img
        mkmbrdrv -p d:4g:boot -p l:2g:prstnt -p l:2g:log -p l:0:home -o /build/boot-images/grub/efi-source -l thinstation.img:10g
}

wrap_efi()
{
        if [ ! -e build/boot-images/grub ]; then
                echo "$IMAGE not found?"
                echo "Did you build an image."
                exit 1
        fi
        export AGREE=true
        rm thinstation.img
        mkgptdrv -p d:4g:boot -p l:2g:prstnt -p l:2g:log -p l:0:home -o /build/boot-images/grub/efi-source -l thinstation.img:10g
}

setup_qemu $@

case $1 in
        hd)
		wrap_bios && \
                $QEMU $STANDARD_MACHINE $HDD --boot c
                echo "Exit code $?"
	;;
	hd-efi)
		wrap_efi && \
		$QEMU $STANDARD_MACHINE $EFI $HDD --boot c
		echo "Exit code $?"
	;;
	net)
		$QEMU $NET_MACHINE -boot n -device e1000,netdev=n1 -netdev user,id=n1,tftp=/build/boot-images/grub/efi-source,bootfile=/boot/grub2/pxeboot.img
		echo "Exit code $?"
	;;
        net-efi)
                $QEMU $NET_MACHINE -boot n -device e1000,netdev=n1 -netdev user,id=n1,tftp=/build/boot-images/grub/efi-source,bootfile=/EFI/BOOT/BOOTX64.EFI $EFI
		echo "Exit code $?"
	;;
        cd)
		confirm_iso grub && \
                $QEMU $STANDARD_MACHINE $CDROM --boot d
                echo "Exit code $?"
	;;
	cd-efi)
		confirm_iso grub && \
		confirm_usb_hd && \
		$QEMU $STANDARD_MACHINE $CDROM $USBHD $EFI --boot d
		echo "Exit code $?"
        ;;
	image)
                confirm_hd && \
                confirm_iso grub && \
                $QEMU $STANDARD_MACHINE $HDD $CDROM --boot c,menu=on
                echo "Exit code $?"
	;;
	image-efi)
		confirm_hd && \
		confirm_iso grub && \
		$QEMU $STANDARD_MACHINE $HDD $CDROM $EFI --boot c,menu=on
		echo "Exit code $?"
	;;
	cd-image)
		confirm_iso grub
		HDD="-drive file=$ISO,index=0,media=disk,format=raw"
		$QEMU $STANDARD_MACHINE $HDD --boot c,menu=on
		echo "Exit code $?"
	;;
	cd-image-efi)
		confirm_iso grub
		HDD="-drive file=$ISO,index=0,media=disk,format=raw"
		$QEMU $STANDARD_MACHINE $HDD $EFI --boot c,menu=on
		echo "Exit code $?"
	;;
esac
cd "$start"
