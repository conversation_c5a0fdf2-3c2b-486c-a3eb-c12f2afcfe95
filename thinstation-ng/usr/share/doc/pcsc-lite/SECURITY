SECURITY

This file discusses security related issues with pcsc-lite and how to
handle them.

Sometimes it is dangerous to run daemons under a root account.  If there is
a chance to exploit a buffer overflow you can protect sensitive information
by running it under a different account. It might be useful to create another
user with hardware privileges and run pcscd as that user.  Be sure this user
can manipulate the server sockets.


Application suggestions:

Make sure you Disconnect when you are not using the reader.  Also be sure to
ReleaseContext before exiting your program.
