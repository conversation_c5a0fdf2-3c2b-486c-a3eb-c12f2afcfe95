#!/bin/bash
# Description: Thinstation Utility to install chroot for Thinstation Development
# URL: http://www.thinstation.org
# Maintainer: <PERSON>. (don cupp jr at ya hoo dot com)

core_ports="`grep -v '^\s*#' ts/rpms/core |sed ':a;N;$!ba;s/\n/ /g'`"
grub_ports="`grep -v '^\s*#' ts/rpms/grub |sed ':a;N;$!ba;s/\n/ /g'`"
kernel_ports="`grep -v '^\s*#' ts/rpms/kernel |sed ':a;N;$!ba;s/\n/ /g'`"
firmware_ports="`grep -v '^\s*#' ts/rpms/firmware |sed ':a;N;$!ba;s/\n/ /g'`"
system_ports="`grep -v '^\s*#' ts/rpms/system |sed ':a;N;$!ba;s/\n/ /g'`"
ts_ports="`grep -v '^\s*#' ts/rpms/ts |sed ':a;N;$!ba;s/\n/ /g'`"
if [ -e ts/rpms/other ]; then
	other_ports="`grep -v '^\s*#' ts/rpms/other |sed ':a;N;$!ba;s/\n/ /g'`"
fi

stage1()
{
	if dnf --version| head -n1 |grep -qe dnf5; then
		host_config="--use-host-config"
		xfce=xfce-desktop
	else
		xfce="Xfce Desktop"
	fi
	echo "Chroot not previously setup. Doing it NOW!"

	chown -R root:root ts setup-chroot README.md

	for dir in proc sys home dev mnt downloads var/cache/dnf; do
		if [ ! -e $dir ] ; then
				mkdir -p -m 755 $dir
		fi
	done

	if [ ! -e tmp ]; then
		mkdir -m 1777 tmp
	fi
	if [ ! -e root ]; then
		mkdir -m 750 root
	fi

        if ! mountpoint dev >/dev/null 2>&1 ; then mount --bind /dev dev ; fi
        if ! mountpoint tmp >/dev/null 2>&1 ; then mount -t tmpfs /tmp tmp ; fi
        if ! mountpoint proc >/dev/null 2>&1 ; then mount -t proc proc proc ; fi
        if ! mountpoint sys >/dev/null 2>&1 ; then mount -t sysfs none sys ; fi

	ROOT=$PWD
	dnf install --setopt=keepcache=1 -y --installroot=$ROOT $host_config --releasever=42 $core_ports $kernel_ports $grub_ports $firmware_ports
	cd $ROOT
	touch $ROOT/dostage2
}

stage2()
{
#	exclude=`echo "$core_ports"|sed -e 's/ /\/\|/g'`
#	mkdir -p /var/lib/pkg
#	procs=`nproc`
#	let procs-=1
#	for Root in $ROOT/ts $XTRWRKNG; do
#		find -L $Root/ports -name Pkgfile | \
#		grep -Ev "nocache|proprietary|binary-core|binary-opt|relegated" | \
#		grep -Ev "$exclude" | \
#		xargs -n 1 -P $procs -I {} bash -c 'install_port "$@"' _ {}
#	done
#	for core_port in $core_ports; do
#              	Pkgfile=/ts/ports/core/$core_port/Pkgfile
#		install_port $Pkgfile
#	done
#	add_to_db
#	printf "\rPort Installation %-40s\n" "Complete"
#	printf "\rRunning the pre and post install scripts\n"
#	post_install
#	printf "\rPort Install scripts %-40s\n" "Complete"
	rm /dostage2
	touch /dostage3
}

stage3()
{
	cp -a /ts/repos/* /etc/yum.repos.d/.
	dnf makecache
        dnf install --setopt=keepcache=1 -y --releasever=42 $system_ports
	dnf install --setopt=keepcache=1 -y --allowerasing $ts_ports $other_ports
	for Root in $TSWRKNG $XTRWRKNG; do
		if [ $Root = $TSWRKNG ]
			then export Cache=/ts
			else export Cache=$Root
		fi
		export Root
		procs=`nproc`
                let procs-=1
		ls --color=never $Root/packages |xargs -P $procs -I {} bash -c 'unwind_package "$@"' _ {}
	done
	printf "\rPackage Population %-40s\n" "Complete"
	echo "Populating tools"
	update -t
	cd $TSWRKNG
	./build --regenconf
	if [ ! -e /build ] ; then
		ln -sf ts/build /build
	fi
	rm /dostage3
	touch /dostage4
}

unwind_package()
{
	pkg=$1
	printf "\rPopulating Package %-40s" "$pkg"
        cd $Root/packages/$pkg
        if [ -e .unwind ]; then
        	if cat .unwind |grep -e "#" |cut -d "#" -f2 |md5sum -c --quiet ; then
                	. .unwind
                else
                       	update $pkg
                fi
        else
              	update $pkg
        fi
}

stage4()
{
	rm /dostage4
#	printf "\rLooking at core binary ports\n"
#	procs=`nproc`
#        let procs-=1
#        for Root in $ROOT/ts $XTRWRKNG; do
#                find -L $Root/ports -name Pkgfile | \
#                grep -E "binary-core" | \
#                xargs -n 1 -P $procs -I {} bash -c 'install_binary_port "$@"' _ {}
#        done
#        # Fix for db access violations
#        if [ -e /var/log/pkgbuild ]; then
#                for prt in `ls /var/log/pkgbuild -1`; do
#                        prt-get install `basename $prt .log` -if -im -ns > /dev/null 2>&1
#                done
#        fi
#	add_to_db
#        printf "\rBinary Core Port Installation %-40s\n" "Complete"
#	post_install
}

update_db()
{
	for db in `find /tmp/ -name \*.db`; do
                cat $db >> /var/lib/pkg/db
                echo "" >> /var/lib/pkg/db
		rm $db
        done
}

install_binary_port()
{
	Pkgfile=$1
	if ! install_port $Pkgfile; then
		port_name=`echo $Pkgfile |cut -d "/" -f6`
		if ! $autodl; then
			answered=false
			while ! $answered; do
				printf "Would you like to try and build it now? Y/N:"
				read -n 1 answer
				case $answer in
					Y|y) prt-get install -fr -if -im -is -ns $port_name;answered=true;;
					n|n) answered=true;;
					*) answered=false;;
				esac
			done
		else
			prt-get install -fr -if -im -is -ns $port_name
		fi
		printf "\n"
	fi
}

install_port()
{
	Pkgfile=$1
	. $Pkgfile
	port_dir=`dirname $Pkgfile`
	port_name=`basename $port_dir`
	pkg_name="`find -L $port_dir -type f -name ${name}#${version}-${release}.pkg.tar.\* -printf '%f'`"
	if [ -n "$XTRWRKNG" ] \
	&& [ -e "$XTRWRKNG/${port_dir##//ts/}" ]; then
		return 0
	else
	printf "\rInstalling Port %-40s" "$port_name"
		if [ -f $port_dir/$pkg_name ]; then
			echo $name > /tmp/$name.db
			echo $version-$release >> /tmp/$name.db
			tar -hxvf $port_dir/$pkg_name -C / >> /tmp/$name.db 2>/dev/null #|| rm /tmp/$name.db && echo "$pkg_name failed"
			if [ -e $port_dir/pre-install ]; then
#				echo "echo $port_name pre" >> $ROOT/post-install.sh
				echo ". $port_dir/pre-install || echo \"$port_name pre-install failed!\" && true" >> $ROOT/post-install.sh
			fi
			if [ -e $port_dir/post-install ]; then
#				echo "echo $port_name post" >> $ROOT/post-install.sh
				echo ". $port_dir/post-install || echo \"$port_name post-install failed!\" && true" >> $ROOT/post-install.sh
			fi
			return 0
		else
			printf "Could not find an archive.\n"
			return 1
		fi
	fi
}

add_to_db()
{
	for db in `find /tmp/ -name \*.db`; do
                cat $db >> /var/lib/pkg/db
                echo "" >> /var/lib/pkg/db
		rm $db
        done
}

post_install()
{
	if [ -e /post-install.sh ]; then
		source /post-install.sh
		rm /post-install.sh
	fi
}

export -f unwind_package
export -f install_port
export -f install_binary_port

if [ -z "$autodl" ]; then
	autodl=false
fi

ROOT=$PWD
if [ -e $ROOT/dostage4 ]; then
		stage4
	elif [ -e $ROOT/dostage3 ]; then
		stage3
	elif [ -e $ROOT/dostage2 ]; then
		stage2
	else
		stage1
fi
