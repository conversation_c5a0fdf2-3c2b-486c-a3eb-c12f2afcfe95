#!/bin/bash

# Validate input argument
if [[ "$#" -ne 1 ]]; then
    echo "Usage: $0 <blue|green|alt>"
    exit 1
fi

OPTION=$1

# Define the mount point and target directory
MOUNT_POINT="/mnt/cdrom0"
TARGET_DIR="/boot/boot"

# Function to mount ThinStation
mount_thinstation() {
    echo "Mounting ThinStation..."
    if ! mount | grep -q "$MOUNT_POINT"; then
        mkdir -p "$MOUNT_POINT"
        mount -L ThinStation "$MOUNT_POINT" || { echo "Failed to mount ThinStation at $MOUNT_POINT"; exit 1; }
    else
        echo "ThinStation is already mounted at $MOUNT_POINT"
    fi
}

# Determine active and inactive environments
ACTIVE_ENV=""
INACTIVE_ENV=""

BOOT_IMAGE=$(cat /proc/cmdline | grep -o 'BOOT_IMAGE=[^ ]*' | cut -d'=' -f2)
if [[ "$BOOT_IMAGE" =~ blue ]]; then
    ACTIVE_ENV="blue"
    INACTIVE_ENV="green"
elif [[ "$BOOT_IMAGE" =~ green ]]; then
    ACTIVE_ENV="green"
    INACTIVE_ENV="blue"
fi

# Determine which environment to update
ENV_TO_UPDATE=""
case $OPTION in
    blue|green)
        ENV_TO_UPDATE=$OPTION
        ;;
    alt)
        if [ -n "$INACTIVE_ENV" ]; then
            ENV_TO_UPDATE=$INACTIVE_ENV
        else
            echo "Could not determine the inactive environment to update."
            exit 1
        fi
        ;;
    *)
        echo "Invalid option: $OPTION. Please specify 'blue', 'green', or 'alt'."
        exit 1
        ;;
esac

# Mount ThinStation
mount_thinstation

# Update the environment
echo "Updating $ENV_TO_UPDATE environment..."
cp -f "$MOUNT_POINT/boot/vmlinuz" "$TARGET_DIR/vmlinuz-$ENV_TO_UPDATE"
cp -f "$MOUNT_POINT/boot/initrd" "$TARGET_DIR/initrd-$ENV_TO_UPDATE"
cp -f "$MOUNT_POINT/boot/lib.squash" "$TARGET_DIR/lib.squash-$ENV_TO_UPDATE"

echo "$ENV_TO_UPDATE environment updated successfully."
