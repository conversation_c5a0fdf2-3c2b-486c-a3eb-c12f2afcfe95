# /etc/acpi/events/sleepbtn
# This is called when the user presses the sleep button and calls
# /etc/acpi/lid.sh for further processing.

# Optionally you can specify the placeholder %e. It will pass
# through the whole kernel event message to the program you've
# specified.

# We need to react on "button sleep.*" and "button/sleep.*" because
# of kernel changes.

event=button[ /]sleep
action=/etc/acpi/lid.sh
